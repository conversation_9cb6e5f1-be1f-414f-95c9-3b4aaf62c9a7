import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import {
  DailyOverviewEntity,
  PersonalChatMessageEntity,
  UserRegisterStatisticEntity,
  WorkCommentEntity
} from '@qdy/mongo'
import { AnyObject, Model } from 'mongoose'
import { Prisma, PrismaService } from '@qdy/mysql'
import { Cron } from '@nestjs/schedule'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { OrderStatus } from '../vip/vip.dto'
import dayjs from 'dayjs'
import { WebhookEvents } from './type'
import { SalesType, TeamStatisticDTO, TeamStatisticListRequest } from './overview.dto'

@Injectable()
export class OverviewService implements OnModuleInit {
  constructor(
    @InjectModel(DailyOverviewEntity.name) private dailyOverviewModel: Model<DailyOverviewEntity>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(WorkCommentEntity.name)
    private workCommentModel: Model<WorkCommentEntity>,
    @InjectModel(UserRegisterStatisticEntity.name)
    private userRegisterStatisticModel: Model<UserRegisterStatisticEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService
  ) {}

  onModuleInit() {
    // this.compensateForOrderLoss()
  }

  async getTopTen(type: number) {
    const startDate = new Date()

    switch (type) {
      case 1:
        startDate.setDate(startDate.getDate() - 1)
        break
      case 2:
        startDate.setDate(startDate.getDate() - 7)
        break
      case 3:
        startDate.setDate(startDate.getDate() - 30)
        break
      default:
        return
    }

    startDate.setHours(0, 0, 0, 0)

    const res = await this.dailyOverviewModel.aggregate([
      {
        $match: {
          createTime: {
            $gte: dayjs(startDate).format('YYYY-MM-DD'),
            $lte: dayjs().format('YYYY-MM-DD')
          }
        }
      },
      {
        $group: {
          _id: '$teamId',
          totalCount: {
            $sum: {
              $add: ['$singleCount', '$groupCount', '$commentCount']
            }
          },
          singleTotalCount: {
            $sum: {
              $add: ['$singleCount']
            }
          },
          groupTotalCount: {
            $sum: {
              $add: ['$groupCount']
            }
          },
          commentTotalCount: {
            $sum: {
              $add: ['$commentCount']
            }
          }
        }
      },
      {
        $sort: { totalCount: -1 }
      },
      {
        $limit: 30
      },
      {
        $project: {
          _id: 0,
          teamId: '$_id',
          commentTotalCount: 1,
          groupTotalCount: 1,
          singleTotalCount: 1,
          totalCount: 1
        }
      }
    ])

    const autoRes = await this.dailyOverviewModel.aggregate([
      {
        $match: {
          createTime: {
            $gte: dayjs(startDate).format('YYYY-MM-DD'),
            $lte: dayjs().format('YYYY-MM-DD')
          }
        }
      },
      {
        $group: {
          _id: '$teamId',
          totalCount: {
            $sum: {
              $add: ['$autoSingleCount', '$autoGroupCount', '$autoCommentCount']
            }
          },
          singleTotalCount: {
            $sum: {
              $add: ['$autoSingleCount']
            }
          },
          groupTotalCount: {
            $sum: {
              $add: ['$autoGroupCount']
            }
          },
          commentTotalCount: {
            $sum: {
              $add: ['$autoCommentCount']
            }
          }
        }
      },
      {
        $sort: { totalCount: -1 }
      },
      {
        $limit: 30
      },
      {
        $project: {
          _id: 0,
          teamId: '$_id',
          commentTotalCount: 1,
          groupTotalCount: 1,
          singleTotalCount: 1,
          totalCount: 1
        }
      }
    ])

    const autoTeams = await this.prisma.team.findMany({
      where: {
        id: {
          in: autoRes.map((item) => item.teamId)
        }
      }
    })

    const teams = await this.prisma.team.findMany({
      where: {
        id: {
          in: res.map((item) => item.teamId)
        }
      }
    })

    const teamMap = teams.reduce((acc, cur) => {
      acc[cur.id] = cur
      return acc
    }, {})

    const autoTeamMap = autoTeams.reduce((acc, cur) => {
      acc[cur.id] = cur
      return acc
    }, {})

    for (let i = 0; i < res.length; i++) {
      const item = res[i]
      const team = teamMap[item.teamId]
      item.teamName = team?.name
      item.teamAvatar = team?.avatar
      item.invitationCode = team?.invitationCode

      let groupMesssageCount = 0
      let singleMesssageCount = 0
      let commentMesssageCount = 0

      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: item.teamId
        },
        select: {
          openId: true
        }
      })

      const messages = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            toUserId: { $in: platformAccounts.map((item) => item.openId) },
            createTime: {
              $gte: startDate.getTime(),
              $lte: new Date(dayjs().format('YYYY-MM-DD')).getTime()
            },
            event: {
              $in: [WebhookEvents.IMGroupReceiveMessage, WebhookEvents.IMReceiveMessage]
            }
          }
        },
        {
          $group: {
            _id: '$event',
            count: { $sum: 1 }
          }
        }
      ])

      const comments = await this.workCommentModel.aggregate([
        {
          $match: {
            toUserId: { $in: platformAccounts.map((item) => item.openId) },
            createTime: {
              $gte: startDate.getTime(),
              $lte: new Date(dayjs().format('YYYY-MM-DD')).getTime()
            }
          }
        },
        {
          $match: {
            $expr: { $ne: ['$toUserId', '$fromUserId'] }
          }
        },
        {
          $group: {
            _id: '$event',
            count: { $sum: 1 }
          }
        }
      ])

      ;[...messages, ...comments].forEach((item) => {
        switch (item._id) {
          case WebhookEvents.IMGroupReceiveMessage:
            groupMesssageCount = item.count
            break
          case WebhookEvents.IMReceiveMessage:
            singleMesssageCount = item.count
            break
          case WebhookEvents.CommentReply:
            commentMesssageCount = item.count
            break
          default:
            break
        }
      })

      item.receiveGroupCount = groupMesssageCount
      item.receiveSingleCount = singleMesssageCount
      item.receiveCommentCount = commentMesssageCount
      item.receiveTotalCount = groupMesssageCount + singleMesssageCount + commentMesssageCount
    }

    for (let i = 0; i < autoRes.length; i++) {
      const item = autoRes[i]
      const team = autoTeamMap[item.teamId]
      item.teamName = team?.name
      item.teamAvatar = team?.avatar
      item.invitationCode = team?.invitationCode

      let groupMesssageCount = 0
      let singleMesssageCount = 0
      let commentMesssageCount = 0

      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: item.teamId
        },
        select: {
          openId: true
        }
      })

      const messages = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            toUserId: { $in: platformAccounts.map((item) => item.openId) },
            createTime: {
              $gte: startDate.getTime(),
              $lte: new Date(dayjs().format('YYYY-MM-DD')).getTime()
            },
            event: {
              $in: [WebhookEvents.IMGroupReceiveMessage, WebhookEvents.IMReceiveMessage]
            }
          }
        },
        {
          $group: {
            _id: '$event',
            count: { $sum: 1 }
          }
        }
      ])

      const comments = await this.workCommentModel.aggregate([
        {
          $match: {
            toUserId: { $in: platformAccounts.map((item) => item.openId) },
            createTime: {
              $gte: startDate.getTime(),
              $lte: new Date(dayjs().format('YYYY-MM-DD')).getTime()
            }
          }
        },
        {
          $match: {
            $expr: { $ne: ['$toUserId', '$fromUserId'] }
          }
        },
        {
          $group: {
            _id: '$event',
            count: { $sum: 1 }
          }
        }
      ])

      ;[...messages, ...comments].forEach((item) => {
        switch (item._id) {
          case WebhookEvents.IMGroupReceiveMessage:
            groupMesssageCount = item.count
            break
          case WebhookEvents.IMReceiveMessage:
            singleMesssageCount = item.count
            break
          case WebhookEvents.CommentReply:
            commentMesssageCount = item.count
            break
          default:
            break
        }
      })

      item.receiveGroupCount = groupMesssageCount
      item.receiveSingleCount = singleMesssageCount
      item.receiveCommentCount = commentMesssageCount
      item.receiveTotalCount = groupMesssageCount + singleMesssageCount + commentMesssageCount
    }

    return {
      auto: autoRes.filter((item) => item.totalCount > 0),
      normal: res.filter((item) => item.totalCount > 0)
    }
  }

  @Cron('55 23 * * *', {
    name: 'dailyReport',
    timeZone: 'Asia/Shanghai'
  })
  async task() {
    const topTen1 = await this.getTopTen(1)
    await this.cacheManager.set('topTen1', topTen1, 0)
    this.cleanup(topTen1)
    const topTen2 = await this.getTopTen(2)
    await this.cacheManager.set('topTen2', topTen2, 0)
    this.cleanup(topTen2)
    const topTen3 = await this.getTopTen(3)
    await this.cacheManager.set('topTen3', topTen3, 0)
  }

  async getData(type: number) {
    return (await this.cacheManager.get(`topTen${type}`)) ?? []
  }

  @Cron('30 00 * * *', {
    name: 'cashDailyReport',
    timeZone: 'Asia/Shanghai'
  })
  async cashTask() {
    const now = dayjs().tz('Asia/Shanghai')

    const whereDicOrderRecord: Prisma.OrderRecordWhereInput = {}
    const whereDicOrder: Prisma.OrderWhereInput = { orderStatus: OrderStatus.SUCCESS }

    const startDay = now.subtract(2, 'day').format('YYYY-MM-DD')
    const yesterday = now.subtract(1, 'day').format('YYYY-MM-DD')

    const startOfYesterday = new Date(startDay + 'T16:00:00') // 获取昨天的开始时间
    const endOfYesterday = new Date(yesterday + 'T16:00:00') // 获取昨天的结束时间

    whereDicOrderRecord.createTime = { gte: startOfYesterday, lte: endOfYesterday }
    whereDicOrder.payTime = { gte: startOfYesterday, lte: endOfYesterday }

    const outSum = await this.prisma.orderRecord.aggregate({
      where: whereDicOrderRecord,
      _sum: {
        realityPrice: true
      }
    })

    const groupedSum = await this.prisma.order.aggregate({
      where: whereDicOrder,
      _sum: {
        payAmount: true
      }
    })

    const lastCashDailyReport = await this.prisma.cashDailyReport.findFirst({
      where: {
        date: new Date(yesterday)
      }
    })

    if (lastCashDailyReport) {
      await this.prisma.cashDailyReport.update({
        where: {
          id: lastCashDailyReport.id
        },
        data: {
          income: groupedSum._sum.payAmount ?? 0,
          expense: outSum._sum.realityPrice ?? 0
        }
      })
    } else {
      await this.prisma.cashDailyReport.create({
        data: {
          date: new Date(yesterday),
          income: groupedSum._sum.payAmount ?? 0,
          expense: outSum._sum.realityPrice ?? 0
        }
      })
    }
  }

  async getFinancial(type: number) {
    if (!type) {
      type = 3
    }
    const now = new Date()
    let reDays = 30
    if (type === 1) {
      reDays = 1
    } else if (type === 2) {
      reDays = 7
    } else if (type === 3) {
      reDays = 30
    }
    const date = new Date(new Date(now.setDate(now.getDate() - reDays)).toISOString().split('T')[0])
    return this.prisma.cashDailyReport.findMany({ where: { date: { gte: date }, type: 0 } })
  }

  @Cron('0 0 2 * * *', {
    name: 'registerUserStatistic',
    timeZone: 'Asia/Shanghai'
  })
  async RegisterUserStatisticTask() {
    const now = dayjs().tz('Asia/Shanghai')
    // 获取昨天的日期
    const startDay = now.subtract(2, 'day').format('YYYY-MM-DD')
    const yesterday = now.subtract(1, 'day').format('YYYY-MM-DD')

    const startOfYesterday = new Date(startDay + 'T16:00:00') // 获取昨天的开始时间
    const endOfYesterday = new Date(yesterday + 'T16:00:00') // 获取昨天的结束时间

    const registerUser = await this.userRegisterStatisticModel.findOne({
      createTime: yesterday
    })
    const count = await this.prisma.user.count({
      where: {
        createTime: {
          gte: startOfYesterday,
          lte: endOfYesterday
        }
      }
    })
    if (registerUser) {
      await this.userRegisterStatisticModel.updateOne(
        { createTime: yesterday },
        {
          registerCount: count
        }
      )
    } else {
      await this.userRegisterStatisticModel.create({
        createTime: yesterday,
        registerCount: count
      })
    }
  }

  /**
   * 30天注册用户数
   * @returns
   */
  async getRegister() {
    const date = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
    return this.userRegisterStatisticModel.aggregate([
      {
        $match: {
          $and: [
            {
              createTime: {
                $gte: date
              }
            }
          ]
        }
      },
      {
        $project: {
          createTime: 1,
          registerCount: 1
        }
      }
    ])
  }

  cleanup(obj: AnyObject) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    obj = null

    // 如果暴露了 GC，可以手动触发垃圾回收
    if (global.gc) {
      global.gc()
    }
  }

  async compensateForOrderLoss() {
    // 未过期并付款金额大于0的订单
    const orderList = await this.prisma.order.findMany({
      where: {
        orderStatus: OrderStatus.SUCCESS,
        toTime: {
          gt: new Date()
        },
        payAmount: {
          gt: 0
        }
      },
      include: {
        Contract: true
      },
      orderBy: { toTime: 'desc' }
    })

    const unContractList = orderList.filter((channel) => channel.Contract.length === 0)

    for (let index = 0; index < unContractList.length; index++) {
      const order = unContractList[index]

      if (!order.isUpgrade) {
        await this.prisma.contract.create({
          data: {
            orderId: order.id,
            orderNo: order.orderNo,
            startTime: order.payTime,
            endTime: order.toTime,
            amount: order.payAmount,
            teamId: order.teamId,
            interestId: order.interestId,
            interestCount: order.interestCount,
            createTime: order.payTime,
            status: 0
          }
        })
      } else {
        let startDate = order.fromTime
        const startTime = dayjs(order.toTime)
          .subtract(order.vipMonth + order.freeMonth, 'month')
          .toDate()

        if (startTime > order.fromTime) {
          startDate = startTime
        }

        await this.prisma.contract.create({
          data: {
            orderId: order.id,
            orderNo: order.orderNo,
            startTime: startDate,
            endTime: order.toTime,
            amount: order.payAmount,
            teamId: order.teamId,
            interestId: order.interestId,
            interestCount: order.interestCount,
            createTime: order.payTime,
            status: 0
          }
        })
      }
    }
  }

  async getTeamRate(query: TeamStatisticListRequest): Promise<TeamStatisticDTO> {
    const { page, size } = query

    if (!query.startTime || !query.endTime) {
      // 计算默认的 7 天时间范围
      const now = dayjs().tz('Asia/Shanghai')
      query.endTime = now.subtract(1, 'day').endOf('day').valueOf()
      query.startTime = now.subtract(7, 'day').startOf('day').valueOf()
    }

    const startTime = dayjs(Number(query.startTime)).tz('Asia/Shanghai').format('YYYY-MM-DD')
    const endTime = dayjs(Number(query.endTime)).tz('Asia/Shanghai').format('YYYY-MM-DD')

    const result = await this.prisma.teamStatistic.aggregate({
      where: {
        statisticDate: {
          gte: new Date(startTime),
          lte: new Date(endTime)
        }
      },
      _sum: {
        registerTeamCount: true,
        paidTeamCount: true,
        renewTeamCount: true,
        payAmountTotal: true
      }
    })

    const registerTeamTotal = result._sum.registerTeamCount
    const paidTeamTotal = result._sum.paidTeamCount
    const renewTeamTotal = result._sum.renewTeamCount
    const { payAmountTotal } = result._sum

    const where: Parameters<typeof this.prisma.teamStatistic.findMany>[0]['where'] = {
      statisticDate: {
        gte: new Date(startTime),
        lte: new Date(endTime)
      }
    }

    const total = await this.prisma.teamStatistic.count({ where })

    const data = await this.prisma.teamStatistic.findMany({
      where,
      orderBy: {
        statisticDate: 'desc'
      },
      select: {
        statisticDate: true,
        registerTeamCount: true,
        paidTeamCount: true,
        conversionRate: true,
        expiredTeamCount: true,
        renewTeamCount: true,
        renewRate: true,
        payAmountTotal: true,
        customerUnitPrice: true
      },
      skip: (page - 1) * size,
      take: size
    })

    const expiredTeams = await this.prisma.team.findMany({
      where: {
        salesType: {
          in: [SalesType.FirstBuy, SalesType.ReBuy]
        },
        vip: {
          expirationTime: {
            gt: new Date(Number(query.startTime)),
            lt: new Date(Number(query.endTime))
          }
        },
        isDelete: false
      }
    })

    const expiredTeamTotal = expiredTeams.length

    const conversionRateTotal =
      paidTeamTotal > 0 && registerTeamTotal > 0
        ? parseFloat(((paidTeamTotal * 100) / registerTeamTotal).toFixed(2))
        : 0
    const renewRateTotal =
      renewTeamTotal > 0
        ? parseFloat(((renewTeamTotal * 100) / (renewTeamTotal + expiredTeamTotal)).toFixed(2))
        : 0

    const customerUnitPrice =
      paidTeamTotal + renewTeamTotal > 0
        ? parseFloat((payAmountTotal / (paidTeamTotal + renewTeamTotal)).toFixed(2))
        : 0

    return {
      total,
      page,
      size,
      registerTeamTotal,
      paidTeamTotal,
      conversionRateTotal,
      expiredTeamTotal,
      renewTeamTotal,
      renewRateTotal,
      payAmountTotal,
      customerUnitPrice,
      data: data.map((item) => {
        return {
          ...item,
          statisticDate: new Date(item.statisticDate).getTime()
        }
      })
    }
  }
}
