/*
  Warnings:

  - You are about to drop the column `commentCardinalNumber` on the `SystemDosage` table. All the data in the column will be lost.
  - You are about to drop the column `groupCardinalNumber` on the `SystemDosage` table. All the data in the column will be lost.
  - You are about to drop the column `signCardinalNumber` on the `SystemDosage` table. All the data in the column will be lost.
  - You are about to drop the column `commentDosageLimit` on the `Team` table. All the data in the column will be lost.
  - You are about to drop the column `groupDosageLimit` on the `Team` table. All the data in the column will be lost.
  - You are about to drop the column `signDosageLimit` on the `Team` table. All the data in the column will be lost.
  - Added the required column `memberAccountCommentCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.
  - Added the required column `memberAccountGroupCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.
  - Added the required column `memberAccountSignCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.
  - Added the required column `standardAccountCommentCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.
  - Added the required column `standardAccountGroupCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.
  - Added the required column `standardAccountSignCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `SystemDosage` DROP COLUMN `commentCardinalNumber`,
    DROP COLUMN `groupCardinalNumber`,
    DROP COLUMN `signCardinalNumber`,
    ADD COLUMN `memberAccountCommentCardinalNumber` INTEGER NOT NULL,
    ADD COLUMN `memberAccountGroupCardinalNumber` INTEGER NOT NULL,
    ADD COLUMN `memberAccountSignCardinalNumber` INTEGER NOT NULL,
    ADD COLUMN `standardAccountCommentCardinalNumber` INTEGER NOT NULL,
    ADD COLUMN `standardAccountGroupCardinalNumber` INTEGER NOT NULL,
    ADD COLUMN `standardAccountSignCardinalNumber` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `Team` DROP COLUMN `commentDosageLimit`,
    DROP COLUMN `groupDosageLimit`,
    DROP COLUMN `signDosageLimit`;

-- CreateTable
CREATE TABLE `VIP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `teamId` INTEGER NOT NULL,
    `signDosageLimit` INTEGER NOT NULL DEFAULT 0,
    `groupDosageLimit` INTEGER NOT NULL DEFAULT 0,
    `commentDosageLimit` INTEGER NOT NULL DEFAULT 0,
    `platformAccountNumberLimit` INTEGER NOT NULL DEFAULT 0,
    `teamMemberNumberLimit` INTEGER NOT NULL DEFAULT 0,
    `expirationTime` DATETIME(3) NOT NULL,

    UNIQUE INDEX `VIP_teamId_key`(`teamId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `VIP` ADD CONSTRAINT `VIP_teamId_fkey` FOREIGN KEY (`teamId`) REFERENCES `Team`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
