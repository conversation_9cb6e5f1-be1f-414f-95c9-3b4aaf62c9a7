import { Injectable, OnModuleInit } from '@nestjs/common'
import { Client, ClientGrpc, Transport } from '@nestjs/microservices'
import { Socket, socketConfig } from '@qdy/proto'

@Injectable()
export class AccountSocketService implements OnModuleInit {
  @Client({
    transport: Transport.GRPC,
    options: socketConfig
  })
  private readonly client: ClientGrpc

  socketService: Socket

  constructor() {}

  onModuleInit() {
    this.socketService = this.client.getService<Socket>('Socket')
  }
}
