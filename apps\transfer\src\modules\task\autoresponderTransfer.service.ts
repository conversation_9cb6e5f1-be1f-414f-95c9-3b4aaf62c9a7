import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { Queue, Worker } from 'bullmq'
import { AnyObject } from 'mongoose'
import { PrismaService, type Autoresponder } from '@qdy/mysql'

@Injectable()
export class autoresponderTransferService implements OnModuleInit {
  private readonly logger = new Logger(autoresponderTransferService.name)

  LOCK_TIMEOUT = 10 * 60 * 1000 // 10分钟的锁超时时间

  dataQueue: Queue

  dataWorker: Worker

  serverNumber: number

  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  async onModuleInit() {
    // this.dataQueue = new Queue('data-migration-autoresponder-transfer', {
    //   connection: {
    //     host: process.env.REDIS_HOST,
    //     port: parseInt(process.env.REDIS_PORT, 10),
    //     db: parseInt(process.env.REDIS_SYNC_DB, 10),
    //     password: process.env.REDIS_PASSWORD
    //   }
    // })
    // this.dataWorker = new Worker(
    //   'data-migration-autoresponder-transfer',
    //   async (job) => {
    //     const { autoresponder } = job.data

    //     this.logger.log(autoresponder)

    //     await this.migrateUserTableData(autoresponder)
    //   },
    //   {
    //     connection: {
    //       host: process.env.REDIS_HOST,
    //       port: parseInt(process.env.REDIS_PORT, 10),
    //       db: parseInt(process.env.REDIS_SYNC_DB, 10),
    //       password: process.env.REDIS_PASSWORD
    //     }
    //   }
    // )

    const lock = ((await this.cacheManager.get('init-autoresponder-transfer')) || 0) as number

    if (!lock) {
      this.serverNumber = 1
      this.cacheManager.set('init-autoresponder-transfer', 1, 1000 * 60 * 60)
      // this.onAutoresponderTransfer()
    }

    this.logger.log('autoresponderTransferService init')
  }

  /**
   * 来执行迁移
   */
  async onAutoresponderTransfer() {
    const autoresponderList = await this.prisma.autoresponder.findMany({
      where: {
        isNew: false
      },
      orderBy: {
        id: 'asc'
      }
    })

    const activeArray = autoresponderList.slice(0, this.serverNumber)

    if (activeArray.length > 0) {
      for (let j = 0; j < activeArray.length; j++) {
        const autoresponder = activeArray[j]
        await this.dataQueue.add(
          'data-migration-autoresponder-transfer',
          {
            autoresponder
          },
          {
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `data-migration-autoresponder-transfer-sms-${autoresponder.id}`
          }
        )
      }
    }

    const otherContents = autoresponderList.slice(this.serverNumber)

    if (otherContents.length) {
      await this.cacheManager.store.client.hset(
        'autoresponder:transfer',
        'autoresponderList',
        JSON.stringify(otherContents)
      )
    }
  }

  async migrateUserTableData(autoresponder: Autoresponder) {
    const isLock = await this.tryAcquireLock(`${autoresponder.id}`)

    this.logger.log(`${autoresponder.id}-${isLock}`)

    if (!isLock) {
      // 数据迁移逻辑
      await this.onTransferOverviewData(autoresponder)
    }

    const autoresponderList = await this.cacheManager.store.client.hget(
      'autoresponder:transfer',
      'autoresponderList'
    )

    if (autoresponderList) {
      const otherList = JSON.parse(autoresponderList)

      const activeArray = otherList.slice(0, this.serverNumber)

      if (activeArray.length > 0) {
        for (let j = 0; j < activeArray.length; j++) {
          const autoresponder = activeArray[j]
          await this.dataQueue.add(
            'data-migration-autoresponder-transfer',
            {
              autoresponder
            },
            {
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `data-migration-autoresponder-transfer-sms-${autoresponder.id}`
            }
          )
        }
      }

      const otherContents = otherList.slice(this.serverNumber)

      if (otherContents.length) {
        await this.cacheManager.store.client.hset(
          'autoresponder:transfer',
          'autoresponderList',
          JSON.stringify(otherContents)
        )
      } else {
        await this.cacheManager.store.client.hdel('autoresponder:transfer', 'autoresponderList')
      }
    }
  }

  async onTransferOverviewData(autoresponder: Autoresponder) {
    if (
      (autoresponder.opusers as AnyObject[]).length === 0 &&
      (autoresponder.platformAccountIds as AnyObject[]).length === 0
    ) {
      return
    }

    this.logger.log('autoresponder.id', autoresponder.id)

    // const { opusers, platformAccountIds } = autoresponder
    // const platformAccountIdsByOpuser = (opusers as AnyObject[]).map(
    //   (item) => item.platformAccountId
    // )

    // const itemPlatformAccountIds = new Set<number>(platformAccountIds as number[])

    // // eslint-disable-next-line @typescript-eslint/no-unused-vars
    // const ids = [...new Set([...itemPlatformAccountIds, ...platformAccountIdsByOpuser])]

    // const platformAccounts = await this.prisma.platformAccount.findMany({
    //   where: {
    //     id: {
    //       in: ids
    //     }
    //   }
    // })

    // const platformAccountMap: Record<string, PlatformAccount> = {}
    // platformAccounts.forEach((platformAccount) => {
    //   platformAccountMap[platformAccount.id] = platformAccount
    // })

    // // 账号根据平台分组再重写写入数据库
    // const accountsByPlatform = platformAccounts.reduce(
    //   (acc, account) => {
    //     const { platform } = account
    //     if (!acc[platform]) {
    //       acc[platform] = []
    //     }
    //     acc[platform].push(account)
    //     return acc
    //   },
    //   {} as Record<string, typeof platformAccounts>
    // )

    // const newAutoresponder: Autoresponder[] = []
    // // 根据平台，查找Autoresponder中对应的账号，重新组装成一个新的Autoresponder插入数据库
    // Object.entries(accountsByPlatform).forEach(([platform, accounts]) => {
    //   this.logger.log(`Processing platform: ${platform} with ${accounts.length} accounts`)
    //   const accountIds = accounts.map((item) => item.id)

    //   let trigger = AutoresponderTriggerType.Chat

    //   const rule = [
    //     AutoresponderKeywordRule.Comment,
    //     AutoresponderKeywordRule.Like,
    //     AutoresponderKeywordRule.Follow
    //   ].includes(autoresponder.rule as AutoresponderKeywordRule)
    //     ? AutoresponderKeywordRule.Instantly
    //     : autoresponder.rule

    //   if (autoresponder.contentType === 1) {
    //     trigger = AutoresponderTriggerType.welcome
    //   } else if (autoresponder.contentType === 0) {
    //     if (autoresponder.scene === 1) {
    //       trigger = AutoresponderTriggerType.Comment
    //     } else {
    //       switch (autoresponder.rule) {
    //         case 'comment':
    //           trigger = AutoresponderTriggerType.Comment
    //           break
    //         case 'instantly':
    //           trigger = AutoresponderTriggerType.Chat
    //           break
    //         case 'match':
    //           trigger = AutoresponderTriggerType.Chat
    //           break
    //         case 'like':
    //           trigger = AutoresponderTriggerType.Like
    //           break
    //         case 'follow':
    //           trigger = AutoresponderTriggerType.Follow
    //           break
    //       }
    //     }
    //   }

    //   // Add logic here to handle accounts for each platform
    //   if ((autoresponder.opusers as AnyObject[]).length > 0) {
    //     // 评论策略
    //     newAutoresponder.push({
    //       ...autoresponder,
    //       id: undefined, // Remove the id field
    //       platform: Number(platform),
    //       parentId: autoresponder.id,
    //       isNew: true,
    //       trigger,
    //       rule,
    //       platformAccountIds: [],
    //       opusers: (autoresponder.opusers as AnyObject[]).filter((item) =>
    //         accountIds.includes(item.platformAccountId)
    //       )
    //     })
    //   } else if (autoresponder.platformAccountIds) {
    //     newAutoresponder.push({
    //       ...autoresponder,
    //       id: undefined, // Remove the id field
    //       platform: Number(platform),
    //       parentId: autoresponder.id,
    //       isNew: true,
    //       trigger,
    //       rule,
    //       platformAccountIds: (autoresponder.platformAccountIds as number[]).filter((id) =>
    //         accountIds.includes(id)
    //       )
    //     })
    //   }
    // })

    // await this.prisma.autoresponder.createMany({
    //   data: newAutoresponder
    // })

    this.logger.log(`onTransferOverviewData 刷新 ${autoresponder.id}`)
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(`overview-transfer-${key}`, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
