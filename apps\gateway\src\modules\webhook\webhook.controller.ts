import { Controller, Get, HttpCode, Logger, Post, RawBodyRequest, Req } from '@nestjs/common'
import { FastifyRequest } from 'fastify'
import { WebhookService } from './webhook.service'
import {
  WebhookBody,
  WebhookKuaishouBody,
  WebhookXiaohongshuBody,
  WebhookXiaohongshuCardMessageBody,
  WebhookXiaohongshuCommentBody,
  WebWeiboBody
} from './types'
import {
  BindEventXiaohongshu,
  WebhookEvents,
  WebhookKuaishouEvents,
  WebhookWechatEvents
} from './constant'
import axios from 'axios'
import { WebHookServiceWechat } from './webhook.service.wechat'
import JsonBigint from 'json-bigint'
import { WebhookServiceKuaishou } from './webhook.service.kuaishou'
import querystring from 'querystring'
import crypto from 'crypto'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { WebhookServiceWeibo } from './webhook.service.weibo'
import { WebhookServiceXiaohongshu } from './webhook.service.xiaohongshu'
import { WebHookServiceWechatNew } from './webhook.service.wechatNew'
import { TlsManageService } from '@qdy/common'

@Controller()
export class WebhookController {
  logger = new Logger('WebhookController')

  constructor(
    private readonly webhookService: WebhookService,
    private readonly webhookWechatService: WebHookServiceWechat,
    private readonly webHookServiceWechatNew: WebHookServiceWechatNew,
    private readonly webhookKuaishouService: WebhookServiceKuaishou,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly tlsManageService: TlsManageService,
    private readonly webhookWeiboService: WebhookServiceWeibo,
    private readonly webhookServiceXiaohongshu: WebhookServiceXiaohongshu
  ) {}

  @Post(process.env.WEBHOOK_URL)
  @HttpCode(200)
  async webhook(@Req() req: FastifyRequest) {
    const { body, headers } = req as { body: WebhookBody; headers: Record<string, string> }

    if (process.env.NODE_ENV === 'prod') {
      this.tlsManageService.putLogs({
        logData: JSON.stringify(body),
        logLevel: 'info',
        requestUri: process.env.WEBHOOK_URL,
        jobStatus: 'webhook'
      })
    } else if (process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'dev') {
      if (
        body.event === WebhookEvents.IMReceiveMessage ||
        body.event === WebhookEvents.IMSendMessage ||
        body.event === WebhookEvents.NewFollowAction ||
        body.event === WebhookEvents.NewVideoDigg ||
        body.event === WebhookEvents.IMRecallMsg
      ) {
        this.tlsManageService.putLogs({
          logData: JSON.stringify(body),
          logLevel: 'info',
          requestUri: process.env.WEBHOOK_URL,
          jobStatus: 'webhook'
        })
      }
    }

    if (process.env.NODE_ENV === 'dev') {
      axios
        .post('https://www.qdy.com/api/ttac99dde1ess-webhook', body, {
          headers: {
            'x-douyin-signature': headers['x-douyin-signature'],
            'msg-id': headers['msg-id']
          }
        })
        .catch((e) => {
          this.logger.debug('测试转发失败', e)
        })

      axios
        .post('https://www-test.qdy.com/api/ttac99dde1ess-webhook', body, {
          headers: {
            'x-douyin-signature': headers['x-douyin-signature'],
            'msg-id': headers['msg-id']
          }
        })
        .catch((e) => {
          this.logger.debug('生产转发失败', e)
        })
    }

    if (process.env.NODE_ENV === 'prod') {
      axios
        .post('http://************/ttac99dde1ess-webhook', body, {
          headers: {
            'x-douyin-signature': headers['x-douyin-signature'],
            'msg-id': headers['msg-id']
          }
        })
        .catch((e) => {
          this.logger.debug('生产转发失败', e)
        })
    }

    if (body.event === WebhookEvents.VerifyWebhook) {
      return body.content
    }

    if (typeof body.content === 'string') {
      body.content = JSON.parse(body.content)
    }

    return this.webhookService.webhook(body, headers['msg-id'])
  }

  @Post(process.env.WECHAT_WEBHOOK_URL)
  @HttpCode(200)
  async wechatWebhook(@Req() req: RawBodyRequest<FastifyRequest>) {
    const body = JsonBigint({ storeAsString: true }).parse(req.rawBody.toString())

    if (
      body.TypeName === WebhookWechatEvents.Offline ||
      body.TypeName === WebhookWechatEvents.FinderMsg ||
      body.TypeName === WebhookWechatEvents.FinderSyncMsg
    ) {
      this.tlsManageService.putLogs({
        logData: JSON.stringify(body),
        logLevel: 'info',
        requestUri: process.env.WEBHOOK_URL,
        jobStatus: 'wechatWebhook'
      })
    }

    if (body.TypeName === WebhookWechatEvents.Offline) {
      return this.webhookWechatService.webHookOffline(body)
    }
    if (body.TypeName === WebhookWechatEvents.FinderMsg) {
      return this.webhookWechatService.webHookNewWechat(body)
    }
    if (body.TypeName === WebhookWechatEvents.FinderSyncMsg) {
      return this.webhookWechatService.webHookFinderSyncMsg(body)
    }
  }

  @Post(process.env.WECHAT_WEBHOOK_NEW_URL)
  @HttpCode(200)
  async wechatNewWebhook(@Req() req: RawBodyRequest<FastifyRequest>) {
    const body = JsonBigint({ storeAsString: true }).parse(req.rawBody.toString())

    if (
      body.TypeName === WebhookWechatEvents.Offline ||
      body.TypeName === WebhookWechatEvents.FinderMsg ||
      body.TypeName === WebhookWechatEvents.FinderSyncMsg
    ) {
      this.tlsManageService.putLogs({
        logData: JSON.stringify(body),
        logLevel: 'info',
        requestUri: process.env.WEBHOOK_URL,
        jobStatus: 'wechatNewWebhook'
      })
    }

    if (body.TypeName === WebhookWechatEvents.Offline) {
      return this.webHookServiceWechatNew.webHookOffline(body)
    }
    if (body.TypeName === WebhookWechatEvents.FinderMsg) {
      return this.webHookServiceWechatNew.webHookNewWechat(body)
    }
    if (body.TypeName === WebhookWechatEvents.FinderSyncMsg) {
      return this.webHookServiceWechatNew.webHookFinderSyncMsg(body)
    }
  }

  @Post(process.env.ALIPAY_WEBHOOK_URL)
  @HttpCode(200)
  async alipayWebhook(@Req() req: FastifyRequest) {
    const { body } = req as { body: WebhookBody; headers: Record<string, string> }
    if (process.env.NODE_ENV === 'prod') {
      axios.post(`http://************/${process.env.ALIPAY_WEBHOOK_URL}`, body).catch((e) => {
        this.logger.debug('测试转发失败', e)
      })
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: process.env.ALIPAY_WEBHOOK_URL,
      jobStatus: 'ALIPAY_WEBHOOK_URL'
    })

    return this.webhookService.alipayWebhook(body)
  }

  @Post(process.env.WECHATPAY_WEBHOOK_URL)
  @HttpCode(200)
  async wechatPayWebhook(@Req() req: FastifyRequest) {
    const { body, headers } = req as { body: WebhookBody; headers: Record<string, string> }
    if (process.env.NODE_ENV === 'dev') {
      this.logger.debug('body', body)
      this.logger.debug('headers', headers)
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: process.env.WECHATPAY_WEBHOOK_URL,
      jobStatus: 'WECHATPAY_WEBHOOK_URL'
    })

    const wechatTimestamp = headers['wechatpay-timestamp']
    const wechatNonce = headers['wechatpay-nonce']
    const wechatSerial = headers['wechatpay-serial']
    const wechatSignature = headers['wechatpay-signature']
    return this.webhookService.wechatPayWebhook(body, {
      wechatTimestamp,
      wechatNonce,
      wechatSerial,
      wechatSignature
    })
  }

  @Post(process.env.KUAISHOU_WEBHOOK_URL)
  @HttpCode(200)
  async kuaishouWebhook(@Req() req: FastifyRequest) {
    const { body } = req as { body: WebhookKuaishouBody; headers: Record<string, string> }
    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: process.env.WEBHOOK_URL,
      jobStatus: 'kuaishouwebhook'
    })
    if (body.event === WebhookKuaishouEvents.Test) {
      return {
        result: 1,
        message_id: body.message_id
      }
    }

    return this.webhookKuaishouService.webhook(body)
  }

  @Get(process.env.WEIBO_WEBHOOK_URL)
  @HttpCode(200)
  async weiboWebhook(@Req() req: FastifyRequest) {
    const { weiboClientSecret } = this.configService.get<RootConfigMap['app']>('app')
    const { body, url } = req as {
      body: WebWeiboBody
      url: string
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: url,
      jobStatus: 'weibo'
    })

    const queryParams = querystring.parse(url.split('?')[1])
    const { signature, timestamp, nonce, echostr } = queryParams
    this.logger.log(echostr)
    const tmpArr = [weiboClientSecret, timestamp, nonce]
    tmpArr.sort()
    const tmpStr = tmpArr.join('')
    const expectedSignature = crypto.createHash('sha1').update(tmpStr).digest('hex')

    if (expectedSignature === signature) {
      return echostr
    }
  }

  @Post(process.env.WEIBO_WEBHOOK_URL)
  @HttpCode(200)
  async weiboPostWebhook(@Req() req: FastifyRequest) {
    const { weiboClientSecret } = this.configService.get<RootConfigMap['app']>('app')
    const { body, url } = req as {
      body: WebWeiboBody
      url: string
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: url,
      jobStatus: 'weibo'
    })

    const queryParams = querystring.parse(url.split('?')[1])
    const { signature, timestamp, nonce } = queryParams
    const tmpArr = [weiboClientSecret, timestamp, nonce]
    tmpArr.sort()
    const tmpStr = tmpArr.join('')
    const expectedSignature = crypto.createHash('sha1').update(tmpStr).digest('hex')

    if (expectedSignature === signature) {
      return this.webhookWeiboService.webhook(body, '')
    }
  }

  /**
   * 聚光账号绑定
   */
  @Post('/open/im/third/bind_account')
  @HttpCode(200)
  async xiaohongshuBindAccountWebhook(@Req() req: FastifyRequest) {
    const { body } = req as {
      body: {
        content: string
      }
    }

    if (process.env.NODE_ENV === 'test') {
      axios
        .post(
          'https://1c3e-118-249-196-233.ngrok-free.app/api/webhook/xiaohongshu/open/im/third/bind_account',
          body
        )
        .catch((e) => {
          this.logger.debug('转发失败', e)
        })
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: 'bind_account',
      jobStatus: 'xiaohongshu'
    })

    return this.webhookServiceXiaohongshu.bindAccountWebhook({
      content: body.content,
      type: BindEventXiaohongshu.BindAccount
    })
  }

  /**
   * 聚光账号解绑
   */
  @Post('/open/im/third/unbind_account')
  @HttpCode(200)
  async xiaohongshuUnbindAccountWebhook(@Req() req: FastifyRequest) {
    const { body } = req as {
      body: {
        content: string
      }
    }

    if (process.env.NODE_ENV === 'test') {
      axios
        .post(
          'https://1c3e-118-249-196-233.ngrok-free.app/api/webhook/xiaohongshu/open/im/third/unbind_account',
          body
        )
        .catch((e) => {
          this.logger.debug('转发失败', e)
        })
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: 'unbind_account',
      jobStatus: 'xiaohongshu'
    })

    return this.webhookServiceXiaohongshu.bindAccountWebhook({
      content: body.content,
      type: BindEventXiaohongshu.UnBindAccount
    })
  }

  /**
   * kos账号绑定
   */
  @Post('/open/im/auth/bind_user/event')
  @HttpCode(200)
  async xiaohongshubindUserWebhook(@Req() req: FastifyRequest) {
    const { body } = req as {
      body: {
        content: string
      }
    }

    if (process.env.NODE_ENV === 'test') {
      axios
        .post(
          'https://1c3e-118-249-196-233.ngrok-free.app/api/webhook/xiaohongshu/open/im/auth/bind_user/event',
          body
        )
        .catch((e) => {
          this.logger.debug('转发失败', e)
        })
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: 'event',
      jobStatus: 'xiaohongshu'
    })

    return this.webhookServiceXiaohongshu.bindAccountWebhook({
      content: body.content,
      type: BindEventXiaohongshu.BindKosUser
    })
  }

  /**
   * 接收信息
   */
  @Post('/open/im/send')
  @HttpCode(200)
  async xiaohongshuWebhook(@Req() req: FastifyRequest) {
    const { body } = req as {
      body: WebhookXiaohongshuBody
    }

    if (process.env.NODE_ENV === 'test') {
      axios
        .post(
          'https://1c3e-118-249-196-233.ngrok-free.app/api/webhook/xiaohongshu/open/im/send',
          body
        )
        .catch((e) => {
          this.logger.debug('转发失败', e)
        })
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: 'send',
      jobStatus: 'xiaohongshu'
    })

    return this.webhookServiceXiaohongshu.webhook(body)
  }

  /**
   * 留资喝广告归隐数据推送
   */
  @Post('/open/im/push_lead')
  @HttpCode(200)
  async xiaohongshubindPushLeadWebhook(@Req() req: FastifyRequest) {
    const { body } = req as {
      body: WebhookXiaohongshuCardMessageBody
    }

    if (process.env.NODE_ENV === 'test') {
      axios
        .post(
          'https://1c3e-118-249-196-233.ngrok-free.app/api/webhook/xiaohongshu/open/im/push_lead',
          body
        )
        .catch((e) => {
          this.logger.debug('转发失败', e)
        })
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: 'event',
      jobStatus: 'xiaohongshu'
    })

    return this.webhookServiceXiaohongshu.cardMessageWebhook(body)
  }

  /**
   * 小红书意向评论
   */
  @Post('/open/intent/comment')
  @HttpCode(200)
  async xiaohongshuIntentCommentWebhook(@Req() req: FastifyRequest) {
    const { body } = req as {
      body: WebhookXiaohongshuCommentBody
    }

    if (process.env.NODE_ENV === 'test') {
      axios
        .post(
          'https://1c3e-118-249-196-233.ngrok-free.app/api/webhook/xiaohongshu/open/intent/comment',
          body
        )
        .catch((e) => {
          this.logger.debug('转发失败', e)
        })
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify(body),
      logLevel: 'info',
      requestUri: 'intent',
      jobStatus: 'xiaohongshu'
    })

    return this.webhookServiceXiaohongshu.webhookComment(body)
  }
}
