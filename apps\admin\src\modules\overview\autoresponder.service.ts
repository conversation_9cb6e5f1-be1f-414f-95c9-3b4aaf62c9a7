import { Inject, Injectable, Logger } from '@nestjs/common'
import { PrismaService, PlatformAccount } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { AnyObject } from 'mongoose'
import { AutoresponderKeywordRedisValue } from '../account/account.dto'
import { AutoresponderKeywordKey } from '@qdy/utils'
import { PlatformAccountManageService } from '@qdy/common'

@Injectable()
export class AutoresponderService {
  serverNumber: number

  LOCK_TIMEOUT = 10 * 60 * 1000

  logger = new Logger('AutoresponderService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  async onModuleInit() {
    // this.onPlatformAccountInit()
  }

  async onInite() {
    const lock = ((await this.cacheManager.get('init-autoresponder')) || 0) as number
    if (!lock) {
      try {
        this.cacheManager.set('init-autoresponder', 1, 1000 * 60)
        const platformAccount = await this.prisma.platformAccount.findMany()
        const platformAccountMap: Record<number, PlatformAccount> = {}

        const autoresponders = await this.prisma.autoresponder.findMany({
          where: {
            isNew: true
          }
        })

        const keywordMaps: Record<string, AutoresponderKeywordRedisValue[]> = {}

        platformAccount.forEach((account) => {
          platformAccountMap[account.id] = account
          keywordMaps[account.openId] = []
        })

        autoresponders.forEach((autoresponder) => {
          const { platformAccountIds, opusers } = autoresponder as unknown as {
            platformAccountIds: number[]
            opusers: { id: number; platformAccountId: number }[]
          }

          opusers.forEach((opuser) => {
            const platformAccount = platformAccountMap[opuser.platformAccountId]
            if (platformAccount) {
              const opuserKey = `${platformAccount.openId}:${opuser.id}`
              if (!keywordMaps[opuserKey]) {
                keywordMaps[opuserKey] = []
              }
              // autoresponder-update-value 全局搜索需要修改的地方
              keywordMaps[opuserKey].push({
                keyword: autoresponder.keywords,
                rule: autoresponder.rule,
                contents: autoresponder.contents as AnyObject[],
                contentType: autoresponder.contentType,
                scene: autoresponder.scene,
                trigger: autoresponder.trigger,
                stopReply: autoresponder.stopReply,
                stopInterval: autoresponder.stopInterval,
                stopTime: autoresponder.stopTime,
                isDelay: autoresponder.isDelay,
                delayTime: autoresponder.delayTime,
                state: autoresponder.state,
                executionCount: autoresponder.executionCount,
                isNew: autoresponder.isNew,
                platform: platformAccount.platform,
                autoresponderId: autoresponder.id
              })
            } else {
              this.logger.error(`${opuser.platformAccountId} is 无效`)
            }
          })

          platformAccountIds.forEach((pid) => {
            const platformAccount = platformAccountMap[pid]
            if (platformAccount) {
              // autoresponder-update-value 全局搜索需要修改的地方
              keywordMaps[platformAccount.openId].push({
                scene: autoresponder.scene,
                trigger: autoresponder.trigger,
                keyword: autoresponder.keywords,
                rule: autoresponder.rule,
                contents: autoresponder.contents as AnyObject[],
                contentType: autoresponder.contentType,
                state: autoresponder.state,
                executionCount: autoresponder.executionCount,
                stopReply: autoresponder.stopReply,
                stopInterval: autoresponder.stopInterval,
                stopTime: autoresponder.stopTime,
                isDelay: autoresponder.isDelay,
                isNew: autoresponder.isNew,
                delayTime: autoresponder.delayTime,
                platform: platformAccount.platform,
                autoresponderId: autoresponder.id
              })
            } else {
              this.logger.error(`${pid} is 无效`)
            }
          })
        })

        const newKeywordMaps = {}
        if (Object.keys(keywordMaps).length) {
          Object.keys(keywordMaps).forEach((key) => {
            newKeywordMaps[key] = JSON.stringify(keywordMaps[key])
          })
          this.cacheManager.store.client.hmset(AutoresponderKeywordKey, newKeywordMaps)
        }
      } catch (e) {
        this.logger.error(e)
      } finally {
        await this.cacheManager.del('init-autoresponder')
      }
    }
  }

  async onPlatformAccountInit() {
    const lock = ((await this.cacheManager.get('init-platformAccount-autoresponder')) ||
      0) as number

    if (lock) {
      return
    }

    this.cacheManager.set('init-platformAccount-autoresponder', 1, 1000 * 60)

    const platformAccounts = await this.prisma.platformAccount.findMany()

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]
      const key = platformAccount.openId

      if (await this.tryAcquireLock(key)) {
        // eslint-disable-next-line no-continue
        continue
      }

      await this.platformAccountManageService.updatePlatformAccountRedisInfo(platformAccount)
    }
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(`team-transfer-${key}`, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
