import { Module } from '@nestjs/common'
import { EventsModule } from './events.module'
import { SocketController } from './socket.controller'
import { ConfigModule } from '@nestjs/config'

@Module({
  controllers: [SocketController],
  imports: [
    ConfigModule.forRoot({
      envFilePath: `.env.${process.env.NODE_ENV}`,
      ignoreEnvFile: false,
      isGlobal: true,
      cache: true
    }),
    EventsModule
  ],
  providers: [SocketController]
})
export class SocketModule {}
