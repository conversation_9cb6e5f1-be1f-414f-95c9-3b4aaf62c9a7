import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { Queue, Worker } from 'bullmq'
import {
  WechatMessagesEntity,
  WechatCommentEntity,
  PersonalChatMessageEntity,
  WorkCommentEntity,
  MessagesEntity,
  CommentEntity
} from '@qdy/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { AnyObject, Model } from 'mongoose'
import { WebhookEvents } from '../webhook/constant'
import { Platform } from '@qdy/utils'

@Injectable()
export class DatadTransferService implements OnModuleInit {
  private readonly logger = new Logger(DatadTransferService.name)

  LOCK_TIMEOUT = 60 * 60 * 1000 // 30秒的锁超时时间

  taskQueue: Queue

  taskWorker: Worker

  dataQueue: Queue

  dataWorker: Worker

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @InjectModel(WechatMessagesEntity.name) private wechatMessageModel: Model<WechatMessagesEntity>,
    @InjectModel(WechatCommentEntity.name)
    private wechatCommentMessageModel: Model<WechatCommentEntity>,
    @InjectModel(PersonalChatMessageEntity.name)
    private PersonalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(WorkCommentEntity.name)
    private WorkCommentModel: Model<WorkCommentEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(MessagesEntity.name) private messageModel: Model<MessagesEntity>,
    @InjectModel(CommentEntity.name) private commentModel: Model<CommentEntity>
  ) {}

  async onModuleInit() {
    this.taskQueue = new Queue('data-migration', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'data-migration',
      async (job) => {
        const { platform, openId } = job.data
        this.logger.log(job.data)
        this.logger.log(`Running task ${platform} ${openId}`)
        switch (platform) {
          case Platform.Wechat:
            await this.onTransferPrivateDataByWechat(openId)
            await this.onTransferCommentDataByWechat(openId)
            break
          case Platform.Douyin:
            await this.onTransferPrivateReceiveDataByDouyin(openId)
            await this.onTransferPrivateSendDataByDouyin(openId)
            await this.onTransferCommentDataByDouyin(openId)
            break
        }
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.dataQueue = new Queue('data-migration-sms', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.dataWorker = new Worker(
      'data-migration-send',
      async (job) => {
        const { openList } = job.data

        await this.migrateUserTableData(openList)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // const lock = ((await this.cacheManager.get('init-datatransferPlatformAccount')) || 0) as number

    // if (!lock) {
    //   this.cacheManager.set('init-datatransferPlatformAccount', 1, 1000 * 60 * 60)
    //   this.onDataTransferByAccount()
    // }

    // this.logger.log('DatadTransferService init')
  }

  /**
   * 通过账号来执行迁移
   */
  async onDataTransferByAccount() {
    const platformAccountList = await this.prisma.platformAccount.findMany({
      where: {
        platform: {
          in: [0, 1]
        },
        status: 0
      },
      orderBy: {
        createTime: 'desc'
      }
    })

    const openIdList = platformAccountList.map((account) => {
      const newData = {
        openId: account.openId,
        platform: account.platform
      }
      return newData
    })

    this.dataQueue.add(
      'data-migration-send',
      {
        openIdList
      },
      {
        delay: 10 * 60 * 1000,
        removeOnComplete: true,
        removeOnFail: true,
        jobId: `platformAccount-data-transfer-send`
      }
    )
  }

  async onDatadTransferPlatformAccount() {
    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        platform: Platform.Wechat,
        teamId: 338
      }
    })

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]

      this.taskQueue.add(
        'data-migration',
        {
          platform: platformAccount.platform,
          openId: platformAccount.openId
        },
        {
          delay: 1 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `platformAccount-data-transfer-${platformAccount.id}`
        }
      )
    }

    const douyinPlatformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        platform: Platform.Douyin,
        teamId: 338
      }
    })

    for (let i = 0; i < douyinPlatformAccounts.length; i++) {
      const platformAccount = douyinPlatformAccounts[i]

      this.taskQueue.add(
        'data-migration',
        {
          platform: platformAccount.platform,
          openId: platformAccount.openId
        },
        {
          delay: 1 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `platformAccount-data-transfer-${platformAccount.id}`
        }
      )
    }

    this.logger.log('DatadTransferService 刷新初始化')
  }

  async migrateUserTableData(openIdList: AnyObject[]) {
    // 数据迁移逻辑

    const [content, ...otherContents] = openIdList

    if (!content) {
      return
    }

    const isLock = await this.tryAcquireLock(content.openId)

    if (!isLock) {
      // 如果没有被锁，则执行
      if (content.platform === 0) {
        await this.onTransferPrivateReceiveDataByDouyin(content.openId)
        await this.onTransferPrivateSendDataByDouyin(content.openId)
        await this.onTransferCommentDataByDouyin(content.openId)
      }

      if (content.platform === 1) {
        await this.onTransferPrivateDataByWechat(content.openId)
        await this.onTransferCommentDataByWechat(content.openId)
      }
    }

    if (otherContents.length) {
      await this.dataQueue.add(
        'data-migration-send',
        {
          openIdList: otherContents
        },
        {
          delay: 5 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `platformAccount-data-transfer-send`
        }
      )
    }
  }

  async onTransferPrivateDataByWechat(openId: string) {
    const cursor = this.wechatMessageModel
      .find({
        wxid: openId,
        createTime: {
          $lt: *************
        }
      })
      .cursor()
    let batch: WechatMessagesEntity[] = []
    const pageSize = 100

    // eslint-disable-next-line no-restricted-syntax
    for await (const doc of cursor) {
      batch.push(doc)
      if (batch.length === pageSize) {
        // 进行数据迁移操作
        batch.map(async (oldData) => {
          const newData = new this.PersonalChatMessageModel({
            platformType: 'wechat',
            uniqueId: oldData.newMsgId,
            fromUserId: oldData.fromUserName,
            fromName: oldData.fromName,
            fromAvatar: oldData.fromAvatar,
            toUserId: oldData.toUserName,
            toName: oldData.toName,
            toAvatar: oldData.toAvatar,
            sessionId: oldData.isSender ? oldData.toUserName : oldData.fromUserName,
            openId: oldData.wxid,
            createTime: oldData.createTime,
            isAuto: oldData.isAuto,
            event: oldData.isSender ? 'im_send_msg' : 'im_receive_msg',
            messageId: oldData.newMsgId,
            content: {
              appId: oldData.appid,
              messageType:
                oldData.msgType === 1 ? 'text' : oldData.msgType === 3 ? 'image' : 'video',
              text: oldData.content,
              sessionId: oldData.newMsgId,
              isSender: oldData.isSender
            }
          })
          try {
            await newData.save()
          } catch (error) {
            this.logger.log(error)
          }
        })
        batch = []
      }
    }
    // 处理最后一批数据（如果不足一页）
    if (batch.length > 0) {
      batch.map(async (oldData) => {
        const newData = new this.PersonalChatMessageModel({
          platformType: 'wechat',
          uniqueId: oldData.newMsgId,
          fromUserId: oldData.fromUserName,
          fromName: oldData.fromName,
          fromAvatar: oldData.fromAvatar,
          toUserId: oldData.toUserName,
          toName: oldData.toName,
          toAvatar: oldData.toAvatar,
          sessionId: oldData.isSender ? oldData.toUserName : oldData.fromUserName,
          openId: oldData.wxid,
          createTime: oldData.createTime,
          isAuto: oldData.isAuto,
          event: oldData.isSender ? 'im_send_msg' : 'im_receive_msg',
          messageId: oldData.newMsgId,
          content: {
            appId: oldData.appid,
            messageType: oldData.msgType === 1 ? 'text' : oldData.msgType === 3 ? 'image' : 'video',
            text: oldData.content,
            sessionId: oldData.newMsgId,
            isSender: oldData.isSender
          }
        })
        try {
          await newData.save()
        } catch (error) {
          this.logger.log(error)
        }
      })
    }
    this.logger.log(`onTransferPrivateDataByWechat 刷新 ${openId}`)
  }

  async onTransferCommentDataByWechat(openId: string) {
    const cursor = this.wechatCommentMessageModel
      .find({
        wxid: openId,
        createTime: {
          $lt: *************
        }
      })
      .cursor()
    let batch: WechatCommentEntity[] = []
    const pageSize = 100

    // eslint-disable-next-line no-restricted-syntax
    for await (const doc of cursor) {
      batch.push(doc)
      if (batch.length === pageSize) {
        // 进行数据迁移操作
        batch.map(async (oldData) => {
          this.logger.log(oldData)
          const jsonData = JSON.parse(oldData.jsonData)
          const newData = new this.WorkCommentModel({
            platformType: 'wechat',
            uniqueId: oldData.uniqueId,
            fromUserId: oldData.fromUserName,
            fromName: jsonData.nickname,
            fromAvatar: jsonData.headUrl,
            toUserId: oldData.toUserName,
            toName: '',
            toAvatar: '',
            sessionId: oldData.refObjectId,
            openId: oldData.wxid,
            createTime: oldData.createTime,
            isAuto: 0,
            event: 'item_comment_reply',
            content: {
              commentId: jsonData.refCommentId,
              name: jsonData.nickname,
              avatar: jsonData.headUrl,
              mentionType: jsonData.mentionType,
              content: jsonData.mentionContent,
              replyToItemId: oldData.refObjectId,
              refObjectNonceId: oldData.refObjectNonceId,
              thumbUrl: oldData.thumbUrl,
              description: oldData.description,
              sessionBuffer: oldData.sessionBuffer,
              refContent: jsonData.refContent,
              replyNickname: jsonData?.replyContact?.contact?.nickname,
              replyHeadUrl: jsonData?.replyContact?.contact?.headUrl,
              refCommentId: jsonData.refCommentId,
              appId: oldData.appid
            }
          })
          try {
            await newData.save()
          } catch (error) {
            this.logger.log(error)
          }
        })
        batch = []
      }
    }
    // 处理最后一批数据（如果不足一页）
    if (batch.length > 0) {
      batch.map(async (oldData) => {
        const jsonData = JSON.parse(oldData.jsonData)
        const newData = new this.WorkCommentModel({
          platformType: 'wechat',
          uniqueId: oldData.uniqueId,
          fromUserId: oldData.fromUserName,
          fromName: jsonData.nickname,
          fromAvatar: jsonData.headUrl,
          toUserId: oldData.toUserName,
          toName: '',
          toAvatar: '',
          sessionId: oldData.refObjectId,
          openId: oldData.wxid,
          createTime: oldData.createTime,
          isAuto: 0,
          event: 'item_comment_reply',
          content: {
            commentId: jsonData.refCommentId,
            name: jsonData.nickname,
            avatar: jsonData.headUrl,
            mentionType: jsonData.mentionType,
            content: jsonData.mentionContent,
            replyToItemId: oldData.refObjectId,
            refObjectNonceId: oldData.refObjectNonceId,
            thumbUrl: oldData.thumbUrl,
            description: oldData.description,
            sessionBuffer: oldData.sessionBuffer,
            refContent: jsonData.refContent,
            replyNickname: jsonData?.replyContact?.contact?.nickname,
            replyHeadUrl: jsonData?.replyContact?.contact?.headUrl,
            refCommentId: jsonData.refCommentId,
            appId: oldData.appid
          }
        })
        try {
          await newData.save()
        } catch (error) {
          this.logger.log(error)
        }
      })
    }
    this.logger.log(`onTransferCommentDataByWechat 刷新 ${openId}`)
  }

  async onTransferPrivateReceiveDataByDouyin(openId: string) {
    const cursor = this.messageModel
      .find({
        toUserId: openId,
        createTime: {
          $lt: *************
        }
      })
      .cursor()
    let batch: MessagesEntity[] = []
    const pageSize = 100

    // eslint-disable-next-line no-restricted-syntax
    for await (const doc of cursor) {
      batch.push(doc)
      if (batch.length === pageSize) {
        // 进行数据迁移操作
        // eslint-disable-next-line array-callback-return
        const newDataList = batch
          .filter((item) => item.event !== 'new_video_digg' && item.event !== 'new_follow_action')
          // eslint-disable-next-line array-callback-return
          .map((oldData) => {
            const { content } = oldData
            let openId = oldData.fromUserId
            switch (oldData.event) {
              case WebhookEvents.IMSendMessage:
                openId = oldData.fromUserId
                break
              case WebhookEvents.CommentReply:
                openId = oldData.toUserId
                break
              case WebhookEvents.IMReceiveMessage:
                openId = oldData.toUserId
                break
              case WebhookEvents.IMGroupSendMessage:
                openId = oldData.fromUserId
                break
              case WebhookEvents.IMGroupReceiveMessage:
                openId = oldData.toUserId
                break
              case WebhookEvents.GroupFansEvent:
                openId = oldData.toUserId
                break
              case WebhookEvents.EnterGroupAuditChange:
                openId = oldData.fromUserId
                break
              default:
                break
            }
            const newData = new this.PersonalChatMessageModel({
              platformType: 'douyin',
              uniqueId: oldData.uniqueId,
              fromUserId: oldData.fromUserId,
              fromName: oldData.fromName,
              fromAvatar: oldData.fromAvatar,
              toUserId: oldData.toUserId,
              toName: oldData.toName,
              toAvatar: oldData.toAvatar,
              sessionId: oldData.sessionId,
              openId,
              createTime: oldData.createTime,
              isAuto: oldData.isAuto,
              event: oldData.event,
              messageId: oldData.content.get('serverMessageId'),
              content
            })
            return newData
          })

        try {
          await this.PersonalChatMessageModel.insertMany(newDataList)
        } catch (error) {
          this.logger.log(error)
        }
        batch = []
      }
    }
    // 处理最后一批数据（如果不足一页）
    if (batch.length > 0) {
      // eslint-disable-next-line array-callback-return
      const newDataList = batch
        .filter((item) => item.event !== 'new_video_digg' && item.event !== 'new_follow_action')
        // eslint-disable-next-line array-callback-return
        .map((oldData) => {
          const { content } = oldData
          let openId = oldData.fromUserId
          switch (oldData.event) {
            case WebhookEvents.IMSendMessage:
              openId = oldData.fromUserId
              break
            case WebhookEvents.CommentReply:
              openId = oldData.toUserId
              break
            case WebhookEvents.IMReceiveMessage:
              openId = oldData.toUserId
              break
            case WebhookEvents.IMGroupSendMessage:
              openId = oldData.fromUserId
              break
            case WebhookEvents.IMGroupReceiveMessage:
              openId = oldData.toUserId
              break
            case WebhookEvents.GroupFansEvent:
              openId = oldData.toUserId
              break
            case WebhookEvents.EnterGroupAuditChange:
              openId = oldData.fromUserId
              break
            default:
              break
          }
          const newData = new this.PersonalChatMessageModel({
            platformType: 'douyin',
            uniqueId: oldData.uniqueId,
            fromUserId: oldData.fromUserId,
            fromName: oldData.fromName,
            fromAvatar: oldData.fromAvatar,
            toUserId: oldData.toUserId,
            toName: oldData.toName,
            toAvatar: oldData.toAvatar,
            sessionId: oldData.sessionId,
            openId,
            createTime: oldData.createTime,
            isAuto: oldData.isAuto,
            event: oldData.event,
            messageId: oldData.content.get('serverMessageId'),
            content
          })
          return newData
        })

      try {
        await this.PersonalChatMessageModel.insertMany(newDataList)
      } catch (error) {
        this.logger.log(error)
      }
    }
    this.logger.log(`onTransferPrivateDataByDouyin 刷新`)
  }

  async onTransferPrivateSendDataByDouyin(openId: string) {
    const cursor = this.messageModel
      .find({
        fromUserId: openId,
        createTime: {
          $lt: *************
        }
      })
      .cursor()
    let batch: MessagesEntity[] = []
    const pageSize = 100

    // eslint-disable-next-line no-restricted-syntax
    for await (const doc of cursor) {
      batch.push(doc)
      if (batch.length === pageSize) {
        // 进行数据迁移操作
        // eslint-disable-next-line array-callback-return
        const newDataList = batch
          .filter((item) => item.event !== 'new_video_digg' && item.event !== 'new_follow_action')
          // eslint-disable-next-line array-callback-return
          .map((oldData) => {
            const { content } = oldData
            let openId = oldData.fromUserId
            switch (oldData.event) {
              case WebhookEvents.IMSendMessage:
                openId = oldData.fromUserId
                break
              case WebhookEvents.CommentReply:
                openId = oldData.toUserId
                break
              case WebhookEvents.IMReceiveMessage:
                openId = oldData.toUserId
                break
              case WebhookEvents.IMGroupSendMessage:
                openId = oldData.fromUserId
                break
              case WebhookEvents.IMGroupReceiveMessage:
                openId = oldData.toUserId
                break
              case WebhookEvents.GroupFansEvent:
                openId = oldData.toUserId
                break
              case WebhookEvents.EnterGroupAuditChange:
                openId = oldData.fromUserId
                break
              default:
                break
            }
            const newData = new this.PersonalChatMessageModel({
              platformType: 'douyin',
              uniqueId: oldData.uniqueId,
              fromUserId: oldData.fromUserId,
              fromName: oldData.fromName,
              fromAvatar: oldData.fromAvatar,
              toUserId: oldData.toUserId,
              toName: oldData.toName,
              toAvatar: oldData.toAvatar,
              sessionId: oldData.sessionId,
              openId,
              createTime: oldData.createTime,
              isAuto: oldData.isAuto,
              event: oldData.event,
              messageId: oldData.content.get('serverMessageId'),
              content
            })
            return newData
          })

        try {
          await this.PersonalChatMessageModel.insertMany(newDataList)
        } catch (error) {
          this.logger.log(error)
        }
        batch = []
      }
    }
    // 处理最后一批数据（如果不足一页）
    if (batch.length > 0) {
      // eslint-disable-next-line array-callback-return
      const newDataList = batch
        .filter((item) => item.event !== 'new_video_digg' && item.event !== 'new_follow_action')
        // eslint-disable-next-line array-callback-return
        .map((oldData) => {
          const { content } = oldData
          let openId = oldData.fromUserId
          switch (oldData.event) {
            case WebhookEvents.IMSendMessage:
              openId = oldData.fromUserId
              break
            case WebhookEvents.CommentReply:
              openId = oldData.toUserId
              break
            case WebhookEvents.IMReceiveMessage:
              openId = oldData.toUserId
              break
            case WebhookEvents.IMGroupSendMessage:
              openId = oldData.fromUserId
              break
            case WebhookEvents.IMGroupReceiveMessage:
              openId = oldData.toUserId
              break
            case WebhookEvents.GroupFansEvent:
              openId = oldData.toUserId
              break
            case WebhookEvents.EnterGroupAuditChange:
              openId = oldData.fromUserId
              break
            default:
              break
          }
          const newData = new this.PersonalChatMessageModel({
            platformType: 'douyin',
            uniqueId: oldData.uniqueId,
            fromUserId: oldData.fromUserId,
            fromName: oldData.fromName,
            fromAvatar: oldData.fromAvatar,
            toUserId: oldData.toUserId,
            toName: oldData.toName,
            toAvatar: oldData.toAvatar,
            sessionId: oldData.sessionId,
            openId,
            createTime: oldData.createTime,
            isAuto: oldData.isAuto,
            event: oldData.event,
            messageId: oldData.content.get('serverMessageId'),
            content
          })
          return newData
        })

      try {
        await this.PersonalChatMessageModel.insertMany(newDataList)
      } catch (error) {
        this.logger.log(error)
      }
    }
    this.logger.log(`onTransferPrivateDataByDouyin 刷新`)
  }

  async onTransferCommentDataByDouyin(openId: string) {
    const cursor = this.commentModel
      .find({
        toUserId: openId,
        createTime: {
          $lt: *************
        }
      })
      .cursor()
    let batch: CommentEntity[] = []
    const pageSize = 100

    // eslint-disable-next-line no-restricted-syntax
    for await (const doc of cursor) {
      batch.push(doc)
      if (batch.length === pageSize) {
        // 进行数据迁移操作
        // eslint-disable-next-line array-callback-return
        const newDataList = batch.map((oldData) => {
          const { content } = oldData

          const newData = new this.PersonalChatMessageModel({
            platformType: 'douyin',
            uniqueId: oldData.uniqueId,
            fromUserId: oldData.fromUserId,
            fromName: content.get('name'),
            fromAvatar: content.get('avatar'),
            toUserId: oldData.toUserId,
            toName: '',
            toAvatar: '',
            sessionId: oldData.sessionId,
            openId: oldData.fromUserId,
            createTime: oldData.createTime,
            isAuto: 0,
            event: oldData.event,
            content
          })
          return newData
        })

        try {
          await this.WorkCommentModel.insertMany(newDataList)
        } catch (error) {
          this.logger.log(error)
        }
        batch = []
      }
    }
    // 处理最后一批数据（如果不足一页）
    if (batch.length > 0) {
      // eslint-disable-next-line array-callback-return
      const newDataList = batch.map((oldData) => {
        const { content } = oldData
        const newData = new this.PersonalChatMessageModel({
          platformType: 'douyin',
          uniqueId: oldData.uniqueId,
          fromUserId: oldData.fromUserId,
          fromName: content.get('name'),
          fromAvatar: content.get('avatar'),
          toUserId: oldData.toUserId,
          toName: '',
          toAvatar: '',
          sessionId: oldData.sessionId,
          openId: oldData.fromUserId,
          createTime: oldData.createTime,
          isAuto: 0,
          event: oldData.event,
          content
        })
        return newData
      })

      try {
        // this.logger.log(newDataList)
        await this.WorkCommentModel.insertMany(newDataList)
      } catch (error) {
        this.logger.log(error)
      }
    }
    this.logger.log(`onTransferCommentDataByDouyin 刷新`)
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(key, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
