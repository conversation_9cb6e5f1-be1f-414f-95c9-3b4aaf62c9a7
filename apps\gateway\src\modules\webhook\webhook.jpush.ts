import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { PrismaService } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { sendJiGuangPush } from './external.jiguang'

export const sendJpushMessageEventKey = 'send-jpush-message'

export const sendJpushMessageEventEmitter = new EventEmitter()

@Injectable()
export class WebhookJpushService implements OnModuleInit {
  logger = new Logger('WebhookJpushService')

  taskQueue: Queue

  taskWorker: Worker

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  onModuleInit() {
    sendJpushMessageEventEmitter.on(sendJpushMessageEventKey, this.sendJpushMessageTask.bind(this))

    this.taskQueue = new Queue('send-jpush-message', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'send-jpush-message',
      async (job) => {
        await this.saveMessageAndSendSocket(job.data)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async sendJpushMessageTask({
    openId,
    messageId,
    event,
    content
  }: {
    openId: string
    messageId: string
    event: 'im_receive_msg' | 'item_comment_reply'
    content: string
  }) {
    this.logger.log('sendJpushMessageTask')
    await this.taskQueue.add(
      'send-jpush-message',
      {
        openId,
        event,
        content
      },
      {
        removeOnComplete: true,
        removeOnFail: true,
        jobId: `${messageId}-send-message`
      }
    )
  }

  async saveMessageAndSendSocket(data: {
    openId: string
    content: string
    event: 'im_receive_msg' | 'item_comment_reply'
  }) {
    try {
      const { jpushKey, jpushSecret } = this.configService.get<RootConfigMap['app']>('app')

      const platfromAccount = await this.prisma.platformAccount.findUnique({
        where: {
          openId: data.openId
        },
        include: {
          affiliates: {
            include: {
              user: true
            }
          }
        }
      })

      if (platfromAccount) {
        let registrationIds = []
        let androidRegistrationIds = []
        let harmonyRegistrationIds = []

        const memberUser = platfromAccount.affiliates
          .map((item) => item.user)
          .filter((data) => data !== undefined)

        memberUser.forEach((item) => {
          if (item.registrationId && item.platform === 'ios') {
            registrationIds.push(item.registrationId)
          }
          if (item.registrationId && item.platform === 'android') {
            androidRegistrationIds.push(item.registrationId)
          }
          if (item.registrationId && item.platform === 'hmos') {
            harmonyRegistrationIds.push(item.registrationId)
          }
        })

        const teamMember = await this.prisma.teamMember.findMany({
          where: {
            teamId: platfromAccount.teamId,
            status: 0,
            role: {
              in: [1, 2]
            }
          },
          include: {
            user: true
          }
        })

        const teamMemberUser = teamMember
          .map((item) => item.user)
          .filter((data) => data !== undefined)

        teamMemberUser.forEach((item) => {
          if (item.registrationId && item.platform === 'ios') {
            registrationIds.push(item.registrationId)
          }
          if (item.registrationId && item.platform === 'android') {
            androidRegistrationIds.push(item.registrationId)
          }
        })

        registrationIds = [...new Set(registrationIds)]

        androidRegistrationIds = [...new Set(androidRegistrationIds)]

        harmonyRegistrationIds = [...new Set(harmonyRegistrationIds)]

        if (registrationIds.length > 0) {
          const pushData = {
            platform: 'ios',
            audience: {
              registration_id: registrationIds
            },
            notification: {
              ios: {
                alert: data.content,
                sound: 'default',
                badge: '+1'
              }
            }
          }

          await sendJiGuangPush({ jpushKey, jpushSecret, jsonData: pushData })
        }

        if (androidRegistrationIds.length > 0) {
          const pushData = {
            platform: 'android',
            audience: {
              registration_id: androidRegistrationIds
            },
            notification: {
              android: {
                alert: data.content,
                title: '青豆云'
              }
            }
          }

          await sendJiGuangPush({ jpushKey, jpushSecret, jsonData: pushData })
        }

        if (harmonyRegistrationIds.length > 0) {
          const pushData = {
            platform: 'hmos',
            audience: {
              registration_id: harmonyRegistrationIds
            },
            notification: {
              hmos: {
                alert: data.content,
                title: '青豆云',
                intent: {
                  url: 'action.system.home'
                },
                badge_add_num: 1
              }
            }
          }

          await sendJiGuangPush({ jpushKey, jpushSecret, jsonData: pushData })
        }
      }
      // 发极光推送通知
    } catch (error) {
      this.logger.error(error)
    }
  }
}
