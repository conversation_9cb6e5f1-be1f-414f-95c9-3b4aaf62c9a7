import { BadRequestException, Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { WebHookServiceGrpc } from './webhook.rpc'
import { PersonalChatMessageEntity } from 'packages/mongo/lib'
import { postKosUserList } from './external.xiaohongshu'
import { PrismaService } from '@qdy/mysql'
import { TeamMemberRole } from '../team/team.dto'
import { AutoresponsederManageService } from '@qdy/common'
import { genSocketRedisKey, Platform } from 'packages/utils'
import { AccountAccountsStatus } from '../account/account.dto'

export const bindAccountEventKey = 'bind-juguang-account'

export const bindAccountEventEmitter = new EventEmitter()

@Injectable()
export class XhsBindAccountEventService implements OnModuleInit {
  logger = new Logger('XhsBindAccountEventService')

  constructor(
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly autoresponsederManageService: AutoresponsederManageService,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>
  ) {}

  taskQueue: Queue

  taskWorker: Worker

  onModuleInit() {
    bindAccountEventEmitter.on(bindAccountEventKey, this.bindJuGuangAccountTask.bind(this))

    this.taskQueue = new Queue('bind-juguang-account', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'bind-juguang-account',
      async (job) => {
        await this.handleKosAccount(job.data)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async bindJuGuangAccountTask({ openId, token }: { openId: string; token: string }) {
    await this.taskQueue.add(
      'bind-juguang-account',
      {
        token,
        openId
      },
      {
        removeOnComplete: true,
        removeOnFail: true,
        jobId: `${openId}-bind-juguang-account`
      }
    )
  }

  async handleKosAccount(data: { token: string; openId: string }) {
    try {
      const account = await this.prisma.platformAccount.findUnique({
        where: {
          openId: data.openId
        }
      })

      if (!account) {
        return
      }

      // 第一:查询所有勾选了的kos账号
      const pageNum = 1
      const size = 20
      const kosUserResult = await postKosUserList({
        openId: data.openId,
        accessToken: data.token,
        page: pageNum,
        size
      })

      const userList = kosUserResult.kos_user_list ?? []

      if (kosUserResult.total > size) {
        const pages = Math.ceil(kosUserResult.total / size)

        for (let i = 1; i < pages; i++) {
          const result = await postKosUserList({
            openId: data.openId,
            accessToken: data.token,
            page: pageNum + i,
            size
          })

          userList.push(...(result.kos_user_list ?? []))
        }
      }

      if (userList.length === 0) {
        return
      }

      const childAccountList = await this.prisma.platformAccount.findMany({
        where: {
          parentOpenId: account.openId
        }
      })

      const [team, platformAccountCount] = await Promise.all([
        this.prisma.team.findUnique({
          where: {
            id: account.teamId
          },
          include: {
            vip: true
          }
        }),
        this.prisma.platformAccount.count({ where: { teamId: account.teamId } })
      ])

      let addQuantity =
        team?.vip?.platformAccountNumberLimit > platformAccountCount
          ? team.vip.platformAccountNumberLimit - platformAccountCount
          : 0

      for (let i = 0; i < userList.length; i++) {
        const user = userList[i]
        const childAccount = childAccountList.find((item) => item.openId === user.user_id)
        if (childAccount) {
          // 如果用户id已存在系统中
          if (childAccount.teamId !== account.teamId) {
            // 当子账号团队id与当前主账号团队不一致
            const updateAccount = await this.prisma.platformAccount.update({
              where: {
                id: account.id
              },
              data: {
                accessToken: account.accessToken,
                tokenTime: account.tokenTime,
                refreshToken: account.refreshToken,
                refreshExpiresIn: account.refreshExpiresIn,
                refreshTime: account.refreshTime,
                expiresIn: account.expiresIn,
                teamId: account.teamId,
                name: user.nick_name,
                avatar: user.avatar_img,
                status:
                  addQuantity <= 0 ? AccountAccountsStatus.Disable : AccountAccountsStatus.Normal,
                isBind: true
              },
              include: {
                affiliates: true
              }
            })

            await this.prisma.platformAccount.update({
              where: {
                id: account.id
              },
              data: {
                affiliates: {
                  disconnect: updateAccount.affiliates.map((item) => ({ id: item.id }))
                }
              }
            })

            await this.autoresponsederManageService.deleteAutoresponder(updateAccount)

            const userIds = updateAccount.affiliates.map((item) => item.userId)
            const teamMemberByOldPlatformAccount = await this.prisma.teamMember.findMany({
              where: {
                teamId: updateAccount.teamId,
                role: {
                  not: TeamMemberRole.Member
                }
              }
            })

            const manageUserIds = teamMemberByOldPlatformAccount.map((item) => item.userId)

            const combinedAndUniqueIds = [...new Set([...userIds, ...manageUserIds])]

            const users = await this.prisma.user.findMany({
              where: {
                id: {
                  in: combinedAndUniqueIds
                }
              }
            })
            if (users.length > 0) {
              for (let i = 0; i < users.length; i++) {
                const userInfo = users[i]
                const auth = await this.cacheManager.get<string>(userInfo.phone)
                if (auth) {
                  await this.cacheManager.store.client.hdel(
                    genSocketRedisKey(auth),
                    updateAccount.openId
                  )
                }
              }
            }

            // 删除旧账号的socketids
            await this.cacheManager.store.client.del(genSocketRedisKey(account.openId))

            // 该账号之前团队对应的自动回复移除该账号
            const autoresponderList = await this.prisma.autoresponder.findMany({
              where: {
                platformAccountIds: {
                  array_contains: account.id
                }
              }
            })

            autoresponderList.forEach(async (item) => {
              if (item.platformAccountIds) {
                const accountIds = (item.platformAccountIds as number[]).filter(
                  (id) => id !== account.id
                )
                await this.prisma.autoresponder.update({
                  where: { id: item.id },
                  data: { platformAccountIds: accountIds }
                })
              }
            })

            const teamMembers = await this.prisma.teamMember.findMany({
              where: {
                teamId: updateAccount.teamId,
                role: {
                  not: TeamMemberRole.Member
                }
              },
              include: {
                user: true
              }
            })

            const socketIds = []

            for (let i = 0; i < teamMembers.length; i++) {
              const item = teamMembers[i]
              const [socketId, appSocketId] = await Promise.all([
                this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
                this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
              ])

              this.logger.debug(`teamMembers-item-${item.name}`, {
                socketId,
                reidsKey: genSocketRedisKey(item.userId),
                openId: updateAccount.id
              })

              await Promise.allSettled([
                (async () => {
                  if (appSocketId) {
                    socketIds.push(appSocketId)
                    try {
                      const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

                      if (authApp) {
                        await this.cacheManager.store.client.hset(
                          genSocketRedisKey(authApp),
                          updateAccount.openId,
                          updateAccount.id
                        )
                      }
                    } catch (error) {
                      this.logger.error('更新缓存错误', error)
                    }

                    await this.cacheManager.set(
                      genSocketRedisKey(item.userId + 'app'),
                      appSocketId,
                      0
                    )
                  }
                })(),
                (async () => {
                  if (socketId) {
                    socketIds.push(socketId)
                    try {
                      const auth = await this.cacheManager.get<string>(item.user.phone)

                      if (auth) {
                        await this.cacheManager.store.client.hset(
                          genSocketRedisKey(auth),
                          updateAccount.openId,
                          updateAccount.id
                        )
                      }
                    } catch (error) {
                      this.logger.error('更新缓存错误', error)
                    }

                    await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
                  }
                })()
              ])
            }

            if (socketIds.length) {
              try {
                this.webhookGrpcService.socketService
                  .send({
                    list: JSON.stringify(
                      socketIds.map((socketId) => ({
                        socketId,
                        data: {
                          type: 'changePlatformAccount',
                          data: [
                            {
                              action: 'add',
                              platform: updateAccount.platform,
                              name: updateAccount.name,
                              avatar: updateAccount.avatar,
                              openId: updateAccount.openId,
                              accountRole: updateAccount.accountRole,
                              id: updateAccount.id,
                              teamId: updateAccount.teamId,
                              expiresTime: updateAccount.expiresIn
                                ? updateAccount.expiresIn * 1000 + updateAccount.tokenTime.getTime()
                                : 0
                            }
                          ]
                        }
                      }))
                    )
                  })
                  .subscribe({
                    next: () => {},
                    error: (err) => {
                      throw new BadRequestException(`发送失败 error${err.message}`)
                    },
                    complete: () => {}
                  })
              } catch (error) {
                throw new BadRequestException(`socketService error${error.message}`)
              }
            }
          } else {
            await this.prisma.platformAccount.update({
              where: {
                id: childAccount.id
              },
              data: {
                isBind: true
              }
            })
          }
        } else {
          // 不存在的话,新创建账号
          const value = {
            name: user.nick_name,
            avatar: user.avatar_img,
            openId: user.user_id,
            accessToken: account.accessToken,
            refreshToken: account.refreshToken,
            expiresIn: account.expiresIn,
            refreshExpiresIn: account.refreshExpiresIn,
            teamId: account.teamId,
            status: addQuantity <= 0 ? AccountAccountsStatus.Disable : AccountAccountsStatus.Normal,
            isBind: true,
            parentOpenId: account.openId,
            platform: Platform.Xiaohongshu,
            unauthorize: '',
            affiliates: {
              connect: {
                id: account.teamId
              }
            }
          }

          const platformAccount = await this.prisma.platformAccount.create({
            data: value
          })

          const teamMembers = await this.prisma.teamMember.findMany({
            where: {
              teamId: platformAccount.teamId,
              role: {
                not: TeamMemberRole.Member
              }
            },
            include: {
              user: true
            }
          })

          const socketIds = []

          for (let i = 0; i < teamMembers.length; i++) {
            const item = teamMembers[i]
            const [socketId, appSocketId] = await Promise.all([
              this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
              this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
            ])

            this.logger.debug(`teamMembers-item-${item.name}`, {
              socketId,
              reidsKey: genSocketRedisKey(item.userId),
              openId: platformAccount.id
            })

            await Promise.allSettled([
              (async () => {
                if (appSocketId) {
                  socketIds.push(appSocketId)
                  try {
                    const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

                    if (authApp) {
                      await this.cacheManager.store.client.hset(
                        genSocketRedisKey(authApp),
                        platformAccount.openId,
                        platformAccount.id
                      )
                    }
                  } catch (error) {
                    this.logger.error('更新缓存错误', error)
                  }

                  await this.cacheManager.set(
                    genSocketRedisKey(item.userId + 'app'),
                    appSocketId,
                    0
                  )
                }
              })(),
              (async () => {
                if (socketId) {
                  socketIds.push(socketId)
                  try {
                    const auth = await this.cacheManager.get<string>(item.user.phone)

                    if (auth) {
                      await this.cacheManager.store.client.hset(
                        genSocketRedisKey(auth),
                        platformAccount.openId,
                        platformAccount.id
                      )
                    }
                  } catch (error) {
                    this.logger.error('更新缓存错误', error)
                  }

                  await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
                }
              })()
            ])
          }

          if (socketIds.length) {
            try {
              this.webhookGrpcService.socketService
                .send({
                  list: JSON.stringify(
                    socketIds.map((socketId) => ({
                      socketId,
                      data: {
                        type: 'changePlatformAccount',
                        data: [
                          {
                            action: 'add',
                            platform: platformAccount.platform,
                            name: platformAccount.name,
                            avatar: platformAccount.avatar,
                            openId: platformAccount.openId,
                            accountRole: platformAccount.accountRole,
                            id: platformAccount.id,
                            teamId: platformAccount.teamId,
                            expiresTime: platformAccount.expiresIn
                              ? platformAccount.expiresIn * 1000 +
                                platformAccount.tokenTime.getTime()
                              : 0
                          }
                        ]
                      }
                    }))
                  )
                })
                .subscribe({
                  next: () => {},
                  error: (err) => {
                    throw new BadRequestException(`发送失败 error${err.message}`)
                  },
                  complete: () => {}
                })
            } catch (error) {
              throw new BadRequestException(`socketService error${error.message}`)
            }
          }
        }

        addQuantity--
      }
    } catch (error) {
      this.logger.error(error)
    }
  }
}
