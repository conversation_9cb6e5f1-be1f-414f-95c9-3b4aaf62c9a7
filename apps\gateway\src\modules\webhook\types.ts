import { WebhookEvents, WebhookKuaishouEvents, WebhookWechatEvents } from './constant'

/**
 *
 */

export interface WebhookBody {
  event: WebhookEvents
  client_key: string
  from_user_id: string
  to_user_id: string
  content: {
    challenge: string
    user_infos?: {
      open_id: string
      nick_name: string
      avatar: string
    }[]
    card_data?: {
      label: string
      value: string
    }[]
    card_status?: number
    scopes?: [string]
  } & Record<string, unknown>
  create_time: number
  log_id?: string
}

export interface WebhookKuaishouBody {
  event: WebhookKuaishouEvents
  app_id: string
  message_id: string
  data: {
    timestamp?: number
    kpn: string
    sub_biz: string
    message_create_time: number
    open_id?: string
    from_user?: {
      user_role: number
      user_id: string
    }
    to_user?: {
      user_role: number
      user_id: string
    }
    content?: {
      content_type: number
      content: string
    }
    message_id: string
    expand_content: string
  } & Record<string, unknown>
  timestamp: number
}

export interface WebHookWechatBody {
  /**
   * 回调消息类型
   */
  TypeName: WebhookWechatEvents

  /**
   * 设备appid
   */
  Appid: string

  /**
   * 归属微信的wxid
   */
  Wxid: string

  Data: {
    isSender: number
    /**
     * 回复私信的sessionId
     */
    msgSessionId: string

    msg: {
      MsgId: number

      /**
       * 消息发送人的username
       */
      FromUserName: {
        string: string
      }

      /**
       * 消息接收人的username
       */
      ToUserName: {
        string: string
      }

      /**
       * 是否是图片类型 1为文本类型 3为图片类型
       */
      MsgType: number

      /**
       * 内容
       */
      Content: {
        string: string
      }

      Status: number

      ImgStatus: number

      /**
       * 创建时间
       */
      CreateTime: number

      /**
       * 消息ID
       */
      NewMsgId: string
    }
  }
}

export interface WebhookWechatFinderSyncMsg {
  /**
   * 回调消息类型
   */
  TypeName: WebhookWechatEvents

  /**
   * 设备appid
   */
  Appid: string

  /**
   * 归属微信的wxid
   */
  Wxid: string

  Data: {
    priority: number
    type: number
    extInfo: string
    tipsId: string
    selfFinderUsername: string
    reportExtInfo: string
    tipsUuid: string
    showInfos: {
      showType: number
      clearType: number
      title: string
      showExtInfo: string
    }[]
  }
}

export interface WechatCommentBody {
  fromUserName: string
  toUserName: string
  wxid: string
  appid: string
  sessionBuffer: string
  content: string
  objectId: string
  objectNonceId: string
  refCommentId: string
}

export interface WebWeiboBody {
  receiver_id: string
  created_at: string
  text: string
  type: string
  sender_id: string
  data: {
    latitude: string
    longitude: string
    subtype: string
    tovfid: number
    vfid: number
  }
}

export interface WebhookXiaohongshuBody {
  message_id: string
  message_type: string
  message_source: number
  from_user_id: string
  to_user_id: string
  content: string
  timestamp: number
  from_user_name: string
  from_user_avatar: string
  to_user_name: string
  to_user_avatar: string
  user_info: {
    user_id: string
    nickname: string
    header_image: string
  }[]
  log_id?: string
}

export interface WebhookXiaohongshuCommentBody {
  note_id: string
  cover: string
  comment_time: number
  note_title: string
  comment_content: string
  comment_user_name: string
  comment_user_id: string
  note_author_user_id: string
  uniq_id: string
  comment_id: number
  reply_state: number
  reply_third_account_id: string
}

export interface xiaohongshuCommentAutoResponderBody {
  from_user_id: string
  to_user_id: string
  message_id: string
  content: string
  timestamp: number
  from_user_name: string
  from_user_avatar: string
  to_user_name: string
  to_user_avatar: string
  comment_id: string
  title: string
  cover: string
}

export interface WebhookXiaohongshuCardMessageBody {
  area: string
  wechat: string
  remark: string
  brand_user_id: string
  user_id: string
  kos_user_id: string
  push_type: number
  conv_time: string
  advertiser_name: string
  advertiser_id: string
  campaign_name: string
  campaign_id: string
  creativity_name: string
  creativity_id: string
  leads_tag: string
  phone_num: string
  wechat_type: string
  wechat_copy: number
  link_id: string
  link_name: string
  msg_app_open: number
}
