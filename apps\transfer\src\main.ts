import { NestFactory } from '@nestjs/core'
import { TransferModule } from './transfer.module'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'
import compression from '@fastify/compress'
import helmet from '@fastify/helmet'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { HttpStatus, Logger, ValidationPipe } from '@nestjs/common'
import multipart from '@fastify/multipart'

export async function bootstrap() {
  const fastify = new FastifyAdapter()
  fastify.register(compression, { encodings: ['gzip', 'deflate'] })
  fastify.register(helmet)
  fastify.register(multipart, {
    limits: {
      fileSize: 1024 * 1024 * 10
    }
  })

  const app = await NestFactory.create<NestFastifyApplication>(TransferModule, fastify)
  const configService = app.get(ConfigService) as ConfigService<RootConfigMap, true>
  const appConfig = configService.get<RootConfigMap['app']>('app')

  app.enableCors({
    origin: appConfig.cors.allowOrigin,
    methods: appConfig.cors.allowMethod,
    allowedHeaders: appConfig.cors.allowHeader,
    preflightContinue: false,
    credentials: true,
    optionsSuccessStatus: HttpStatus.NO_CONTENT
  })

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true
    })
  )

  await app.listen(3007, appConfig.http.host)

  Logger.log(`Server running on ${await app.getUrl()}`, 'NestApplication')
}

bootstrap()
