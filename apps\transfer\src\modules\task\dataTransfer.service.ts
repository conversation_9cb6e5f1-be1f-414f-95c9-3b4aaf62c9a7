import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { Queue, Worker } from 'bullmq'
import { Platform } from './constant'
import {
  MessageStatisticEntity,
  DailyMessageStatisticEntity,
  MessagesEntity,
  PersonalChatMessageEntity,
  WechatMessagesEntity,
  WorkCommentEntity
} from '@qdy/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import dayjs from 'dayjs'

@Injectable()
export class DataTransferService implements OnModuleInit {
  private readonly logger = new Logger(DataTransferService.name)

  LOCK_TIMEOUT = 10 * 60 * 1000 // 10分钟的锁超时时间

  dataQueue: Queue

  dataWorker: Worker

  serverNumber: number

  constructor(
    private readonly prisma: PrismaService,
    @InjectModel(MessageStatisticEntity.name)
    private messageStatisticModel: Model<MessageStatisticEntity>,
    @InjectModel(MessagesEntity.name)
    private messagesModel: Model<MessagesEntity>,
    @InjectModel(DailyMessageStatisticEntity.name)
    private DailyMessageStatisticModel: Model<DailyMessageStatisticEntity>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(WechatMessagesEntity.name)
    private wechatMessagesModel: Model<WechatMessagesEntity>,
    @InjectModel(WorkCommentEntity.name)
    private workCommontModel: Model<WorkCommentEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  async onModuleInit() {
    this.dataQueue = new Queue('data-migration-message-abc-123', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.dataWorker = new Worker(
      'data-migration-message-abc-123',
      async (job) => {
        const { openId, platformType, teamId } = job.data

        this.logger.log(openId)

        await this.migrateUserTableData(openId, platformType, teamId)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    const lock = ((await this.cacheManager.get('init-transfer')) || 0) as number
    if (!lock) {
      // this.onDataTransferByAccount()
      // this.serverNumber = 8
      // this.cacheManager.set('init-transfer', 1, 1000 * 60 * 60)
    }

    this.logger.log('DatadTransferService init')
  }

  /**
   * 通过账号来执行迁移
   */
  async onDataTransferByAccount() {
    const platformAccountList = await this.prisma.platformAccount.findMany({
      where: {
        status: 0,
        createTime: {
          lte: new Date('2024-11-07')
        }
      },
      orderBy: {
        createTime: 'desc'
      }
    })

    const openIdList = platformAccountList.map((account) => {
      let platformType = ''
      switch (account.platform) {
        case Platform.Douyin:
          platformType = 'douyin'
          break
        case Platform.Wechat:
          platformType = 'wechat'
          break
        case Platform.Weibo:
          platformType = 'weibo'
          break
        case Platform.Kuaishou:
          platformType = 'kuaishou'
          break
      }
      const newData = {
        openId: account.openId,
        teamId: account.teamId,
        platformType
      }
      return newData
    })

    const activeArray = openIdList.slice(0, this.serverNumber)

    if (activeArray.length > 0) {
      for (let j = 0; j < activeArray.length; j++) {
        const platformAccount = activeArray[j]
        await this.dataQueue.add(
          'data-migration-message-abc-123',
          {
            openId: platformAccount.openId,
            teamId: platformAccount.teamId,
            platformType: platformAccount.platformType
          },
          {
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `platformAccount-data-transfer-message-sms-${platformAccount.openId}`
          }
        )
      }
    }

    const otherContents = openIdList.slice(this.serverNumber)

    if (otherContents.length) {
      await this.cacheManager.store.client.hset(
        'message:transfer',
        'accountIds',
        JSON.stringify(otherContents)
      )
    }
  }

  async migrateUserTableData(openId, platformType, teamId) {
    const isLock = await this.tryAcquireLock(openId)

    this.logger.log(`${openId}-${isLock}`)

    if (!isLock) {
      // 数据迁移逻辑
      await this.onTransferMessageStatisticData(openId, platformType, teamId)
    }

    const platformAccountIds = await this.cacheManager.store.client.hget(
      'message:transfer',
      'accountIds'
    )

    if (platformAccountIds) {
      const accountListArray = JSON.parse(platformAccountIds)

      const activeArray = accountListArray.slice(0, this.serverNumber)

      if (activeArray.length > 0) {
        for (let j = 0; j < activeArray.length; j++) {
          const platformAccount = activeArray[j]
          await this.dataQueue.add(
            'data-migration-message-abc-123',
            {
              openId: platformAccount.openId,
              teamId: platformAccount.teamId,
              platformType: platformAccount.platformType
            },
            {
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `platformAccount-data-transfer-message-sms-${platformAccount.openId}`
            }
          )
        }
      }

      const otherContents = accountListArray.slice(this.serverNumber)

      if (otherContents.length) {
        await this.cacheManager.store.client.hset(
          'message:transfer',
          'accountIds',
          JSON.stringify(otherContents)
        )
      } else {
        await this.cacheManager.store.client.hdel('message:transfer', 'accountIds')
      }
    }
  }

  async onTransferMessageStatisticData(openId: string, platformType: string, teamId: number) {
    const startDate = new Date('2024-12-01')
    const cursor = this.messageStatisticModel
      .find({
        accountId: openId,
        createdAt: {
          $gt: startDate
        }
      })
      .cursor()

    let batch: MessageStatisticEntity[] = []
    const pageSize = 100

    // eslint-disable-next-line no-restricted-syntax
    for await (const doc of cursor) {
      batch.push(doc)
      if (batch.length === pageSize) {
        // 进行数据迁移操作
        batch.map(async (oldData) => {
          const newData = new this.DailyMessageStatisticModel({
            platformType,
            openId,
            teamId: oldData.teamId,
            createTime: oldData.createTime,
            receiveMessageCount: oldData.receiveMessageCount,
            singleCount:
              platformType === 'douyin' ? oldData.singleCount : oldData.wechatMessageCount,
            autoSingleCount:
              platformType === 'douyin' ? oldData.autoSingleCount : oldData.wechatAutoMessageCount,
            commentCount:
              platformType === 'douyin' ? oldData.commentCount : oldData.wechatCommentCount,
            autoCommentCount:
              platformType === 'douyin' ? oldData.autoCommentCount : oldData.wechatAutoCommentCount,
            groupCount: oldData.groupCount,
            commentPeopleCount:
              platformType === 'douyin'
                ? oldData.commentPeopleCount
                : oldData.wechatCommentPeopleCount,
            singlePeopleCount:
              platformType === 'douyin' ? oldData.singleCount : oldData.wechatSinglePeopleCount
          })
          try {
            await newData.save()
          } catch (error) {
            this.logger.log(error)
          }
        })

        batch = []
      }
    }
    // 处理最后一批数据（如果不足一页）
    if (batch.length > 0) {
      batch.map(async (oldData) => {
        const newData = new this.DailyMessageStatisticModel({
          platformType,
          openId,
          teamId: oldData.teamId,
          createTime: oldData.createTime,
          receiveMessageCount: oldData.receiveMessageCount,
          singleCount: platformType === 'douyin' ? oldData.singleCount : oldData.wechatMessageCount,
          autoSingleCount:
            platformType === 'douyin' ? oldData.autoSingleCount : oldData.wechatAutoMessageCount,
          commentCount:
            platformType === 'douyin' ? oldData.commentCount : oldData.wechatCommentCount,
          autoCommentCount:
            platformType === 'douyin' ? oldData.autoCommentCount : oldData.wechatAutoCommentCount,
          groupCount: oldData.groupCount,
          commentPeopleCount:
            platformType === 'douyin'
              ? oldData.commentPeopleCount
              : oldData.wechatCommentPeopleCount,
          singlePeopleCount:
            platformType === 'douyin' ? oldData.singleCount : oldData.wechatSinglePeopleCount
        })
        try {
          await newData.save()
        } catch (error) {
          this.logger.log(error)
        }
      })
    }
    this.logger.log(`onTransferMessageStatisticData 刷新 ${openId}`)

    await this.onStatisticReceiveByAccount(openId, platformType)

    await this.onCompleteMessageStatisticData(openId, platformType, teamId)
  }

  async onStatisticReceiveByAccount(openId: string, platformType: string) {
    let receiveMessageCount = 0
    const comparisonDate = new Date('2024-12-01')
    comparisonDate.setHours(0, 0, 0, 0)
    const zeroTimestamp = comparisonDate.getTime()
    if (platformType === 'douyin') {
      const messageResult = await this.messagesModel.aggregate([
        {
          $match: {
            $and: [
              {
                event: 'im_receive_msg',
                toUserId: openId,
                createTime: {
                  $lt: zeroTimestamp
                }
              }
            ]
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      if (messageResult.length) {
        receiveMessageCount += messageResult[0].totalCount
      }

      const personalResult = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            $and: [
              {
                event: 'im_receive_msg',
                openId,
                createTime: {
                  $gt: zeroTimestamp
                }
              }
            ]
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      if (personalResult.length) {
        receiveMessageCount += personalResult[0].totalCount
      }
    } else if (platformType === 'wechat') {
      const messageResult = await this.wechatMessagesModel.aggregate([
        {
          $match: {
            $and: [
              {
                isSender: 0,
                wxid: openId,
                createTime: {
                  $lt: zeroTimestamp
                }
              }
            ]
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      if (messageResult.length) {
        receiveMessageCount += messageResult[0].totalCount
      }

      const personalResult = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            $and: [
              {
                event: 'im_receive_msg',
                openId,
                createTime: {
                  $gt: zeroTimestamp
                }
              }
            ]
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      if (personalResult.length) {
        receiveMessageCount += personalResult[0].totalCount
      }
    }

    this.logger.log(`${openId}-${receiveMessageCount}`)

    if (receiveMessageCount > 0) {
      await this.prisma.platformAccount.update({
        where: {
          openId
        },
        data: {
          receiveMessage: receiveMessageCount
        }
      })
    }
  }

  async onCompleteMessageStatisticData(openId: string, platformType: string, teamId: number) {
    const startDate = new Date('2025-01-07')
    for (let i = 0; i < 6; i++) {
      const yesterday = dayjs(startDate).add(i, 'day').toDate()
      yesterday.setHours(0, 0, 0, 0)
      const zeroTimestamp = yesterday.getTime()

      const twentyFourDate = dayjs(yesterday)
        .add(i + 1, 'day')
        .toDate()
      twentyFourDate.setHours(0, 0, 0, 0)
      const twentyFourTimestamp = twentyFourDate.getTime() - 1

      if (dayjs(yesterday).toDate() > new Date()) {
        return
      }

      const messageResult = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            $and: [
              {
                event: 'im_receive_msg',
                platformType,
                openId
              },
              {
                createTime: {
                  $gte: zeroTimestamp,
                  $lte: twentyFourTimestamp
                }
              }
            ]
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      const commentPeople = await this.workCommontModel.aggregate([
        {
          $match: {
            $and: [
              { openId },
              {
                createTime: {
                  $gte: yesterday,
                  $lte: twentyFourDate
                }
              }
            ]
          }
        },
        {
          $group: {
            _id: '$fromUserId'
          }
        },
        {
          $count: 'totalCount'
        }
      ])
      // 获取抖音私信人数
      const singlePeople = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            $and: [
              {
                event: 'im_receive_msg',
                platformType,
                openId
              },
              {
                createTime: {
                  $gte: yesterday,
                  $lte: twentyFourDate
                }
              }
            ]
          }
        },
        {
          $group: {
            _id: '$fromUserId',
            count: { $sum: 1 }
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      const count = messageResult.length > 0 ? messageResult[0].totalCount : 0

      const commentPeopleCount = commentPeople.length > 0 ? commentPeople[0].totalCount : 0
      const singlePeopleCount = singlePeople.length > 0 ? singlePeople[0].totalCount : 0

      const yesterdayFormat = dayjs(yesterday).format('YYYY-MM-DD')

      if (count > 0 || commentPeopleCount > 0 || singlePeopleCount > 0) {
        const dailyMessageStatistic = await this.DailyMessageStatisticModel.findOne({
          createTime: yesterdayFormat,
          teamId,
          openId
        })

        if (!dailyMessageStatistic) {
          await this.DailyMessageStatisticModel.create({
            platformType,
            teamId,
            openId,
            createTime: yesterdayFormat,
            receiveMessageCount: count,
            commentPeopleCount,
            singlePeopleCount
          })
        } else {
          await this.DailyMessageStatisticModel.findByIdAndUpdate(dailyMessageStatistic.id, {
            receiveMessageCount: count,
            commentPeopleCount,
            singlePeopleCount
          })
        }
      }
    }
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(`transfer-${key}`, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
