/*
  Warnings:

  - Added the required column `commentDosageLimit` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `expirationTime` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fansGroupManageLimit` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `groupDosageLimit` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `platformAccountNumberLimit` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `signDosageLimit` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `teamMemberNumberLimit` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `uploadImageLimit` to the `Order` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `Order` ADD COLUMN `commentDosageLimit` INTEGER NOT NULL,
    ADD COLUMN `expirationTime` DATETIME(3) NOT NULL,
    ADD COLUMN `fansGroupManageLimit` INTEGER NOT NULL,
    ADD COLUMN `groupDosageLimit` INTEGER NOT NULL,
    ADD COLUMN `platformAccountNumberLimit` INTEGER NOT NULL,
    ADD COLUMN `signDosageLimit` INTEGER NOT NULL,
    ADD COLUMN `teamMemberNumberLimit` INTEGER NOT NULL,
    ADD COLUMN `uploadImageLimit` INTEGER NOT NULL;
