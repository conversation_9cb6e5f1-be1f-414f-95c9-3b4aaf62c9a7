import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'

const groupGreetingApi = 'https://open.douyin.com/im/group/setting/set/'

const logger = new Logger('autoresponder')

export async function setGroupGreeting({
  accessToken,
  openId,
  groupId,
  greeting
}: {
  accessToken: string
  openId: string
  groupId: string
  greeting: string
}) {
  const res = await axios.post(
    `${groupGreetingApi}?open_id=${openId}`,
    {
      group_id: groupId,
      group_setting_type: 1,
      msg_list: [
        {
          msg_type: 1,
          text: {
            text: greeting
          }
        }
      ]
    },
    {
      headers: {
        'access-token': accessToken,
        'Content-Type': 'application/json'
      }
    }
  )

  if (res.data.err_no !== 0) {
    logger.error({ desc: res.data.err_msg, code: res.data.err_no })
    throw new BadRequestException(`[抖音官方]:${res.data.err_msg}`)
  }

  return res.data
}

export async function disableGroupGreeting({
  accessToken,
  openId,
  groupId
}: {
  accessToken: string
  openId: string
  groupId: string
}) {
  const res = await axios.post(
    'https://open.douyin.com/im/group/setting/disable/',
    {
      group_id: groupId,
      group_setting_type: 1
    },
    {
      params: {
        open_id: openId
      },
      headers: {
        'access-token': accessToken,
        'content-type': 'application/json'
      }
    }
  )

  if (res.data.err_no !== 0) {
    logger.error({ desc: res.data.err_msg, code: res.data.err_no })
    throw new BadRequestException(`[抖音官方]:${res.data.err_msg}`)
  }

  return res.data
}
