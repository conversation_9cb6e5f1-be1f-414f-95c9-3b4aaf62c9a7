/*
  Warnings:

  - You are about to drop the column `allowInvitations` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `doorsill` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `groupType` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `joinAttention` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `joinCheck` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `joinGroupLevel` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `liveAsync` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `opusAsync` on the `FanGroups` table. All the data in the column will be lost.
  - You are about to drop the column `showHomePage` on the `FanGroups` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `FanGroups` DROP COLUMN `allowInvitations`,
    DROP COLUMN `doorsill`,
    DROP COLUMN `groupType`,
    DROP COLUMN `joinAttention`,
    DROP COLUMN `joinCheck`,
    DROP COLUMN `joinGroupLevel`,
    DROP COLUMN `liveAsync`,
    DROP COLUMN `opusAsync`,
    DROP COLUMN `showHomePage`;

-- AlterTable
ALTER TABLE `PlatformAccount` ADD COLUMN `inited` BOOLEAN NULL DEFAULT false;
