import { Inject, Injectable } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { OrderStatus } from './order.dto'
import { MemberService } from '../member/member.service'

@Injectable()
export class OrderService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly memberService: MemberService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getOrders({
    page,
    size,
    orderNo,
    payStartTime,
    payEndTime
  }: {
    page: number
    size: number
    orderNo: string
    payStartTime: number
    payEndTime: number
  }) {
    const { user } = this.request
    const where: Parameters<typeof this.prisma.order.findMany>[0]['where'] = {
      orderNo,
      payTime: {
        gte: new Date(payStartTime),
        lte: new Date(payEndTime)
      },
      orderStatus: OrderStatus.SUCCESS,
      channelId: user.channelId
    }

    if (!orderNo) {
      delete where.orderNo
    }

    if (!payStartTime || !payEndTime) {
      delete where.payTime
    }

    const [orders, total, totalAmount] = await Promise.all([
      this.prisma.order.findMany({
        where,
        orderBy: { fromTime: 'desc' },
        skip: (page - 1) * size,
        take: size,
        select: {
          id: true,
          orderNo: true,
          payTime: true,
          phone: true,
          payAmount: true,
          orderStatus: true
        }
      }),
      this.prisma.order.count({ where }),
      this.prisma.order.aggregate({
        _sum: {
          payAmount: true
        },
        where
      })
    ])
    return {
      total,
      page,
      size,
      totalAmount: totalAmount._sum.payAmount || 0,
      data: orders.map((item) => ({
        ...item,
        phone: this.memberService.maskPhoneNumber(item.phone),
        payTime: item.payTime ? item.payTime.getTime() : 0
      }))
    }
  }
}
