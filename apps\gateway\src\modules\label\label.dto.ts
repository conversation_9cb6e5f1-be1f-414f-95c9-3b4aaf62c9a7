import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsString } from 'class-validator'

export class Label {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '标签'
  })
  title: string
}

export class PatchLabelRequest {
  @ApiProperty({
    description: '标签名称',
    default: '标签',
    required: true,
    type: String
  })
  @IsString()
  title: string
}

/**
 * 创建标签
 */
export class PostLabelRequest {
  @ApiProperty({
    type: String,
    example: '标签',
    required: true
  })
  @IsString()
  title: string
}

export class LabelResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [Label]
  })
  data: Label[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  totalPage: number
}
