import { BadRequestException, Inject, Injectable } from '@nestjs/common'
import { PlatformAccount, PrismaService } from '@qdy/mysql'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { Platform, PlatformAccountKeywordKey } from '@qdy/utils'
import { PlatformAccountKeywordRedisValue } from './autoresponderManage.dto'

@Injectable()
export class PlatformAccountManageService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  async updatePlatformAccountRedisInfo(platformAccount: PlatformAccount) {
    try {
      // 根据账号openId查找符合条件的记录
      const existingCacheValueString = await this.cacheManager.store.client.hget(
        PlatformAccountKeywordKey,
        platformAccount.openId
      )

      if (existingCacheValueString) {
        const existingCacheValue = JSON.parse(
          existingCacheValueString
        ) as PlatformAccountKeywordRedisValue
        const updatedCacheValue = {
          ...existingCacheValue,
          token:
            platformAccount.platform === Platform.Wechat
              ? platformAccount.appId
              : platformAccount.accessToken,
          accountExpired: platformAccount.expiresIn
            ? platformAccount.expiresIn * 1000 * 5 * 2 + platformAccount.tokenTime.getTime()
            : 0,
          teamId: platformAccount.teamId,
          status: platformAccount.status,
          username: platformAccount.username
        }
        await this.cacheManager.store.client.hset(
          PlatformAccountKeywordKey,
          platformAccount.openId,
          JSON.stringify(updatedCacheValue)
        )
      } else {
        const cacheValue: PlatformAccountKeywordRedisValue = {
          token:
            platformAccount.platform === Platform.Wechat
              ? platformAccount.appId
              : platformAccount.accessToken,
          accountExpired: platformAccount.expiresIn
            ? platformAccount.expiresIn * 1000 * 5 * 2 + platformAccount.tokenTime.getTime()
            : 0,
          platformAccountId: platformAccount.id,
          teamId: platformAccount.teamId,
          openId: platformAccount.openId,
          status: platformAccount.status,
          username: platformAccount.username
        }

        await this.cacheManager.store.client.hset(
          PlatformAccountKeywordKey,
          platformAccount.openId,
          JSON.stringify(cacheValue)
        )
      }
    } catch (e) {
      throw new BadRequestException('更新缓存失败')
    }
  }

  async deletePlatformAccountRedisInfo(platformAccount: PlatformAccount) {
    // 查找所有符合条件的记录
    try {
      await this.cacheManager.store.client.hdel(PlatformAccountKeywordKey, platformAccount.openId)
    } catch {
      // ignore
    }
  }
}
