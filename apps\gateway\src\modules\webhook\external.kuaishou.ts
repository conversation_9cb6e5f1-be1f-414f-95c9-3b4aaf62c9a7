import { BadRequestException, Logger } from '@nestjs/common'
import crypto from 'crypto'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { AnyObject } from 'mongoose'
import axios from 'axios'
import { sendEvent } from '../overview/event'
import { sendMessageEventEmitter, sendMessageEventKey } from './webhook.event'
import { customAlphabet } from 'nanoid'

const logger = new Logger('webhook external kuaishou')

const sendMessageApi = 'https://open.kuaishou.com/openapi/message/send'

export function decrypt(encryptedDataStr: string, secret: string) {
  try {
    const encryptDatatoArray = encryptedDataStr.split(':')

    // 将 Base64 编码的加密数据转为 Buffer
    const encryptedData = Buffer.from(encryptDatatoArray[1], 'base64')

    // 创建 AES 解密器
    const decipher = crypto.createDecipheriv(
      'aes-128-cbc',
      Buffer.from(secret, 'base64'),
      Buffer.from(encryptDatatoArray[0], 'base64')
    )

    // 解密数据
    let decryptedData = decipher.update(encryptedData)
    decryptedData = Buffer.concat([decryptedData, decipher.final()])

    // 返回解密后的字符串
    return decryptedData.toString('utf8')
  } catch (err) {
    logger.log(err.message)
    throw new BadRequestException(`[快手解密数据失败]:${err.message}`)
  }
}

export function encrypt(plaintext, key) {
  try {
    const iv = crypto.randomBytes(16)

    // 创建 AES 加密器（使用 AES-128-CBC）
    const cipher = crypto.createCipheriv('aes-128-cbc', Buffer.from(key, 'base64'), iv)

    // 加密数据
    let encryptedData = cipher.update(plaintext, 'utf8')
    encryptedData = Buffer.concat([encryptedData, cipher.final()])

    // 返回加密后的数据和 IV（Base64 编码）
    return iv.toString('base64') + ':' + encryptedData.toString('base64')
  } catch (err) {
    throw new BadRequestException(`[快手加密数据失败]:${err.message}`)
  }
}

export enum ContentType {
  Text = 1,
  Image = 2,
  Card = 10000
}

export async function postSendMessage(data: {
  messageType: ContentType
  content: string
  secret: string
  openId: string
  accessToken: string
  toUserId: string
  teamId: number
  appId: string
  platformAccountId: number
  fromName?: string
  fromAvatar?: string
  toName?: string
  toAvatar?: string
  auto?: boolean
  autoresponderId?: number
  welcome?: boolean
  redisClient: Cache<RedisStore>
  scene?: string
  width?: number
  height?: number
  contentLenght?: number
}) {
  const teamInfo = (await data.redisClient.get(`overview:${data.teamId}`)) as {
    residueCount: number
  }

  if (teamInfo) {
    if (teamInfo.residueCount <= 0) {
      throw new BadRequestException('剩余回复次数不足')
    }
  }

  if (data.autoresponderId) {
    const key = `postSendMessage:${data.openId}-${data.toUserId}`
    const toUserCount = parseInt((await data.redisClient.get(key)) || '0', 10)

    // 1小时内发送次数超过3次
    if (toUserCount && toUserCount >= 100) {
      return
    }
    // 1小时内发送次数
    if (toUserCount) {
      const ttl = await data.redisClient.store.ttl(key)

      await data.redisClient.set(key, toUserCount + 1, ttl)
    } else {
      await data.redisClient.set(key, 1, 1000 * 60 * 60 * 24)
    }
  }

  if (data.scene === 'im_enter_session_prologue_msg') {
    const key = `postSendwelcomeMessage:${data.openId}-${data.toUserId}`
    const toUserWelcomeCount = parseInt((await data.redisClient.get(key)) || '0', 10)

    // 1小时内发送次数超过3次
    if (toUserWelcomeCount && toUserWelcomeCount >= 3) {
      return
    }
    // 30s内发送次数
    if (toUserWelcomeCount) {
      const ttl = await data.redisClient.store.ttl(key)

      await data.redisClient.set(key, toUserWelcomeCount + 1, ttl)
    } else {
      await data.redisClient.set(key, 1, 1000 * 30)
    }
  }

  const content: AnyObject = {}
  let eventText = 'text'

  switch (data.messageType) {
    case ContentType.Text:
      content.contentType = 1
      eventText = 'text'
      content.content = JSON.stringify({
        text: encrypt(data.content, data.secret)
      })
      break
    case ContentType.Image:
      content.contentType = 2
      eventText = 'image'
      content.content = JSON.stringify({
        uri: encrypt(data.content, data.secret),
        width: data.width,
        height: data.height,
        contentLenght: data.contentLenght
      })
      break
    case ContentType.Card:
      eventText = 'retain_consult_card'
      content.contentType = 10000
      content.content = JSON.stringify({
        id: encrypt(data.content, data.secret)
      })
      break
    default:
      break
  }

  const nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 16)

  const transformData = {
    kpn: 'KUAISHOU',
    sub_biz: 'KSIM_TO_ADIM',
    from_user: {
      userId: data.openId,
      userRole: 1
    },
    to_user: {
      userId: data.toUserId,
      userRole: 2
    },
    content,
    scene: data.scene || 'im_replay_msg',
    request_id: nanoid()
  }

  const res = (await axios.post(sendMessageApi, transformData, {
    params: {
      app_id: data.appId,
      access_token: data.accessToken
    }
  })) as {
    data: {
      errorCode: number
      errorMsg: string
      requestId: string
      messageId: string
    }
  }

  if (res.data.errorCode !== 1) {
    logger.debug({
      code: res.data.errorCode,
      description: res.data.errorMsg
    })

    throw new BadRequestException(`[快手官方]:${res.data.errorMsg}`)
  }

  sendMessageEventEmitter.emit(sendMessageEventKey, {
    platformType: 'kuaishou',
    uniqueId: res.data.messageId,
    openId: data.openId,
    fromUserId: data.openId,
    toUserId: data.toUserId,
    sessionId: data.toUserId,
    fromName: data.fromName,
    fromAvatar: data.fromAvatar,
    toName: data.toName,
    toAvatar: data.toAvatar,
    messageId: res.data.messageId,
    isAuto: data.auto ? 1 : 0,
    content: {
      messageType: eventText,
      text: data.content,
      width: data.width,
      height: data.height,
      contentLenght: data.contentLenght
    }
  })

  sendEvent({
    platformType: 'kuaishou',
    teamId: data.teamId,
    platformAccountId: data.platformAccountId,
    autoresponderId: data.autoresponderId,
    autoCommentCount: 0,
    commentCount: 0,
    groupCount: 0,
    openId: data.openId,
    ...(data.autoresponderId
      ? { autoSingleCount: 1, singleCount: 0 }
      : { singleCount: 1, autoSingleCount: 0 })
  })

  return {
    requestId: res.data.requestId,
    messageId: res.data.messageId
  }
}
