import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'
import { AnyObject } from 'mongoose'
import crypto from 'crypto'

const logger = new Logger('account external')

const authorizeAccountApi = 'https://open.kuaishou.com/oauth2/access_token'
const authorizeAccountInfoApi = 'https://open.kuaishou.com/openapi/user_info'
const authorizeAccountRefreshApi = 'https://open.kuaishou.com/oauth2/refresh_token'
const marketToolList = 'https://open.kuaishou.com/openapi/kuailiao/getMarketToolList'

export async function getAuthorizeAccount(data: {
  clientSecret: string
  clientKey: string
  code: string
}) {
  const res = (await axios.get(authorizeAccountApi, {
    params: {
      app_id: data.clientKey,
      app_secret: data.clientSecret,
      code: data.code,
      grant_type: 'authorization_code'
    }
  })) as {
    data: {
      result: number
      access_token: string
      expires_in: number
      open_id: string
      refresh_token_expires_in: number
      refresh_token: string
      scopes: [string]
      error_msg: string
    }
  }

  if (res.data.result !== 1) {
    throw new BadRequestException(`[快手官方]:${res.data.error_msg}`)
  }

  return {
    openId: res.data.open_id,
    accessToken: res.data.access_token,
    refreshToken: res.data.refresh_token,
    expiresIn: res.data.expires_in,
    refreshExpiresIn: res.data.refresh_token_expires_in
  }
}

export async function getAuthorizeAccountInfoApi({
  accessToken,
  clientKey
}: {
  accessToken: string
  clientKey: string
}) {
  try {
    const res = (await axios.get(authorizeAccountInfoApi, {
      params: {
        access_token: accessToken,
        app_id: clientKey
      }
    })) as {
      data: {
        result: number
        user_info: {
          head: string
          bigHead: string
          follow: number
          fan: number
          name: string
          open_id: string
          city: string
        }
        error_msg: string
      }
    }

    if (res.data.result !== 1) {
      throw new BadRequestException(`[快手官方]:${res.data.error_msg}`)
    }

    return {
      avatar: res.data.user_info.head,
      name: res.data.user_info.name,
      userinfo: res.data.user_info
    } as {
      avatar: string
      name: string
      userinfo: AnyObject
    }
  } catch (error) {
    logger.error(error)
    throw new BadRequestException('获取账号信息失败')
  }
}

export async function getAuthorizeAccountRefresh({
  clientSecret,
  clientKey,
  refreshToken
}: {
  clientSecret: string
  clientKey: string
  refreshToken: string
}) {
  const res = (await axios.get(authorizeAccountRefreshApi, {
    params: {
      app_secret: clientSecret,
      app_id: clientKey,
      refresh_token: refreshToken,
      grant_type: 'refresh_token'
    }
  })) as {
    data: {
      result: number
      access_token: string
      expires_in: number
      refresh_token: string
      refresh_token_expires_in: number
      error_msg: string
    }
  }

  if (res.data.result !== 1) {
    throw new BadRequestException(`[快手官方]:${res.data.error_msg}`)
  }

  return {
    expiresIn: res.data.expires_in,
    accessToken: res.data.access_token,
    refreshToken: res.data.refresh_token,
    refreshExpiresIn: res.data.refresh_token_expires_in
  }
}

/**
 * 获取快手留资卡片
 */
export async function postMarketToolList(data: {
  accessToken: string
  appId: string
  openId: string
}) {
  const res = await axios.post(
    marketToolList,
    {
      open_id: data.openId,
      request_id: crypto.randomBytes(16).toString()
    },
    {
      params: {
        app_id: data.appId,
        access_token: data.accessToken
      }
    }
  )

  logger.log(res.data.error_code)

  if (res.data.error_code !== 1) {
    if (res.data.error_code === 1706) {
      throw new BadRequestException(`[快手官方]:该账号非蓝V`)
    } else {
      throw new BadRequestException(`[快手官方]:${res.data.error_msg}`)
    }
  }

  return res.data.data || []
}
