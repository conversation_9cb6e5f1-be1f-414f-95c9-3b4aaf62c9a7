import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { PrismaService } from '@qdy/mysql'
import { FastifyRequest } from 'fastify'
import { WeiboMessagesRequestDTO } from './interact.dto'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { Platform } from '@qdy/utils'
import { TeamMemberRole } from '../team/team.dto'
import { AccountAccountsStatus } from '../account/account.dto'
import { weiboSendMessages } from './external.weibo'

@Injectable()
export class InteractWeiboService {
  logger = new Logger('InteractWechatService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async sendWeiboMessage({
    toUserId,
    platformAccountId,
    content,
    messageType,
    toAvatar,
    toName
  }: WeiboMessagesRequestDTO) {
    const { user } = this.request

    const { platformAccount, teamMember } = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId
    })

    const res = await weiboSendMessages({
      platformAccountId: platformAccount.id,
      teamId: platformAccount.teamId,
      accessToken: platformAccount.accessToken,
      content,
      type: messageType,
      fromUserId: platformAccount.openId,
      fromName: platformAccount.name,
      fromAvatar: platformAccount.avatar,
      toAvatar,
      toName,
      toUserId,
      redisClient: this.cacheManager
    })

    await this.prisma.teamMember.update({
      where: {
        id: teamMember.id
      },
      data: {
        replyMessage: {
          increment: 1
        }
      }
    })

    return {
      messageId: res.messageId
    }
  }

  private async checkPlatformAccount({
    teamId,
    userId,
    platformAccountId
  }: {
    teamId: number
    userId: number
    platformAccountId: number
  }) {
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('用户未加入此团队')
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    const platformAccount = platformAccounts.find((item) => item.id === platformAccountId)

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在或你不属于运营人员')
    }

    if (platformAccount.platform !== Platform.Weibo) {
      throw new ForbiddenException('此账号不是微博账号')
    }

    if (platformAccount.status === AccountAccountsStatus.Disable) {
      throw new ForbiddenException('账号已被冻结')
    }

    return { platformAccount, teamMember }
  }
}
