import { Inject, Injectable, Logger } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { VIPOften } from './constant'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { SalesType } from 'apps/common/modules/orderManage/orderManage.dto'

@Injectable()
export class VipService {
  logger = new Logger('UserService')

  constructor(
    private readonly prisma: PrismaService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getInterest() {
    const res = await this.prisma.interest.findMany()
    const { user } = this.request
    const team = await this.prisma.team.findUnique({
      where: {
        id: user.currentTeamId
      }
    })

    return {
      interests: res,
      vipOften: VIPOften.map((item) => {
        return {
          ...item,
          originalPrice: item.mount === 1 ? 498 : 5970,
          price: item.mount === 12 ? 3980 : team.salesType === SalesType.NotBuy ? 398 : 498
        }
      })
    }
  }
}
