import { Module } from '@nestjs/common'

import { UserController } from './user.controller'
import { UserService } from './user.service'
import { ThrottlerModule } from '@nestjs/throttler'
import { OrderManageModule, TosManageModule, TlsManageModule } from '@qdy/common'
import { TeamModule } from '../team/team.module'

@Module({
  imports: [
    OrderManageModule,
    TosManageModule,
    TeamModule,
    TlsManageModule,
    ThrottlerModule.forRoot({
      errorMessage: '请求过于频繁，请稍后再试',
      throttlers: [
        {
          ttl: process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'test' ? 1000 : 60000,
          limit: 5
        }
      ]
    })
  ],
  providers: [UserService],
  controllers: [UserController],
  exports: [UserService]
})
export class UserModule {}
