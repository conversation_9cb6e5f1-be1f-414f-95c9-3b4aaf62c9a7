import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { Autoresponder } from '@qdy/mysql'
import { AnyObject } from 'mongoose'

export class AutoresponderOpuser {
  @ApiProperty({
    description: 'id',
    example: 4,
    required: true
  })
  @IsNotEmpty({ message: 'platformAccountId不能为空' })
  @IsNumber({}, { message: 'platformAccountId必须是数字' })
  platformAccountId: number

  @ApiProperty({
    description: 'id',
    example: 'xx',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  id: string

  @ApiProperty({
    description: 'title',
    example: ' 你好',
    required: false
  })
  @IsString()
  @IsOptional()
  title: string

  @ApiProperty({
    description: '头像',
    example: ' xxx.png',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  avatar: string

  @ApiProperty({
    description: '创建时间',
    example: '2021-01-01T00:00:00.000Z',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  createTime: number

  @ApiProperty({
    description: '评论数',
    example: 123,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  commentCount: number
}

export class AutoresponderKeywordRedisValue {
  platform: Autoresponder['platform']

  scene: Autoresponder['scene']

  trigger: Autoresponder['trigger']

  keyword: Autoresponder['keywords']

  stopReply: Autoresponder['stopReply']

  stopInterval: Autoresponder['stopInterval']

  isDelay: Autoresponder['isDelay']

  isNew: Autoresponder['isNew']

  delayTime: Autoresponder['delayTime']

  stopTime: Autoresponder['stopTime']

  rule: Autoresponder['rule']

  contents: AnyObject[]

  contentType: Autoresponder['contentType']

  state: Autoresponder['state']

  autoresponderId: number

  executionCount: Autoresponder['executionCount']

  // imageId: Autoresponder['imageId']
}

export class PlatformAccountKeywordRedisValue {
  accountExpired: number

  token: string

  platformAccountId: number

  teamId: number

  openId: string

  status: number

  username: string
  // imageId: Autoresponder['imageId']
}
