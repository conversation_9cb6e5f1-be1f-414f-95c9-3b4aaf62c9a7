/* eslint-disable no-case-declarations */
/* eslint-disable no-continue */
import { BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import {
  PersonalChatMessageEntity,
  MessagesByAutoresponderEntity,
  WorkCommentEntity
} from '@qdy/mongo'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { InjectModel } from '@nestjs/mongoose'
import { AnyObject, Model } from 'mongoose'
import { Cache } from 'cache-manager'
import { PrismaService } from '@qdy/mysql'
import { BindEventXiaohongshu, WebhookEvents } from './constant'
import { genSocketRedisKey, Platform } from '@qdy/utils'
import {
  WebhookXiaohongshuBody,
  WebhookXiaohongshuCardMessageBody,
  WebhookXiaohongshuCommentBody,
  xiaohongshuCommentAutoResponderBody
} from './types'
import {
  AutoresponderKeywordKey,
  AutoresponderVaria<PERSON><PERSON><PERSON>,
  PlatformAccountKeywordKey
} from '../autoresponder/constant'
import {
  AutoresponderContentChildType,
  AutoresponderContentTextType,
  AutoresponderContentType,
  AutoresponderKeywordContent,
  AutoresponderKeywordRedisValue,
  AutoresponderKeywordRule,
  AutoresponderTriggerType
} from '../autoresponder/autoresponder.dto'
import { generateRandom, wait } from '../../common/utils'
import { Queue, Worker } from 'bullmq'
import { WebHookServiceGrpc } from './webhook.rpc'
import { AccountAccountsStatus } from '../account/account.dto'
import { customAlphabet } from 'nanoid'
import { sendJpushMessageEventKey, sendJpushMessageEventEmitter } from './webhook.jpush'
import { ContentType, decrypt, postSendMessage } from './external.xiaohongshu'
import { TeamMemberRole } from '../team/team.dto'
import { PlatformAccountManageService, TlsManageService } from '@qdy/common'
import { bindAccountEventKey, bindAccountEventEmitter } from './xhsBindAccount.event'

@Injectable()
export class WebhookServiceXiaohongshu {
  logger = new Logger('WebhookServiceXiaohongshu')

  taskQueue: Queue

  taskWorker: Worker

  callbackTaskQueue: Queue

  callbackTaskWorker: Worker

  callbackCommentTaskQueue: Queue

  callbackCommentTaskWorker: Worker

  bindQueue: Queue

  bindWorker: Worker

  cardMessageQueue: Queue

  cardMessageWorker: Worker

  private secretKey = process.env.XIAOHONGSHU_SECRET

  constructor(
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(WorkCommentEntity.name) private workCommentModel: Model<WorkCommentEntity>,
    @InjectModel(MessagesByAutoresponderEntity.name)
    private messagesByAutoresponderModel: Model<MessagesByAutoresponderEntity>,
    private readonly prisma: PrismaService,
    private readonly tlsManageService: TlsManageService,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 8)

  onModuleInit() {
    this.logger.log('WebhookServiceXiaohongshu init')

    this.taskQueue = new Queue('autoresponderXiaohongshu', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'autoresponderXiaohongshu',
      async (job) => {
        const { contents, ...others } = job.data

        setTimeout(() => {
          this.autoresponderTask(contents, others).catch((err) => {
            throw new BadRequestException(`task${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.callbackTaskQueue = new Queue('callbackEventXiaohongshu', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.callbackTaskWorker = new Worker(
      'callbackEventXiaohongshu',
      async (job) => {
        const { body, createTime } = job.data

        setTimeout(() => {
          this.callbackEventTask(body, createTime).catch((err) => {
            this.logger.log(err.message)
            throw new BadRequestException(`callbackEventXiaohongshuTask${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.callbackCommentTaskQueue = new Queue('callbackEventCommentXiaohongshu', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.callbackCommentTaskWorker = new Worker(
      'callbackEventCommentXiaohongshu',
      async (job) => {
        const { body } = job.data

        setTimeout(() => {
          this.callbackEventCommentTask(body).catch((err) => {
            this.logger.log(err.message)
            throw new BadRequestException(`callbackEventXiaohongshuTask${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.bindQueue = new Queue('bindEventXiaohongshu', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.bindWorker = new Worker(
      'bindEventXiaohongshu',
      async (job) => {
        const { body, type } = job.data

        this.logger.log(body)

        this.logger.log(type)

        switch (type) {
          case BindEventXiaohongshu.BindAccount:
            await this.bindAccount(body)
            break
          case BindEventXiaohongshu.UnBindAccount:
            await this.unbindAccount(body)
            break
          case BindEventXiaohongshu.BindKosUser:
            await this.bindUser(body)
            break
        }
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.cardMessageQueue = new Queue('cardMessageEvent', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.cardMessageWorker = new Worker(
      'cardMessageEvent',
      async (job) => {
        const { content } = job.data

        setTimeout(() => {
          this.cardMessage({ content }).catch((err) => {
            this.logger.log(err.message)
            throw new BadRequestException(`cardMessageEvent${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async webhook(body: WebhookXiaohongshuBody) {
    const { from_user_id: fromUserId, timestamp: createTime } = body

    const job = await this.callbackTaskQueue.getJob(`${fromUserId}-${createTime}`)

    if (!job) {
      await this.callbackTaskQueue.add(
        'callbackEventXiaohongshu',
        {
          body,
          createTime: Number(createTime)
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${fromUserId}-${createTime}`
        }
      )
    }

    return {
      code: 0,
      msg: '成功',
      success: true
    }
  }

  async webhookComment(body: WebhookXiaohongshuCommentBody) {
    const { uniq_id: uniqueId, comment_time: commentTime } = body

    const job = await this.callbackCommentTaskQueue.getJob(`${uniqueId}-${commentTime}`)

    if (!job) {
      await this.callbackCommentTaskQueue.add(
        'callbackEventCommentXiaohongshu',
        {
          body,
          createTime: commentTime
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${uniqueId}-${commentTime}`
        }
      )
    }

    return {
      code: 0,
      msg: '成功',
      success: true
    }
  }

  async callbackEventTask(body: WebhookXiaohongshuBody, createTime: number) {
    let sessionId = ''
    let openId = ''

    const { from_user_id: fromUserId } = body
    let { to_user_id: toUserId } = body
    let userIdMaps: Record<string, string>

    if (!toUserId) {
      toUserId = fromUserId
    }

    let event = WebhookEvents.IMReceiveMessage

    if (body.message_source === 3) {
      openId = fromUserId
      sessionId = toUserId
      event = WebhookEvents.IMSendMessage
      userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(fromUserId))
    } else {
      openId = toUserId
      sessionId = fromUserId
      userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(toUserId))
    }

    const content = decrypt(body.content, this.secretKey)

    const decryptData = JSON.parse(content)

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId
      },
      include: {
        Team: true
      }
    })

    if (!platformAccount) {
      // 账号未添加直接过滤
      return
    }

    // if (body.message_type === 'TEXT' && body.message_source === 3 && decryptData.text === '') {
    //   // 欢迎语
    //   return
    // }

    if (body.message_type === 'REVOKE') {
      // 撤回
      const revokeMessage = await this.personalChatMessageModel.findOne({
        messageId: decryptData.message_id
      })

      if (revokeMessage) {
        const dataList: { socketId: string; data: Record<string, unknown> }[] = []

        Object.keys(userIdMaps).forEach((socketId) => {
          dataList.push({
            socketId,
            data: {
              platformType: 'xiaohongshu',
              uniqueId: decryptData.message_id,
              event: WebhookEvents.IMRecallMsg,
              openId,
              fromUserId,
              toUserId,
              fromAvatar: '',
              fromName: '',
              toAvatar: platformAccount.avatar,
              toName: platformAccount.name,
              sessionId,
              content: {
                MessageType: 'im_recall_msg',
                serverMessageId: decryptData.message_id
              },
              createTime,
              isAuto: 0,
              messageId: decryptData.message_id,
              platformAccountId: parseInt(userIdMaps[socketId], 10)
            }
          })
        })

        if (dataList.length) {
          try {
            this.webhookGrpcService.socketService
              .send({ list: JSON.stringify(dataList) })
              .subscribe({
                next: () => {},
                error: (err) => {
                  throw new BadRequestException(`发送失败 error${err.message}`)
                },
                complete: () => {}
              })
          } catch (error) {
            throw new BadRequestException(`socketService error${error.message}`)
          }
        }

        await this.personalChatMessageModel.findByIdAndUpdate(revokeMessage.id, { isRecall: 1 })
      }
      return
    }

    const data = {
      platformType: 'xiaohongshu',
      uniqueId: body.message_id,
      event,
      openId,
      fromUserId,
      toUserId,
      fromAvatar: '',
      fromName: '',
      toAvatar: platformAccount.avatar,
      toName: platformAccount.name,
      sessionId,
      content: {},
      createTime,
      isAuto: 0,
      messageId: body.message_id
    }

    body.user_info.forEach((item) => {
      if (item.user_id === fromUserId) {
        data.fromAvatar = item.header_image
        data.fromName = item.nickname
      } else if (item.user_id === toUserId) {
        data.toAvatar = item.header_image
        data.toName = item.nickname
      }
    })

    switch (body.message_type) {
      case 'TEXT':
        data.content = {
          messageType: 'text',
          type: body.message_type,
          text: decryptData.text
        }
        break
      case 'SMILES':
        data.content = {
          messageType: 'smiles',
          type: body.message_type,
          link: decryptData.link,
          width: decryptData?.size?.width,
          height: decryptData?.size?.height
        }
        break
      case 'IMAGE':
        data.content = {
          messageType: 'image',
          type: body.message_type,
          link: decryptData.link,
          width: decryptData?.size?.width,
          height: decryptData?.size?.height
        }
        break
      case 'VIDEO':
        data.content = {
          messageType: 'video',
          type: body.message_type,
          link: decryptData.link,
          duration: decryptData.duration,
          cover: decryptData.cover,
          videoSize: decryptData.video_size,
          width: decryptData?.size?.width,
          height: decryptData?.size?.height
        }
        break
      case 'CARD':
        if (decryptData.content_type === 'lead_card') {
          data.content = {
            messageType: 'retain_consult_card',
            type: body.message_type,
            title: decryptData.title,
            image: decryptData.image,
            cardStatus: 1
          }
        } else if (decryptData.content_type === 'tradeBusinessCard') {
          data.content = {
            messageType: 'trade_business_card',
            type: body.message_type,
            title: decryptData.title,
            subTitle: decryptData.sub_title,
            image: decryptData.image,
            linkPlatform: decryptData.link_platform,
            cardStatus: 1
          }
        } else if (decryptData.content_type === 'social_card') {
          data.content = {
            messageType: 'social_card',
            type: body.message_type,
            name: decryptData.name,
            image: decryptData.image,
            cardStatus: 1
          }
        } else if (decryptData.content_type === 'common') {
          data.content = {
            messageType: 'common',
            type: body.message_type,
            title: decryptData.title,
            cover: decryptData.cover,
            link: decryptData.link,
            desc: decryptData.desc,
            cardStatus: 1
          }
        } else {
          data.content = {
            messageType: 'other',
            type: body.message_type,
            text: '你收到一条新消息，请打开小红书app查看'
          }
        }

        break
    }

    if (data.openId !== data.fromUserId) {
      sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
        openId: data.openId,
        messageId: data.messageId,
        event: data.event,
        content: '收到一条小红书私信消息'
      })
    }

    let isWelcome = false

    if (body.message_source === 2 && body.message_type === 'HINT') {
      // 进入会话后发送私信
      const regex = /对方通过([\s\S]*?)进入私信/
      const match = decryptData.text.match(regex)

      if (match) {
        // 匹配通过作品进线，则发送私信消息
        data.content = {
          messageType: 'hint',
          type: body.message_type,
          text: decryptData.text
        }

        isWelcome = true
      } else {
        return
      }
    }

    await this.personalChatMessageModel.create(data)

    const targetTypes = ['TEXT', 'SMILES', 'IMAGE', 'VIDEO']

    if (
      data.event === WebhookEvents.IMReceiveMessage &&
      targetTypes.some((type) => body.message_type.includes(type))
    ) {
      // 接收私信并且是文字私信
      this.autoresponderKeywordChat({
        fromUserId,
        toUserId,
        fromAvatar: data.fromAvatar,
        fromName: data.fromName,
        toAvatar: data.toAvatar,
        toName: data.toName,
        text: decryptData.text,
        messageType: body.message_type
      }).catch((err) => {
        throw new BadRequestException(`autoresponderKeywordChat${err.message}`)
      })
    }

    if (isWelcome) {
      await this.autoresponderWelcome(
        data.toUserId,
        data.fromUserId,
        platformAccount.name,
        platformAccount.avatar
      )
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    Object.keys(userIdMaps).forEach((socketId) => {
      dataList.push({
        socketId,
        data: { ...data, platformAccountId: parseInt(userIdMaps[socketId], 10) }
      })
    })

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`发送失败 error${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }

  async callbackEventCommentTask(body: WebhookXiaohongshuCommentBody) {
    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId: body.note_author_user_id
      }
    })

    if (!platformAccount) {
      throw new BadRequestException(`账号不存在`)
    }

    const data = {
      platformType: 'xiaohongshu',
      uniqueId: body.uniq_id.toString(),
      event: WebhookEvents.CommentReply,
      openId: body.note_author_user_id,
      fromUserId: body.comment_user_id,
      toUserId: body.note_author_user_id,
      fromAvatar: '',
      fromName: body.comment_user_name,
      toAvatar: platformAccount.avatar,
      toName: platformAccount.name,
      sessionId: body.note_id,
      content: {
        content: body.comment_content,
        name: body.comment_user_name,
        avatar: '',
        title: body.note_title,
        thumbUrl: body.cover,
        state: body.reply_state,
        objectId: body.note_id,
        commentId: body.comment_id?.toString() ?? ''
      },
      createTime: body.comment_time,
      isAuto: 0,
      messageId: body.uniq_id.toString()
    }

    const userIdMaps = await this.cacheManager.store.client.hgetall(
      genSocketRedisKey(body.note_author_user_id)
    )
    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    try {
      let commentData

      if (body.reply_state === 1) {
        // 更新评论
        commentData = await this.workCommentModel.updateOne(
          { openId: body.note_author_user_id, uniqueId: body.uniq_id.toString() },
          {
            content: {
              content: body.comment_content,
              name: body.comment_user_name,
              avatar: '',
              title: body.note_title,
              thumbUrl: body.cover,
              state: body.reply_state,
              objectId: body.note_id,
              commentId: body.comment_id?.toString() ?? ''
            }
          }
        )
      } else {
        commentData = await this.workCommentModel.create(data)
      }

      if (userIdMaps) {
        Object.keys(userIdMaps).forEach((socketId) => {
          dataList.push({
            socketId,
            data: {
              ...data,
              id: commentData._id,
              platformAccountId: parseInt(userIdMaps[socketId], 10)
            }
          })
        })
      }

      if (dataList.length) {
        try {
          this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
            next: () => {},
            error: (err) => {
              throw new BadRequestException(`发送失败 error${err.message}`)
            },
            complete: () => {}
          })
        } catch (error) {
          throw new BadRequestException(`socketService error${error.message}`)
        }
      }

      // 回复评论
      if (body.reply_state === 0) {
        this.autoresponderKeywordComment({
          from_user_id: body.comment_user_id,
          to_user_id: body.note_author_user_id,
          message_id: body.uniq_id,
          content: body.comment_content,
          timestamp: body.comment_time,
          from_user_name: body.comment_user_name,
          from_user_avatar: '',
          to_user_name: platformAccount.name,
          to_user_avatar: platformAccount.avatar,
          comment_id: body.comment_id?.toString() ?? '',
          title: body.note_title,
          cover: body.cover
        })
      }
    } catch (error) {
      this.logger.error('微信视频号回复评论写入数据库失败 error', error)
      if (error.code !== 11000) {
        this.logger.error('微信视频号回复评论写入数据库失败 error', error)
      }
      this.logger.log(JSON.stringify(data))
      throw new BadRequestException(
        `wechatCommentModel error${error.message} ${JSON.stringify(data)}`
      )
    }
  }

  async autoresponderWelcome(
    toUserId: string,
    fromUserId: string,
    fromName: string,
    fromAvatar: string
  ) {
    const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

    let isSuc = false

    if (res) {
      const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]

      await wait(1000)

      if (!arr.length) {
        return
      }

      const accountRes = await this.cacheManager.store.client.hget(
        PlatformAccountKeywordKey,
        toUserId
      )

      const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

      for (let i = 0; i < arr.length; i++) {
        const { contents, contentType, state, autoresponderId, executionCount } = arr[i]

        if (
          contentType !== AutoresponderContentType.Greeting ||
          status === AccountAccountsStatus.Disable
        ) {
          continue
        }

        if (!state || accountExpired < Date.now()) {
          continue
        }

        if (!Array.isArray(contents)) {
          continue
        }

        if (!contents.length) {
          continue
        }

        if (executionCount) {
          // 查询是否有对这个账号有发送过消息
          const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
            fromUserId,
            toUserId,
            autoresponderId
          })

          if (messageByAutoresponder) {
            return
          }

          await this.messagesByAutoresponderModel.create({
            fromUserId,
            toUserId,
            autoresponderId,
            platformType: 'xiaohongshu'
          })
        }

        for (let i = 0; i < contents.length; i++) {
          const content = contents[i] as AutoresponderKeywordContent

          await this.autoresponderSend(content, {
            token,
            openId: toUserId,
            toUserId: fromUserId,
            platformAccountId,
            teamId,
            autoresponderId,
            fromName,
            fromAvatar
          })

          await wait(100)
          isSuc = true
        }
      }
    }

    return isSuc
  }

  async autoresponderKeywordComment(body: xiaohongshuCommentAutoResponderBody) {
    try {
      const res = await this.cacheManager.store.client.hget(
        AutoresponderKeywordKey,
        body.to_user_id
      )

      if (res) {
        await wait(15000)

        const arr = JSON.parse(res || '[]') as AutoresponderKeywordRedisValue[]

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          body.to_user_id
        )

        const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

        const items = []

        for (let i = 0; i < arr.length; i++) {
          const {
            contents,
            contentType,
            keyword,
            rule,
            state,
            stopReply,
            scene,
            autoresponderId,
            trigger
          } = arr[i] as AutoresponderKeywordRedisValue

          if (status === AccountAccountsStatus.Disable) {
            continue
          }

          if (scene) {
            continue
          }

          if (trigger !== AutoresponderTriggerType.Comment) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired * 1000 < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${body.from_user_id}-${body.to_user_id}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          if (
            rule !== AutoresponderKeywordRule.Match &&
            rule !== AutoresponderKeywordRule.Instantly
          ) {
            // 收到私信回复只有两种规则（关键字和即刻回复）
            continue
          }

          let send = rule !== AutoresponderKeywordRule.Match

          if (!send) {
            send = !!(keyword as string[]).find((item) => {
              return RegExp(item, 'g').test(body.content)
            })
          }

          const chatMessage = trigger === AutoresponderTriggerType.Comment

          if (send && !job && chatMessage) {
            items.push(arr[i])
          }
        }

        const matchItem =
          items.find((item) => item.rule === AutoresponderKeywordRule.Match) ||
          items[generateRandom(items.length - 1)]

        this.logger.debug('autoresponderKeywordChat matchItem', JSON.stringify(matchItem))

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(
              `stop-chat:${body.from_user_id}-${body.to_user_id}-${autoresponderId}`
            ))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${body.from_user_id}-${body.to_user_id}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId: body.from_user_id,
              toUserId: body.to_user_id,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId: body.from_user_id,
              toUserId: body.to_user_id,
              autoresponderId,
              platformType: 'xiaohongshu'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponderXiaohongshu',
            {
              token,
              fromUserId: body.to_user_id,
              toUserId: body.from_user_id,
              platformAccountId,
              teamId,
              autoresponderId,
              contents,
              fromName: body.to_user_name,
              fromAvatar: body.to_user_avatar,
              toName: body.from_user_name,
              toAvatar: body.from_user_avatar,
              scene: 'comment',
              commentId: body.comment_id || '',
              commentContent: body.content,
              noteTitle: body.title,
              cover: body.cover
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${body.from_user_id}-${body.to_user_id}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`AutoresponderKeywordKey error${error.message}`)
    }
  }

  async autoresponderKeywordChat({
    fromUserId,
    toUserId,
    text,
    messageType,
    fromName,
    fromAvatar,
    toName,
    toAvatar
  }: {
    fromUserId: string
    toUserId: string
    text: string
    messageType: string
    fromName?: string
    fromAvatar?: string
    toName?: string
    toAvatar?: string
  }) {
    if (fromUserId === toUserId) {
      return
    }
    try {
      const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

      if (res) {
        await wait(1000)
        const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]
        const items = []

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          toUserId
        )

        const { token, platformAccountId, teamId, status } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const {
            contents,
            contentType,
            keyword,
            rule,
            state,
            stopReply,
            scene,
            autoresponderId,
            trigger
          } = arr[i] as AutoresponderKeywordRedisValue

          if (scene || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (!state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          if (messageType !== 'TEXT' && rule === AutoresponderKeywordRule.Match) {
            // 只有文本类型才能触发关键字回复
            continue
          }

          if (
            rule !== AutoresponderKeywordRule.Match &&
            rule !== AutoresponderKeywordRule.Instantly
          ) {
            // 收到私信回复只有两种规则（关键字和即刻回复）
            continue
          }

          let send = rule !== AutoresponderKeywordRule.Match

          if (!send) {
            send = !!(keyword as string[]).find((item) => {
              const cleanedText = text.replace(/\[.*?\]/g, '')
              return RegExp(item, 'g').test(cleanedText)
            })
          }

          const chatMessage = trigger === AutoresponderTriggerType.Chat

          if (send && !job && chatMessage) {
            items.push(arr[i])
          }
        }

        const matchItem =
          items.find((item) => item.rule === AutoresponderKeywordRule.Match) ||
          items[generateRandom(items.length - 1)]

        this.logger.debug(
          'autoresponderKeywordChat matchItem',
          JSON.stringify(matchItem),
          JSON.stringify(items)
        )

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId,
              toUserId,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'xiaohongshu'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponderXiaohongshu',
            {
              token,
              fromUserId: toUserId,
              toUserId: fromUserId,
              platformAccountId,
              teamId,
              autoresponderId,
              contents,
              fromName: toName,
              fromAvatar: toAvatar,
              toName: fromName,
              toAvatar: fromAvatar
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`AutoresponderKeywordKey error${error.message}`)
    }
  }

  async autoresponderTask(
    contents: AnyObject[],
    {
      token,
      fromUserId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId,
      scene,
      fromName,
      fromAvatar,
      toName,
      toAvatar,
      commentId,
      commentContent,
      noteTitle,
      cover
    }: {
      token: string
      fromUserId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
      scene?: string
      fromName?: string
      fromAvatar?: string
      toName?: string
      toAvatar?: string
      commentId?: string
      commentContent?: string
      noteTitle?: string
      cover?: string
    }
  ) {
    const [content, ...otherContents] = contents as AutoresponderKeywordContent[]

    if (!content) {
      return
    }

    try {
      await this.autoresponderSend(content, {
        token,
        openId: fromUserId,
        toUserId,
        platformAccountId,
        teamId,
        autoresponderId,
        fromName,
        fromAvatar,
        toName,
        scene,
        toAvatar,
        commentId,
        commentContent,
        noteTitle,
        cover
      })

      if (otherContents.length) {
        const [start] = otherContents

        await this.taskQueue.add(
          'autoresponderXiaohongshu',
          {
            token,
            fromUserId,
            toUserId,
            platformAccountId,
            teamId,
            fromName,
            fromAvatar,
            toName,
            toAvatar,
            autoresponderId,
            contents: otherContents,
            scene,
            commentId,
            commentContent,
            noteTitle,
            cover
          },
          {
            delay: start.delay * 1000 || 100,
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `${AutoresponderKeywordKey}-${toUserId}-${fromUserId}-${autoresponderId}`
          }
        )
      }
    } catch (error) {
      throw new BadRequestException(`autoresponderTask error${error.message}`)
    }
  }

  async autoresponderSend(
    content: AutoresponderKeywordContent,
    {
      token,
      openId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId,
      scene,
      fromName,
      fromAvatar,
      toName,
      toAvatar,
      commentId,
      commentContent,
      noteTitle,
      cover
    }: {
      token: string
      openId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
      scene?: string
      fromName?: string
      fromAvatar?: string
      toName?: string
      toAvatar?: string
      commentId?: string
      commentContent?: string
      noteTitle?: string
      cover?: string
    }
  ) {
    let contentText = ''
    let messageType = ContentType.Text
    let cardTitle = ''
    let cardImage = ''
    let cardSubTitle = ''
    let socialType
    let linkPlatform
    let cardLink = ''
    let cardDesc = ''

    if (content.messageType === AutoresponderContentChildType.Text) {
      for (let j = 0; j < content.texts.length; j++) {
        const text = content.texts[j]
        if (text.type === AutoresponderContentTextType.Text) {
          contentText += text.text
        } else if (text.type === AutoresponderContentTextType.Variable) {
          const value = await this.cacheManager.store.client.hget(
            AutoresponderVariableKey,
            `${text.variableId}`
          )
          try {
            const values = JSON.parse(value).value
            contentText += values[generateRandom(values.length - 1)]
          } catch (err) {
            throw new BadRequestException(`autoresponderSend text error${err.message}`)
          }
        }
      }
    } else if (content.messageType === AutoresponderContentChildType.Image) {
      messageType = ContentType.Image
      contentText = content.imageUrl
    } else if (content.messageType === AutoresponderContentChildType.Card) {
      messageType = ContentType.Card
      contentText = content.cardInfo.find((item) => item.accountId === platformAccountId).cardId
      cardTitle = content.cardInfo.find((item) => item.accountId === platformAccountId).title
      cardImage = content.cardInfo.find((item) => item.accountId === platformAccountId).image
    } else if (content.messageType === AutoresponderContentChildType.BusinessCard) {
      messageType = ContentType.BusinessCard
      contentText = content.cardInfo.find((item) => item.accountId === platformAccountId).cardId
      cardTitle = content.cardInfo.find((item) => item.accountId === platformAccountId).title
      cardImage = content.cardInfo.find((item) => item.accountId === platformAccountId).image
      // eslint-disable-next-line prefer-destructuring
      socialType = content.cardInfo.find((item) => item.accountId === platformAccountId).socialType
    } else if (content.messageType === AutoresponderContentChildType.TradeBusinessCard) {
      messageType = ContentType.TradeBusinessCard
      contentText = content.cardInfo.find((item) => item.accountId === platformAccountId).cardId
      cardTitle = content.cardInfo.find((item) => item.accountId === platformAccountId).title
      cardSubTitle = content.cardInfo.find((item) => item.accountId === platformAccountId).subTitle
      cardImage = content.cardInfo.find((item) => item.accountId === platformAccountId).image
      // eslint-disable-next-line prefer-destructuring
      socialType = content.cardInfo.find((item) => item.accountId === platformAccountId).socialType
      // eslint-disable-next-line prefer-destructuring
      linkPlatform = content.cardInfo.find(
        (item) => item.accountId === platformAccountId
      ).linkPlatform
    } else if (content.messageType === AutoresponderContentChildType.Common) {
      messageType = ContentType.Common
      contentText = content.cardInfo.find((item) => item.accountId === platformAccountId).cardId
      cardTitle = content.cardInfo.find((item) => item.accountId === platformAccountId).title
      cardImage = content.cardInfo.find((item) => item.accountId === platformAccountId).image
      cardLink = content.cardInfo.find((item) => item.accountId === platformAccountId).link
      cardDesc = content.cardInfo.find((item) => item.accountId === platformAccountId).desc
    }

    if (scene === 'comment') {
      messageType = ContentType.Comment
    }

    this.tlsManageService.putLogs({
      logData: JSON.stringify({
        messageType,
        content: contentText,
        secret: this.secretKey,
        openId,
        accessToken: token,
        fromUserId: openId,
        toUserId,
        platformAccountId,
        teamId,
        auto: true,
        width: content.width ?? 0,
        height: content.height ?? 0,
        contentLenght: content.contentLenght ?? 0,
        autoresponderId,
        redisClient: this.cacheManager,
        scene,
        fromName,
        fromAvatar,
        toName,
        toAvatar,
        commentId,
        title: cardTitle,
        subTitle: cardSubTitle,
        image: cardImage,
        socialType,
        linkPlatform,
        link: cardLink,
        desc: cardDesc,
        commentContent,
        noteTitle,
        cover
      }),
      logLevel: 'info',
      requestUri: 'autoresponderSend',
      jobStatus: 'xiaohongshuservice'
    })

    const data = await postSendMessage({
      messageType,
      content: contentText,
      secret: this.secretKey,
      openId,
      accessToken: token,
      fromUserId: openId,
      toUserId,
      platformAccountId,
      teamId,
      auto: true,
      width: content.width ?? 0,
      height: content.height ?? 0,
      contentLenght: content.contentLenght ?? 0,
      autoresponderId,
      redisClient: this.cacheManager,
      scene,
      fromName,
      fromAvatar,
      toName,
      toAvatar,
      commentId,
      title: cardTitle,
      subTitle: cardSubTitle,
      image: cardImage,
      socialType,
      linkPlatform,
      link: cardLink,
      desc: cardDesc,
      commentContent,
      noteTitle,
      cover
    })

    this.tlsManageService.putLogs({
      logData: JSON.stringify(data),
      logLevel: 'info',
      requestUri: 'autoresponderSendResult',
      jobStatus: 'xiaohongshuservice'
    })

    await this.cacheManager.set(data.messageId, 1, 1000 * 60 * 10)
  }

  async bindAccountWebhook({ content, type }: { content: string; type: BindEventXiaohongshu }) {
    const decryptData = decrypt(content, this.secretKey)

    this.logger.log(decryptData)

    const data: {
      user_id: string
      nick_name: string
      app_id: string
      token: string
      auth_status: number
      kos_user_id: string
      kos_nick_name: string
      kos_avatar_img: string
    } = JSON.parse(decryptData)

    await this.bindQueue.add(
      'bindEventXiaohongshu',
      {
        body: content,
        type
      },
      {
        removeOnComplete: true,
        removeOnFail: true,
        jobId: `${data.user_id}-${data.kos_user_id}`
      }
    )

    return {
      code: 0,
      msg: '成功',
      success: true
    }
  }

  /**
   * 绑定账号
   */
  private async bindAccount(content: string) {
    const accountInfo = decrypt(content, this.secretKey)

    const account: {
      user_id: string
      nick_name: string
      app_id: string
      token: string
    } = JSON.parse(accountInfo)

    const updateAccount = await this.prisma.platformAccount.update({
      where: {
        openId: account.user_id
      },
      data: {
        isBind: true
      }
    })

    const juGuang = await this.prisma.juGuang.findUnique({
      where: {
        userId: account.user_id
      }
    })

    if (!juGuang) {
      await this.prisma.juGuang.create({
        data: {
          userId: account.user_id,
          nickName: account.nick_name,
          appId: account.app_id,
          token: account.token
        }
      })
    }

    const userIdMaps = await this.cacheManager.store.client.hgetall(
      genSocketRedisKey(account.user_id)
    )

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    Object.keys(userIdMaps).forEach((socketId) => {
      dataList.push({
        socketId,
        data: {
          type: 'changePlatformAccount',
          data: [
            {
              action: 'add',
              platform: updateAccount.platform,
              name: updateAccount.name,
              avatar: updateAccount.avatar,
              openId: updateAccount.openId,
              accountRole: updateAccount.accountRole,
              id: updateAccount.id,
              teamId: updateAccount.teamId,
              expiresTime: **********
            }
          ]
        }
      })
    })

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`发送失败 error${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }

    bindAccountEventEmitter.emit(bindAccountEventKey, {
      openId: updateAccount.openId,
      token: updateAccount.accessToken
    })
  }

  /**
   * 解除绑定
   */
  private async unbindAccount(content: string) {
    const decryptData = decrypt(content, this.secretKey)

    const account: {
      user_id: string
      nick_name: string
      app_id: number
      token: string
    } = JSON.parse(decryptData)

    await this.prisma.platformAccount.update({
      where: {
        openId: account.user_id
      },
      data: {
        isBind: false
      }
    })

    // 子账号解除绑定聚光
    await this.prisma.platformAccount.updateMany({
      where: {
        parentOpenId: account.user_id
      },
      data: {
        isBind: false
      }
    })

    await this.prisma.juGuang.delete({
      where: {
        userId: account.user_id
      }
    })
  }

  private async bindUser(content: string) {
    const decryptData = decrypt(content, this.secretKey)

    this.logger.log(decryptData, 'bindUser')

    try {
      const userInfo: {
        user_id: string
        auth_status: number
        kos_nick_name: string
        kos_user_id: string
        kos_avatar_img: string
      } = JSON.parse(decryptData)

      if (userInfo.auth_status === 4) {
        // 解除授权
        await this.prisma.platformAccount.update({
          where: {
            openId: userInfo.kos_user_id
          },
          data: {
            isBind: false
          }
        })
      } else {
        const parentAccount = await this.prisma.platformAccount.findUnique({
          where: {
            openId: userInfo.user_id
          }
        })

        this.logger.log(parentAccount, 'parentAccount')

        if (parentAccount) {
          const account = await this.prisma.platformAccount.findUnique({
            where: {
              openId: userInfo.kos_user_id
            },
            include: {
              affiliates: true
            }
          })

          if (account) {
            // 根据状态
            if (parentAccount.teamId === account.teamId) {
              // 如果主账号跟子账号是属于一个团队
              await this.prisma.platformAccount.update({
                where: {
                  id: account.id
                },
                data: {
                  isBind: true
                }
              })
            } else {
              // 如果不是在同一个团队，则更改团队
              const [team, platformAccountCount] = await Promise.all([
                this.prisma.team.findUnique({
                  where: {
                    id: parentAccount.teamId
                  },
                  include: {
                    vip: true
                  }
                }),
                this.prisma.platformAccount.count({ where: { teamId: parentAccount.teamId } })
              ])

              const status =
                team.vip.platformAccountNumberLimit > platformAccountCount
                  ? AccountAccountsStatus.Normal
                  : AccountAccountsStatus.Disable

              const platformAccount = await this.prisma.platformAccount.update({
                where: {
                  id: account.id
                },
                data: {
                  teamId: parentAccount.teamId,
                  accessToken: parentAccount.accessToken,
                  expiresIn: parentAccount.expiresIn,
                  refreshToken: parentAccount.refreshToken,
                  refreshExpiresIn: parentAccount.refreshExpiresIn,
                  tokenTime: parentAccount.tokenTime,
                  refreshTime: parentAccount.refreshTime,
                  status,
                  isBind: true
                },
                include: {
                  affiliates: true
                }
              })

              await this.prisma.platformAccount.update({
                where: {
                  id: account.id
                },
                data: {
                  affiliates: {
                    disconnect: platformAccount.affiliates.map((item) => ({ id: item.id }))
                  }
                }
              })

              await this.platformAccountManageService.deletePlatformAccountRedisInfo(
                platformAccount
              )

              const userIds = platformAccount.affiliates.map((item) => item.userId)
              const teamMemberByOldPlatformAccount = await this.prisma.teamMember.findMany({
                where: {
                  teamId: platformAccount.teamId,
                  role: {
                    not: TeamMemberRole.Member
                  }
                }
              })

              const manageUserIds = teamMemberByOldPlatformAccount.map((item) => item.userId)

              const combinedAndUniqueIds = [...new Set([...userIds, ...manageUserIds])]

              const users = await this.prisma.user.findMany({
                where: {
                  id: {
                    in: combinedAndUniqueIds
                  }
                }
              })
              if (users.length > 0) {
                for (let i = 0; i < users.length; i++) {
                  const userInfo = users[i]
                  const auth = await this.cacheManager.get<string>(userInfo.phone)
                  if (auth) {
                    await this.cacheManager.store.client.hdel(
                      genSocketRedisKey(auth),
                      platformAccount.openId
                    )
                  }
                }
              }

              // 删除旧账号的socketids
              await this.cacheManager.store.client.del(genSocketRedisKey(account.openId))

              // 该账号之前团队对应的自动回复移除该账号
              const autoresponderList = await this.prisma.autoresponder.findMany({
                where: {
                  platformAccountIds: {
                    array_contains: account.id
                  }
                }
              })

              autoresponderList.forEach(async (item) => {
                if (item.platformAccountIds) {
                  const accountIds = (item.platformAccountIds as number[]).filter(
                    (id) => id !== account.id
                  )
                  await this.prisma.autoresponder.update({
                    where: { id: item.id },
                    data: { platformAccountIds: accountIds }
                  })
                }
              })

              const teamMembers = await this.prisma.teamMember.findMany({
                where: {
                  teamId: platformAccount.teamId,
                  role: {
                    not: TeamMemberRole.Member
                  }
                },
                include: {
                  user: true
                }
              })

              const socketIds = []

              for (let i = 0; i < teamMembers.length; i++) {
                const item = teamMembers[i]
                const [socketId, appSocketId] = await Promise.all([
                  this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
                  this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
                ])

                this.logger.debug(`teamMembers-item-${item.name}`, {
                  socketId,
                  reidsKey: genSocketRedisKey(item.userId),
                  openId: platformAccount.id
                })

                await Promise.allSettled([
                  (async () => {
                    if (appSocketId) {
                      socketIds.push(appSocketId)
                      try {
                        const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

                        if (authApp) {
                          await this.cacheManager.store.client.hset(
                            genSocketRedisKey(authApp),
                            platformAccount.openId,
                            platformAccount.id
                          )
                        }
                      } catch (error) {
                        this.logger.error('更新缓存错误', error)
                      }

                      await this.cacheManager.set(
                        genSocketRedisKey(item.userId + 'app'),
                        appSocketId,
                        0
                      )
                    }
                  })(),
                  (async () => {
                    if (socketId) {
                      socketIds.push(socketId)
                      try {
                        const auth = await this.cacheManager.get<string>(item.user.phone)

                        if (auth) {
                          await this.cacheManager.store.client.hset(
                            genSocketRedisKey(auth),
                            platformAccount.openId,
                            platformAccount.id
                          )
                        }
                      } catch (error) {
                        this.logger.error('更新缓存错误', error)
                      }

                      await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
                    }
                  })()
                ])
              }

              if (socketIds.length) {
                try {
                  this.webhookGrpcService.socketService
                    .send({
                      list: JSON.stringify(
                        socketIds.map((socketId) => ({
                          socketId,
                          data: {
                            type: 'changePlatformAccount',
                            data: [
                              {
                                action: 'add',
                                platform: platformAccount.platform,
                                name: platformAccount.name,
                                avatar: platformAccount.avatar,
                                openId: platformAccount.openId,
                                accountRole: platformAccount.accountRole,
                                id: platformAccount.id,
                                teamId: platformAccount.teamId,
                                expiresTime: platformAccount.expiresIn
                                  ? platformAccount.expiresIn * 1000 +
                                    platformAccount.tokenTime.getTime()
                                  : 0
                              }
                            ]
                          }
                        }))
                      )
                    })
                    .subscribe({
                      next: () => {},
                      error: (err) => {
                        throw new BadRequestException(`发送失败 error${err.message}`)
                      },
                      complete: () => {}
                    })
                } catch (error) {
                  throw new BadRequestException(`socketService error${error.message}`)
                }
              }
            }
          } else {
            // 新增账号
            const [team, platformAccountCount] = await Promise.all([
              this.prisma.team.findUnique({
                where: {
                  id: parentAccount.teamId
                },
                include: {
                  vip: true
                }
              }),
              this.prisma.platformAccount.count({ where: { teamId: parentAccount.teamId } })
            ])

            const teamMember = await this.prisma.teamMember.findUnique({
              where: {
                userId_teamId: {
                  teamId: team.id,
                  userId: team.ownerId
                }
              }
            })

            const status =
              team.vip.platformAccountNumberLimit > platformAccountCount
                ? AccountAccountsStatus.Normal
                : AccountAccountsStatus.Disable

            const value = {
              name: userInfo.kos_nick_name,
              avatar: userInfo.kos_avatar_img,
              openId: userInfo.kos_user_id,
              accessToken: parentAccount.accessToken,
              refreshToken: parentAccount.refreshToken,
              expiresIn: parentAccount.expiresIn,
              refreshExpiresIn: parentAccount.refreshExpiresIn,
              teamId: parentAccount.teamId,
              status,
              isBind: true,
              parentOpenId: userInfo.user_id,
              platform: Platform.Xiaohongshu,
              unauthorize: '',
              affiliates: {
                connect: {
                  id: teamMember.id
                }
              }
            }

            const platformAccount = await this.prisma.platformAccount.create({
              data: value
            })

            const teamMembers = await this.prisma.teamMember.findMany({
              where: {
                teamId: platformAccount.teamId,
                role: {
                  not: TeamMemberRole.Member
                }
              },
              include: {
                user: true
              }
            })

            const socketIds = []

            for (let i = 0; i < teamMembers.length; i++) {
              const item = teamMembers[i]
              const [socketId, appSocketId] = await Promise.all([
                this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
                this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
              ])

              this.logger.debug(`teamMembers-item-${item.name}`, {
                socketId,
                reidsKey: genSocketRedisKey(item.userId),
                openId: platformAccount.id
              })

              await Promise.allSettled([
                (async () => {
                  if (appSocketId) {
                    socketIds.push(appSocketId)
                    try {
                      const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

                      if (authApp) {
                        await this.cacheManager.store.client.hset(
                          genSocketRedisKey(authApp),
                          platformAccount.openId,
                          platformAccount.id
                        )
                      }
                    } catch (error) {
                      this.logger.error('更新缓存错误', error)
                    }

                    await this.cacheManager.set(
                      genSocketRedisKey(item.userId + 'app'),
                      appSocketId,
                      0
                    )
                  }
                })(),
                (async () => {
                  if (socketId) {
                    socketIds.push(socketId)
                    try {
                      const auth = await this.cacheManager.get<string>(item.user.phone)

                      if (auth) {
                        await this.cacheManager.store.client.hset(
                          genSocketRedisKey(auth),
                          platformAccount.openId,
                          platformAccount.id
                        )
                      }
                    } catch (error) {
                      this.logger.error('更新缓存错误', error)
                    }

                    await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
                  }
                })()
              ])
            }

            if (socketIds.length) {
              try {
                this.webhookGrpcService.socketService
                  .send({
                    list: JSON.stringify(
                      socketIds.map((socketId) => ({
                        socketId,
                        data: {
                          type: 'changePlatformAccount',
                          data: [
                            {
                              action: 'add',
                              platform: platformAccount.platform,
                              name: platformAccount.name,
                              avatar: platformAccount.avatar,
                              openId: platformAccount.openId,
                              accountRole: platformAccount.accountRole,
                              id: platformAccount.id,
                              teamId: platformAccount.teamId,
                              expiresTime: platformAccount.expiresIn
                                ? platformAccount.expiresIn * 1000 +
                                  platformAccount.tokenTime.getTime()
                                : 0
                            }
                          ]
                        }
                      }))
                    )
                  })
                  .subscribe({
                    next: () => {},
                    error: (err) => {
                      throw new BadRequestException(`发送失败 error${err.message}`)
                    },
                    complete: () => {}
                  })
              } catch (error) {
                throw new BadRequestException(`socketService error${error.message}`)
              }
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('子账号授权错误', error)
    }
  }

  async cardMessageWebhook(content: WebhookXiaohongshuCardMessageBody) {
    if (content.push_type === 3 || content.push_type === 4) {
      await this.cardMessageQueue.add(
        'cardMessageEvent',
        {
          content
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${content.user_id}-${content.brand_user_id}-${content.kos_user_id}`
        }
      )
    }

    return {
      code: 0,
      msg: '成功',
      success: true
    }
  }

  private async cardMessage({ content }: { content: WebhookXiaohongshuCardMessageBody }) {
    let phone
    if (content.phone_num) {
      phone = decrypt(content.phone_num, this.secretKey)
    }

    let wechat

    if (content.wechat) {
      wechat = decrypt(content.wechat, this.secretKey)
    }

    const openId = content.kos_user_id ? content.kos_user_id : content.brand_user_id

    const account = await this.prisma.platformAccount.findUnique({
      where: {
        openId
      }
    })

    const createTime = new Date()

    await this.prisma.cardMessage.create({
      data: {
        cardId: openId,
        teamId: account.teamId,
        fromUserId: openId,
        toUserId: account.openId,
        toAvatar: account.avatar,
        toName: account.name,
        name: wechat,
        phone,
        cardData: JSON.stringify(content),
        createTime
      }
    })

    if (content.wechat_copy || content.link_id || content.msg_app_open) {
      return
    }

    const uniqueId = this.nanoid()

    const data = {
      platformType: 'xiaohongshu',
      uniqueId,
      event: WebhookEvents.IMReceiveMessage,
      openId,
      fromUserId: content.user_id,
      toUserId: openId,
      fromAvatar: '',
      fromName: '',
      toAvatar: account.avatar,
      toName: account.name,
      sessionId: content.user_id,
      content: {
        messageType: 'retain_consult_card',
        cardStatus: 2,
        phone,
        wechat
      },
      createTime: createTime.getTime(),
      isAuto: 0,
      messageId: uniqueId
    }

    await this.personalChatMessageModel.create(data)

    if (data.openId !== data.fromUserId) {
      sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
        openId: data.openId,
        messageId: data.messageId,
        event: data.event,
        content: '收到一条小红书私信消息'
      })
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    const userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(openId))

    Object.keys(userIdMaps).forEach((socketId) => {
      dataList.push({
        socketId,
        data: { ...data, platformAccountId: parseInt(userIdMaps[socketId], 10) }
      })
    })

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`发送失败 error${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }
}
