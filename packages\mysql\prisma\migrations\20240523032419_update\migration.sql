-- CreateTable
CREATE TABLE `PlatformAccountConfig` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `top` BOOLEAN NULL DEFAULT false,
    `mute` BOOLEAN NULL DEFAULT false,
    `platformAccountId` INTEGER NOT NULL,
    `userId` INTEGER NOT NULL,

    INDEX `PlatformAccountConfig_userId_idx`(`userId`),
    UNIQUE INDEX `PlatformAccountConfig_platformAccountId_userId_key`(`platformAccountId`, `userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `PlatformAccountConfig` ADD CONSTRAINT `PlatformAccountConfig_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
