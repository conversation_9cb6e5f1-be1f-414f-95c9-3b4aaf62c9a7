import { Injectable, Logger } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { Queue, Worker } from 'bullmq'
import { wechatLogout } from '../vip/external.wechat'
import dayjs from 'dayjs'

@Injectable()
export class VipTransferService {
  dataQueue: Queue

  dataWorker: Worker

  serverNumber: number

  LOCK_TIMEOUT = 10 * 60 * 1000

  logger = new Logger('VipTransferService')

  constructor(private readonly prisma: PrismaService) {}

  async onModuleInit() {
    // this.onDataTransferByVip()
  }

  async onDataTransferByVip() {
    const now = dayjs().tz('Asia/Shanghai')

    const startDay = now.subtract(2, 'day').format('YYYY-MM-DD')
    const yesterday = now.subtract(1, 'day').format('YYYY-MM-DD')

    const startOfYesterday = new Date(startDay + 'T16:00:00') // 获取昨天的开始时间
    const endOfYesterday = new Date(yesterday + 'T16:00:00') // 获取昨天的结束时间

    const teamList = await this.prisma.team.findMany({
      where: {
        vip: {
          expirationTime: {
            gte: startOfYesterday,
            lte: endOfYesterday
          }
        },
        platformAccounts: {
          some: {
            expiresIn: {
              gte: 100
            }
          }
        }
      },
      include: {
        vip: true,
        platformAccounts: true
      },
      orderBy: {
        createTime: 'desc'
      }
    })
    const limitData = await this.prisma.systemDosage.findFirst()
    for (let i = 0; i < teamList.length; i++) {
      const team = teamList[i]
      this.logger.log('teamId', team.id)
      await this.prisma.vip.update({
        where: {
          teamId: team.id
        },
        data: {
          messageLimit: limitData?.standardMessageLimit ?? 0,
          teamMemberNumberLimit: limitData?.standardTeamMemberNumberLimit ?? 0,
          platformAccountNumberLimit: limitData?.standardPlatformAccountNumberLimit ?? 0
        }
      })
      if (team.platformAccounts.length > 0) {
        await this.onTransferVipData(team.id)
      }
    }
  }

  async onTransferVipData(teamId: number) {
    this.logger.log('teamId', teamId)

    const platformAccount = await this.prisma.platformAccount.findMany({
      where: {
        teamId
      }
    })

    for (let j = 0; j < platformAccount.length; j++) {
      const account = platformAccount[j]
      this.logger.log('accountId', account.id)
      await this.prisma.platformAccount.update({
        where: {
          id: account.id
        },
        data: {
          expiresIn: 1,
          refreshExpiresIn: 1,
          status: 1
        }
      })

      if (account.platform === 1) {
        try {
          await wechatLogout({
            appId: account.appId
          })
        } catch (error) {
          this.logger.error('wechatLogout error', error)
        }
      }
    }
  }
}
