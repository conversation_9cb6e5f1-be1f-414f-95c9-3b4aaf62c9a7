import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested
} from 'class-validator'
import { Type } from 'class-transformer'
import { Autoresponder } from '@qdy/mysql'
import { AnyObject } from 'mongoose'
import { TextLengthSum } from '../../common/validator/TextLengthSum'
import { ContainsUrl } from '../../common/validator/ContainsUrl'

export enum AutoresponderKeywordRule {
  Match = 'match',
  Instantly = 'instantly',
  Comment = 'comment',
  Follow = 'follow',
  Like = 'like'
}

export enum AutoresponderType {
  CommentAutoresponder = 1,
  ChatAutoresponder = 2,
  Greeting = 3
}

export enum AutoresponderContentType {
  Autoresponder,
  Greeting
}

export enum AutoresponderContentChildType {
  Text,
  Image,
  Card,
  BusinessCard,
  TradeBusinessCard,
  Common
}

export enum AutoresponderPlatformAccountType {
  All,
  Custom
}

export enum AutoresponderSceneType {
  Single,
  Comment
}

export enum AutoresponderTriggerType {
  welcome,
  Chat,
  Comment,
  Follow,
  Like
}

export enum AutoresponderContentTextType {
  Text = 'text',
  Variable = 'variable'
}

export class AutoresponderOpuser {
  @ApiProperty({
    description: 'id',
    example: 4,
    required: true
  })
  @IsNotEmpty({ message: 'platformAccountId不能为空' })
  @IsNumber({}, { message: 'platformAccountId必须是数字' })
  platformAccountId: number

  @ApiProperty({
    description: 'id',
    example: 'xx',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  id: string

  @ApiProperty({
    description: 'title',
    example: ' 你好',
    required: false
  })
  @IsString()
  @IsOptional()
  title: string

  @ApiProperty({
    description: '头像',
    example: ' xxx.png',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  avatar: string

  @ApiProperty({
    description: '创建时间',
    example: '2021-01-01T00:00:00.000Z',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  createTime: number

  @ApiProperty({
    description: '评论数',
    example: 123,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  commentCount: number
}

export class AutoresponderRequestUpdateStateDTO {
  @ApiProperty({
    description: '开关',
    example: false,
    required: true
  })
  @IsBoolean()
  state: boolean
}

export class AutoresponderKeywordContentText {
  @ApiProperty({
    description: '回复内容',
    example: '你好呀',
    required: false
  })
  @IsOptional()
  @IsString()
  text: string

  @ApiProperty({
    enum: AutoresponderContentTextType,
    description: '回复内容类型 文字 text,  变量 variable',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(AutoresponderContentTextType)
  type: AutoresponderContentTextType

  @ApiProperty({
    description: '变量ID',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  variableId: number
}

export class CardInfo {
  @ApiProperty({
    description: '留资卡片id',
    example: '@9VwNxuKKBZ03MXG7M8ooWM771FjUAMW/BqhMlDebEmyyzJD7cZENrR868oDbX9xx',
    required: true
  })
  @IsString()
  cardId: string

  @ApiProperty({
    description: '留资卡片名称',
    example: '留资卡片名称',
    required: true
  })
  @IsString()
  title: string

  @ApiProperty({
    description: '交易卡片副标题',
    example: '交易卡片副标题',
    required: false
  })
  @IsString()
  subTitle: string

  @ApiProperty({
    description: '留资卡片图片',
    example: '留资卡片图片',
    required: false
  })
  @IsString()
  image: string

  @ApiProperty({
    description: '跳转链接类型',
    example: '跳转链接类型',
    required: false
  })
  @IsString()
  linkPlatform: string

  @ApiProperty({
    description: '名片类型',
    example: '留资卡片图片',
    required: false
  })
  @IsNumber()
  socialType: number

  @ApiProperty({
    description: '账号id',
    example: 1,
    required: true
  })
  @IsNumber()
  accountId: number

  @ApiProperty({
    description: '落地页链接',
    example: '落地页链接',
    required: false
  })
  @IsString()
  link: string

  @ApiProperty({
    description: '落地页描述',
    example: '落地页描述',
    required: false
  })
  @IsString()
  desc: string
}

export class AutoresponderKeywordContent {
  @ApiProperty({
    description: '延时 单位：秒',
    example: 0,
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  delay: number

  @ApiProperty({
    type: [AutoresponderKeywordContentText],
    description: '回复内容',
    example: [
      {
        type: AutoresponderContentTextType.Text,
        text: '你好'
      },
      {
        type: AutoresponderContentTextType.Variable,
        variableId: 1
      }
    ],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({
    each: true
  })
  @Type(() => AutoresponderKeywordContentText)
  @TextLengthSum()
  texts: AutoresponderKeywordContentText[]

  @ApiProperty({
    enum: AutoresponderContentChildType,
    description: '回复内容类型 0: 文字, 1: 图片,2: 留资卡片, 3:商家名片, 4:交易名片, 5: 落地页',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(AutoresponderContentChildType)
  messageType: AutoresponderContentChildType

  @ApiProperty({
    description: '图片ID',
    example: 'xx',
    required: false
  })
  @IsOptional()
  @IsString()
  imageId: string

  @ApiProperty({
    description: '图片Url',
    example: 'xx',
    required: false
  })
  @IsOptional()
  @IsString()
  imageUrl: string

  @ApiProperty({
    description: '图片宽度',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  width: number

  @ApiProperty({
    description: '图片高度',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  height: number

  @ApiProperty({
    description: '图片大小',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  contentLenght: number

  @ApiProperty({
    type: [CardInfo],
    example: [
      {
        cardId: '',
        accountId: 1
      }
    ]
  })
  @IsOptional()
  @IsArray()
  cardInfo?: CardInfo[]
}

export class AutoresponderKeywordCreateDTO {
  @ApiProperty({
    description: '策略名称',
    example: '666',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({
    description: '是否停止回复',
    example: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  stopReply?: boolean

  @ApiProperty({
    description: '是否间隔内停止回复',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  stopInterval?: boolean

  @ApiProperty({
    description: '间隔时间 单位秒',
    example: '100',
    required: false
  })
  @IsOptional()
  @IsNumber()
  stopTime?: number

  @ApiProperty({
    description: '是否延时回复',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isDelay?: boolean

  @ApiProperty({
    description: '延时回复时间 单位秒',
    example: '100',
    required: false
  })
  @IsOptional()
  @IsNumber()
  delayTime?: number

  @ApiProperty({
    description: '同一用户回复次数',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  executionCount?: number

  @ApiProperty({
    type: [AutoresponderKeywordContent],
    description: '回复内容',
    example: [
      {
        messageType: AutoresponderContentChildType.Text,
        texts: [
          {
            type: AutoresponderContentTextType.Text,
            text: '你好'
          },
          {
            type: AutoresponderContentTextType.Variable,
            variableId: 1
          }
        ]
      },
      {
        messageType: AutoresponderContentChildType.Image,
        imageId: 'xx',
        imageUrl: 'xx',
        delay: 1000
      }
    ],
    required: false
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({
    each: true
  })
  @Type(() => AutoresponderKeywordContent)
  contents: AutoresponderKeywordContent[]

  @ApiProperty({
    enum: AutoresponderKeywordRule,
    description: `匹配规则 ${AutoresponderKeywordRule.Match} / ${AutoresponderKeywordRule.Instantly}`,
    required: true
  })
  @IsEnum(AutoresponderKeywordRule)
  rule: AutoresponderKeywordRule

  @ApiProperty({
    enum: AutoresponderContentType,
    description: '回复内容类型 <0：自动回复, 1：欢迎语>',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(AutoresponderContentType)
  contentType: AutoresponderContentType

  @ApiProperty({
    description: '场景 <0:私信 1：评论>',
    enum: AutoresponderSceneType,
    required: true
  })
  @IsNotEmpty()
  @IsEnum(AutoresponderSceneType)
  scene: AutoresponderSceneType

  @ApiProperty({
    description: '触发条件 <0:欢迎语 1：私信，2：评论，3：关注，4：点赞>',
    enum: AutoresponderTriggerType,
    required: true
  })
  @IsNotEmpty()
  @IsEnum(AutoresponderTriggerType)
  trigger: AutoresponderTriggerType

  @ApiProperty({
    enum: AutoresponderPlatformAccountType,
    description: '平台账号类型 <0：全部, 1：指定>',
    required: true
  })
  @IsEnum(AutoresponderPlatformAccountType)
  platformAccountType: AutoresponderPlatformAccountType

  @ApiProperty({
    type: [Number],
    description: '平台账号Id列表<空数组则表示全部>',
    example: [],
    required: true
  })
  @IsArray()
  platformAccountIds: number[]

  @ApiProperty({
    type: Number,
    description: '平台Id<抖音:0, 视频号:1等>',
    example: 0,
    required: true
  })
  @IsNumber()
  platform: number

  @ApiProperty({
    description: '是否启用',
    example: true,
    type: Boolean
  })
  @IsBoolean()
  state: boolean

  @ApiProperty({
    description: '关键词',
    type: [String],
    example: ['你好', '世界'],
    required: true
  })
  @IsArray()
  keywords: string[]

  @ApiProperty({
    description: '作品列表',
    type: [AutoresponderOpuser],
    required: true
  })
  @IsArray()
  @ValidateNested({
    each: true
  })
  @Type(() => AutoresponderOpuser)
  opusers: AutoresponderOpuser[]
}

export class AutoresponderKeywordRedisValue {
  platform: Autoresponder['platform']

  scene: Autoresponder['scene']

  trigger: Autoresponder['trigger']

  keyword: Autoresponder['keywords']

  stopReply: Autoresponder['stopReply']

  stopInterval: Autoresponder['stopInterval']

  isDelay: Autoresponder['isDelay']

  delayTime: Autoresponder['delayTime']

  stopTime: Autoresponder['stopTime']

  rule: Autoresponder['rule']

  contents: AnyObject[]

  contentType: Autoresponder['contentType']

  state: Autoresponder['state']

  isNew: Autoresponder['isNew']

  autoresponderId: number

  executionCount: Autoresponder['executionCount']
  // imageId: Autoresponder['imageId']
}

export class AutoresponderVariable {
  @ApiProperty({
    description: '变量名称',
    type: String,
    required: true
  })
  @IsNotEmpty()
  @IsString()
  name: string

  @ApiProperty({
    description: '变量值',
    type: [String],
    required: true
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  @ContainsUrl({ each: true })
  value: string[]
}

export class AutoresponderResponesVariable {
  @ApiResponseProperty({
    type: Number
  })
  id: number

  @ApiResponseProperty({
    type: Number
  })
  teamId: number

  @ApiResponseProperty({
    type: String
  })
  name: string

  @ApiResponseProperty({
    type: [String]
  })
  value: string[]
}

export class AutoresponderResponesVariableDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AutoresponderResponesVariable]
  })
  data: AutoresponderResponesVariable[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class AutoresponderKeywordContentTextRs {
  @ApiResponseProperty({
    type: String
  })
  text: string

  @ApiResponseProperty({
    enum: AutoresponderContentTextType
  })
  type: AutoresponderContentTextType

  @ApiResponseProperty({
    type: Number
  })
  variableId: number

  @ApiResponseProperty({
    type: Object
  })
  variable: object
}

export class AutoresponderKeywordContentRs {
  @ApiResponseProperty({
    type: Number
  })
  delay: number

  @ApiResponseProperty({
    type: [AutoresponderKeywordContentTextRs]
  })
  texts: AutoresponderKeywordContentTextRs[]

  @ApiResponseProperty({
    enum: AutoresponderContentChildType
  })
  messageType: AutoresponderContentChildType

  @ApiResponseProperty({
    type: String
  })
  imageId: string

  @ApiResponseProperty({
    type: String
  })
  imageUrl: string
}

export class AutoresponderKeyword {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: Boolean
  })
  stopReply: boolean

  @ApiResponseProperty({
    type: Boolean
  })
  stopInterval: boolean

  @ApiResponseProperty({
    enum: AutoresponderType
  })
  type: AutoresponderType

  @ApiResponseProperty({
    type: Number
  })
  stopTime: number

  @ApiResponseProperty({
    type: [AutoresponderKeywordContentRs]
  })
  contents: AutoresponderKeywordContentRs[]

  @ApiResponseProperty({
    enum: AutoresponderContentType,
    example: '0: 自动回复, 1: 欢迎语言'
  })
  contentType: AutoresponderContentType

  @ApiResponseProperty({
    enum: AutoresponderSceneType
  })
  scene: AutoresponderSceneType

  @ApiResponseProperty({
    enum: AutoresponderKeywordRule,
    example: `${AutoresponderKeywordRule.Match}<关键词匹配> | ${AutoresponderKeywordRule.Instantly}<即刻回复>`
  })
  rule: AutoresponderKeywordRule

  @ApiResponseProperty({
    type: [String],
    example: ['你好', '世界']
  })
  keywords: string[]

  @ApiResponseProperty({
    type: [AutoresponderOpuser],
    example: [
      {
        id: 'xx',
        title: '你好',
        avatar: 'xxx.png',
        createTime: '2021-01-01T00:00:00.000Z',
        commentCount: 123
      }
    ]
  })
  opusers: AutoresponderOpuser[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  opusersLength: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  singleDegree: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  commentDegree: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  platformAccountsLength: number

  @ApiResponseProperty({
    enum: AutoresponderPlatformAccountType,
    example: '0: 全部, 1: 指定'
  })
  platformAccountType: AutoresponderPlatformAccountType

  @ApiResponseProperty({
    type: [Number],
    example: [1]
  })
  platformAccountIds: number[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  ownerTeamMemberId: number

  @ApiResponseProperty({
    type: Number,
    example: 0
  })
  degree: number

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  state: boolean

  @ApiResponseProperty({
    example: ['枚举<目前只需要传 0>'],
    type: Number
  })
  replyTime: number

  @ApiResponseProperty({
    example: ['枚举<目前只需要传 0>'],
    type: Number
  })
  effectTime: number
}

export class AutoresponderKeywordResponseDTO {
  @ApiResponseProperty({
    type: AutoresponderKeyword
  })
  data: AutoresponderKeyword
}

export class AutoresponderKeywordResponse {
  @ApiResponseProperty({
    type: [AutoresponderKeyword]
  })
  data: AutoresponderKeyword[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class AutoresponderKeywordsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AutoresponderKeywordResponse
  })
  data: AutoresponderKeywordResponse
}
