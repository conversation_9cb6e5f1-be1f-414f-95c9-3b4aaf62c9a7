{
    "version": "0.2.0",
    "configurations": [

      // 独立调试配置（每个服务单独调试）
      {
        "type": "node",
        "request": "launch",
        "name": "Launch admin (local)",
        "runtimeExecutable": "npm",
        "runtimeArgs": ["run", "start-admin:local"],
        "console": "integratedTerminal",
        "env": {
          "NODE_ENV": "dev"
        }
      },
      {
        "type": "node",
        "request": "launch",
        "name": "Launch gateway (local)",
        "runtimeExecutable": "npm",
        "runtimeArgs": ["run", "start-gateway:local"],
        "console": "integratedTerminal",
        "env": {
          "NODE_ENV": "dev"
        }
      },
      {
        "type": "node",
        "request": "launch",
        "name": "Launch transfer (local)",
        "runtimeExecutable": "npm",
        "runtimeArgs": ["run", "start-transfer:local"],
        "console": "integratedTerminal",
        "env": {
          "NODE_ENV": "dev"
        }
      }
    ],
    "compounds": [
      // 复合配置：一键启动所有服务
      {
        "name": "Launch ALL Services (local)",
        "configurations": [
          "Launch channel_admin (local)",
          "Launch admin (local)",
          "Launch socket (local)",
          "Launch gateway (local)",
          "Launch transfer (local)"
        ],
        "stopAll": true
      }
    ]
  }