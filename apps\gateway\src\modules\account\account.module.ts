import { Module } from '@nestjs/common'
import { AccountController } from './account.controller'
import { AccountService } from './account.service'
import { AccountSocketService } from './account.task'
import { CommentMongoose, MessagesMongoose, WechatOpusMongoose, WechatOpusEntity } from '@qdy/mongo'
import { AccountWechatService } from './account.service.wechat'
import { AccountEventService } from './account.event'
import { AccountKuaishouService } from './account.service.kuaishou'
import { AccountWeiboService } from './account.service.weibo'
import { AutoresponderManageModule, TosManageModule } from '@qdy/common'
import { AccountXiaohongshuService } from './account.service.xiaohongshu'

@Module({
  imports: [
    CommentMongoose,
    MessagesMongoose,
    WechatOpusMongoose,
    WechatOpusEntity,
    AutoresponderManageModule,
    TosManageModule
  ],
  providers: [
    AccountService,
    AccountSocketService,
    AccountWechatService,
    AccountEventService,
    AccountKuaishouService,
    AccountWeiboService,
    AccountXiaohongshuService
  ],
  controllers: [AccountController],
  exports: [AccountService, AccountWechatService, AccountKuaishouService]
})
export class AccountModule {}
