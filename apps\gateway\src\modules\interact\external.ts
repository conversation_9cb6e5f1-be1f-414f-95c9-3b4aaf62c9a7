import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'
import { InteractRequestSendType, InteractResponseMessageType } from './interact.dto'
import { type AnyObject } from 'mongoose'
import FormData from 'form-data'
import { sendEvent } from '../overview/event'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'

const createFanGroupsApi = 'https://open.douyin.com/im/group/fans/create/'

const fanGroupsApi = 'https://open.douyin.com/im/group/fans/list/'

const sendMessageApi = 'https://open.douyin.com/im/send/msg/'

const sendMessageGroupApi = 'https://open.douyin.com/im/send/msg/group/'

const replyCommentApi = 'https://open.douyin.com/item/comment/reply/'

const topCommentApi = 'https://open.douyin.com/item/comment/top/'

const videoList = 'https://open.douyin.com/api/douyin/v1/video/video_list'

const uploadImageFile = 'https://open.douyin.com/tool/imagex/client_upload/'

const clientToken = 'https://open.douyin.com/oauth/client_token/'

const videoDetail = 'https://open.douyin.com/api/douyin/v1/video/video_data'

const auditFansApi = 'https://open.douyin.com/im/group/enter/audit/set/'

const commentListApi = 'https://open.douyin.com/item/comment/list/'

const recallMessage = 'https://open.douyin.com/im/recall/msg/'

const logger = new Logger('external')

export const postCreateFanGroups = async (data: unknown) => {
  const res = (await axios.post(createFanGroupsApi, data)) as {
    group_id: string
    data: { error_code: number; description: string }
  } as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(res.data.data.description)
  }

  return res.data.group_id
}

export async function getFanGroups({
  accessToken,
  openId,
  teamId,
  platformAccountId
}: {
  accessToken: string
  openId: string
  teamId: number
  platformAccountId: number
}): Promise<
  {
    groupId: string
    name: string
    avatar: string
    description: string
    teamId: number
    platformAccountId: number
  }[]
> {
  const res = (await axios.get(fanGroupsApi, {
    params: {
      open_id: openId
    },
    headers: {
      'access-token': accessToken,
      'content-type': 'application/json'
    }
  })) as {
    group_list: {
      exist_num: number
      group_name: string
      max_num: number
      description: string
      entry_limit: ['万粉', '无要求'] | null
      group_id: string
      status: string
      tags: ['活跃群', '群主近期发言'] | null
      avatar_uri: string
    }[]
    data: {
      error_code: number
      description: string
    }
  } as { data: Record<string, any> }

  if (res.data.data.error_code) {
    throw new BadRequestException(res.data.data.description)
  }

  return (res.data.group_list || []).map(
    (item: {
      group_name: string
      avatar_uri: string
      description: string
      entry_limit: string[]
      status: string
      max_num: number
      group_id: string
      openId: string
    }) => {
      return {
        groupId: item.group_id,
        name: item.group_name,
        avatar: item.avatar_uri,
        description: item.description,
        teamId,
        platformAccountId,
        openId
      }
    }
  )
}

export const postSendMessage = async (data: {
  messageType: InteractResponseMessageType
  content: string
  openId: string
  accessToken: string
  messageId: string
  conversationId: string
  toUserId: string
  teamId: number
  platformAccountId: number
  sendType: InteractRequestSendType
  auto?: boolean
  autoresponderId?: number
  welcome?: boolean
  redisClient: Cache<RedisStore>
  scene?: string
}) => {
  const teamInfo = (await data.redisClient.get(`overview:${data.teamId}`)) as {
    residueCount: number
  }

  if (teamInfo) {
    if (teamInfo.residueCount <= 0) {
      throw new BadRequestException('剩余回复次数不足')
    }
  }

  if (data.autoresponderId) {
    const key = `postSendMessage:${data.openId}-${data.toUserId}`
    const toUserCount = parseInt((await data.redisClient.get(key)) || '0', 10)

    // 1小时内发送次数超过3次
    if (toUserCount && toUserCount >= 100) {
      return
    }
    // 1小时内发送次数
    if (toUserCount) {
      const ttl = await data.redisClient.store.ttl(key)

      await data.redisClient.set(key, toUserCount + 1, ttl)
    } else {
      await data.redisClient.set(key, 1, 1000 * 60 * 60)
    }
  }

  const content: AnyObject = {}

  switch (data.messageType) {
    case InteractResponseMessageType.Text:
      content.msg_type = 1
      content.text = {
        text: data.content
      }
      break
    case InteractResponseMessageType.Image:
      content.msg_type = 2
      content.image = {
        media_id: data.content
      }
      break
    case InteractResponseMessageType.Card:
      content.msg_type = 8
      content.retain_consult_card = {
        card_id: data.content
      }
      break
    default:
      break
  }

  let transformData = {
    msg_id: data.messageId,
    conversation_id: data.conversationId,
    to_user_id: data.toUserId,
    content,
    scene: data.scene || 'im_replay_msg',
    channel: data.autoresponderId ? 3 : 1 // 通过策略发送的为自动消息,否则人工发送
  }

  if (data.sendType === InteractRequestSendType.Group) {
    transformData = {
      // @ts-expect-error ts-migrate(2322) FIXME: Type 'Record<string, any>' is not assignable to typ... Remove this comment to see the full error message
      group_id: data.conversationId,
      content
    }
  }

  const res = (await axios.post(
    `${data.sendType === InteractRequestSendType.Group ? sendMessageGroupApi : sendMessageApi}?open_id=${data.openId}`,
    transformData,
    {
      headers: {
        'Content-Type': 'application/json',
        'access-token': data.accessToken
      }
    }
  )) as {
    data: Record<string, any>
  }

  if (res.data.data.error_code !== 0) {
    logger.debug({
      code: res.data.data.error_code,
      description: res.data.data.description
    })
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  let groupCount = 0
  let singleCount = 0
  let autoSingleCount = 0

  if (data.sendType === InteractRequestSendType.Group) {
    groupCount = 1
  } else if (data.autoresponderId) {
    autoSingleCount = 1
  } else {
    singleCount = 1
  }

  sendEvent({
    platformType: 'douyin',
    teamId: data.teamId,
    platformAccountId: data.platformAccountId,
    autoresponderId: data.autoresponderId,
    autoCommentCount: 0,
    commentCount: 0,
    autoSingleCount,
    singleCount,
    groupCount,
    openId: data.openId
  })

  return res.data
}

export async function postReplyComment(data: {
  platformAccountId: number
  openId: string
  itemId: string
  commentId: string
  content: string
  accessToken: string
  teamId: number
  auto?: boolean
  autoresponderId?: number
  redisClient: Cache<RedisStore>
  commentUserId: string
}) {
  const teamInfo = (await data.redisClient.get(`overview:${data.teamId}`)) as {
    residueCount: number
  }

  if (teamInfo) {
    if (teamInfo.residueCount <= 0) {
      throw new BadRequestException('剩余评论次数不足')
    }
  }

  const douyinComment = (await data.redisClient.get(`douyinComment:${data.teamId}`)) as {
    residueCount: number
  }

  if (douyinComment) {
    if (douyinComment.residueCount <= 0) {
      throw new BadRequestException('今日抖音评论回复额度已达上限')
    }
  }

  const deviceByTeam = (await data.redisClient.get(`device:${data.teamId}`)) as {
    device_brand: string
    device_platform: string
    os_version: string
    ip: string
    device_type: string
  }

  if (!deviceByTeam) {
    throw new BadRequestException('设备信息未绑定,刷新页面后重试')
  }

  if (data.autoresponderId) {
    const key = `postReplyComment:-${data.commentUserId}:${data.itemId}`
    const toUserCount = parseInt((await data.redisClient.get(key)) || '0', 10)

    // 1小时内发送次数超过3次
    if (toUserCount && toUserCount >= 100) {
      return
    }
    // 1小时内发送次数
    if (toUserCount) {
      const ttl = await data.redisClient.store.ttl(key)
      await data.redisClient.set(key, toUserCount + 1, ttl)
    } else {
      await data.redisClient.set(key, 1, 1000 * 60 * 60)
    }
  }

  const res = (await axios.post(
    replyCommentApi,
    {
      item_id: data.itemId,
      comment_id: data.commentId,
      content: data.content
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'access-token': data.accessToken
      },
      params: {
        open_id: data.openId,
        device_brand: deviceByTeam.device_brand,
        device_platform: deviceByTeam.device_platform,
        device_type: deviceByTeam.device_type,
        ip: deviceByTeam.ip,
        os_version: deviceByTeam.os_version,
        shark_channel: 'open_api'
      }
    }
  )) as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  sendEvent({
    platformType: 'douyin',
    teamId: data.teamId,
    platformAccountId: data.platformAccountId,
    autoresponderId: data.autoresponderId,
    autoSingleCount: 0,
    singleCount: 0,
    groupCount: 0,
    openId: data.openId,
    isDouyinComment: true,
    ...(data.autoresponderId
      ? { autoCommentCount: 1, commentCount: 0 }
      : { commentCount: 1, autoCommentCount: 0 })
  })

  return res.data.data
}

export async function postTopComment(data: {
  openId: string
  itemId: string
  commentId: string
  top: boolean
  accessToken: string
}) {
  const res = await axios.post(
    `${topCommentApi}?open_id=${data.openId}`,
    {
      item_id: data.itemId,
      comment_id: data.commentId,
      top: data.top
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'access-token': data.accessToken
      }
    }
  )

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.data
}

export async function getVideoList({
  openId,
  cursor,
  count,
  accessToken
}: {
  openId: string
  cursor: number
  count: number
  accessToken: string
}) {
  const res = await axios.get(`${videoList}?open_id=${openId}&cursor=${cursor}&count=${count}`, {
    headers: {
      'Content-Type': 'application/json',
      'access-token': accessToken
    }
  })

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.data
}

export async function postUploadImageFile(image: Buffer, filename: string, accessToken: string) {
  const formData = new FormData()
  formData.append('image', image, {
    filename
  })

  const res = await axios.post(uploadImageFile, formData, {
    headers: {
      'Content-Type': `multipart/form-data`,
      'access-token': accessToken
    }
  })

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return {
    imageId: res.data.image_id
  }
}

export async function postClientToken(data: { clientKey: string; clientSecret: string }) {
  const res = await axios.post(clientToken, {
    client_key: data.clientKey,
    client_secret: data.clientSecret,
    grant_type: 'client_credential'
  })

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.data
}

export async function postVideoDetail({
  openId,
  accessToken,
  itemIds,
  videoIds
}: {
  openId: string
  accessToken: string
  itemIds: string[]
  videoIds: string[]
}) {
  const res = await axios.post(
    videoDetail,
    {
      item_ids: itemIds,
      video_ids: videoIds
    },
    {
      params: {
        open_id: openId
      },
      headers: {
        'access-token': accessToken
      }
    }
  )

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.data.list
}

export async function postAuditFans(data: {
  openId: string
  accessToken: string
  applyId: string
  status: number
}) {
  const res = await axios.post(
    auditFansApi,
    {
      apply_id: data.applyId,
      status: data.status
    },
    {
      headers: {
        'access-token': data.accessToken
      },
      params: {
        open_id: data.openId
      }
    }
  )
  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.data
}

export async function getCommentList(data: {
  openId: string
  itemId: string
  accessToken: string
  cursor: number
  count: number
}) {
  const res = await axios.get(commentListApi, {
    params: {
      open_id: data.openId,
      item_id: data.itemId,
      cursor: data.cursor,
      count: data.count,
      sort_type: 'time'
    },
    headers: {
      'access-token': data.accessToken
    }
  })

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.data
}

/**
 * 撤回私信或群聊
 */
export async function postRecallMsg(data: {
  openId: string
  accessToken: string
  conversationId: string
  conversationType: number
  msgId: string
}) {
  const res = await axios.post(
    recallMessage,
    {
      conversation_id: data.conversationId,
      conversation_type: data.conversationType,
      msg_id: data.msgId
    },
    {
      headers: {
        'access-token': data.accessToken
      },
      params: {
        open_id: data.openId
      }
    }
  )
  if (res.data.err_no !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.err_msg}`)
  }

  return res.data
}
