import { Injectable } from '@nestjs/common'
import { redisStore } from 'cache-manager-ioredis-yet'

@Injectable()
export class RedisService {
  /**
   * Create redis options
   * @returns
   */
  createOptions() {
    const option = {
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT, 10),
      db: parseInt(process.env.REDIS_NORMAL_DB, 10),
      password: process.env.REDIS_PASSWORD,
      store: redisStore
    }

    if (!option.password) {
      delete option.password
    }

    return option
  }
}
