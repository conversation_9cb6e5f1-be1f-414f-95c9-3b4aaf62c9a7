import { HttpException, Inject, Injectable } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { DailyMessageStatisticEntity, DailyOverviewEntity, OverviewEntity } from '@qdy/mongo'
import { FastifyRequest } from 'fastify'
import { Model } from 'mongoose'
import { PrismaService } from '@qdy/mysql'
import dayjs from 'dayjs'

@Injectable()
export class OverviewService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(OverviewEntity.name) private overviewModel: Model<OverviewEntity>,
    @InjectModel(DailyMessageStatisticEntity.name)
    private dailyMessageStatisticModel: Model<DailyMessageStatisticEntity>,
    @InjectModel(DailyOverviewEntity.name) private dailyOverviewModel: Model<DailyOverviewEntity>,
    private readonly prisma: PrismaService
  ) {}

  async getOverview() {
    const { user } = this.request

    await this.overviewModel.find({
      createAt: dayjs().format('YYYY-MM-DD')
    })

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)
    startDate.setHours(0, 0, 0, 0)

    // const result = await this.dailyOverviewModel.find({
    //   teamId: teamMember.teamId,
    //   createTime: {
    //     $gte: dayjs(startDate).format('YYYY-MM-DD'),
    //     $lte: dayjs().format('YYYY-MM-DD')
    //   }
    // })

    const result = await this.dailyMessageStatisticModel.aggregate([
      {
        $match: {
          createTime: {
            $gte: dayjs(startDate).format('YYYY-MM-DD'),
            $lte: dayjs().format('YYYY-MM-DD')
          },
          teamId: teamMember.teamId
        }
      },
      {
        $group: {
          _id: {
            teamId: '$teamId',
            createTime: '$createTime',
            platformType: '$platformType'
          },
          singleCount: {
            $sum: { $ifNull: ['$singleCount', 0] }
          },
          autoSingleCount: {
            $sum: { $ifNull: ['$autoSingleCount', 0] }
          },
          commentCount: {
            $sum: { $ifNull: ['$commentCount', 0] }
          },
          autoCommentCount: {
            $sum: { $ifNull: ['$autoCommentCount', 0] }
          },
          groupCount: {
            $sum: { $ifNull: ['$groupCount', 0] }
          },
          singlePeopleCount: {
            $sum: { $ifNull: ['$singlePeopleCount', 0] }
          },
          commentPeopleCount: {
            $sum: { $ifNull: ['$commentPeopleCount', 0] }
          }
        }
      },
      {
        $project: {
          _id: 0,
          platformType: '$_id.platformType',
          createTime: '$_id.createTime',
          teamId: '$_id.teamId',
          singleCount: 1,
          autoSingleCount: 1,
          commentCount: 1,
          autoCommentCount: 1,
          groupCount: 1,
          singlePeopleCount: 1,
          commentPeopleCount: 1
        }
      }
    ])

    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        teamId: user.currentTeamId
      }
    })

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        teamId: user.currentTeamId
      },
      include: {
        user: true
      },
      orderBy: {
        replyMessage: 'desc'
      }
    })

    return {
      list: result,
      teamMemberList: teamMembers
        .map((item) => ({
          id: item.id,
          name: item.name || item.user.name,
          avatar: item.user.avatar,
          replyMessage: item.replyMessage
        }))
        .filter((item) => item.replyMessage),
      platformAccountList: platformAccounts
        .map((item) => {
          return {
            id: item.id,
            name: item.name,
            platform: item.platform,
            avatar: item.avatar,
            receiveMessage: item.receiveMessage,
            replyMessage: item.replyMessage,
            autoMessage: item.autoMessage,
            totalMessage: item.receiveMessage + item.replyMessage + item.autoMessage
          }
        })
        .sort((a, b) => b.totalMessage - a.totalMessage)
        .slice(0, 15)
    }
  }

  async getDosage() {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    const messageByTeam = await this.dailyOverviewModel.aggregate([
      {
        $match: {
          createTime: dayjs().subtract(8, 'hour').format('YYYY-MM-DD'),
          teamId: teamMember.teamId
        }
      },
      {
        $group: {
          _id: null,
          singleTotal: { $sum: '$singleCount' },
          commentTotal: { $sum: '$commentCount' },
          autoSingleTotal: { $sum: '$autoSingleCount' },
          autoCommentTotal: { $sum: '$autoCommentCount' }
        }
      }
    ])

    let singleTotal = 0
    let commentTotal = 0
    let autoSingleTotal = 0
    let autoCommentTotal = 0

    if (messageByTeam.length > 0) {
      ;({ singleTotal } = messageByTeam[0])
      ;({ commentTotal } = messageByTeam[0])
      ;({ autoSingleTotal } = messageByTeam[0])
      ;({ autoCommentTotal } = messageByTeam[0])
    }

    const [teamVIP, systemSetting] = await Promise.all([
      this.prisma.vip.findUnique({
        where: {
          teamId: teamMember.teamId
        }
      }),
      this.prisma.systemDosage.findFirst(),
      this.prisma.platformAccount.count({ where: { teamId: teamMember.teamId } })
    ])

    let messageTotalCount = 0

    let teamMemberTotalCount = 0
    let teamPlatformAccountTotalCount = 0

    if (teamVIP) {
      messageTotalCount = teamVIP.messageLimit
      teamMemberTotalCount = teamVIP.teamMemberNumberLimit
      teamPlatformAccountTotalCount = teamVIP.platformAccountNumberLimit
    } else {
      messageTotalCount = systemSetting.standardMessageLimit

      teamMemberTotalCount = systemSetting.standardTeamMemberNumberLimit
      teamPlatformAccountTotalCount = systemSetting.standardPlatformAccountNumberLimit
    }

    const [teamMemberCount, teamPlatformAccountCount, teamInfo] = await Promise.all([
      this.prisma.teamMember.count({
        where: {
          teamId: teamMember.teamId
        }
      }),
      this.prisma.platformAccount.count({
        where: {
          teamId: teamMember.teamId
        }
      }),
      this.prisma.team.findUnique({
        where: {
          id: teamMember.teamId
        },
        include: {
          vip: true
        }
      })
    ])

    const totalValue = {
      teamMemberTotalCount,
      teamPlatformAccountTotalCount,
      messageTotalCount,
      expirationTime: teamInfo.vip?.expirationTime,
      teamMemberCount,
      teamPlatformAccountCount
    }

    if (messageByTeam.length) {
      return {
        ...totalValue,
        messageCount: singleTotal + commentTotal + autoSingleTotal + autoCommentTotal
      }
    }

    return totalValue
  }
}
