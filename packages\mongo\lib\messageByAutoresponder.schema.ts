import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { SchemaTypes } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class MessagesByAutoresponderEntity {
  /**
   * @description 发送方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  fromUserId: string

  /**
   * @description 目标方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  toUserId: string

  @Prop({
    type: String,
    default: 'douyin'
  })
  platformType: string

  /**
   * @description 策略id
   */
  @Prop({
    type: Number,
    default: 0,
    required: true,
    index: true
  })
  autoresponderId: number

  /**
   * @description 时间
   */
  @Prop({
    type: Number,
    default: () => Date.now(),
    index: true
  })
  createTime: number
}

export const MessagesByAutoresponderSchema: ModelDefinition = <const>{
  name: MessagesByAutoresponderEntity.name,
  schema: SchemaFactory.createForClass(MessagesByAutoresponderEntity)
    .index({
      fromUserId: 1,
      toUserId: 1,
      autoresponderId: 1
    })
}

export const MessagesByAutoresponderMongoose = MongooseModule.forFeature([MessagesByAutoresponderSchema])
