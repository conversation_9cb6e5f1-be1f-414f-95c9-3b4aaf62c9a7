import { ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class Wechats {
  @ApiResponseProperty({
    type: String,
    example: '昵称'
  })
  nickName: string

  @ApiResponseProperty({
    type: String,
    example: '头像'
  })
  headImgUrl: string

  @ApiResponseProperty({
    type: String,
    example: '微信id'
  })
  wxid: string

  @ApiResponseProperty({
    type: String,
    example: '设备id'
  })
  appId: string

  @ApiResponseProperty({
    type: String,
    example: '地区code'
  })
  regionId: string

  @ApiResponseProperty({
    type: String,
    example: '创建时间'
  })
  createTime: string

  @ApiResponseProperty({
    type: String,
    example: '更新时间'
  })
  updateTime: string

  @ApiResponseProperty({
    type: String,
    example: '最后一次登录时间'
  })
  loginTime: string
}

export class WechatsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [Wechats]
  })
  data: Wechats[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class LoginRecord {
  @ApiResponseProperty({
    type: String,
    example: '昵称'
  })
  nickName: string

  @ApiResponseProperty({
    type: String,
    example: '头像'
  })
  headImgUrl: string

  @ApiResponseProperty({
    type: String,
    example: '微信id'
  })
  wxid: string

  @ApiResponseProperty({
    type: String,
    example: '设备id'
  })
  appId: string

  @ApiResponseProperty({
    type: String,
    example: '地区code'
  })
  regionId: string

  @ApiResponseProperty({
    type: String,
    example: '退出登录时间'
  })
  logoutTime: string

  @ApiResponseProperty({
    type: String,
    example: '登录时间'
  })
  loginTime: string

  @ApiResponseProperty({
    type: Number,
    example: '709(分钟)'
  })
  onlineDuration: number
}

export class WechatLoginRecordResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [LoginRecord]
  })
  data: LoginRecord[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class StatData {
  @ApiResponseProperty({
    type: String,
    example: '2023-10-01'
  })
  statDay: string

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  wxCount: number
}

export class WechatOnlineResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  onlineCount: number

  @ApiResponseProperty({
    type: [StatData]
  })
  data: StatData[]
}

export class OfflineStatData {
  @ApiResponseProperty({
    type: String,
    example: '2023-10-01'
  })
  offlineDate: string

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  offlineCount: number
}

export class WechatOfflineResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  onlineCount: number

  @ApiResponseProperty({
    type: [OfflineStatData]
  })
  data: OfflineStatData[]
}
