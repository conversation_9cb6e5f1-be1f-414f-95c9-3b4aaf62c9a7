import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { SchemaTypes } from 'mongoose'

export interface CommentContent {
  /**
   * @description 评论id
   */

  commentId: string

  /**
   * @description 评论用户id
   */
  commentUserId: string

  /**
   * @description 该评论回复的上一级评论的评论id
   */
  replyToCommentId: string

  /**
   * @description 该评论回复的视频id
   */
  replyToItemId: string

  /**
   * @description 头像url
   */
  avatar: string

  /**
   * @description 昵称
   */
  name: string

  /**
   * @description 回复评论总数
   */
  replyCommentTotal: number

  /**
   * @description 点赞总数
   */
  diggCount: number

  /**
   * @description 二级评论和三级评论的parentId是所属的一级评论id，一级评论parentId为视频ID
   */
  parentId: string

  /**
   * @description 提及用户id
   */
  atUserId: string

  /**
   * @description
   * 是否置顶
   */
  top: boolean
}

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class CommentEntity {
  @Prop({
    type: String,
    required: true,
    unique: true,
    index: true
  })
  uniqueId: string

  @Prop({
    type: String,
    required: true
  })
  event: string

  @Prop({
    type: String,
    required: false
  })
  clientKey: string

  @Prop({
    type: String,
    required: true,
    index: true
  })
  toUserId: string

  @Prop({
    type: String,
    required: true,
    index: true
  })
  fromUserId: string
  /**
   * @description 单个邀请码使用上限
   */
  @Prop({
    type: Number,
    default: () => Date.now(),
    index: true
  })
  createTime: number

  /**
   * @description 消息体
   */
  @Prop({
    type: SchemaTypes.Map,
    required: true
  })
  content: Map<string, string>

  /**
   * @description 会话id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  sessionId: string
}

export const CommentSchema: ModelDefinition = {
  name: CommentEntity.name,
  schema: SchemaFactory.createForClass(CommentEntity).index({
    toUserId: 1,
    createTime: -1
  })
}

export const CommentMongoose = MongooseModule.forFeature([CommentSchema])
