import { ModelDefinition, MongooseModule, Prop, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class WechatOpusEntity {
  @Prop({
    type: String,
    required: true,
    unique: true
  })
  objectId: string

  @Prop({
    type: String,
    required: true,
    index: true
  })
  wxid: string

  @Prop({
    type: String
  })
  nickname: string

  @Prop({
    type: String
  })
  headUrl: string

  @Prop({
    type: String,
    required: true
  })
  username: string

  @Prop({
    type: String
  })
  description: string

  @Prop({
    type: String,
    required: true
  })
  thumbUrl: string

  @Prop({
    type: String,
    required: true
  })
  objectNonceId: string

  @Prop({
    type: Number,
    required: false,
    index: true
  })
  createTime: number

  @Prop({
    type: Number,
  })
  forwardCount: number

  @Prop({
    type: Number,
  })
  likeCount: number

  @Prop({
    type: Number,
  })
  commentCount: number

  @Prop({
    type: String,
    required: true
  })
  sessionBuffer: string

  @Prop({ type: String })
  jsonData: string
}

export const WechatOpusSchema: ModelDefinition = {
  name: WechatOpusEntity.name,
  schema: SchemaFactory.createForClass(WechatOpusEntity)
}

export const WechatOpusMongoose = MongooseModule.forFeature([WechatOpusSchema])
