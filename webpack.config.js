/* eslint-disable prettier/prettier */
const CopyPlugin = require('copy-webpack-plugin')
const webpack = require('webpack')
const path = require('path')
const swcDefaultConfig =
  require('@nestjs/cli/lib/compiler/defaults/swc-defaults').swcDefaultsFactory().swcOptions

let GRPC_URL = '0.0.0.0'
let WEBHOOK_URL = 'webhook'
let WECHAT_WEBHOOK_URL = 'ttac99dde1ess-wechat-webhook'
let WECHAT_WEBHOOK_NEW_URL = 'wb3C7heeQzky-wechat-webhook'
let ALIPAY_WEBHOOK_URL = ''
let WECHATPAY_WEBHOOK_URL = ''
let KUAISHOU_WEBHOOK_URL = 'mv65990jne81iqqx-kuaishou-webhook'
let WEIBO_WEBHOOK_URL = 'wmkn8tr7cctohq4l-weibo-webhook'
let APPLE_ENV = 'Sandbox'
let XIAOHONGSHU_SECRET='afsr3NHO3xH65KU3ZRQahQ=='

if (process.env.NODE_ENV === 'dev') {
  GRPC_URL = '************'
  ALIPAY_WEBHOOK_URL = '3MCrp9Bt0zsdsNpiJd-alipay-webhook'
  WECHATPAY_WEBHOOK_URL = 'cDQJ8ckw5P5HP1zZ8h-wechatpay-webhook'
  WECHAT_WEBHOOK_NEW_URL = 'wb3C7heeQzky-wechat-webhook'
  KUAISHOU_WEBHOOK_URL = 'mv65990jne81iqqx-kuaishou-webhook'
  WEIBO_WEBHOOK_URL = 'wmkn8tr7cctohq4l-weibo-webhook'
  XIAOHONGSHU_SECRET='afsr3NHO3xH65KU3ZRQahQ=='
} else if (process.env.NODE_ENV === 'test') {
  GRPC_URL = '*************'
  WEBHOOK_URL = 'ttac99dde1ess-webhook'
  WECHAT_WEBHOOK_URL = 'ttac99dde1ess-wechat-webhook'
  WECHAT_WEBHOOK_NEW_URL = 'R09DhT83emtx-wechat-webhook'
  ALIPAY_WEBHOOK_URL = '3MCrp9Bt0zsdsNpiJd-alipay-webhook'
  WECHATPAY_WEBHOOK_URL = 'cDQJ8ckw5P5HP1zZ8h-wechatpay-webhook'
  KUAISHOU_WEBHOOK_URL = '24q60v5mdphni8fl-kuaishou-webhook'
  WEIBO_WEBHOOK_URL = '50i6ka3n0klter4l-weibo-webhook'
  XIAOHONGSHU_SECRET='afsr3NHO3xH65KU3ZRQahQ=='
} else if (process.env.NODE_ENV === 'prod') {
  GRPC_URL = '************'
  WEBHOOK_URL = 'ttac99dde1ess-webhook'
  WECHAT_WEBHOOK_URL = 'k9u7r3gspm05d-wechat-webhook'
  WECHAT_WEBHOOK_NEW_URL = 'GMpbijcMwTcY-wechat-webhook'
  ALIPAY_WEBHOOK_URL = '3MCrp9Bt0zsdsNpiJd-alipay-webhook'
  WECHATPAY_WEBHOOK_URL = 'cDQJ8ckw5P5HP1zZ8h-wechatpay-webhook'
  KUAISHOU_WEBHOOK_URL = 'nqzce4jt4vz6mxh1-kuaishou-webhook'
  WEIBO_WEBHOOK_URL = 'ziii3794pw9i6dsc-weibo-webhook'
  APPLE_ENV = 'Production'
  XIAOHONGSHU_SECRET='afsr3NHO3xH65KU3ZRQahQ=='
}

console.log('GRPC_URL:', GRPC_URL)
console.log('WEBHOOK_URL:', WEBHOOK_URL)

module.exports = {
  target: 'node',
  node: {
    __dirname: false
  },
  plugins: [
    new webpack.DefinePlugin({
      // 初始化需要的环境变量, 不能卸载.env文件中
      'process.env.GRPC_URL': JSON.stringify(GRPC_URL),
      'process.env.OSS_ACCESS_KEY_ID': JSON.stringify('LTAI5tD3QdxUQZbJAeHeX7Ma'),
      'process.env.OSS_ACCESS_KEY_SECRET': JSON.stringify('******************************'),
      'process.env.TOS_ACCESS_KEY_ID': JSON.stringify('AKLTYWU3ZTdlZjA4YjU1NGFjZjk1MDFlMzRjNDVkMGU4OGY'),
      'process.env.TOS_ACCESS_KEY_SECRET': JSON.stringify('TTJSbFlqUTVOR1U1WkRZME5HWXpPVGt3TlRRMFpUSXhORFk0WldFMk9XWQ=='),
      'process.env.WEBHOOK_URL': JSON.stringify(WEBHOOK_URL),
      'process.env.WECHAT_WEBHOOK_URL': JSON.stringify(WECHAT_WEBHOOK_URL),
      'process.env.WECHAT_WEBHOOK_NEW_URL': JSON.stringify(WECHAT_WEBHOOK_NEW_URL),
      'process.env.ALIPAY_WEBHOOK_URL': JSON.stringify(ALIPAY_WEBHOOK_URL),
      'process.env.WECHATPAY_WEBHOOK_URL': JSON.stringify(WECHATPAY_WEBHOOK_URL),
      'process.env.KUAISHOU_WEBHOOK_URL': JSON.stringify(KUAISHOU_WEBHOOK_URL),
      'process.env.WEIBO_WEBHOOK_URL': JSON.stringify(WEIBO_WEBHOOK_URL),
      'process.env.APPLE_ENV': JSON.stringify(APPLE_ENV),
      'process.env.XIAOHONGSHU_SECRET': JSON.stringify(XIAOHONGSHU_SECRET)
    }),
    new CopyPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, 'cert'),
          to: path.resolve(__dirname, 'dist/apps/cert')
        }
      ]
    })
  ],
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: {
          loader: 'swc-loader',
          options: swcDefaultConfig
        }
      },
      {
        test: /\.node$/,
        loader: 'node-loader'
      }
    ]
  }
}
