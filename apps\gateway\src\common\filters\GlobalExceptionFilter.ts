import { Catch, ArgumentsHost, HttpException, Inject } from '@nestjs/common'
import { FastifyReply } from 'fastify'
import { TlsManageService } from '@qdy/common'

@Catch(HttpException)
export class GlobalExceptionFilter {
  @Inject()
  private tlsService: TlsManageService

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp()

    const reply = ctx.getResponse<FastifyReply>()

    const status = exception.getStatus()

    const request = ctx.getRequest()

    try {
      const response = exception.getResponse()
      const message = typeof response === 'object' ? (response as any).message : response

      if (status !== 0) {
        const { headers } = request
        this.tlsService.putLogs({
          logData: response ? JSON.stringify(response) : '内部异常',
          requestUri: `${headers.host}${request.url}`,
          jobStatus: 'fail',
          logLevel: 'error'
        })
      }

      reply.code(status < 0 ? 403 : status).send({
        statusCode: status,
        message: (Array.isArray(message) ? message[0] : message) || '服务错误'
      })
    } catch {
      reply.code(status < 0 ? 403 : status).send({
        statusCode: status,
        message: exception.message || '服务错误'
      })
    }
  }
}
