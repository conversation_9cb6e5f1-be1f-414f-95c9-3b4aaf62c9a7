import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { SchemaTypes } from 'mongoose'

export interface JoinGroupMessage {
  messageType: string
  openId: string
  groupType: string
  createGroupType: string
  imGroupId: string,
  serverMessageId: string,
  conversationShortId: string,
  cardStatus: number,
  cardId: string
  cardData: string
}

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class MessagesEntity {
  @Prop({
    type: String,
    required: false,
    unique: true,
    index: true
  })
  uniqueId: string
  /**
   * @description 事件类型
   */
  @Prop({
    type: String,
    required: true
  })
  event: string

  /**
   * @description 客户端key
   */
  @Prop({
    type: String,
    required: false
  })
  clientKey: string

  /**
   * @description 发送方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  fromUserId: string

  /**
   * @description 目标方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  toUserId: string

  /**
   * @description 时间戳 评论是秒，其他是毫秒
   */
  @Prop({
    type: Number,
    default: () => Date.now(),
    index: true
  })
  createTime: number

  /**
   * @description 消息体
   */
  @Prop({
    type: SchemaTypes.Map,
    required: true
  })
  content: Map<string, string>

  /**
   * @description 发送方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  fromAvatar: string

  /**
   * @description 发送方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  fromName: string

  /**
   * @description 目标方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  toAvatar: string

  /**
   * @description 目标方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  toName: string

  /**
   * @description 会话id
   */
  @Prop({
    type: String,
    default: '',
    index: true
  })
  sessionId: string

  @Prop({
    type: String,
    default: 'douyin'
  })
  platformType: string

  /**
   * @description 是否撤回(默认未撤回)
   */
  @Prop({
    type: Number,
    default: 0
  })
  isRecall: number

  /**
   * @description 是否自动发送(默认手动发送)
   */
  @Prop({
    type: Number,
    default: 0
  })
  isAuto: number
}

export const MessagesSchema: ModelDefinition = <const>{
  name: MessagesEntity.name,
  schema: SchemaFactory.createForClass(MessagesEntity)
    .index({
      event: 1,
      fromUserId: 1,
      sessionId: 1
    })
    .index({
      event: 1,
      toUserId: 1,
      sessionId: 1
    })
}

export const MessagesMongoose = MongooseModule.forFeature([MessagesSchema])
