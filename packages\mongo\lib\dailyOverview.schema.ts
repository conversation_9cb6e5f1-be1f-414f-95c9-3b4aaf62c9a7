import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class DailyOverviewEntity {
  @Prop({
    type: Number,
    required: true,
    index: true
  })
  teamId: number

  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  // 自动回复私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoSingleCount: number

  // 自动回复群发数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoGroupCount: number

  // 自动回复评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoCommentCount: number

  // 手动回复私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  singleCount: number

  // 手动回复群发数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  groupCount: number

  // 手动回复评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  commentCount: number

  //评论人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  commentPeopleCount: number

  //私信人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  singlePeopleCount: number
}

export const DailyOverviewSchema: ModelDefinition = {
  name: DailyOverviewEntity.name,
  schema: SchemaFactory.createForClass(DailyOverviewEntity)
    .index({
      teamId: 1,
      createTime: -1
    })
    .index(
      {
        teamId: 1,
        createTime: 1
      },
      { unique: true }
    )
}

export const DailyOverviewMongoose = MongooseModule.forFeature([DailyOverviewSchema])
