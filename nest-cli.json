{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/gateway/src", "root": "apps/gateway", "compilerOptions": {"assets": ["**/*.proto"], "deleteOutDir": true, "webpack": true, "builder": "swc"}, "monorepo": true, "projects": {"@qdy/gateway": {"type": "application", "root": "apps/gateway", "entryFile": "main", "sourceRoot": "apps/gateway/src", "compilerOptions": {"tsConfigPath": "apps/gateway/tsconfig.json"}}, "@qdy/socket": {"type": "application", "root": "apps/socket", "entryFile": "main", "sourceRoot": "apps/socket/src", "compilerOptions": {"tsConfigPath": "apps/socket/tsconfig.json"}}, "@qdy/admin": {"type": "application", "root": "apps/admin", "entryFile": "main", "sourceRoot": "apps/admin/src", "compilerOptions": {"tsConfigPath": "apps/admin/tsconfig.json"}}, "@qdy/channel_admin": {"type": "application", "root": "apps/channel_admin", "entryFile": "main", "sourceRoot": "apps/channel_admin/src", "compilerOptions": {"tsConfigPath": "apps/channel_admin/tsconfig.json"}}, "@qdy/transfer": {"type": "application", "root": "apps/transfer", "entryFile": "main", "sourceRoot": "apps/transfer/src", "compilerOptions": {"tsConfigPath": "apps/transfer/tsconfig.json"}}}}