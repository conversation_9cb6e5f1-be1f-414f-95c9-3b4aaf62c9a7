import { BadRequestException, Logger } from '@nestjs/common'
import crypto from 'crypto'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { customAlphabet } from 'nanoid'
import axios from 'axios'
import { sendMessageEventEmitter, sendMessageEventKey } from './webhook.event'
import { sendEvent } from '../overview/event'

const sendMessageApi = 'https://adapi.xiaohongshu.com/api/open/im/third/send'
const materialListApi = 'https://adapi.xiaohongshu.com/api/open/im/material/list'
const kosUserListApi = 'https://adapi.xiaohongshu.com/api/open/im/auth/bind_users'
const pageListApi = 'https://adapi.xiaohongshu.com/api/open/im/page/list'

const logger = new Logger('webhook external xiaohongshu')

export function decrypt(cipherText: string, secretKey: string) {
  try {
    // 解码密钥
    const key = Buffer.from(secretKey, 'base64')

    // 分割 IV 和加密内容
    const arr = cipherText.split('~split~')
    if (!arr || arr.length !== 2) {
      throw new Error('Invalid cipher text format')
    }

    // 解码 IV
    const iv = Buffer.from(arr[0], 'base64')

    // 创建 AES-CBC 解密器
    const decipher = crypto.createDecipheriv('aes-128-cbc', key, iv)

    // 解密内容
    let decrypted = decipher.update(arr[1], 'base64', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  } catch (err) {
    logger.log(err.message)
    throw new BadRequestException(`小红书解密数据失败]:${err.message}`)
  }
}

export function encrypt(content, secretKey) {
  try {
    // 解码密钥
    const key = Buffer.from(secretKey, 'base64')

    // 生成 16 字节的 IV
    const iv = crypto.randomBytes(16)

    // 创建 AES-CBC 加密器
    const cipher = crypto.createCipheriv('aes-128-cbc', key, iv)

    // 加密内容
    let encrypted = cipher.update(content, 'utf8', 'base64')
    encrypted += cipher.final('base64')

    // 返回 IV 和加密内容，用 ~split~ 分隔
    return `${iv.toString('base64')}~split~${encrypted}`
  } catch (err) {
    throw new BadRequestException(`[小红书加密数据失败]:${err.message}`)
  }
}

export enum ContentType {
  Text = 'TEXT',
  Image = 'IMAGE',
  Card = 'CARD',
  BusinessCard = 'BUSINESSCARD',
  TradeBusinessCard = 'TRADEBUSINESSCARD',
  Revoke = 'REVOKE',
  Comment = 'Comment',
  Common = 'Common'
}

export async function postSendMessage(data: {
  messageType: ContentType
  content: string
  secret: string
  openId: string
  accessToken: string
  fromUserId: string
  toUserId: string
  teamId: number
  platformAccountId: number
  fromName?: string
  fromAvatar?: string
  toName?: string
  toAvatar?: string
  auto?: boolean
  autoresponderId?: number
  welcome?: boolean
  redisClient: Cache<RedisStore>
  scene?: string
  width?: number
  height?: number
  contentLenght?: number
  title?: string
  subTitle?: string
  linkPlatform?: string
  socialType?: number
  image?: string
  cover?: string
  commentContent?: string
  noteTitle?: string
  commentId?: string
  link?: string
  desc?: string
}) {
  const teamInfo = (await data.redisClient.get(`overview:${data.teamId}`)) as {
    residueCount: number
  }

  if (teamInfo) {
    if (teamInfo.residueCount <= 0) {
      throw new BadRequestException('剩余回复次数不足')
    }
  }

  if (data.autoresponderId) {
    const key = `postSendMessage:${data.openId}-${data.toUserId}`
    const toUserCount = parseInt((await data.redisClient.get(key)) || '0', 10)

    // 1小时内发送次数超过3次
    if (toUserCount && toUserCount >= 100) {
      return
    }
    // 1小时内发送次数
    if (toUserCount) {
      const ttl = await data.redisClient.store.ttl(key)

      await data.redisClient.set(key, toUserCount + 1, ttl)
    } else {
      await data.redisClient.set(key, 1, 1000 * 60 * 60 * 24)
    }
  }

  if (data.scene === 'im_enter_session_prologue_msg') {
    const key = `postSendWelcomeMessage:${data.openId}-${data.toUserId}`
    const toUserWelcomeCount = parseInt((await data.redisClient.get(key)) || '0', 10)

    // 1小时内发送次数超过3次
    if (toUserWelcomeCount && toUserWelcomeCount >= 3) {
      return
    }
    // 30s内发送次数
    if (toUserWelcomeCount) {
      const ttl = await data.redisClient.store.ttl(key)

      await data.redisClient.set(key, toUserWelcomeCount + 1, ttl)
    } else {
      await data.redisClient.set(key, 1, 1000 * 30)
    }
  }

  let content: string = ''
  let eventText = 'text'

  switch (data.messageType) {
    case ContentType.Text:
      eventText = 'text'
      content = encrypt(JSON.stringify({ text: data.content }), data.secret)
      break
    case ContentType.Image:
      eventText = 'image'
      content = encrypt(
        JSON.stringify({
          link: data.content,
          size: {
            width: data.width,
            height: data.height
          }
        }),
        data.secret
      )
      break
    case ContentType.Card:
      eventText = 'retain_consult_card'
      content = encrypt(
        JSON.stringify({
          id: data.content,
          content_type: 'lead_card'
        }),
        data.secret
      )
      break
    case ContentType.BusinessCard:
      eventText = 'consult_card'
      content = encrypt(
        JSON.stringify({
          id: data.content,
          content_type: 'social_card'
        }),
        data.secret
      )
      break
    case ContentType.TradeBusinessCard:
      eventText = 'trade_business_card'
      content = encrypt(
        JSON.stringify({
          id: data.content,
          content_type: 'tradeBusinessCard'
        }),
        data.secret
      )
      break
    case ContentType.Revoke:
      eventText = 'im_recall_msg'
      content = encrypt(
        JSON.stringify({
          message_id: data.content
        }),
        data.secret
      )
      break
    case ContentType.Comment:
      eventText = 'comment'
      content = encrypt(
        JSON.stringify({
          comment_id: data.commentId,
          content: data.content,
          content_type: 'purchaseComments'
        }),
        data.secret
      )
      break
    case ContentType.Common:
      eventText = 'common'
      content = encrypt(
        JSON.stringify({
          page_id: data.content,
          content_type: 'common'
        }),
        data.secret
      )
      break
    default:
      break
  }

  const nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 16)

  const transformData = {
    user_id: data.openId,
    message_type:
      data.messageType === ContentType.BusinessCard ||
      data.messageType === ContentType.TradeBusinessCard ||
      data.messageType === ContentType.Comment ||
      data.messageType === ContentType.Common
        ? ContentType.Card
        : data.messageType,
    from_user_id: data.fromUserId,
    to_user_id: data.toUserId,
    content,
    third_account_id: data.platformAccountId,
    timestamp: Date.now(),
    request_id: nanoid()
  }

  const res = (await axios.post(sendMessageApi, transformData, {
    headers: {
      'Access-Token': data.accessToken
    }
  })) as {
    data: {
      code: number
      msg: string
      success: string
      data: {
        request_id: string
        message_id: string
      }
    }
  }

  if (!res.data.success) {
    logger.debug({
      code: res.data.code,
      description: res.data.msg
    })

    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }

  sendMessageEventEmitter.emit(sendMessageEventKey, {
    platformType: 'xiaohongshu',
    uniqueId: res.data.data.message_id,
    openId: data.openId,
    fromUserId: data.openId,
    toUserId: data.toUserId,
    sessionId: data.toUserId,
    fromName: data.fromName,
    fromAvatar: data.fromAvatar,
    toName: data.toName,
    toAvatar: data.toAvatar,
    messageId: res.data.data.message_id,
    isAuto: data.auto ? 1 : 0,
    content: {
      messageType: eventText,
      text: data.content,
      width: data.width,
      height: data.height,
      contentLenght: data.contentLenght,
      subTitle: data.subTitle,
      title: data.title,
      image: data.image,
      cover: data.cover,
      commentContent: data.commentContent,
      noteTitle: data.noteTitle,
      socialType: data.socialType,
      linkPlatform: data.linkPlatform,
      link: data.link,
      desc: data.desc,
      cardStatus:
        eventText === 'retain_consult_card' ||
        eventText === 'consult_card' ||
        eventText === 'trade_business_card' ||
        eventText === 'common'
          ? 1
          : undefined
    }
  })

  sendEvent({
    platformType: 'xiaohongshu',
    teamId: data.teamId,
    platformAccountId: data.platformAccountId,
    autoresponderId: data.autoresponderId,
    autoCommentCount: 0,
    commentCount: 0,
    groupCount: 0,
    openId: data.openId,
    ...(data.autoresponderId
      ? { autoSingleCount: 1, singleCount: 0 }
      : { singleCount: 1, autoSingleCount: 0 })
  })

  return {
    requestId: res.data.data.request_id,
    messageId: res.data.data.message_id
  }
}

export async function revokeMessage(data: {
  secret: string
  openId: string
  accessToken: string
  fromUserId: string
  toUserId: string
  messageId: number
  platformAccountId: number
}) {
  const nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 16)

  const content = encrypt(JSON.stringify({ message_id: data.messageId }), data.secret)

  const transformData = {
    user_id: data.openId,
    message_type: 'REVOKE',
    from_user_id: data.fromUserId,
    to_user_id: data.toUserId,
    content,
    third_account_id: 'im_replay_msg',
    timestamp: Date.now(),
    request_id: nanoid()
  }

  const res = (await axios.post(sendMessageApi, transformData, {
    headers: {
      'Access-Token': data.accessToken
    }
  })) as {
    data: {
      code: number
      msg: string
      success: string
      data: {
        request_id: string
        message_id: string
      }
    }
  }

  if (!res.data.success) {
    logger.debug({
      code: res.data.code,
      description: res.data.msg
    })

    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }
}

/**
 * 名片/留资卡片（type:4(名片),type:5（留资））
 */
export async function postMaterialList(data: {
  openId: string
  accessToken: string
  page: number
  size: number
  type: number
}) {
  const postData = {
    user_id: data.openId,
    page_num: data.page,
    page_size: data.size,
    type: data.type
  }

  const res = (await axios.post(materialListApi, postData, {
    headers: {
      'Access-Token': data.accessToken
    }
  })) as {
    data: {
      code: number
      msg: string
      success: string
      data: {
        total: number
        list: {
          id: string
          name: string
          image: string
          ext: {
            social_card_ext: {
              card_type: number
            }
          }
        }[]
      }
    }
  }

  if (!res.data.success) {
    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }

  return res.data.data
}

/**
 * 落地页列表
 */
export async function postPageList(data: {
  openId: string
  accessToken: string
  page: number
  size: number
}) {
  const postData = {
    user_id: data.openId,
    page_num: data.page,
    page_size: data.size
  }

  const res = (await axios.post(pageListApi, postData, {
    headers: {
      'Access-Token': data.accessToken
    }
  })) as {
    data: {
      code: number
      msg: string
      success: string
      data: {
        total: number
        list: {
          page_id: string
          title: string
          cover: string
          page_desc: string
          page_url: string
          create_time: number
        }[]
      }
    }
  }

  if (!res.data.success) {
    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }

  return res.data.data
}

/**
 * kos账号列表
 * @param data
 * @returns
 */
export async function postKosUserList(data: {
  openId: string
  accessToken: string
  page: number
  size: number
}) {
  const postData = {
    user_id: data.openId,
    page_num: data.page,
    page_size: data.size
  }

  const res = (await axios.post(kosUserListApi, postData, {
    headers: {
      'Access-Token': data.accessToken
    }
  })) as {
    data: {
      code: number
      msg: string
      success: string
      data: {
        total: number
        kos_user_list: {
          user_id: string
          nick_name: string
          avatar_img: string
        }[]
      }
    }
  }

  if (!res.data.success) {
    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }

  return res.data.data
}
