import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { Queue, Worker } from 'bullmq'
import { AnyObject } from 'mongoose'
import { PrismaService, PlatformAccount, type Autoresponder } from '@qdy/mysql'
import { AutoresponderKeywordKey, AutoresponderKeywordRedisValue } from './overview.dto'
import { Platform } from 'packages/utils'

@Injectable()
export class autoresponderRedisTransferService implements OnModuleInit {
  private readonly logger = new Logger(autoresponderRedisTransferService.name)

  LOCK_TIMEOUT = 10 * 60 * 1000 // 10分钟的锁超时时间

  dataQueue: Queue

  dataWorker: Worker

  serverNumber: number

  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  async onModuleInit() {
    this.dataQueue = new Queue('data-migration-autoresponder-redis-transfer', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.dataWorker = new Worker(
      'data-migration-autoresponder-redis-transfer',
      async (job) => {
        const { autoresponder } = job.data
        await this.migrateUserTableData(autoresponder)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    const lock = ((await this.cacheManager.get('init-autoresponder-redis-transfer')) || 0) as number

    if (!lock) {
      this.serverNumber = 2
      this.cacheManager.set('init-autoresponder-redis-transfer', 1, 1000 * 60 * 60)
      // this.onAutoresponderRedisTransfer()
    }

    this.logger.log('autoresponderRedisTransferService init')
  }

  /**
   * 来执行迁移
   */
  async onAutoresponderRedisTransfer() {
    this.logger.log('开始执行迁移')
    const autoresponderList = await this.prisma.autoresponder.findMany({
      where: {
        isNew: true
      },
      orderBy: {
        id: 'asc'
      }
    })

    const activeArray = autoresponderList.slice(0, this.serverNumber)

    if (activeArray.length > 0) {
      for (let j = 0; j < activeArray.length; j++) {
        const autoresponder = activeArray[j]
        await this.dataQueue.add(
          'data-migration-autoresponder-redis-transfer',
          {
            autoresponder
          },
          {
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `data-migration-autoresponder-redis-transfer-sms-${autoresponder.id}`
          }
        )
      }
    }

    const otherContents = autoresponderList.slice(this.serverNumber)

    if (otherContents.length) {
      await this.cacheManager.store.client.hset(
        'autoresponderTransfer:redisTransfer',
        'autoresponderList',
        JSON.stringify(otherContents)
      )
    }
  }

  async migrateUserTableData(autoresponder: Autoresponder) {
    const isLock = await this.tryAcquireLock(`${autoresponder.id}`)

    this.logger.log(`${autoresponder.id}-${isLock}`)

    if (!isLock) {
      // 数据迁移逻辑
      await this.onTransferRedisData(autoresponder)
    }

    const autoresponderList = await this.cacheManager.store.client.hget(
      'autoresponderTransfer:redisTransfer',
      'autoresponderList'
    )

    if (autoresponderList) {
      const otherList = JSON.parse(autoresponderList)

      const activeArray = otherList.slice(0, this.serverNumber)

      if (activeArray.length > 0) {
        for (let j = 0; j < activeArray.length; j++) {
          const autoresponder = activeArray[j]
          await this.dataQueue.add(
            'data-migration-autoresponder-redis-transfer',
            {
              autoresponder
            },
            {
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `data-migration-autoresponder-redis-transfer-sms-${autoresponder.id}`
            }
          )
        }
      }

      const otherContents = otherList.slice(this.serverNumber)

      if (otherContents.length) {
        await this.cacheManager.store.client.hset(
          'autoresponderTransfer:redisTransfer',
          'autoresponderList',
          JSON.stringify(otherContents)
        )
      } else {
        await this.cacheManager.store.client.hdel(
          'autoresponderTransfer:redisTransfer',
          'autoresponderList'
        )
      }
    }
  }

  async onTransferRedisData(data: Autoresponder) {
    this.logger.log(data.id)
    const { platformAccountIds } = data
    const opusers = new Set<AnyObject>(data.opusers as AnyObject[])
    const platformAccountIdsByOpuser = Array.from(opusers).map((item) => item.platformAccountId)

    const itemPlatformAccountIds = new Set<number>(platformAccountIds as number[])

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const ids = [...new Set([...itemPlatformAccountIds, ...platformAccountIdsByOpuser])]

    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        id: {
          in: ids
        }
      }
    })

    const platformAccountMap: Record<string, PlatformAccount> = {}
    platformAccounts.forEach((platformAccount) => {
      platformAccountMap[platformAccount.id] = platformAccount
    })

    // eslint-disable-next-line no-restricted-syntax
    if (data.state && itemPlatformAccountIds && itemPlatformAccountIds.size > 0) {
      const platformAccountUpdates = Array.from(itemPlatformAccountIds).map(async (id) => {
        const platformAccount = platformAccountMap[id]
        if (!platformAccount) return

        const key = platformAccount.openId
        const existingValue = await this.cacheManager.store.client.hget(
          AutoresponderKeywordKey,
          key
        )

        const existingArr = (JSON.parse(existingValue) ?? []) as AutoresponderKeywordRedisValue[]
        const filteredArr = existingArr.filter((item) => item.isNew)

        const newEntry: AutoresponderKeywordRedisValue = {
          status: platformAccount.status,
          scene: data.scene,
          keyword: data.keywords,
          rule: data.rule,
          trigger: data.trigger,
          contents: data.contents as AnyObject[],
          contentType: data.contentType,
          stopReply: data.stopReply,
          stopInterval: data.stopInterval,
          stopTime: data.stopTime,
          isDelay: data.isDelay,
          delayTime: data.delayTime,
          state: data.state,
          isNew: data.isNew,
          username: platformAccount.username,
          accountExpired: platformAccount.expiresIn
            ? platformAccount.expiresIn * 1000 * 5 * 2 + platformAccount.tokenTime.getTime()
            : 0,
          token: (() => {
            switch (platformAccount.platform) {
              case Platform.Douyin:
                return platformAccount.accessToken
              case Platform.Wechat:
                return platformAccount.appId
              default:
                return platformAccount.accessToken
            }
          })(),
          platformAccountId: platformAccount.id,
          teamId: platformAccount.teamId,
          autoresponderId: data.id,
          platform: platformAccount.platform
        }

        // Check if the new entry already exists in the filtered array
        const exists = filteredArr.some(
          (item) =>
            item.autoresponderId === newEntry.autoresponderId &&
            item.platformAccountId === newEntry.platformAccountId
        )

        if (!exists) {
          filteredArr.push(newEntry)
        }

        await this.cacheManager.store.client.hset(
          AutoresponderKeywordKey,
          key,
          JSON.stringify(filteredArr)
        )
      })

      await Promise.all(platformAccountUpdates)
    }

    if (data.state && opusers && opusers.size) {
      const opuserUpdates = (
        Array.isArray(data.opusers) ? data.opusers : JSON.parse(data.opusers as string)
      ).map(async (item) => {
        const platformAccount = platformAccountMap[item.platformAccountId]
        if (!platformAccount) return

        const opusersKey = `${platformAccount.openId}:${item.id}`
        const value = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, opusersKey)

        const arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
        const filteredArr = arr.filter((item) => item.isNew)

        const newEntry: AutoresponderKeywordRedisValue = {
          status: platformAccount.status,
          username: platformAccount.username,
          scene: data.scene,
          keyword: data.keywords,
          rule: data.rule,
          trigger: data.trigger,
          contents: data.contents as AnyObject[],
          contentType: data.contentType,
          state: data.state,
          stopReply: data.stopReply,
          stopInterval: data.stopInterval,
          isDelay: data.isDelay,
          isNew: data.isNew,
          delayTime: data.delayTime,
          stopTime: data.stopTime,
          accountExpired: platformAccount.expiresIn
            ? platformAccount.expiresIn * 1000 * 5 * 2 + platformAccount.tokenTime.getTime()
            : 0,
          token: (() => {
            switch (platformAccount.platform) {
              case Platform.Douyin:
                return platformAccount.accessToken
              case Platform.Wechat:
                return platformAccount.appId
              default:
                return platformAccount.accessToken
            }
          })(),
          platformAccountId: platformAccount.id,
          teamId: platformAccount.teamId,
          autoresponderId: data.id,
          platform: platformAccount.platform
        }

        // Check if the new entry already exists in the filtered array
        const exists = filteredArr.some(
          (item) =>
            item.autoresponderId === newEntry.autoresponderId &&
            item.platformAccountId === newEntry.platformAccountId
        )

        if (!exists) {
          filteredArr.push(newEntry)
        }

        await this.cacheManager.store.client.hset(
          AutoresponderKeywordKey,
          opusersKey,
          JSON.stringify(filteredArr)
        )
      })

      await Promise.all(opuserUpdates)
    }

    this.logger.log(`autoresponderTransferService redis 刷新 ${data.id}`)
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(`overview-redis-transfer-${key}`, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
