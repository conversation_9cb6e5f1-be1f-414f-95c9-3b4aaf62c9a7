import { ModelDefinition, MongooseModule, Prop, SchemaFactory, Schema } from '@nestjs/mongoose'
import { SchemaTypes } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class LikeAndFollowActionEntity {
  @Prop({
    type: String,
    required: false,
    unique: true,
    index: true
  })
  uniqueId: string

  /**
   * @description 平台类型
   */
  @Prop({
    type: String,
    required: true
  })
  platformType: string

  /**
   * @description 账号唯一id（点赞关注归属者）
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  openId: string

  /**
   * @description 事件类型
   */
  @Prop({
    type: String,
    required: true
  })
  event: string

  /**
   * @description 发送方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  fromUserId: string

  /**
   * @description 目标方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  toUserId: string

  /**
   * @description 发送方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  fromAvatar: string

  /**
   * @description 发送方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  fromName: string

  /**
   * @description 目标方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  toAvatar: string

  /**
   * @description 目标方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  toName: string

  /**
   * @description 时间戳 毫秒
   */
  @Prop({
    type: Number,
    default: () => Date.now(),
    index: true
  })
  createTime: number

  /**
   * @description 消息内容
   */
  @Prop({
    type: SchemaTypes.Map,
    required: true
  })
  content: Map<string, string>
}

export const LikeAndFollowActionSchema: ModelDefinition = <const>{
  name: LikeAndFollowActionEntity.name,
  schema: SchemaFactory.createForClass(LikeAndFollowActionEntity)
    .index({
      event: 1,
      fromUserId: 1,
      sessionId: 1
    })
    .index({
      event: 1,
      toUserId: 1,
      sessionId: 1
    })
}

export const LikeAndFollowActionMongoose = MongooseModule.forFeature([LikeAndFollowActionSchema])
