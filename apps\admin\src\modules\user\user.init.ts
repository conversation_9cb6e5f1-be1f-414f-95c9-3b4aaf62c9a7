import { Injectable, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import crypto from 'crypto'

@Injectable()
export class UserInitService implements OnModuleInit {
  constructor(private readonly prisma: PrismaService) {}

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  async onModuleInit() {
    const initUsername = 'admin'
    const rootAdmin = await this.prisma.adminUser.findUnique({
      where: {
        username: initUsername
      }
    })

    if (!rootAdmin) {
      if (process.env.NODE_ENV === 'prod') {
        const { salt, hash } = this.hashPassword('RFAWqB$U9J36YdJX')

        await this.prisma.adminUser.create({
          data: {
            username: initUsername,
            password: hash,
            salt,
            role: 0
          }
        })
      } else {
        const { salt, hash } = this.hashPassword('admin1234')

        await this.prisma.adminUser.create({
          data: {
            username: initUsername,
            password: hash,
            salt,
            role: 0
          }
        })
      }
    }
  }
}
