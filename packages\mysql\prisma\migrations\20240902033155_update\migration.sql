/*
  Warnings:

  - You are about to drop the column `vipNumber` on the `order` table. All the data in the column will be lost.
  - Added the required column `interestCount` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `interestId` to the `Order` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `order` DROP COLUMN `vipNumber`,
    ADD COLUMN `interestCount` INTEGER NOT NULL,
    ADD COLUMN `interestId` INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE `Order` ADD CONSTRAINT `Order_interestId_fkey` FOREIGN KEY (`interestId`) REFERENCES `Interest`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
