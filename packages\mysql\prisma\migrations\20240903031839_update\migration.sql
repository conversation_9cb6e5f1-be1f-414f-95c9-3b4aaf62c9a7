/*
  Warnings:

  - You are about to drop the column `commentDosageLimit` on the `order` table. All the data in the column will be lost.
  - You are about to drop the column `fansGroupManageLimit` on the `order` table. All the data in the column will be lost.
  - You are about to drop the column `groupDosageLimit` on the `order` table. All the data in the column will be lost.
  - You are about to drop the column `platformAccountNumberLimit` on the `order` table. All the data in the column will be lost.
  - You are about to drop the column `signDosageLimit` on the `order` table. All the data in the column will be lost.
  - You are about to drop the column `teamMemberNumberLimit` on the `order` table. All the data in the column will be lost.
  - You are about to drop the column `uploadImageLimit` on the `order` table. All the data in the column will be lost.
  - You are about to drop the column `memberAccountCommentCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `memberAccountGroupCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `memberAccountManageGroupCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `memberAccountSingleCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `memberAccountUploadImageCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `standardAccountCommentCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `standardAccountGroupCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `standardAccountManageGroupCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `standardAccountSingleCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `standardAccountUploadImageCardinalNumber` on the `systemdosage` table. All the data in the column will be lost.
  - You are about to drop the column `commentDosageLimit` on the `vip` table. All the data in the column will be lost.
  - You are about to drop the column `fansGroupManageLimit` on the `vip` table. All the data in the column will be lost.
  - You are about to drop the column `groupDosageLimit` on the `vip` table. All the data in the column will be lost.
  - You are about to drop the column `signDosageLimit` on the `vip` table. All the data in the column will be lost.
  - You are about to drop the column `uploadImageLimit` on the `vip` table. All the data in the column will be lost.
  - Added the required column `messageLimit` to the `Vip` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `order` DROP COLUMN `commentDosageLimit`,
    DROP COLUMN `fansGroupManageLimit`,
    DROP COLUMN `groupDosageLimit`,
    DROP COLUMN `platformAccountNumberLimit`,
    DROP COLUMN `signDosageLimit`,
    DROP COLUMN `teamMemberNumberLimit`,
    DROP COLUMN `uploadImageLimit`;

-- AlterTable
ALTER TABLE `platformaccount` ADD COLUMN `status` INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `systemdosage` DROP COLUMN `memberAccountCommentCardinalNumber`,
    DROP COLUMN `memberAccountGroupCardinalNumber`,
    DROP COLUMN `memberAccountManageGroupCardinalNumber`,
    DROP COLUMN `memberAccountSingleCardinalNumber`,
    DROP COLUMN `memberAccountUploadImageCardinalNumber`,
    DROP COLUMN `standardAccountCommentCardinalNumber`,
    DROP COLUMN `standardAccountGroupCardinalNumber`,
    DROP COLUMN `standardAccountManageGroupCardinalNumber`,
    DROP COLUMN `standardAccountSingleCardinalNumber`,
    DROP COLUMN `standardAccountUploadImageCardinalNumber`,
    ADD COLUMN `standardMessageLimit` INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `teammember` ADD COLUMN `status` INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `vip` DROP COLUMN `commentDosageLimit`,
    DROP COLUMN `fansGroupManageLimit`,
    DROP COLUMN `groupDosageLimit`,
    DROP COLUMN `signDosageLimit`,
    DROP COLUMN `uploadImageLimit`,
    ADD COLUMN `messageLimit` INTEGER NOT NULL,
    ALTER COLUMN `platformAccountNumberLimit` DROP DEFAULT,
    ALTER COLUMN `teamMemberNumberLimit` DROP DEFAULT,
    MODIFY `expirationTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);
