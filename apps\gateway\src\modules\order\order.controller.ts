import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  AppPurchasesRequestDTO,
  OrderInfoResponseDTO,
  orderPriceRequestDTO,
  OrderRequestCreateOrderDTO,
  OrderResponseCreateOrderDTO,
  OrderResponseQueryOrderDTO,
  OrdersResponseDTO,
  OrderStatusRequestDTO,
  PayInfoResponseDTO,
  PeddingOrderResponseDTO,
  RenewOrderRequest,
  UpgradeOrderRequest
} from './order.dto'
import { OrderService } from './order.service'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { OrderPriceResponseDto } from 'apps/common/modules/orderManage/orderManage.dto'
import { OrderAppService } from './order.service.app'

@Controller('orders')
@ApiTags('订单管理')
export class OrderController {
  constructor(
    private readonly orderService: OrderService,
    private readonly orderAppService: OrderAppService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取订单列表' })
  @ApiOkResponse({ description: '操作成功', type: OrdersResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getOrders(@Query('size') size: number, @Query('page') page: number) {
    const querys = {
      page,
      size
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    return this.orderService.getOrders(querys)
  }

  @Post('price')
  @ApiOperation({ summary: '订单计算金额' })
  @ApiOkResponse({ type: OrderPriceResponseDto })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  calculateOrderPrice(@Body() body: orderPriceRequestDTO) {
    return this.orderService.calculateOrderPrice(body)
  }

  @Post()
  @ApiOperation({ summary: '创建订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createOrder(@Body() body: OrderRequestCreateOrderDTO) {
    return this.orderService.createOrder(body)
  }

  @Post('upgrade')
  @ApiOperation({ summary: '升级订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createUpgradeOrder(@Body() body: UpgradeOrderRequest) {
    return this.orderService.createUpgradeOrder(body)
  }

  @Post('renew')
  @ApiOperation({ summary: '续费订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createRenewOrder(@Body() body: RenewOrderRequest) {
    return this.orderService.createRenewOrder(body)
  }

  @Get(':orderNo/status')
  @ApiOperation({ summary: '查询支付状态' })
  @ApiOkResponse({ type: OrderResponseQueryOrderDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getOrderStatus(@Param('orderNo') orderNo: string) {
    return this.orderService.getOrderStatus(orderNo)
  }

  @Put(':orderNo/status')
  @ApiOperation({ summary: '取消订单' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  putOrderStatus(@Param('orderNo') orderNo: string, @Body() body: OrderStatusRequestDTO) {
    return this.orderService.putOrderStatus(orderNo, body.orderStatus)
  }

  @Get(':orderNo')
  @ApiOperation({ summary: '订单详情' })
  @ApiOkResponse({ type: OrderInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getOrder(@Param('orderNo') orderNo: string) {
    return this.orderService.getOrderInfo(orderNo)
  }

  @Get(':orderNo/payinfo')
  @ApiOperation({ summary: '支付渠道信息' })
  @ApiOkResponse({ type: PayInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getPayInfo(@Param('orderNo') orderNo: string) {
    return this.orderService.getPayInfo(orderNo)
  }

  @Get('/pending')
  @ApiOperation({ summary: '是否有待付款订单' })
  @ApiOkResponse({ type: PeddingOrderResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getPendingOrder() {
    return this.orderService.getPeddingOrders()
  }

  @Post('/in-app-purchases')
  @ApiOperation({ summary: 'APP应用内购买' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  inAppPurchases(@Body() body: AppPurchasesRequestDTO) {
    return this.orderAppService.inAppPurchases(body)
  }
}
