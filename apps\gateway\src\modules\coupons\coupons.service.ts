import { BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { Prisma, PrismaService } from '@qdy/mysql'
import { ChangeCouponsRequestDTO, CouponsStatus, UserCouponsStatus } from './coupons.dto'

@Injectable()
export class UserCouponsService {
  logger = new Logger('UserService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getUserCoupons(status: number = -1, page: number = 1, size: number = 10) {
    if (!page || page < 1) {
      page = 1
    }
    if (!size || size < 1) {
      size = 10
    }
    if (status !== UserCouponsStatus.Normal && status !== UserCouponsStatus.used) {
      status = -1
    }

    const { user } = this.request
    const now = new Date()

    const whereDiction: Prisma.UsersCouponsWhereInput = { userId: user.id }
    if (status !== -1) {
      // 可用
      if (status === 0) {
        whereDiction.status = UserCouponsStatus.Normal
        whereDiction.expireTime = { gte: now }
      }
      // 不可用
      else {
        whereDiction.OR = []
        whereDiction.OR.push({ status: UserCouponsStatus.used })
        whereDiction.OR.push({ expireTime: { lt: now } })
      }
    }
    // 总数
    const total = await this.prisma.usersCoupons.count({
      where: whereDiction
    })

    const reData =
      total > 0
        ? await this.prisma.usersCoupons.findMany({
            where: whereDiction,
            skip: (page - 1) * size,
            take: size,
            orderBy: [
              {
                status: 'asc'
              },
              {
                createTime: 'desc'
              }
            ]
          })
        : null

    return {
      total,
      page,
      size: reData?.length ?? 0,
      data:
        reData?.map((obj) => ({
          id: obj.id,
          name: obj.name,
          minimumSpendingAmount: obj.minimumSpendingAmount,
          discountAmount: obj.discountAmount,
          expireTime: obj.expireTime,
          status:
            obj.status === UserCouponsStatus.Normal && obj.expireTime < now
              ? UserCouponsStatus.expired
              : obj.status
        })) ?? []
    }
  }

  async exchangeCoupons(data: ChangeCouponsRequestDTO) {
    const { user } = this.request

    const channel = await this.prisma.channel.findUnique({
      where: {
        code: data.channelCode
      },
      include: { coupon: true }
    })

    if (!channel) {
      throw new BadRequestException('该兑换码无效')
    }

    if (channel?.status === false) {
      throw new BadRequestException('兑换码已过期')
    }

    if (!channel.couponId || !channel.coupon) {
      throw new BadRequestException('该兑换码无效')
    }

    if (channel.coupon.status === CouponsStatus.Deleted) {
      throw new BadRequestException('该兑换码无效')
    }

    const userCoupon = await this.prisma.usersCoupons.findUnique({
      where: {
        userId_channelId: {
          userId: user.id,
          channelId: channel.id
        }
      }
    })

    if (userCoupon) {
      throw new BadRequestException('不可重复兑换')
    }

    try {
      const result = new Date()
      result.setDate(result.getDate() + channel.coupon.expireDaysNum)
      const expireDate = result
      // 插入发放记录
      const re = await this.prisma.usersCoupons.create({
        data: {
          couponsId: channel.coupon.id,
          userId: user.id,
          expireTime: expireDate,
          minimumSpendingAmount: channel.coupon.minimumSpendingAmount,
          discountAmount: channel.coupon.discountAmount,
          channelId: channel.id,
          creatorId: channel.coupon.creatorId,
          phone: user.phone,
          name: channel.coupon.name
        }
      })
      return {
        id: re.id,
        name: re.name,
        minimumSpendingAmount: re.minimumSpendingAmount,
        discountAmount: re.discountAmount,
        expireTime: re.expireTime,
        status: re.status
      }
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`兑换优惠券失败`)
    }
  }
}
