import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { InjectModel } from '@nestjs/mongoose'
import { AnyObject, Model } from 'mongoose'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { WebHookServiceGrpc } from './webhook.rpc'
import { PersonalChatMessageEntity } from 'packages/mongo/lib'
import { genSocketRedisKey } from 'packages/utils'

export const sendMessageEventKey = 'save-personal-send-message'

export const sendMessageEventEmitter = new EventEmitter()

@Injectable()
export class WebhookEventService implements OnModuleInit {
  logger = new Logger('WebhookEventService')

  constructor(
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>
  ) {}

  taskQueue: Queue

  taskWorker: Worker

  onModuleInit() {
    sendMessageEventEmitter.on(sendMessageEventKey, this.savePersonalChatMessageTask.bind(this))

    this.taskQueue = new Queue('save-personal-message', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'save-personal-message',
      async (job) => {
        await this.saveMessageAndSendSocket(job.data)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async savePersonalChatMessageTask({
    platformType,
    uniqueId,
    openId,
    fromUserId,
    toUserId,
    fromAvatar,
    fromName,
    toAvatar,
    toName,
    sessionId,
    isAuto,
    messageId,
    content
  }: {
    platformType: string
    uniqueId: string
    openId: string
    fromUserId: string
    toUserId: string
    fromAvatar: string
    fromName: string
    toAvatar: string
    toName: string
    sessionId: string
    isAuto: number
    messageId: string
    content: AnyObject
  }) {
    await this.taskQueue.add(
      'save-personal-message',
      {
        platformType,
        event: content.messageType === 'im_recall_msg' ? 'im_recall_msg' : 'im_send_msg',
        uniqueId,
        openId,
        fromUserId,
        toUserId,
        fromAvatar,
        fromName,
        toAvatar,
        toName,
        sessionId,
        isAuto,
        messageId,
        createTime: Date.now(),
        content
      },
      {
        removeOnComplete: true,
        removeOnFail: true,
        jobId: `${uniqueId}-send-message`
      }
    )
  }

  async saveMessageAndSendSocket(data: {
    platformType: string
    event: string
    uniqueId: string
    openId: string
    fromUserId: string
    toUserId: string
    fromAvatar: string
    fromName: string
    toAvatar: string
    toName: string
    sessionId: string
    isAuto: number
    messageId: string
    createTime: number
    content: AnyObject
  }) {
    try {
      this.personalChatMessageModel.create(data)

      const userIdMaps = await this.cacheManager.store.client.hgetall(
        genSocketRedisKey(data.fromUserId)
      )

      const dataList: { socketId: string; data: Record<string, unknown> }[] = []

      Object.keys(userIdMaps).forEach((socketId) => {
        dataList.push({
          socketId,
          data: { ...data, platformAccountId: parseInt(userIdMaps[socketId], 10) }
        })
      })

      if (dataList.length) {
        try {
          this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
            next: () => {},
            error: (error) => {
              this.logger.error(error)
            },
            complete: () => {}
          })
        } catch (error) {
          this.logger.error(error)
        }
      }
      // 发socket通知
    } catch (error) {
      this.logger.error(error)
    }
  }
}
