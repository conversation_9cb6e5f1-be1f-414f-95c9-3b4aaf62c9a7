import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { PrismaService } from '@qdy/mysql'
import { FastifyRequest } from 'fastify'
import {
  DownloadMediaRequestDTO,
  InteractRequestCommentMessageWeChatDTO,
  WechatCommentReplyRequestDTO,
  WechatCommentRequestDTO,
  WechatDeleteCommentRequestDTO,
  WechatMessagesRequestDTO
} from './interact.dto'
import {
  postVideoComments,
  deleteWechatComment,
  getWechatMessageSessionId,
  wechatSendMessages,
  createComment
} from './external.wechat'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import {
  PersonalChatMessageEntity,
  WechatCommentEntity,
  WechatMessagesEntity,
  WechatOpusEntity,
  WorkCommentEntity
} from '@qdy/mongo'
import { Types, Document, Model } from 'mongoose'
import { TeamMemberRole } from '../team/team.dto'
import { InjectModel } from '@nestjs/mongoose'
import { AccountAccountsStatus } from '../account/account.dto'
import { updateOpusEventKey, eventEmitter } from '../account/account.event'
import { Platform } from '@qdy/utils'

@Injectable()
export class InteractWechatService {
  logger = new Logger('InteractWechatService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(WechatMessagesEntity.name) private messagesModel: Model<WechatMessagesEntity>,
    @InjectModel(WechatCommentEntity.name) private wechatCommentModel: Model<WechatCommentEntity>,
    @InjectModel(WorkCommentEntity.name) private workCommentModel: Model<WorkCommentEntity>,
    @InjectModel(WechatOpusEntity.name) private wechatOpus: Model<WechatOpusEntity>
  ) {}

  async wechatVideos({
    platformAccountId,
    page = 1,
    size = 10
  }: {
    platformAccountId: number
    page: number
    size: number
  }) {
    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId
      }
    })

    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }

    eventEmitter.emit(updateOpusEventKey, { wxid: platformAccount.openId })

    const total = await this.wechatOpus.countDocuments({
      wxid: platformAccount.openId
    })

    const skip = (page - 1) * size

    const res = await this.wechatOpus
      .find({
        wxid: platformAccount.openId
      })
      .sort({ createTime: -1 })
      .skip(skip)
      .limit(size)
      .exec()

    return {
      total,
      page,
      size,
      data: res
    }
  }

  async removeWechatComment(body: WechatDeleteCommentRequestDTO) {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
    const { user } = this.request

    const platformAccount = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId: body.platformAccountId
    })

    await deleteWechatComment({
      ...body,
      appId: platformAccount.appId,
      token: wechatConfig.Token,
      myUserName: platformAccount.username,
      isNew: platformAccount.isNew
    })

    await this.wechatCommentModel.deleteOne({
      uniqueId: body.commentId
    })
  }

  async postWechatCommentReply(id: string, body: WechatCommentReplyRequestDTO) {
    const comment = await this.workCommentModel.findOne({
      uniqueId: id
    })

    if (comment) {
      const oldReply = comment.commentReply

      oldReply.unshift({
        wxid: body.wxid,
        content: body.content,
        createTime: Date.now()
      })

      return this.workCommentModel
        .findByIdAndUpdate(
          comment._id,
          {
            commentReply: oldReply
          },
          { new: true }
        )
        .exec()
    }
  }

  async sendWechatMessage({
    toUserName,
    platformAccountId,
    content,
    imgUrl,
    sessionId: propSessionId
  }: WechatMessagesRequestDTO) {
    const { user } = this.request

    const platformAccount = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId
    })

    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
    const cacheKey = `wechat:${platformAccountId}-${toUserName}`

    let sessionId = propSessionId || (await this.cacheManager.get<string>(cacheKey))

    if (!sessionId) {
      ;({ sessionId } = await getWechatMessageSessionId({
        token: wechatConfig.Token,
        appId: platformAccount.appId,
        toUserName,
        myUserName: platformAccount.username,
        isNew: platformAccount.isNew
      }))
      await this.cacheManager.set(cacheKey, sessionId, 0)
    }

    return wechatSendMessages({
      platformAccountId: platformAccount.id,
      appId: platformAccount.appId,
      toUserName,
      myUserName: platformAccount.username,
      sessionId,
      content,
      imgUrl,
      token: wechatConfig.Token,
      teamId: user.currentTeamId,
      redisClient: this.cacheManager,
      openId: platformAccount.openId,
      isNew: platformAccount.isNew
    })
  }

  async wechatVideoComment(body: WechatCommentRequestDTO) {
    const { user } = this.request
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    const platformAccount = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId: body.platformAccountId
    })

    try {
      const commentList = await postVideoComments({
        appId: platformAccount.appId,
        objectId: body.objectId,
        lastBuffer: body.lastBuffer,
        sessionBuffer: body.sessionBuffer,
        objectNonceId: body.objectNonceId,
        token: wechatConfig.Token,
        rootCommentId: body.rootCommentId,
        isNew: platformAccount.isNew
      })

      return {
        lastBuffer: commentList?.lastBuffer,
        downContinueFlag: commentList?.downContinueFlag,
        upContinueFlag: commentList?.upContinueFlag,
        monotonicData: commentList?.monotonicData,
        countInfo: commentList?.countInfo,
        data: commentList?.commentInfo ? commentList?.commentInfo : []
      }
    } catch (err) {
      this.logger.error('获取视频评论列表失败', err)
      throw new ForbiddenException(err)
    }
  }

  async wechatVideoNewComment(body: WechatCommentRequestDTO) {
    const { user } = this.request
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    const platformAccount = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId: body.platformAccountId
    })

    const opus = await this.wechatOpus.findOne({
      objectId: body.objectId
    })

    try {
      const commentList = await postVideoComments({
        appId: platformAccount.appId,
        objectId: body.objectId,
        lastBuffer: body.lastBuffer,
        sessionBuffer: body.sessionBuffer,
        objectNonceId: body.objectNonceId,
        token: wechatConfig.Token,
        rootCommentId: body.rootCommentId,
        isNew: platformAccount.isNew
      })

      const dataList = commentList?.commentInfo
        ? commentList.commentInfo.map((item) => {
            const data = {
              platformType: 'wechat',
              uniqueId: item.commentId,
              event: 'item_comment_reply',
              openId: platformAccount.openId,
              fromUserId: item.username,
              toUserId: '',
              fromAvatar: item.headUrl,
              fromName: item.nickname,
              toAvatar: '',
              toName: '',
              sessionId: body.objectId,
              messageId: item.commentId,
              content: {
                messageType: 'comment',
                content: item.content,
                commentId: item.commentId,
                name: item.nickname,
                avatar: item.headUrl,
                thumbUrl: opus.thumbUrl,
                description: opus.description,
                sessionBuffer: opus.sessionBuffer,
                mentionType: item.contentType,
                diggCount: item.likeCount,
                replyToItemId: body.objectId,
                refObjectNonceId: body.objectNonceId,
                refContent: item.replyContent,
                appId: platformAccount.appId,
                expandCommentCount: item.expandCommentCount
              },
              createTime: item.createtime * 1000,
              isAuto: 0
            }
            return data
          })
        : []

      return {
        lastBuffer: commentList?.lastBuffer,
        downContinueFlag: commentList?.downContinueFlag,
        upContinueFlag: commentList?.upContinueFlag,
        monotonicData: commentList?.monotonicData,
        countInfo: commentList?.countInfo,
        data: dataList
      }
    } catch (err) {
      this.logger.error('获取视频评论列表失败', err)
      throw new ForbiddenException(err)
    }
  }

  async getHistoryMessages(createTime: number, platformAccountId: number) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const tasks: Promise<
      (Document<unknown, object, WechatMessagesEntity> &
        WechatMessagesEntity & {
          _id: Types.ObjectId
        })[]
    >[] = []

    const platformAccountIds: number[] = []

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    if (platformAccountId) {
      platformAccounts = platformAccounts.filter((item) => item.id === platformAccountId)
    }

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const comparisonDate =
      createTime && createTime > sevenDaysAgo.getTime() ? createTime : sevenDaysAgo.getTime()

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]
      platformAccountIds.push(platformAccount.id)
      tasks.push(
        this.messagesModel
          .aggregate([
            {
              $match: {
                wxid: platformAccount.openId,
                createTime: { $gt: comparisonDate },
                sessionId: { $ne: null }
              }
            },
            {
              $sort: { timestamp: -1 }
            },
            {
              $group: {
                _id: '$sessionId',
                docs: { $push: '$$ROOT' }
              }
            },
            {
              $project: {
                _id: 1,
                docs: { $slice: ['$docs', 25] }
              }
            }
          ])
          .exec()
      )
    }

    const values = await Promise.all(tasks)

    const platformAccountMap: Record<string, (WechatMessagesEntity & { id: string })[]> = {}

    for (let i = 0; i < values.length; i++) {
      const value = values[i] as unknown as {
        _id: string
        docs: (WechatMessagesEntity & { _id: string })[]
      }[]

      const id = platformAccountIds[i]
      platformAccountMap[id] = []

      for (let i = 0; i < value.length; i++) {
        const item = value[i]
        item.docs.forEach((doc) => {
          platformAccountMap[id].unshift({
            ...doc,
            platformAccountId: id,
            id: doc._id.toString()
          } as WechatMessagesEntity & {
            id: string
          })
        })

        platformAccountMap[id].sort((a, b) => a.createTime - b.createTime)
      }
    }

    return platformAccountMap
  }

  async getSingleHistoryMessages({
    createTime,
    platformAccountId,
    sessionId
  }: {
    createTime: number
    platformAccountId: number
    sessionId: string
  }) {
    const { user } = this.request

    const platformAccount = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId
    })

    const query = {
      createTime: { $lt: createTime }
    }

    if (!createTime) {
      delete query.createTime
    }

    const res = await this.messagesModel
      .find({
        wxid: platformAccount.openId,
        sessionId,
        ...query
      })
      .sort({ createTime: 1 })
      .limit(50)
      .exec()

    return res.map((item) => ({
      ...item.toJSON(),
      platformAccountId,
      id: item.id
    }))
  }

  async sendReplyMessage({ platformAccountId, ...rest }: InteractRequestCommentMessageWeChatDTO) {
    const { user } = this.request

    const platformAccount = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId
    })

    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    return createComment({
      platformAccountId: platformAccount.id,
      token: wechatConfig.Token,
      teamId: user.currentTeamId,
      appId: platformAccount.appId,
      myUserName: platformAccount.username,
      redisClient: this.cacheManager,
      openId: platformAccount.openId,
      isNew: platformAccount.isNew,
      ...rest
    })
  }

  async checkPlatformAccount({
    teamId,
    userId,
    platformAccountId
  }: {
    teamId: number
    userId: number
    platformAccountId: number
  }) {
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('用户未加入此团队')
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    const platformAccount = platformAccounts.find((item) => item.id === platformAccountId)

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在或你不属于运营人员')
    }

    if (platformAccount.platform !== Platform.Wechat) {
      throw new ForbiddenException('此账号不是微信账号')
    }

    if (platformAccount.status === AccountAccountsStatus.Disable) {
      throw new ForbiddenException('账号已被冻结')
    }

    return platformAccount
  }

  async downloadMedia(body: DownloadMediaRequestDTO) {
    const data = await this.personalChatMessageModel.findOne({ uniqueId: body.uniqueId })

    return data?.content?.get('responseUrl') ?? ''
  }
}
