import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'

const logger = new Logger('account external')

const authorizeAccountApi = 'https://open.douyin.com/oauth/access_token/'
const authorizeAccountInfoApi = 'https://open.douyin.com/oauth/userinfo/'
const authorizeAccountRefreshApi = 'https://open.douyin.com/oauth/refresh_token/'
const authorizeAccountRefreshAccApi = 'https://open.douyin.com/oauth/refresh_token/'

export async function postAuthorizeAccount(data: {
  clientSecret: string
  clientKey: string
  code: string
}) {
  const res = (await axios.post(authorizeAccountApi, {
    client_key: data.clientKey,
    client_secret: data.clientSecret,
    code: data.code,
    grant_type: 'authorization_code'
  })) as {
    data: {
      access_token: string
      captcha: string
      desc_url: string
      description: string
      error_code: number
      expires_in: number
      log_id: string
      open_id: string
      refresh_expires_in: number
      refresh_token: string
      scope: string
    }
    message: string
  } as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(res.data.data.description)
  }

  return {
    openId: res.data.data.open_id,
    accessToken: res.data.data.access_token,
    refreshToken: res.data.data.refresh_token,
    expiresIn: res.data.data.expires_in,
    refreshExpiresIn: res.data.data.refresh_expires_in
  }
}

export async function postAuthorizeAccountInfoApi({
  accessToken,
  openId
}: {
  accessToken: string
  openId: string
}) {
  try {
    const res = (await axios.post(authorizeAccountInfoApi, {
      access_token: accessToken,
      open_id: openId
    })) as {
      data: {
        avatar: string
        avatar_larger: string
        client_key: string
        e_account_role: string
        error_code: number
        log_id: string
        nickname: string
        open_id: string
        union_id: string
        description: string
      }
    } as { data: Record<string, any> }

    if (res.data.data.error_code !== 0) {
      throw new BadRequestException(res.data.data.description)
    }

    return {
      avatar: res.data.data.avatar_larger,
      name: res.data.data.nickname,
      accountRole: res.data.data.e_account_role
    } as {
      avatar: string
      name: string
      accountRole: string
    }
  } catch (error) {
    logger.error(error)
    throw new BadRequestException('获取账号信息失败')
  }
}

export async function postAuthorizeAccountRefresh({
  clientKey,
  refreshToken
}: {
  clientKey: string
  refreshToken: string
}) {
  const res = (await axios.post(authorizeAccountRefreshApi, {
    client_key: clientKey,
    refresh_token: refreshToken
  })) as {
    data: {
      description: string
      error_code: number
      expires_in: number
      refresh_token: string
    }
    message: string
  } as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(res.data.data.description)
  }

  return {
    refreshToken: res.data.data.refresh_token,
    refreshExpiresIn: res.data.data.expires_in
  }
}

export async function postAuthorizeAccountRefreshAcc({
  clientKey,
  refreshToken
}: {
  clientKey: string
  refreshToken: string
}) {
  const res = (await axios.post(authorizeAccountRefreshAccApi, {
    client_key: clientKey,
    refresh_token: refreshToken,
    grant_type: 'refresh_token'
  })) as {
    data: {
      description: string
      error_code: number
      access_token: number
      expires_in: number
      refresh_token: string
      refresh_expires_in: number
    }
    message: string
  } as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(res.data.data.description)
  }

  return {
    refreshToken: res.data.data.refresh_token,
    expiresIn: res.data.data.expires_in,
    refreshExpiresIn: res.data.data.refresh_expires_in,
    accessToken: res.data.data.access_token
  }
}
