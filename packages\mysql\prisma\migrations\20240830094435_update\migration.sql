/*
  Warnings:

  - You are about to alter the column `type` on the `order` table. The data in that column could be lost. The data in that column will be cast from `Enum(EnumId(0))` to `VarChar(191)`.
  - You are about to alter the column `payType` on the `order` table. The data in that column could be lost. The data in that column will be cast from `Enum(EnumId(1))` to `VarChar(191)`.
  - Added the required column `expireTime` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `orderStatus` to the `Order` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `order` ADD COLUMN `expireTime` DATETIME(3) NOT NULL,
    ADD COLUMN `orderStatus` VARCHAR(191) NOT NULL,
    MODIFY `type` VARCHAR(191) NOT NULL,
    MODIFY `payType` VARCHAR(191) NOT NULL;

-- CreateTable
CREATE TABLE `OrderInfo` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `orderId` INTEGER NOT NULL,
    `orderNo` VARCHAR(191) NOT NULL,
    `payType` VARCHAR(191) NOT NULL,
    `tradeStatus` VARCHAR(191) NOT NULL,
    `tradeNo` VARCHAR(191) NOT NULL,
    `paymentTime` DATETIME(3) NOT NULL,
    `urlInfo` VARCHAR(191) NOT NULL,
    `callbackInfo` JSON NOT NULL,

    INDEX `OrderInfo_orderId_idx`(`orderId`),
    INDEX `OrderInfo_orderNo_idx`(`orderNo`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `OrderInfo` ADD CONSTRAINT `OrderInfo_orderId_fkey` FOREIGN KEY (`orderId`) REFERENCES `Order`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
