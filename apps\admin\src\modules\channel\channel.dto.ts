import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, Length } from 'class-validator'

export class ChannelRequestBodyDTO {
  /**
   * 渠道名称
   */
  @ApiProperty({
    type: String,
    required: true,
    example: '渠道名称'
  })
  @IsString()
  name: string

  /**
   * 渠道码
   */
  @ApiProperty({
    type: String,
    required: true,
    example: 'BRO001'
  })
  @IsString()
  code: string

  /**
   * 渠道商账号
   */
  @ApiProperty({
    type: String,
    required: true,
    example: 'admin-agent'
  })
  @IsString({ message: '渠道商账号格式不正确' })
  @IsNotEmpty({ message: '渠道商账号不能为空' })
  username: string

  /**
   * 渠道商密码
   */
  @ApiProperty({
    type: String,
    required: true,
    example: 'Aa123456'
  })
  @IsString({ message: '渠道商账号密码格式不正确' })
  @IsNotEmpty({ message: '渠道商账号密码不能为空' })
  @Length(8, 15, { message: '渠道商账号密码请输入8-15位密码' })
  password: string

  /**
   * 优惠券id
   */
  @ApiProperty({
    type: Number,
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  couponId?: number
}

export class ChannelRequestUpdateDTO {
  /**
   * 渠道用户id
   */
  @ApiProperty({
    type: Number,
    required: true,
    example: 1
  })
  @IsNumber()
  channelUserId: number

  /**
   * 渠道名称
   */
  @ApiProperty({
    type: String,
    required: true,
    example: '渠道名称'
  })
  @IsString()
  name: string

  /**
   * 渠道码
   */
  @ApiProperty({
    type: String,
    required: true,
    example: 'BRO001'
  })
  @IsString()
  code: string

  /**
   * 渠道商账号
   */
  @ApiProperty({
    type: String,
    required: true,
    example: 'admin-agent'
  })
  @IsString({ message: '渠道商账号格式不正确' })
  @IsNotEmpty({ message: '渠道商账号不能为空' })
  username: string

  /**
   * 优惠券id
   */
  @ApiProperty({
    type: Number,
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  couponId?: number
}

export class ChannelPasswordUpdateRequestDTO {
  /**
   * 渠道用户id
   */
  @ApiProperty({
    type: Number,
    required: true,
    example: 1
  })
  @IsNumber()
  channelUserId: number

  @ApiProperty({
    type: String,
    required: true,
    example: 'Aa123456'
  })
  @IsString({ message: '渠道商账号密码格式不正确' })
  @IsNotEmpty({ message: '渠道商账号密码不能为空' })
  @Length(8, 15, { message: '渠道商账号密码请输入8-15位密码' })
  password: string
}

export class ChannelStatusRequestDTO {
  @ApiProperty({
    type: Boolean,
    required: true,
    example: true
  })
  @IsBoolean()
  status: boolean
}

export class ChannelResponse {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '长沙易撰'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'YZ2001'
  })
  code: string

  @ApiResponseProperty({
    type: Number,
    example: '1726626176'
  })
  createTime: number

  @ApiResponseProperty({
    type: Number,
    example: '201'
  })
  userCount: number

  @ApiResponseProperty({
    type: Number,
    example: '201'
  })
  orderCount: number

  @ApiResponseProperty({
    type: Number,
    example: '订单总金额'
  })
  orderTotal: number

  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  status: boolean
}

export class ChannelResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [ChannelResponse]
  })
  data: ChannelResponse[]
}
