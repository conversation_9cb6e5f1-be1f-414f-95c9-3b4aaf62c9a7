import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Queue, Worker } from 'bullmq'
import { <PERSON>ron } from '@nestjs/schedule'
import { WechatOpusEntity } from '@qdy/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { AnyObject, Model } from 'mongoose'
import { PrismaService } from '@qdy/mysql'
import { postUserPage } from '../interact/external.wechat'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { Platform } from '@qdy/utils'

@Injectable()
export class WechantAccountOpusCronService implements OnModuleInit {
  private readonly logger = new Logger(WechantAccountOpusCronService.name)

  wechatAccountQueue: Queue

  wechatAccountWorker: Worker

  wechatToken: string

  constructor(
    private readonly prisma: PrismaService,
    @InjectModel(WechatOpusEntity.name)
    private wechatOpusModel: Model<WechatOpusEntity>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async onModuleInit() {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    this.wechatToken = wechatConfig.Token

    this.wechatAccountQueue = new Queue('wechat-account-opus-init', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })

    this.wechatAccountWorker = new Worker(
      'wechat-account-opus-init',
      async (job) => {
        const { data } = job.data
        this.logger.log(`Running ${job.id} data ${data}`)
        await this.onUpdateWechatAccountOpusCron(data)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.logger.log('wechat-account-opus init')
  }

  /**
   * 微信账号更新作品定时任务
   * 每天凌晨1点
   */
  @Cron('0 0 1 * * *', {
    name: 'WechatAccountUpdateCron',
    timeZone: 'Asia/Shanghai'
  })
  async WechatAccountUpdateCron() {
    // 查询所有未过期的视频号
    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        platform: Platform.Wechat,
        expiresIn: {
          gt: 0
        }
      }
    })

    for (let i = 0; i < platformAccounts.length; i++) {
      const { appId, openId, username, isNew } = platformAccounts[i]

      const accountLastOpus = await this.wechatOpusModel
        .findOne({
          wxid: openId
        })
        .sort({ createTime: -1 })
      let maxOpusTime = 0
      if (accountLastOpus) {
        maxOpusTime = accountLastOpus.createTime
      }

      this.wechatAccountQueue.add(
        'wechat-account-opus-init',
        {
          data: {
            appId,
            openId,
            username,
            maxOpusTime,
            isNew
          }
        },
        {
          delay: 0, // 立即执行
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `wechat-account-init-opus-${openId}`
        }
      )
    }
  }

  async onUpdateWechatAccountOpusCron({ appId, openId, username, maxOpusTime, isNew }) {
    if (!appId || !username) {
      return
    }

    try {
      const opusList = await this.getOpusList({
        appid: appId,
        myUserName: username,
        maxTime: maxOpusTime,
        isNew
      })

      const record = []
      opusList.forEach((item) => {
        const data = {
          objectId: item.id,
          wxid: openId,
          username: item.username,
          nickname: item.nickName,
          headUrl: item.contact.headUrl,
          objectNonceId: item.objectNonceId,
          createTime: item.createtime,
          sessionBuffer: item.sessionBuffer,
          description: item?.objectDesc?.description,
          forwardCount: item.forwardCount,
          likeCount: item.likeCount,
          commentCount: item.commentCount,
          thumbUrl: `${item?.objectDesc?.media[0]?.ThumbUrl}${item?.objectDesc?.media[0]?.thumbUrlToken}`,
          jsonData: JSON.stringify(item)
        }
        record.push(data)
      })

      if (record) {
        await this.wechatOpusModel.create(record)
      }
    } catch (error) {
      this.logger.error('作品数据更新失败', error)
    }
  }

  async getOpusList(data: { appid: string; myUserName: string; maxTime: number; isNew: boolean }) {
    let attempts = 0
    let lastBuffer = ''
    let maxId = '0'
    const result = []
    let continueFlag = 0

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const videoList = await postUserPage({
          appId: data.appid,
          toUserName: data.myUserName,
          lastBuffer,
          maxId,
          token: this.wechatToken
        })

        ;({ continueFlag, lastBuffer } = videoList)

        if (videoList.object) {
          maxId = videoList.object[videoList.object.length - 1].id
          if (data.maxTime === 0) {
            // 如果maxTime为0 不比较时间
            const list = (videoList.object as AnyObject[]).filter((item) => item?.privateFlag !== 1)

            list.forEach((element) => {
              result.push(element)
            })
          } else {
            const recordLength = (videoList.object as AnyObject[]).filter(
              (item) => item?.privateFlag !== 1
            ).length

            const maxRecordLength = (videoList.object as AnyObject[]).filter(
              (item) => item?.privateFlag !== 1 && item.createtime > data.maxTime
            ).length

            const list = (videoList.object as AnyObject[]).filter(
              (item) => item?.privateFlag !== 1 && item.createtime > data.maxTime
            )

            list.forEach((element) => {
              result.push(element)
            })

            if (recordLength !== maxRecordLength) {
              break
            }
          }
        } else {
          break
        }

        if (attempts >= 10) {
          break
        }

        if (!continueFlag) {
          break
        }

        attempts++
      } catch (err) {
        this.logger.error('获取视频列表失败', err)
        break
      }
    }

    return result
  }
}
