diff --git a/server/server-grpc.js b/server/server-grpc.js
index e099e9a31b16a9e8ec887593db117a17b7a5c16d..02a815ead4b721f4dfd7f893383c857c7c8ebbeb 100644
--- a/server/server-grpc.js
+++ b/server/server-grpc.js
@@ -36,7 +36,7 @@ class ServerGrpc extends server_1.Server {
     }
     async start(callback) {
         await this.bindEvents();
-        this.grpcClient.start();
+        // this.grpcClient.start();
         callback();
     }
     async bindEvents() {
@@ -447,7 +447,7 @@ class ServerGrpc extends server_1.Server {
         for (const definition of this.getServiceNames(grpcPkg)) {
             this.grpcClient.addService(
             // First parameter requires exact service definition from proto
-            definition.service.service, 
+            definition.service.service,
             // Here full proto definition required along with namespaced pattern name
             await this.createService(definition.service, definition.name));
         }
