{"extends": ["iroot/typescript"], "root": true, "env": {"node": true, "jest": true}, "rules": {"no-console": "error", "no-await-in-loop": "off", "no-useless-constructor": "off", "no-empty-function": "off", "max-classes-per-file": "off", "@typescript-eslint/interface-name-prefix": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off"}}