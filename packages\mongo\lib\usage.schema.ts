import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class UsageEntity {
  @Prop({
    type: Number,
    required: true
  })
  teamId: number

  @Prop({
    type: String,
    index: true
  })
  createTime: string

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoSingleCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoGroupCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoCommentCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  singleCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  groupCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  commentCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatMessageCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatAutoMessageCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatCommentCount: number

  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatAutoCommentCount: number
}

export const UsageSchema: ModelDefinition = {
  name: UsageEntity.name,
  schema: SchemaFactory.createForClass(UsageEntity)
}

export const UsageMongoose = MongooseModule.forFeature([UsageSchema])
