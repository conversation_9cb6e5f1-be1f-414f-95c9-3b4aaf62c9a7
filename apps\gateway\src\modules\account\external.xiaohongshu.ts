import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'

const logger = new Logger('xiaohongshu account external')

const authorizeAccountApi = 'https://adapi.xiaohongshu.com/api/open/oauth2/access_token'
const authorizeAccountRefreshApi = 'https://adapi.xiaohongshu.com/api/open/oauth2/refresh_token'
const commentList = 'https://adapi.xiaohongshu.com/api/open/im/comment/list'

export async function postAuthorizeXiaohongshuAccount(data: {
  clientSecret: string
  clientKey: string
  code: string
}) {
  logger.log(data)

  const res = (await axios.post(authorizeAccountApi, {
    app_id: data.clientKey,
    secret: data.clientSecret,
    auth_code: data.code
  })) as {
    data: {
      user_id: string
      role_type: number
      app_id: number
      refresh_token: string
      advertiser_id: number
      refresh_token_expires_in: number
      approval_role_type: number
      platform_type: number
      access_token: string
      access_token_expires_in: number
      scope: string
    }
    msg: string
    code: number
    success: boolean
  } as { data: Record<string, any> }

  logger.log(res.data)

  if (!res.data.success) {
    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }

  logger.log(res.data)

  const advertisers = res.data.data.approval_advertisers
  const userInfo = advertisers.length > 0 ? advertisers[0] : null

  return {
    openId: res.data.data.user_id,
    accessToken: res.data.data.access_token,
    refreshToken: res.data.data.refresh_token,
    expiresIn: res.data.data.access_token_expires_in,
    refreshExpiresIn: res.data.data.refresh_token_expires_in,
    name: userInfo?.advertiser_name,
    scope: res.data.data.scope
  }
}

export async function postXiaohongshuAuthorizeAccountRefresh(data: {
  clientSecret: string
  clientKey: string
  refreshToken: string
}) {
  const res = (await axios.post(authorizeAccountRefreshApi, {
    app_id: data.clientKey,
    secret: data.clientSecret,
    refresh_token: data.refreshToken
  })) as {
    data: {
      user_id: string
      role_type: number
      app_id: number
      refresh_token: string
      advertiser_id: number
      refresh_token_expires_in: number
      approval_role_type: number
      platform_type: number
      access_token: string
      access_token_expires_in: number
    }
    msg: string
    code: number
    success: boolean
  } as { data: Record<string, any> }

  logger.log(res.data)

  if (!res.data.success) {
    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }

  return {
    accessToken: res.data.data.access_token,
    refreshToken: res.data.data.refresh_token,
    expiresIn: res.data.data.access_token_expires_in,
    refreshExpiresIn: res.data.data.refresh_token_expires_in
  }
}

export async function postXiaohongshuCommentList(data: {
  beginTime: string
  endTime: string
  openId: string
  accessToken: string
  page: number
  size: number
}) {
  const postData = {
    user_id: data.openId,
    page_num: data.page,
    page_size: data.size,
    begin_time: data.beginTime,
    end_time: data.endTime
  }

  const res = (await axios.post(commentList, postData, {
    headers: {
      'Access-Token': data.accessToken
    }
  })) as {
    data: {
      code: number
      msg: string
      success: string
      data: {
        total: number
        list: {
          note_id: string
          cover: string
          note_title: string
          note_author_user_id: string
          comment_content: string
          comment_time: number
          comment_user_name: string
          comment_user_id: string
          comment_id: string
          uniq_id: string
          reply_state: string
          reply_third_account_id: string
        }[]
      }
    }
  }

  if (!res.data.success) {
    throw new BadRequestException(`[小红书官方]:${res.data.msg}`)
  }

  return res.data.data
}
