import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'

class TeamResponse {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  name: string

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  teamId: string

  @ApiResponseProperty({
    type: Number,
    example: **********
  })
  createTime: number

  @ApiResponseProperty({
    type: Number,
    example: '12 团队账号数量上线'
  })
  platformAccountNumberLimit: number

  @ApiResponseProperty({
    type: Number,
    example: '20 团队人数上限'
  })
  teamMemberNumberLimit: number

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  isVip: boolean

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  hasRefund: boolean

  @ApiResponseProperty({
    type: Number,
    example: ********
  })
  expirationTime: number
}

export class AdminTeamQueryDTO {
  @ApiProperty({
    type: String,
    example: '1390000',
    required: false
  })
  phone: string

  @ApiProperty({
    type: String,
    example: 'HTgit4',
    required: false
  })
  invitationCode: string

  @ApiProperty({
    type: Number,
    example: 1,
    required: false
  })
  page: number

  @ApiProperty({
    type: Number,
    example: 10,
    required: false
  })
  size: number

  @ApiProperty({
    type: String,
    example: '***********',
    required: false
  })
  name: string

  @ApiProperty({
    type: Number,
    example: 'Date 注册开始时间',
    required: false
  })
  createStartTime: number

  @ApiProperty({
    type: Number,
    example: 'Date 注册结束时间',
    required: false
  })
  createEndTime: number

  @ApiProperty({
    type: Boolean,
    example: 'bool 是否是vip',
    required: false
  })
  isVip: boolean

  @ApiProperty({
    type: Number,
    example: 'Date vip过期开始时间',
    required: false
  })
  vipExpirationStartTime: number

  @ApiProperty({
    type: Number,
    example: 'Date vip过期结束时间',
    required: false
  })
  vipExpirationEndTime: number

  @ApiProperty({
    type: String,
    example: 'platformAccounts.asc,members.desc',
    required: false
  })
  sort: string

  @ApiProperty({
    type: String,
    example: '销售类型',
    required: false
  })
  salesType: string
}

export class TeamResponseByPhone {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '**********'
  })
  avatar: string

  @ApiResponseProperty({
    type: String,
    example: '**********'
  })
  invitationCode: string

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  ownerId: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  createTime: number
}

export class TeamResponseByPhoneDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamResponseByPhone]
  })
  data: [TeamResponseByPhone]
}

export class TeamOrderResponse {
  @ApiResponseProperty({
    type: Number
  })
  price: number

  @ApiResponseProperty({
    type: Number
  })
  month: number

  @ApiResponseProperty({
    type: Number
  })
  freeMonth: number

  @ApiResponseProperty({
    type: Number
  })
  interestCount: number

  @ApiResponseProperty({
    type: Date
  })
  createTime: Date

  @ApiResponseProperty({
    type: Date
  })
  expirationTime: Date

  @ApiResponseProperty({
    type: Number
  })
  remainingDay: number

  @ApiResponseProperty({
    type: Number
  })
  refundPrice: number

  @ApiResponseProperty({
    type: Number
  })
  availableRefundPrice: number

  @ApiResponseProperty({
    type: Number
  })
  balance: number

  @ApiResponseProperty({
    type: Number
  })
  id: number

  @ApiResponseProperty({
    type: Boolean
  })
  hasPendingOrder: boolean

  @ApiResponseProperty({
    type: Boolean
  })
  hasVip: boolean

  @ApiResponseProperty({
    type: String
  })
  name: string

  @ApiResponseProperty({
    type: String
  })
  avatar: string
}

export class TeamOrderResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TeamOrderResponse
  })
  data: TeamOrderResponse
}

export class TeamResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamResponse]
  })
  data: TeamResponse[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  totalPage: number
}

export class TeamMessage {
  @ApiResponseProperty({
    type: Number
  })
  teamId: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  uploadImageCount: 1

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  commentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  groupCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  singleCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  manageGroupCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  autoCommentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 20
  })
  autoGroupCount: number

  @ApiResponseProperty({
    type: Number,
    example: 20
  })
  autoSingleCount: number

  @ApiResponseProperty({
    type: String,
    example: '2024-06-04'
  })
  createTime: string
}

export class TeamMessageDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamMessage]
  })
  data: TeamMessage[]
}

export class TeamDau {
  @ApiResponseProperty({
    type: String
  })
  date: string

  @ApiResponseProperty({
    type: Number
  })
  count: number
}

export class TeamDauRespones {
  @ApiResponseProperty({
    type: [TeamDau]
  })
  list: TeamDau[]
}

export class TeamDauResponesDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TeamDauRespones
  })
  data: TeamDauRespones
}

class TeamOrderRecordResponse {
  @ApiResponseProperty({
    type: Number
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'create(开通VIP),upgrade(升级/续费),diff(补差),refund(退费),expire(到期)'
  })
  type: string

  @ApiResponseProperty({
    type: Number,
    example: '100,-100.负数为支出'
  })
  price: number
}

export class TeamOrderRecordResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamOrderRecordResponse]
  })
  data: TeamOrderRecordResponse[]
}

export class OrderRefund {
  @ApiResponseProperty({
    type: String,
    example: '订单编号'
  })
  orderNo: string

  @ApiResponseProperty({
    type: Number,
    example: '付款金额'
  })
  refundAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '可退款金额'
  })
  actualRefundAmount: number
}

class TeamRefundRecordResponse {
  @ApiResponseProperty({
    type: Number
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '退款编号'
  })
  refundNo: string

  @ApiResponseProperty({
    type: Number,
    example: '退款金额'
  })
  refundAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '实际退款金额'
  })
  actualRefundAmount: number

  @ApiResponseProperty({
    type: String,
    example: '备注'
  })
  remark: string

  @ApiResponseProperty({
    type: [OrderRefund],
    example: '100,-100.负数为支出'
  })
  orderInfo: OrderRefund[]
}

export class TeamRefundResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamRefundRecordResponse]
  })
  data: TeamRefundRecordResponse[]
}

export class RefundOrderRequestBodyDTO {
  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  refundAmount: number

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  realityPrice: number

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}
