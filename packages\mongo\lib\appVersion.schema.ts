import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: false,
  versionKey: false
})
export class AppVersionEntity {
  @Prop({
    type: Object,
    required: true
  })
  user: Object

  /**
   * 版本号
   */
  @Prop({
    type: String,
    required: true
  })
  version: string

  /**
   * 发布时间
   */
  @Prop({
    type: Number,
    required: true
  })
  releaseTime: number

  /**
   * 发布公告
   */
  @Prop({
    type: String,
    required: true
  })
  desc: string

  /**
   * 强制更新
   */
  @Prop({
    type: Boolean,
    default: false
  })
  force: boolean

  /**
   * 下载链接
   */
  @Prop({
    type: String,
    required: true
  })
  url: string

  @Prop({
    type: String,
    required: true
  })
  type: string
}

export const AppVersionSchema: ModelDefinition = {
  name: AppVersionEntity.name,
  schema: SchemaFactory.createForClass(AppVersionEntity).index(
    { version: 1, type: 1 },
    { unique: true }
  )
}

export const AppVersionMongoose = MongooseModule.forFeature([AppVersionSchema])
