import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { InvitationService } from './invitation.service'
import {
  InvitationActionRequestBodyDTO,
  InvitationActionResponseDTO,
  InvitationQueryDTO,
  InvitationResponseDTO,
  InvitationStatusResponseDTO
} from './invitation.dto'
import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseRequestDTO'

@Controller('invatations')
@ApiTags('消息通知')
export class InvitationController {
  constructor(private readonly invitationService: InvitationService) {}

  @Get()
  @ApiOperation({ summary: '获取邀请列表' })
  @ApiOkResponse({ description: '操作成功', type: InvitationResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({
    type: InvitationQueryDTO,
    required: false
  })
  @ApiQuery({
    name: 'createTime',
    required: false,
    type: Number,
    description: '按时间戳查询'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getInvitations(
    @Query('createTime') createTime: number,
    @Query('size') size: number,
    @Query('page') page: number
  ) {
    const querys = {
      createTime,
      page,
      size
    }

    if (!querys.createTime) {
      delete querys.createTime
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    const response = await this.invitationService.getInvitation(querys)
    return response
  }

  @Post('action')
  @ApiOperation({ summary: '邀请确认' })
  @ApiOkResponse({ type: InvitationActionResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiForbiddenResponse({ description: '用户已加入团队', type: BaseForbiddenResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async invitationAction(@Body() body: InvitationActionRequestBodyDTO) {
    const response = await this.invitationService.invitationAction(body)
    return response
  }

  @Get(`:phone/status`)
  @ApiOperation({ summary: '根据手机号获取邀请状态' })
  @ApiOkResponse({ type: InvitationStatusResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async allowInvitation(@Param('phone') phone: string) {
    const response = await this.invitationService.allowInvitation(phone)
    return response
  }
}
