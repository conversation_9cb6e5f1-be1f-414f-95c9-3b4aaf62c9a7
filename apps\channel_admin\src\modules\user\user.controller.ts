import { Body, Controller, Delete, Post, Get } from '@nestjs/common'
import { UserService } from './user.service'

import {
  ApiForbiddenResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader
} from '@nestjs/swagger'
import {
  UserDeleteResponseDTO,
  UserLoginOkResponseDTO,
  UserLoginRegisterRequestBodyDTO,
  UserOkUserInfoResponseDTO
} from './user.dto'

import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseRequestDTO'

@Controller('users')
@ApiTags('渠道用户管理')
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * 登录
   * @param data
   */
  @Post('auth')
  @ApiOperation({ summary: '用户登录' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '验证码无效', type: BaseBadRequestDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  async registerUser(@Body() data: UserLoginRegisterRequestBodyDTO) {
    const response = await this.userService.putLoginUser(data)
    return response
  }

  /**
   * 退出登录
   */
  @Delete('auth')
  @ApiOperation({ summary: '退出登录' })
  @ApiOkResponse({ description: '操作成功', type: UserDeleteResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async loginOut() {
    return this.userService.deleteAuthorization()
  }

  @Get('info')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiOkResponse({ type: UserOkUserInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getUserInfo() {
    return this.userService.getUserInfo()
  }
}
