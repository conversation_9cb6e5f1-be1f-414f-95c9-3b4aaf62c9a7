import { Module } from '@nestjs/common'
import { OrderService } from './order.service'
import { OrderController } from './order.controller'
import { OrderEventService } from './order.event'
import { OrderManageModule, TlsManageModule } from '@qdy/common'
import { OrderAppService } from './order.service.app'

@Module({
  imports: [OrderManageModule, TlsManageModule],
  providers: [OrderService, OrderEventService, OrderAppService],
  controllers: [OrderController]
})
export class OrderModule {}
