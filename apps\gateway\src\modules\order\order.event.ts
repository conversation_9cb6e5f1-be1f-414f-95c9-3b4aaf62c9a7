import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { OrderStatus } from './order.dto'
import dayjs from 'dayjs'

export const eventKey = 'update-order'

export const orderEventEmitter = new EventEmitter()

@Injectable()
export class OrderEventService implements OnModuleInit {
  logger = new Logger('OrderEventService')

  constructor(private readonly prisma: PrismaService) {}

  taskQueue: Queue

  taskWorker: Worker

  onModuleInit() {
    orderEventEmitter.on(eventKey, this.createUpdateOrderTask.bind(this))

    this.taskQueue = new Queue('order-event', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'order-event',
      async (job) => {
        const { orderNo } = job.data
        await this.updateOrder(orderNo)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // this.migrate()
  }

  async createUpdateOrderTask({ orderNo, type }: { orderNo: string; type: 'create' | 'close' }) {
    switch (type) {
      case 'create':
        await this.taskQueue.add(
          'update-order',
          { orderNo },
          {
            delay: 2 * 60 * 60 * 1000,
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `${orderNo}-order-task`
          }
        )
        break
      case 'close':
        ;(await this.taskQueue.getJob(`${orderNo}-order-task`)).remove()
        break
      default:
        break
    }
  }

  async updateOrder(orderNo: string) {
    try {
      const oldOrder = await this.prisma.order.findUnique({
        where: {
          orderNo
        }
      })

      if (oldOrder && oldOrder.orderStatus === OrderStatus.PENDING) {
        await this.prisma.order.update({
          where: {
            orderNo
          },
          data: {
            orderStatus: OrderStatus.CANCELED
          }
        })
      }
    } catch (error) {
      this.logger.error(error)
    }
  }

  async migrate() {
    const teams = await this.prisma.team.findMany({
      where: {
        vip: {
          expirationTime: {
            gt: new Date()
          }
        }
      },
      include: {
        vip: true
      }
    })

    this.logger.debug('待迁移的团队数量: ' + teams.length)

    for (let i = 0; i < teams.length; i++) {
      const team = teams[i]

      const order = await this.prisma.order.findFirst({
        where: {
          teamId: team.id,
          orderStatus: OrderStatus.SUCCESS
        },
        orderBy: {
          fromTime: 'desc'
        },
        include: {
          vip: true
        }
      })

      if (order) {
        const startTime = dayjs(order.vip.expirationTime)
        const endTime = startTime.subtract(order.freeMonth, 'month')
        const totalTime = endTime.subtract(order.vipMonth, 'month')

        const diffDay = totalTime.diff(endTime, 'day')
        const freeDay = endTime.diff(startTime, 'day')

        await this.prisma.vip.update({
          where: {
            teamId: team.id
          },
          data: {
            lastOrderNo: order.orderNo,
            day: Math.abs(diffDay),
            freeDay: Math.abs(freeDay),
            month: order.vipMonth,
            freeMonth: order.freeMonth,
            price: order.payAmount || order.dueAmount,
            interestCount: order.interestCount
          }
        })
      }
    }

    this.logger.log('migrate success')
  }
}
