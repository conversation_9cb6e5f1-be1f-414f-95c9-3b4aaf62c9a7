import { Body, Controller, Delete, Post, Get, Query, Param, Put } from '@nestjs/common'
import { UserService } from './user.service'

import {
  ApiForbiddenResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiQuery
} from '@nestjs/swagger'
import {
  BindMfaRequestDTO,
  UserDeleteResponseDTO,
  UserLoginOkResponseDTO,
  UserLoginRegisterRequestBodyDTO,
  UserOkUserInfoResponseDTO,
  UserRegisterRequestBodyDTO
} from './user.dto'

import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseRequestDTO'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('users')
@ApiTags('用户管理')
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * 登录
   * @param data
   */
  @Post('auth')
  @ApiOperation({ summary: '用户登录' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '验证码无效', type: BaseBadRequestDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  async registerUser(@Body() data: UserLoginRegisterRequestBodyDTO) {
    const response = await this.userService.putLoginUser(data)
    return response
  }

  /**
   * 退出登录
   */
  @Delete('auth')
  @ApiOperation({ summary: '退出登录' })
  @ApiOkResponse({ description: '操作成功', type: UserDeleteResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async loginOut() {
    return this.userService.deleteAuthorization()
  }

  @Get('info')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiOkResponse({ type: UserOkUserInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getUserInfo() {
    return this.userService.getUserInfo()
  }

  @Get()
  @ApiOperation({ summary: '获取管理员列表' })
  @ApiOkResponse({ type: UserOkUserInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码 <默认 1>'
  })
  @ApiQuery({
    name: 'size',
    required: false,
    type: Number,
    description: '每页数量 <默认 10>'
  })
  async getAdminUser(
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number
  ) {
    return this.userService.getAdminUsers(page, size)
  }

  @Post()
  @ApiOperation({ summary: '创建管理员' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async postAdminUser(@Body() data: UserRegisterRequestBodyDTO) {
    return this.userService.postAdminUsers(data)
  }

  @Put(':usersId')
  @ApiOperation({ summary: '修改管理员信息' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async patchAdminUser(
    @Param('usersId') usersId: number,
    @Body() data: UserRegisterRequestBodyDTO
  ) {
    return this.userService.patchAdminUsers(usersId, data)
  }

  @Delete(':usersId')
  @ApiOperation({ summary: '删除管理员' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async deleteAdminUser(@Param('usersId') usersId: number) {
    return this.userService.deleteAdminUsers(usersId)
  }

  @Post(`two/factor/auth`)
  @ApiOperation({ summary: '二次验证mfa' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async verifyMfa(@Body() data: BindMfaRequestDTO) {
    return this.userService.verifyMfa(data)
  }
}
