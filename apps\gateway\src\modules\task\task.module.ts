import { Module } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'
import { TasksService } from './task.service'
import { OverviewCronService } from './overview.cron.service'
import {
  CommentMongoose,
  MessagesMongoose,
  OverviewMongoose,
  WechatMessagesMongoose,
  WechatCommentMongoose,
  MessageStatisticsMongoose,
  WechatOpusMongoose,
  PersonalChatMessagesMongoose,
  WorkCommentMongoose,
  DailyMessageStatisticMongoose,
  DailyOverviewMongoose
} from '@qdy/mongo'
import { MessageCronService } from './message.cron.service'
import { WechantAccountOpusCronService } from './wechatAccountOpus.cron.service'
import { WechantAccountCheckCronService } from './wechatAccountCheck.cron.service'
import { DatadTransferService } from './dataTransfer.service'
import { AutoresponderManageModule, TlsManageModule } from '@qdy/common'

@Module({
  imports: [
    OverviewMongoose,
    CommentMongoose,
    MessagesMongoose,
    WechatMessagesMongoose,
    WechatCommentMongoose,
    MessageStatisticsMongoose,
    WechatOpusMongoose,
    PersonalChatMessagesMongoose,
    WorkCommentMongoose,
    DailyMessageStatisticMongoose,
    TlsManageModule,
    AutoresponderManageModule,
    DailyOverviewMongoose,
    ScheduleModule.forRoot()
  ],
  controllers: [],
  providers: [
    TasksService,
    OverviewCronService,
    MessageCronService,
    WechantAccountOpusCronService,
    WechantAccountCheckCronService,
    DatadTransferService
  ]
})
export class TaskModule {}
