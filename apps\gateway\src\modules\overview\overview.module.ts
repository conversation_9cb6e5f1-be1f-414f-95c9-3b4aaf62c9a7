import { Module } from '@nestjs/common'
import { OverviewService } from './overview.service'
import { OverviewController } from './overview.controller'
import {
  MessagesMongoose,
  MessageStatisticsMongoose,
  OverviewMongoose,
  DailyOverviewMongoose,
  UsageMongoose,
  WechatMessagesMongoose,
  DailyMessageStatisticMongoose
} from '@qdy/mongo'
import { OverviewEventService } from './overview.event'
import { TlsManageModule } from '@qdy/common'

@Module({
  imports: [
    OverviewMongoose,
    UsageMongoose,
    MessagesMongoose,
    WechatMessagesMongoose,
    MessageStatisticsMongoose,
    DailyOverviewMongoose,
    DailyMessageStatisticMongoose,
    TlsManageModule
  ],
  providers: [OverviewService, OverviewEventService],
  controllers: [OverviewController]
})
export class OverviewModule {}
