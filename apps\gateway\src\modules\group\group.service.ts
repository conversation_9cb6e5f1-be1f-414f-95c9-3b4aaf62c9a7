import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { PatchGroupsRequest, PostGroupsRequest } from './group.dto'
import { Prisma, PrismaService } from '@qdy/mysql'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { TeamMemberRole } from '../team/team.dto'

@Injectable()
export class GroupsService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 添加分组
   * @param body
   * @returns
   */
  async postGroups(body: PostGroupsRequest) {
    const { user } = this.request

    const isManager = await this.checkAdminPermission(user.id, user.currentTeamId)
    // 当前用户团队成员权限小于目标成员权限,则无权限删除
    if (!isManager) {
      throw new ForbiddenException('管理员才能添加分组')
    }

    const group = await this.prisma.group.findFirst({
      where: {
        name: body.name,
        teamId: user.currentTeamId
      }
    })

    if (group) {
      throw new ForbiddenException('分组已存在')
    }

    await this.prisma.group.create({
      data: {
        name: body.name,
        teamId: user.currentTeamId
      }
    })
  }

  /**
   * 分组列表
   * @param name
   * @param page
   * @param size
   * @returns
   */
  async getGroups(name: string, page: number, size: number) {
    const { user } = this.request

    const whereDiction: Prisma.GroupWhereInput = { teamId: user.currentTeamId }

    if (name) {
      whereDiction.name = { contains: name }
    }
    const total = await this.prisma.group.count({
      where: whereDiction
    })

    const groups = await this.prisma.group.findMany({
      where: whereDiction,
      include: {
        platformAccounts: {
          select: {
            id: true
          }
        }
      },
      orderBy: {
        createTime: 'desc'
      },
      skip: (page - 1) * size,
      take: size
    })

    return {
      total,
      page,
      size,
      data: groups.map((item) => ({
        id: item.id,
        name: item.name,
        platformAccounts: item.platformAccounts.map((account) => account.id),
        createTime: item.createTime.getTime()
      }))
    }
  }

  /**
   * 修改分组
   * @param groupId
   * @param body
   */
  async patchGroups(groupId: number, body: PatchGroupsRequest) {
    const { user } = this.request

    const isManager = await this.checkAdminPermission(user.id, user.currentTeamId)

    // 当前用户团队成员权限小于目标成员权限,则无权限删除
    if (!isManager) {
      throw new ForbiddenException('管理员才能修改分组')
    }

    const group = await this.prisma.group.findUnique({
      where: {
        id: groupId,
        teamId: user.currentTeamId
      },
      include: {
        platformAccounts: true
      }
    })

    if (!group) {
      throw new NotFoundException('分组未找到')
    }

    const updateData: Prisma.GroupUpdateInput = {}
    if (body.name) {
      updateData.name = body.name
    }

    if (body.accounts) {
      updateData.platformAccounts = { set: body.accounts.map((id) => ({ id })) }
    }

    if (updateData) {
      await this.prisma.group.update({
        where: {
          id: groupId,
          teamId: user.currentTeamId
        },
        data: updateData
      })
    }
  }

  /**
   * 删除分组
   * @param groupsId
   */
  async deleteGroups(groupId: number) {
    const { user } = this.request

    const isManager = await this.checkAdminPermission(user.id, user.currentTeamId)
    // 当前用户团队成员权限小于目标成员权限,则无权限删除
    if (!isManager) {
      throw new ForbiddenException('管理员才能删除分组')
    }
    const group = await this.prisma.group.findUnique({
      where: {
        id: groupId,
        teamId: user.currentTeamId
      }
    })

    if (!group) {
      throw new NotFoundException('分组未找到')
    }

    await this.prisma.group.delete({
      where: {
        id: groupId,
        teamId: user.currentTeamId
      }
    })
  }

  /**
   * 检查是否有管理员权限
   * @param userId
   * @param teamId
   * @returns
   */
  async checkAdminPermission(userId: number, teamId: number) {
    // 当前用户权限
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        team: true
      }
    })

    return teamMember.role !== TeamMemberRole.Member
  }
}
