import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class DailyMessageStatisticEntity {
  // 平台类型
  @Prop({
    type: String,
    default: 'douyin'
  })
  platformType: string

  // 团队ID
  @Prop({
    type: Number,
    required: true,
    index: true
  })
  teamId: number

  // 账号唯一ID
  @Prop({
    type: String,
    required: false,
    index: true
  })
  openId: string

  // 日期
  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  // 接收信息数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  receiveMessageCount: number

  // 手动回复私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  singleCount: number

  // 自动回复私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoSingleCount: number

  // 手动回复评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  commentCount: number

  // 自动回复评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoCommentCount: number

  // 手动回复群发数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  groupCount: number

  //评论人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  commentPeopleCount: number

  //私信人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  singlePeopleCount: number
}

export const DailyMessageStatisticSchema: ModelDefinition = {
  name: DailyMessageStatisticEntity.name,
  schema: SchemaFactory.createForClass(DailyMessageStatisticEntity)
    .index({
      teamId: 1,
      createTime: -1
    })
    .index(
      {
        teamId: 1,
        createTime: 1,
        openId: 1
      },
      { unique: true }
    )
}

export const DailyMessageStatisticMongoose = MongooseModule.forFeature([
  DailyMessageStatisticSchema
])
