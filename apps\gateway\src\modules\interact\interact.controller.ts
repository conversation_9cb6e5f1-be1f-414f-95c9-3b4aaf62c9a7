import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Put,
  Param,
  Query,
  Req,
  BadRequestException
} from '@nestjs/common'
import {
  ApiOperation,
  ApiTags,
  ApiOkResponse,
  ApiParam,
  ApiForbiddenResponse,
  ApiBadRequestResponse,
  ApiHeader,
  ApiQuery,
  ApiBody,
  ApiConsumes,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  DownloadMediaRequestDTO,
  InteractGroupCreateDTO,
  InteractGroupResponesDTO,
  InteractRequestAuditDTO,
  InteractRequestCommentMessage,
  InteractRequestCommentMessageWeChatDTO,
  InteractRequestSendMessage,
  InteractRequestSessionConfigDTO,
  InteractRequestSpeech,
  InteractRequestSpeechCategory,
  InteractRequestTopComment,
  InteractRequestVideoDetailDTO,
  InteractResponseHistoryCommentDTO,
  InteractResponseHistoryMessagesDTO,
  InteractResponseSessionConfigsDTO,
  InteractResponseUploadImage,
  KuaishouMessagesRequestDTO,
  MessageUserRequestDTO,
  MessageUserResponseDTO,
  RecallMessageRequestDTO,
  SpeechCategoriesResponseDTO,
  SpeechsResponseDTO,
  UserMessageReadRecordRequestDTO,
  UserMessageReadRecordResponseDTO,
  WechatCommentReplyRequestDTO,
  WechatCommentRequestDTO,
  WechatDeleteCommentRequestDTO,
  WechatMessagesRequestDTO,
  WechatVideoResponseDTO,
  WeiboMessagesRequestDTO,
  XiaohongshuMessagesRequestDTO
} from './interact.dto'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { InteractService } from './interact.service'
import { FastifyRequest } from 'fastify'
import { InteractWechatService } from './interact.service.wechat'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { InteractKuaishouService } from './interact.service.kuaishou'
import { InteractWeiboService } from './interact.service.weibo'
import { InteractXiaohongshuService } from './interact.service.xiaohongshu'

@Controller('interact')
@ApiTags('互动管理')
export class InteractController {
  constructor(
    private readonly interactService: InteractService,
    private readonly interactWechatService: InteractWechatService,
    private readonly interactKuaishouService: InteractKuaishouService,
    private readonly interactWeiboService: InteractWeiboService,
    private readonly interactXiaohongshuService: InteractXiaohongshuService
  ) {}

  @Get('platform-accounts/:platformAccountId/fan-groups')
  @ApiOperation({ summary: '根据账号获取粉丝群' })
  @ApiOkResponse({ type: InteractGroupResponesDTO })
  @ApiQuery({
    name: 'autoresponder',
    required: false,
    type: Number,
    description: '是否包含欢迎语关系'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getGroupsByAccountId(
    @Param('platformAccountId') platformAccountId: number,
    @Query('autoresponder') autoresponder: number
  ) {
    return this.interactService.getGroupsByAccountId(platformAccountId, !!autoresponder)
  }

  @Post('platform-accounts/:platformAccountId/fan-groups')
  @ApiOperation({ summary: '根据账号创建粉丝群' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createGroupByAccountId(
    @Param('platformAccountId') platformAccountId: number,
    @Body() body: InteractGroupCreateDTO
  ) {
    return this.interactService.createGroupByAccountId(platformAccountId, body)
  }

  @Post('sessions/messages')
  @ApiOperation({ summary: '发送消息' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiForbiddenResponse({ description: '参数错误' })
  @ApiBadRequestResponse({ description: '回复失败' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  sendSingleByAccountId(@Body() body: InteractRequestSendMessage) {
    return this.interactService.sendMessage(body)
  }

  @Post('sessions/comments')
  @ApiOperation({ summary: '回复评论' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiForbiddenResponse({ description: '参数错误' })
  @ApiBadRequestResponse({ description: '回复失败' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  replyCommentByAccountIdAndCommentId(@Body() body: InteractRequestCommentMessage) {
    return this.interactService.sendReplyMessage(body)
  }

  @Patch('sessions/comments')
  @ApiOperation({ summary: '置顶/取消置顶评论' })
  @ApiForbiddenResponse({ description: '参数错误' })
  @ApiBadRequestResponse({ description: '回复失败' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateCommentByAccountIdAndCommentId(@Body() body: InteractRequestTopComment) {
    return this.interactService.updateCommentTop(body)
  }

  @Get('platform-accounts/sessions/config')
  @ApiOperation({ summary: '获取会话配置' })
  @ApiOkResponse({ type: InteractResponseSessionConfigsDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getSessionConfig() {
    return this.interactService.getSessionConfig()
  }

  @Patch('platform-accounts/session/config')
  @ApiOperation({ summary: '修改会话配置' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateSessionConfig(@Body() body: InteractRequestSessionConfigDTO) {
    return this.interactService.updateSessionConfig(body)
  }

  @Get('history-messages')
  @ApiOperation({ summary: '获取历史消息' })
  @ApiOkResponse({ type: InteractResponseHistoryMessagesDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'createTime', required: false, type: Number, description: '时间戳' })
  getHistoryMessages(
    @Query('createTime', {
      transform(value) {
        return value || 0
      }
    })
    createTime: number,
    @Query('platformAccountId', {
      transform(value) {
        return value
      }
    })
    platformAccountId?: number
  ) {
    return this.interactService.getHistoryMessages(createTime, platformAccountId)
  }

  @Get('history-messages-v2')
  @ApiOperation({ summary: '获取历史消息' })
  @ApiOkResponse({ type: InteractResponseHistoryMessagesDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'createTime', required: false, type: Number, description: '时间戳' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  getHistoryMessagesV2(
    @Query('page', {
      transform(value) {
        return value || 1
      }
    })
    page: number,
    @Query('size', {
      transform(value) {
        return value || 10
      }
    })
    size: number,
    @Query('createTime', {
      transform(value) {
        return value || 0
      }
    })
    createTime: number,
    @Query('platformAccountId', {
      transform(value) {
        return value
      }
    })
    platformAccountId?: number
  ) {
    return this.interactService.getHistoryMessagesV2(createTime, platformAccountId, page, size)
  }

  @Get('platform-accounts/:platformAccountId/session/history-messages')
  @ApiOperation({ summary: '获取单个会话历史消息' })
  @ApiOkResponse({ type: InteractResponseHistoryMessagesDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiParam({ name: 'platformAccountId', required: true, type: Number, description: '平台账号ID' })
  @ApiQuery({ name: 'sessionId', required: true, type: String, description: 'sessionId' })
  @ApiQuery({ name: 'createTime', required: true, type: Number, description: '时间戳' })
  getSingleHistoryMessages(
    @Param('platformAccountId') platformAccountId: number,
    @Query('sessionId') sessionId: string,
    @Query('createTime', {
      transform(value) {
        return value || 0
      }
    })
    createTime: number
  ) {
    return this.interactService.getSingleHistoryMessages({
      platformAccountId,
      sessionId,
      createTime
    })
  }

  @Get('history-comments')
  @ApiOperation({ summary: '获取历史评论' })
  @ApiOkResponse({ type: InteractResponseHistoryCommentDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'createTime', required: false, type: Number, description: '时间戳' })
  @ApiQuery({ name: 'platformAccountId', required: false, type: Number, description: '平台账号ID' })
  getHistoryComments(
    @Query('createTime', {
      transform(value) {
        return value || 0
      }
    })
    createTime: number,
    @Query('platformAccountId', {
      transform(value) {
        return value
      }
    })
    platformAccountId?: number
  ) {
    return this.interactService.getHistoryComments(createTime, platformAccountId)
  }

  @Get('platform-accounts/:platformAccountId/session/history-comments')
  @ApiOperation({ summary: '获取单个会话历史评论' })
  @ApiOkResponse({ type: InteractResponseHistoryCommentDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiParam({ name: 'platformAccountId', required: true, type: Number, description: '平台账号ID' })
  @ApiQuery({ name: 'sessionId', required: true, type: String, description: '群ID/私信ID/作品ID' })
  @ApiQuery({ name: 'createTime', required: true, type: Number, description: '时间戳' })
  getSingleHistoryComments(
    @Param('platformAccountId') platformAccountId: number,
    @Query('sessionId') sessionId: string,
    @Query('createTime', {
      transform(value) {
        return value || 0
      }
    })
    createTime: number
  ) {
    return this.interactService.getSingleHistoryComments({
      platformAccountId,
      sessionId,
      createTime
    })
  }

  @Get('/speechCategories')
  @ApiOperation({ summary: '获取话术分类列表' })
  @ApiOkResponse({ description: '操作成功', type: SpeechCategoriesResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getSpeechCategorys() {
    return this.interactService.getSpeechCategories()
  }

  @Post('/speechCategory')
  @ApiOperation({ summary: '创建话术分类' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createSpeechCategory(@Body() body: InteractRequestSpeechCategory) {
    return this.interactService.createSpeechCategory(body)
  }

  @Put('/speechCategory/:speechCategoryId')
  @ApiOperation({ summary: '修改话术分类' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateSpeechCategory(
    @Param('speechCategoryId') speechId: number,
    @Body() { name }: InteractRequestSpeechCategory
  ) {
    return this.interactService.updateSpeechCategory(speechId, name)
  }

  @Delete('/speechCategory/:speechCategoryId')
  @ApiOperation({ summary: '删除话术分类' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  deleteSpeechCategory(@Param('speechCategoryId') speechCategoryId: number) {
    return this.interactService.deleteSpeechCategory(speechCategoryId)
  }

  @Post('/speechs')
  @ApiOperation({ summary: '创建话术' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createSpeechs(@Body() body: InteractRequestSpeech) {
    return this.interactService.createSpeechs(body)
  }

  @Put('/speechs/:speechId')
  @ApiOperation({ summary: '修改话术' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateSpeechs(
    @Param('speechId') speechId: number,
    @Body() { content, speechCategoryId }: InteractRequestSpeech
  ) {
    return this.interactService.updateSpeech(speechId, content, speechCategoryId)
  }

  @Get('/speechs')
  @ApiOperation({ summary: '获取话术列表' })
  @ApiOkResponse({ description: '操作成功', type: SpeechsResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'speechType',
    required: true,
    type: Number,
    description: '话术类型'
  })
  @ApiQuery({
    name: 'content',
    required: false,
    type: String,
    description: '按帐户名称搜索'
  })
  @ApiQuery({
    name: 'speechCategoryId',
    required: false,
    type: Number,
    description: '分类ID'
  })
  getSpeechs(
    @Query('size') size: number,
    @Query('page') page: number,
    @Query('speechType') speechType: number,
    @Query('content') content: string,
    @Query('speechCategoryId') speechCategoryId: number
  ) {
    const querys = {
      speechType,
      content,
      page,
      size,
      speechCategoryId
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    if (querys.speechCategoryId === undefined) {
      delete querys.speechCategoryId
    }

    return this.interactService.getSpeechs(querys)
  }

  @Delete('/speechs/:speechId')
  @ApiOperation({ summary: '删除话术' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  deleteSpeechs(@Param('speechId') speechId: number) {
    return this.interactService.deleteSpeech(speechId)
  }

  @ApiOperation({ summary: '根据账号获取视频列表' })
  @ApiQuery({ name: 'cursor', required: false, type: Number, description: '游标 <默认 0>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @Get('/platform-accounts/:platformAccountId/opus')
  getVideoList(
    @Query('size', {
      transform(value) {
        return value || 10
      }
    })
    size: number,
    @Query('cursor', {
      transform(value) {
        return value || 0
      }
    })
    cursor: number,
    @Param('platformAccountId') platformAccountId: number
  ) {
    return this.interactService.getVideoList(platformAccountId, { cursor, size })
  }

  @ApiOperation({ summary: '图片消息上传图片' })
  @Post('/platform-accounts/upload-image')
  @ApiOkResponse({ type: InteractResponseUploadImage })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  async uploadImage(@Req() req: FastifyRequest) {
    const data = await req.file()
    const fileBuffer = await data.toBuffer()
    const reg = /\.(jpeg|png|bmp|webp)$/i

    if (!data.filename || !reg.test(data.filename)) {
      throw new BadRequestException('图片格式错误')
    }

    if (fileBuffer.byteLength > 1024 * 1024 * 10) {
      throw new BadRequestException('图片大小不能超过10M')
    }

    return this.interactService.uploadImage(fileBuffer, data.filename)
  }

  @ApiOperation({ summary: '查询特定视频信息' })
  @Post('/platform-accounts/:platformAccountId/video-detail')
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getVideoDetail(
    @Param('platformAccountId') platformAccountId: number,
    @Body() body: InteractRequestVideoDetailDTO
  ) {
    return this.interactService.getVideoDetail({ ...body, platformAccountId })
  }

  @ApiOperation({ summary: '变更用户入群申请状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @Post('/platform-accounts/:platformAccountId/audit-fans')
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateJoinGroupState(
    @Param('platformAccountId') platformAccountId: number,
    @Body() body: InteractRequestAuditDTO
  ) {
    return this.interactService.updateJoinGroupState(platformAccountId, body)
  }

  @Get('wechat/:platformAccountId/videos')
  @ApiOperation({ summary: '视频号视频列表' })
  @ApiOkResponse({ type: WechatVideoResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  async wechatVideos(
    @Param('platformAccountId') platformAccountId: number,
    @Query('size') size: number,
    @Query('page') page: number
  ) {
    const querys = {
      platformAccountId,
      page,
      size
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    querys.platformAccountId = platformAccountId

    const response = this.interactWechatService.wechatVideos(querys)
    return response
  }

  @Post('wechat/video/comments')
  @ApiOperation({ summary: '视频号视频评论列表' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async WechatVideoComment(@Body() body: WechatCommentRequestDTO) {
    //
    return this.interactWechatService.wechatVideoComment(body)
  }

  @Post('wechat/video/newComments')
  @ApiOperation({ summary: '视频号视频评论列表' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async WechatVideoNewComment(@Body() body: WechatCommentRequestDTO) {
    //
    return this.interactWechatService.wechatVideoNewComment(body)
  }

  @Post('wechat/messages')
  @ApiOperation({ summary: '发送微信消息' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  sendWechatMessage(@Body() body: WechatMessagesRequestDTO) {
    return this.interactWechatService.sendWechatMessage(body)
  }

  @Post('wechat/comments')
  @ApiOperation({ summary: '回复微信评论' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiForbiddenResponse({ description: '参数错误' })
  @ApiBadRequestResponse({ description: '回复失败' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  replyCommentByWechat(@Body() body: InteractRequestCommentMessageWeChatDTO) {
    return this.interactWechatService.sendReplyMessage(body)
  }

  @Post('wechat/delete-comment')
  @ApiOperation({ summary: '删除微信评论' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  deleteWechatComment(@Body() body: WechatDeleteCommentRequestDTO) {
    return this.interactWechatService.removeWechatComment(body)
  }

  @Post('wechat/:id/reply')
  @ApiOperation({ summary: '微信评论回复' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  postWechatCommentReply(@Param('id') id: string, @Body() body: WechatCommentReplyRequestDTO) {
    return this.interactWechatService.postWechatCommentReply(id, body)
  }

  @Post('douyin/recall-msg')
  @ApiOperation({ summary: '抖音私信撤回' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  postRecallDouyinMessage(@Body() body: RecallMessageRequestDTO) {
    return this.interactService.postRecallMessage(body)
  }

  @Get('private-message-user')
  @ApiOperation({ summary: '私信用户详情' })
  @ApiOkResponse({ description: '操作成功', type: MessageUserResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'openId',
    required: true,
    type: String,
    description: '私信用户openid'
  })
  getMessageUser(@Query('openId') openId: string) {
    return this.interactService.getMessageUser(openId)
  }

  @Post('private-message-user')
  @ApiOperation({ summary: '创建/更新私信用户' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  postMessageUser(@Body() body: MessageUserRequestDTO) {
    return this.interactService.postMessageUser(body)
  }

  @Post('download-media')
  @ApiOperation({ summary: '视频号下载资源' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  postDownloadMedia(@Body() body: DownloadMediaRequestDTO) {
    return this.interactWechatService.downloadMedia(body)
  }

  @Post('kuaishou/messages')
  @ApiOperation({ summary: '发送快手消息' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  sendKuaishouMessage(@Body() body: KuaishouMessagesRequestDTO) {
    return this.interactKuaishouService.sendKuaishouMessage(body)
  }

  @Post('weibo/messages')
  @ApiOperation({ summary: '发送微博消息' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  sendWeiboMessage(@Body() body: WeiboMessagesRequestDTO) {
    return this.interactWeiboService.sendWeiboMessage(body)
  }

  @Post('xiaohongshu/messages')
  @ApiOperation({ summary: '发送小红书消息' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  sendXiahongshuMessage(@Body() body: XiaohongshuMessagesRequestDTO) {
    return this.interactXiaohongshuService.sendXiaohongshuMessage(body)
  }

  @Post('message/readRecord')
  @ApiOperation({ summary: '上报消息已读记录' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  postMessageReadRecord(@Body() body: UserMessageReadRecordRequestDTO, @Req() req: FastifyRequest) {
    return this.interactService.postUserMessageReadRecord(
      body,
      req.headers['device-type'] as string
    )
  }

  @Get('message/readRecord')
  @ApiOperation({ summary: '获取消息已读记录' })
  @ApiOkResponse({ description: '操作成功', type: UserMessageReadRecordResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getMessageReadRecord() {
    return this.interactService.getUserMessageReadRecord()
  }

  @Post('xiaohongshu/recall-msg')
  @ApiOperation({ summary: '小红书私信撤回' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  postRecallXiaohongshuMessage(@Body() body: RecallMessageRequestDTO) {
    return this.interactXiaohongshuService.postRevokeMessage(body)
  }
}
