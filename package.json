{"name": "qdy", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=18", "pnpm": ">=9"}, "scripts": {"prepare": "husky install", "postinstall": "pnpm run generate-mysql", "build:socket": "nest build @qdy/socket", "build:gateway": "nest build @qdy/gateway", "build:admin": "nest build @qdy/admin", "build:channel_admin": "nest build @qdy/channel_admin", "build:transfer": "nest build @qdy/transfer", "start-channel_admin:local": "cross-env NODE_ENV=dev nest start @qdy/channel_admin --debug --watch", "start-channel_admin:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/channel_admin/ecosystem.config.js", "start-channel_admin:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/channel_admin/ecosystem.config.js", "start-channel_admin:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/channel_admin/ecosystem.config.js", "start-admin:local": "cross-env NODE_ENV=dev nest start @qdy/admin --debug --watch", "start-admin:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/admin/ecosystem.config.js", "start-admin:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/admin/ecosystem.config.js", "start-admin:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/admin/ecosystem.config.js", "start-socket:local": "cross-env NODE_ENV=local nest start @qdy/socket --debug --watch", "start-socket:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/socket/ecosystem.config.js", "start-socket:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/socket/ecosystem.config.js", "start-socket:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/socket/ecosystem.config.js", "start-gateway:local": "cross-env NODE_ENV=test nest start @qdy/gateway --debug --watch", "start-gateway:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/gateway/ecosystem.config.js", "start-gateway:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/gateway/ecosystem.config.js", "start-gateway:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/gateway/ecosystem.config.js", "start-transfer:local": "cross-env NODE_ENV=dev nest start @qdy/transfer --debug --watch", "start-transfer:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/transfer/ecosystem.config.js", "start-transfer:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/transfer/ecosystem.config.js", "start-transfer:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/transfer/ecosystem.config.js", "format": "prettier --write \"apps/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typecheck": "pnpm -r --filter @qdy/socket --filter @qdy/gateway --filter @qdy/admin run typecheck", "test": "jest", "test:u": "jest -u", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/qdy/test/jest-e2e.json", "update:deps": "pnpm update --latest --interactive", "generate-mysql": "pnpm -F @qdy/mysql generate", "generate-back": "cross-env DATABASE_URL_MYSQL=mysql://root:123456@localhost:3306/qdy_dev pnpm -F @qdy/mysql back", "generate-mysql-watch": "pnpm -F @qdy/mysql generate --watch", "prisma:migrate-dev": "cross-env DATABASE_URL_MYSQL=mysql://root:123456@localhost:3306/qdy_dev prisma migrate dev --name update --schema packages/mysql/prisma/schema.prisma"}, "dependencies": {"@alicloud/captcha20230305": "^1.1.3", "@apple/app-store-server-library": "^1.4.0", "@fastify/compress": "^7.0.3", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^8.3.0", "@fastify/static": "^7.0.4", "@grpc/grpc-js": "^1.12.2", "@grpc/proto-loader": "^0.7.13", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.4.5", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.5", "@nestjs/microservices": "^10.4.5", "@nestjs/mongoose": "^10.0.10", "@nestjs/platform-express": "^10.4.5", "@nestjs/platform-fastify": "^10.4.5", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.2.1", "@prisma/client": "^5.21.0", "@volcengine/openapi": "^1.30.1", "@volcengine/tos-sdk": "^2.7.4", "ali-oss": "^6.21.0", "alipay-sdk": "^4.13.0", "aliyun-sdk": "^1.12.10", "axios": "^1.7.7", "bullmq": "^5.20.0", "cache-manager": "^5.7.6", "cache-manager-ioredis-yet": "^2.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "form-data": "^4.0.1", "ioredis": "^5.4.1", "json-bigint": "^1.0.0", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.2", "nanoid": "3.3.7", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "wechatpay-node-v3": "^2.2.1"}, "devDependencies": {"@alicloud/dysmsapi20170525": "^3.0.0", "@alicloud/openapi-client": "^0.4.12", "@alicloud/tea-typescript": "^1.8.0", "@alicloud/tea-util": "^1.4.9", "@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.2.2", "@nestjs/testing": "^10.4.5", "@swc/cli": "^0.4.0", "@swc/core": "^1.7.36", "@swc/jest": "^0.2.36", "@types/ali-oss": "^6.16.11", "@types/jest": "^29.5.13", "@types/json-bigint": "^1.0.4", "@types/node": "^20.16.12", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@volcengine/tos-sdk": "^2.7.4", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-iroot": "^1.5.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.6", "jest": "^29.7.0", "node-loader": "^2.0.0", "prettier": "^3.3.3", "prisma": "^5.21.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "swc-loader": "^0.2.6", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.6.3", "webpack": "^5.95.0"}}