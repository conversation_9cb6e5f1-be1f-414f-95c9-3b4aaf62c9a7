/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments
} from 'class-validator'

@ValidatorConstraint({ async: false })
export class TextLengthSumConstraint implements ValidatorConstraintInterface {
  pattern =
    /(https?:\/\/)([a-zA-Z0-9_]+:[a-zA-Z0-9_]+@)?([a-zA-Z0-9.-]+\.[A-Za-z]{2,4})(:[0-9]+)?(\/.*)?/g

  validate(values: any[], _: ValidationArguments) {
    const totalText = values.reduce((sum, item) => sum + (item.text || ''), '') as string
    const totalLength = totalText.length

    return totalLength <= 1000 && !this.pattern.test(totalText)
  }

  defaultMessage(_: ValidationArguments) {
    return '文字长度不能超过1000个字符/文本不能包含 URL'
  }
}

export function TextLengthSum(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: TextLengthSumConstraint
    })
  }
}
