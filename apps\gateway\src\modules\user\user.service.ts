import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import {
  BaiduOcpcRequestBodyDTO,
  SMSCodeSence,
  TeamState,
  UserInfoExchangeKeyRequestBodyDTO,
  UserLoginRegisterRequestBodyDTO,
  UserPasswordLoginRequestBodyDTO,
  UserPasswordRegisterBodyDto,
  UserPasswordResetBodyDto,
  UserPasswordUpdateBodyDto,
  UserSendCodeRequestBodyDTO,
  UserSendCodeRequestBodyDTOV2,
  UserState
} from './user.dto'
import { PlatformAccount, PrismaService, Team, TeamMember, UsersCoupons, Vip } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { customAlphabet, nanoid } from 'nanoid'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { type User } from '@qdy/mysql'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { genSocketRedisKey, Platform } from '@qdy/utils'
import { TeamMemberRole, TeamMemberStatus } from '../team/team.dto'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'

import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525'
import * as $OpenApi from '@alicloud/openapi-client'
import * as Util from '@alicloud/tea-util'
import {
  GenerateRandomAvatarUrl,
  generateRandomCode,
  GenerateRandomNickName
} from '../../common/utils'
import crypto from 'crypto'
import { TeamService } from '../team/team.service'
import { CouponsStatus } from '../coupons/coupons.dto'
import { postOcpcapi } from './external'
import { OrderManageService, TosManageService, TlsManageService } from '@qdy/common'
import { AutoresponderKeywordKey } from '../autoresponder/constant'
import { wechatLogout } from '../account/external.wechat'
import { encrypt } from '../webhook/external.xiaohongshu'
import captcha20230305, * as $captcha20230305 from '@alicloud/captcha20230305'

@Injectable()
export class UserService {
  logger = new Logger('UserService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly teamService: TeamService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly orderManageService: OrderManageService,
    private readonly TosManageService: TosManageService,
    private readonly tlsManageService: TlsManageService
  ) {}

  isPasswordCorrect(password: string, salt: string, hash: string) {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return hash === hashToVerify
  }

  hasAlphabet(password: string): boolean {
    return /[a-zA-Z]/.test(password)
  }

  hasDigit(password: string): boolean {
    return /\d/.test(password)
  }

  validatePassword(password: string) {
    return this.hasAlphabet(password) && this.hasDigit(password)
  }

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 18)

  /**
   * 用户登录
   * @param param
   * @param ip
   * @returns
   */
  async putLoginUser(
    { phone, code, invitationCode, channelCode, isAdvertisement }: UserLoginRegisterRequestBodyDTO,
    deviceType?: string,
    userAgent?: string,
    registrationId?: string
  ) {
    if (deviceType && deviceType !== 'app') {
      throw new BadRequestException('无效的设备类型')
    }

    let userInfo = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    let team: Team
    let teamMember: TeamMember & {
      platformAccounts: PlatformAccount[]
    }
    let userCoupons: UsersCoupons
    let vipInfo: Vip
    const platformInfo = this.parseUserAgent(userAgent)
    // eslint-disable-next-line prefer-destructuring
    const platform = platformInfo.platform
    this.logger.log(platform)
    this.logger.log(registrationId)

    if (userInfo) {
      if (userInfo.state === UserState.Ban) {
        throw new ForbiddenException('该账号已被禁用')
      }

      if (userInfo.currentTeamId) {
        teamMember = await this.prisma.teamMember.findUnique({
          where: {
            userId_teamId: {
              teamId: userInfo.currentTeamId,
              userId: userInfo.id
            }
          },
          include: {
            platformAccounts: true
          }
        })
      }

      if (!teamMember || teamMember.status === TeamMemberStatus.Disable) {
        teamMember = await this.prisma.teamMember.findFirst({
          where: {
            userId: userInfo.id,
            status: TeamMemberStatus.Normal
          },
          include: {
            platformAccounts: true
          },
          orderBy: {
            joinTime: 'asc'
          }
        })
      }
    }

    if (invitationCode) {
      team = await this.prisma.team.findUnique({
        where: {
          invitationCode
        }
      })

      if (!team) {
        throw new NotFoundException('团队不存在')
      }

      if (userInfo) {
        const res = await this.prisma.teamMember.findMany({
          where: {
            userId: userInfo.id
          },
          include: {
            team: true
          }
        })

        if (res.length >= 10) {
          throw new ForbiddenException('当前账号已有10个团队,请先退出/解散团队后再加')
        }

        const currentTeam = res.find((item) => item.teamId === team.id)

        if (currentTeam) {
          throw new ForbiddenException('你已在当前团队中')
        }
      }
    }

    const phoneValid = await this.cacheManager.get<string>(`${code}:${SMSCodeSence.Auth}`)

    if (!phoneValid || phoneValid !== phone) {
      throw new BadRequestException('验证码无效')
    }

    await this.cacheManager.del(`${code}:${SMSCodeSence.Auth}`)
    let isNewUser = false
    if (!userInfo) {
      isNewUser = true
      let createUserInfo = {
        phone,
        name: GenerateRandomNickName(),
        state: UserState.Normal,
        avatar: GenerateRandomAvatarUrl(),
        channelId: null,
        platform: '',
        registrationId: ''
      }

      let channelInfo

      if (channelCode) {
        channelInfo = await this.prisma.channel.findUnique({
          where: {
            code: channelCode
          },
          include: {
            coupon: true
          }
        })

        if (channelInfo && channelInfo.status) {
          createUserInfo = { ...createUserInfo, channelId: channelInfo.id }
        }
      }

      if (registrationId && deviceType === 'app') {
        createUserInfo = { ...createUserInfo, registrationId, platform }
      }

      userInfo = await this.prisma.user.create({
        data: createUserInfo
      })

      if (
        channelCode &&
        channelInfo &&
        channelInfo.status &&
        channelInfo.couponId &&
        channelInfo.coupon &&
        channelInfo.coupon.status === CouponsStatus.Normal
      ) {
        // 新增用户,如果有渠道码,则发放对应优惠券
        const result = new Date()
        result.setDate(result.getDate() + channelInfo.coupon.expireDaysNum)
        const expireDate = result
        // 插入发放记录
        userCoupons = await this.prisma.usersCoupons.create({
          data: {
            couponsId: channelInfo.coupon.id,
            userId: userInfo.id,
            expireTime: expireDate,
            minimumSpendingAmount: channelInfo.coupon.minimumSpendingAmount,
            discountAmount: channelInfo.coupon.discountAmount,
            channelId: channelInfo.id,
            creatorId: channelInfo.coupon.creatorId,
            phone: userInfo.phone,
            name: channelInfo.coupon.name
          }
        })
      }
    }

    let teamId = team?.id

    if (!teamMember) {
      const { teamId: newTeamId, vipId } = await this.teamService.createBaseTeam(
        `${userInfo.name}的团队`,
        userInfo
      )

      const { membershipDurantionDays } = this.configService.get<RootConfigMap['app']>('app')

      if (isNewUser && membershipDurantionDays > 0) {
        vipInfo = await this.giveVip(userInfo.id, newTeamId, vipId, membershipDurantionDays)
      }

      if (!teamId) {
        teamId = newTeamId
      }
    }

    if (team) {
      teamMember = await this.prisma.teamMember.create({
        data: {
          teamId,
          userId: userInfo.id,
          role: TeamMemberRole.Member
        },
        include: {
          platformAccounts: true
        }
      })
    }

    const updateInfo = {
      currentTeamId: teamId || teamMember.teamId,
      platform: platform || '',
      registrationId: registrationId || ''
    }

    if (!registrationId) {
      delete updateInfo.registrationId
      delete updateInfo.platform
    }

    this.logger.log(updateInfo)

    userInfo = await this.prisma.user.update({
      where: {
        id: userInfo.id
      },
      data: updateInfo
    })

    const authorization = await this.generateAuthorization(userInfo, deviceType)

    if (teamMember) {
      if (teamMember.role === TeamMemberRole.Member) {
        if (teamMember.platformAccounts && teamMember.platformAccounts.length) {
          const value = teamMember.platformAccounts.reduce((acc, item) => {
            acc[item.openId] = item.id
            return acc
          }, {})
          await this.cacheManager.store.client.hmset(genSocketRedisKey(authorization), value)
        }
      } else {
        const platformAccounts = await this.prisma.platformAccount.findMany({
          where: {
            teamId: teamMember.teamId
          }
        })

        if (platformAccounts && platformAccounts.length) {
          const value = platformAccounts.reduce((acc, item) => {
            acc[item.openId] = item.id
            return acc
          }, {})

          await this.cacheManager.store.client.hmset(genSocketRedisKey(authorization), value)
        }
      }
    }

    let exchangeKey = ''

    if (isAdvertisement) {
      // 投放页面注册新用户，则把用户信息缓存
      const { smsCodeTime } = this.configService.get<RootConfigMap['app']>('app')
      exchangeKey = nanoid()
      await this.cacheManager.set(
        exchangeKey,
        JSON.stringify({
          authorization,
          teamState: TeamState.Join,
          coupons: userCoupons || null,
          vipInfo: vipInfo || null,
          isNewUser
        }),
        smsCodeTime
      )
    }

    return {
      authorization: isAdvertisement && isNewUser ? '' : authorization,
      teamState: TeamState.Join,
      coupons: userCoupons || null,
      vipInfo: vipInfo || null,
      exchangeKey,
      isNewUser
    }
  }

  async agreeAuth({ phone, code }: UserLoginRegisterRequestBodyDTO) {
    const { xiaohongshuClientKey } = this.configService.get<RootConfigMap['app']>('app')
    const userInfo = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    if (!userInfo) {
      throw new ForbiddenException('该账号未注册')
    }

    if (userInfo.state === UserState.Ban) {
      throw new ForbiddenException('该账号已被禁用')
    }

    const phoneValid = await this.cacheManager.get<string>(`${code}:${SMSCodeSence.Juguang}`)

    if (!phoneValid || phoneValid !== phone) {
      throw new BadRequestException('验证码无效')
    }

    const content = {
      account_code: phone
    }

    const xiaohongshuSecret = process.env.XIAOHONGSHU_SECRET

    this.logger.log(xiaohongshuSecret)

    const token = encrypt(JSON.stringify(content), xiaohongshuSecret)

    return {
      appId: xiaohongshuClientKey,
      token
    }
  }

  async getThirdAccountToJuguang() {
    const { xiaohongshuClientKey } = this.configService.get<RootConfigMap['app']>('app')
    const { user } = this.request

    if (user.state === UserState.Ban) {
      throw new ForbiddenException('该账号已被禁用')
    }

    const content = {
      account_code: user.phone
    }

    const xiaohongshuSecret = process.env.XIAOHONGSHU_SECRET

    this.logger.log(xiaohongshuSecret)

    const token = encrypt(JSON.stringify(content), xiaohongshuSecret)

    return {
      appId: xiaohongshuClientKey,
      token
    }
  }

  /**
   * 账号密码登录
   * @param param
   * @returns
   */
  async putPasswordLoginUser(
    { phone, password }: UserPasswordLoginRequestBodyDTO,
    deviceType?: string,
    userAgent?: string,
    registrationId?: string
  ) {
    if (deviceType && deviceType !== 'app') {
      throw new BadRequestException('无效的设备类型')
    }

    let userInfo = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    if (!userInfo) {
      throw new ForbiddenException('该账号未注册')
    } else {
      if (!userInfo.password) {
        throw new ForbiddenException('该账号还未设置密码，请先使用验证码登录后前往个人中心设置密码')
      }

      if (!this.isPasswordCorrect(password, userInfo.salt, userInfo.password)) {
        throw new NotFoundException('账号或密码错误')
      }

      if (userInfo.state === UserState.Ban) {
        throw new ForbiddenException('该账号已被禁用')
      }

      let teamMember: TeamMember & {
        platformAccounts: PlatformAccount[]
      }

      if (userInfo.currentTeamId) {
        teamMember = await this.prisma.teamMember.findUnique({
          where: {
            userId_teamId: {
              teamId: userInfo.currentTeamId,
              userId: userInfo.id
            }
          },
          include: {
            platformAccounts: true
          }
        })
      }

      if (!teamMember) {
        teamMember = await this.prisma.teamMember.findFirst({
          where: {
            userId: userInfo.id
          },
          include: {
            platformAccounts: true
          },
          orderBy: {
            joinTime: 'asc'
          }
        })

        if (teamMember) {
          userInfo = await this.prisma.user.update({
            where: {
              phone: userInfo.phone
            },
            data: {
              currentTeamId: teamMember.teamId
            }
          })
        }
      }

      if (registrationId) {
        const platformInfo = this.parseUserAgent(userAgent)
        await this.prisma.user.update({
          where: {
            id: userInfo.id
          },
          data: {
            platform: platformInfo.platform,
            registrationId
          }
        })
      }

      const authorization = await this.generateAuthorization(userInfo, deviceType)

      if (teamMember) {
        if (teamMember.role === TeamMemberRole.Member) {
          if (teamMember.platformAccounts && teamMember.platformAccounts.length) {
            const value = teamMember.platformAccounts.reduce((acc, item) => {
              acc[item.openId] = item.id
              return acc
            }, {})
            await this.cacheManager.store.client.hmset(genSocketRedisKey(authorization), value)
          }
        } else {
          const platformAccounts = await this.prisma.platformAccount.findMany({
            where: {
              teamId: teamMember.teamId
            }
          })

          if (platformAccounts && platformAccounts.length) {
            const value = platformAccounts.reduce((acc, item) => {
              acc[item.openId] = item.id
              return acc
            }, {})

            await this.cacheManager.store.client.hmset(genSocketRedisKey(authorization), value)
          }
        }
      }

      return {
        authorization,
        teamState: teamMember ? TeamState.Join : TeamState.NotJoin
      }
    }
  }

  /**
   *  生成 token 并缓存
   * @param phone
   * @param ip
   */
  async generateAuthorization(userInfo: User, deviceType = '') {
    const authorization = nanoid()
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')
    const authKey = `${userInfo.phone}${deviceType}`
    const oldAuthorization = await this.cacheManager.get<string>(authKey)
    if (oldAuthorization) {
      await Promise.all([this.cacheManager.del(authKey), this.cacheManager.del(oldAuthorization)])
      await this.deleteOldSocketInfo(oldAuthorization)
    }

    await Promise.all([
      this.cacheManager.set(authKey, authorization, overdueToken),
      this.cacheManager.set(authorization, userInfo, overdueToken)
    ])

    return authorization
  }

  /**
   * 删除 Userorization
   * @param Userorization
   * @returns
   */
  async deleteAuthorization(deviceType?: string) {
    const { user, authorization } = this.request

    if (!user) {
      throw new ForbiddenException('登录失效, 请重新登录')
    }

    if (user) {
      const oldAuthorization = await this.cacheManager.get<string>(user.phone)

      await Promise.all([this.cacheManager.del(user.phone), this.cacheManager.del(authorization)])

      await this.deleteOldSocketInfo(oldAuthorization)

      if (deviceType && deviceType === 'app') {
        await this.prisma.user.update({
          where: {
            id: user.id
          },
          data: {
            platform: '',
            registrationId: ''
          }
        })
      }
    }

    this.logger.log(`delete Authorization: ${authorization} and phone: ${user.phone}`)
  }

  async sendVerificationCode({ phone, sence }: UserSendCodeRequestBodyDTO) {
    const {
      smsCodeTime,
      smsAccessKeyId,
      smsAccessKeySecret,
      smsEndpoint,
      smsSignName,
      smsTemplateCode
    } = this.configService.get<RootConfigMap['app']>('app')

    if (phone !== '18811112222' && phone !== '18833334444' && process.env.NODE_ENV === 'prod') {
      const code = generateRandomCode()

      const config = new $OpenApi.Config({
        accessKeyId: smsAccessKeyId,
        accessKeySecret: smsAccessKeySecret
      })
      config.endpoint = smsEndpoint

      const dysmsapiClient = new Dysmsapi20170525(config)

      const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
        phoneNumbers: phone,
        signName: smsSignName,
        templateCode: smsTemplateCode,
        templateParam: `{"code":${code}}`
      })
      const runtime = new Util.RuntimeOptions({})
      try {
        await dysmsapiClient.sendSmsWithOptions(sendSmsRequest, runtime)
      } catch (error) {
        this.logger.error(error)
        throw new BadRequestException('验证码发送失败')
      }

      try {
        await this.cacheManager.set(`${code}:${sence}`, phone, smsCodeTime)
      } catch (error) {
        this.logger.log(error)
      }
    } else {
      await this.cacheManager.set(`666666:${sence}`, phone, smsCodeTime)
    }
  }

  async sendVerificationCodeV2({
    phone,
    sence,
    captchaVerifyParam,
    isApp
  }: UserSendCodeRequestBodyDTOV2) {
    const {
      smsCodeTime,
      smsAccessKeyId,
      smsAccessKeySecret,
      smsEndpoint,
      smsSignName,
      smsTemplateCode
    } = this.configService.get<RootConfigMap['app']>('app')

    const sceneId = !isApp ? '5aun0gpl' : '1mfojczg'

    if (phone !== '18811112222' && phone !== '18833334444' && process.env.NODE_ENV === 'prod') {
      const code = generateRandomCode()

      const config = new $OpenApi.Config({
        accessKeyId: smsAccessKeyId,
        accessKeySecret: smsAccessKeySecret
      })
      config.endpoint = smsEndpoint

      const dysmsapiClient = new Dysmsapi20170525(config)

      const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
        phoneNumbers: phone,
        signName: smsSignName,
        templateCode: smsTemplateCode,
        templateParam: `{"code":${code}}`
      })
      const runtime = new Util.RuntimeOptions({})
      try {
        const checkResult = await this.VerifyAliyunIntelligentCaptchaAsync(
          sceneId,
          captchaVerifyParam
        )

        if (checkResult) {
          await dysmsapiClient.sendSmsWithOptions(sendSmsRequest, runtime)
        } else {
          throw new BadRequestException('滑块验证失败，请重试')
        }
      } catch (error) {
        this.logger.error(error)
        throw new BadRequestException('验证码发送失败')
      }

      try {
        await this.cacheManager.set(`${code}:${sence}`, phone, smsCodeTime)
      } catch (error) {
        this.logger.log(error)
      }
    } else {
      const checkResult = await this.VerifyAliyunIntelligentCaptchaAsync(
        sceneId,
        captchaVerifyParam
      )

      if (checkResult) {
        await this.cacheManager.set(`666666:${sence}`, phone, smsCodeTime)
      } else {
        throw new BadRequestException('滑块验证失败，请重试')
      }
    }
  }

  async getUserInfo() {
    const { user } = this.request
    return user
  }

  async getUserInfoByPhone(phone: string) {
    const userInfo = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    if (!userInfo) {
      return {}
    }

    return {
      id: userInfo.id,
      phone: userInfo.phone,
      name: userInfo.name,
      avatar: userInfo.avatar
    }
  }

  async updateUserInfo(data: Partial<User>) {
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')
    const { user, authorization } = this.request

    try {
      await Promise.all([
        this.cacheManager.set(authorization, Object.assign(user, data), overdueToken),
        this.prisma.user.update({
          where: {
            phone: user.phone
          },
          data
        })
      ])
    } catch (error) {
      this.logger.error(error)
    }
  }

  async deleteOldSocketInfo(authorization: string) {
    const oldKeys = await this.cacheManager.store.client.hkeys(genSocketRedisKey(authorization))

    if (oldKeys && oldKeys.length) {
      await this.cacheManager.store.client.hdel(genSocketRedisKey(authorization), ...oldKeys)
    }
  }

  /**
   * 设置密码
   * @param param
   */
  async setPassword({ password }: UserPasswordRegisterBodyDto) {
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')
    const { user, authorization } = this.request

    if (user.password) {
      throw new ForbiddenException('密码已存在, 请重置密码')
    }

    const result = this.validatePassword(password)

    if (!result) {
      throw new ForbiddenException('密码至少包含一个字母、一位数字')
    }

    const { hash, salt } = this.hashPassword(password)

    const value = { ...user, password: hash, salt }

    try {
      await Promise.all([
        this.cacheManager.set(authorization, Object.assign(user, value), overdueToken),
        this.prisma.user.update({
          where: {
            phone: user.phone
          },
          data: {
            password: hash,
            salt
          }
        })
      ])
    } catch (error) {
      this.logger.error(error)
    }
  }

  /**
   * 修改密码
   * @param param
   */
  async updatePassword({ code, password }: UserPasswordUpdateBodyDto) {
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')
    const { user, authorization } = this.request

    if (!user.password) {
      throw new ForbiddenException('密码不存在, 请先设置密码')
    }

    const result = this.validatePassword(password)

    if (!result) {
      throw new ForbiddenException('密码至少包含一个大写字母、一个小写字母、一位数字和一个特殊字符')
    }

    // 修改密码
    const phoneValid = await this.cacheManager.get<string>(`${code}:${SMSCodeSence.Password}`)

    if (!phoneValid || phoneValid !== user.phone) {
      throw new BadRequestException('验证码无效')
    }

    await this.cacheManager.del(`${code}:${SMSCodeSence.Password}`)

    const { hash, salt } = this.hashPassword(password)

    const value = { ...user, password: hash, salt }

    try {
      await Promise.all([
        this.cacheManager.set(authorization, Object.assign(user, value), overdueToken),
        this.prisma.user.update({
          where: {
            phone: user.phone
          },
          data: {
            password: hash,
            salt
          }
        }),
        this.deleteAuthorization()
      ])
    } catch (error) {
      this.logger.error(error)
    }
  }

  /**
   * 忘记密码
   * @param param
   */
  async resetPassword({ phone, code, password }: UserPasswordResetBodyDto) {
    const userInfo = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    if (!userInfo) {
      throw new ForbiddenException('该账号未注册')
    } else {
      const result = this.validatePassword(password)

      if (!result) {
        throw new ForbiddenException(
          '密码至少包含一个大写字母、一个小写字母、一位数字和一个特殊字符'
        )
      }

      // 修改密码
      const phoneValid = await this.cacheManager.get<string>(`${code}:${SMSCodeSence.Password}`)

      if (!phoneValid || phoneValid !== userInfo.phone) {
        throw new BadRequestException('验证码无效')
      }

      await this.cacheManager.del(`${code}:${SMSCodeSence.Password}`)

      const { hash, salt } = this.hashPassword(password)

      try {
        await Promise.all([
          this.prisma.user.update({
            where: {
              phone: userInfo.phone
            },
            data: {
              password: hash,
              salt
            }
          })
        ])
      } catch (error) {
        this.logger.error(error)
      }
    }
  }

  /**
   * 注册赠送VIP信息
   * @param userId 用户id
   * @param teamId 团队id
   * @param vipId VIP信息id
   * @param days 赠送天数
   */
  async giveVip(userId: number, teamId: number, vipId: number, days: number) {
    if (days <= 0) {
      return
    }

    const interest = await this.prisma.interest.findFirst()

    const orderNo = await this.orderManageService.giftOrder({
      teamId,
      userId,
      interestId: interest.id,
      isRegisterOrder: true,
      giftDays: days
    })

    // 订单处理
    await this.orderManageService.handleCompletedOrder(orderNo)

    const vipInfo = await this.prisma.vip.findUnique({
      where: {
        id: vipId
      }
    })

    return vipInfo
  }

  async getUserInfoByExchage({ exchangeKey }: UserInfoExchangeKeyRequestBodyDTO) {
    const userInfo = await this.cacheManager.get<string>(exchangeKey)

    if (userInfo) {
      await this.cacheManager.del(exchangeKey)

      return JSON.parse(userInfo)
    }

    return null
  }

  async postBaiduOcpcApi({ logidUrl }: BaiduOcpcRequestBodyDTO) {
    const { baiduApiToken } = this.configService.get<RootConfigMap['app']>('app')
    return postOcpcapi(baiduApiToken, logidUrl)
  }

  async deleteMember(id: number, phone: string) {
    const { user } = this.request

    if (user.id !== id) {
      throw new ForbiddenException('用户id参数错误')
    }

    const member = await this.prisma.user.findUnique({
      where: {
        id
      },
      include: {
        teamMember: true
      }
    })

    if (!member) {
      throw new NotFoundException('该用户不存在')
    }

    if (member.phone !== phone) {
      throw new ForbiddenException('手机号参数错误')
    }

    // 一：加入的团队，退出团队
    const memberTeam = member.teamMember.filter((item) => item.role !== 2)

    for (let j = 0; j < memberTeam.length; j++) {
      const { platformAccounts, userId } = await this.prisma.teamMember.delete({
        where: {
          id: memberTeam[j].id,
          teamId: memberTeam[j].teamId
        },
        include: {
          platformAccounts: true
        }
      })

      const [socketId, socketIdApp] = await Promise.all([
        this.cacheManager.get<string>(genSocketRedisKey(userId)),
        this.cacheManager.get<string>(genSocketRedisKey(userId + 'app'))
      ])

      await Promise.allSettled([
        (async () => {
          if (socketIdApp) {
            this.cacheManager.del(genSocketRedisKey(userId + 'app'))

            const tasks: Promise<number>[] = []
            for (let i = 0; i < platformAccounts.length; i++) {
              tasks.push(
                this.cacheManager.store.client.hdel(
                  genSocketRedisKey(platformAccounts[i].openId),
                  socketIdApp
                )
              )
            }

            await Promise.all(tasks)
          }
        })(),
        (async () => {
          if (socketId) {
            this.cacheManager.del(genSocketRedisKey(userId))

            const tasks: Promise<number>[] = []
            for (let i = 0; i < platformAccounts.length; i++) {
              tasks.push(
                this.cacheManager.store.client.hdel(
                  genSocketRedisKey(platformAccounts[i].openId),
                  socketId
                )
              )
            }

            await Promise.all(tasks)
          }
        })()
      ])
    }

    // 二：自己的团队，解散团队
    const ownerTeam = member.teamMember.filter((item) => item.role === 2)

    for (let z = 0; z < ownerTeam.length; z++) {
      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: ownerTeam[z].teamId
        }
      })

      const platformAccountTasks = []

      platformAccounts.forEach((item) => {
        platformAccountTasks.push(this.deleteAccount(item.id))
      })

      await this.prisma.$transaction(async (prisma) => {
        await Promise.all(platformAccountTasks)

        await prisma.teamMember.deleteMany({
          where: {
            teamId: ownerTeam[z].teamId
          }
        })

        await this.prisma.autoresponder.deleteMany({
          where: {
            teamId: ownerTeam[z].teamId
          }
        })

        await Promise.all([
          prisma.platformAccount.deleteMany({
            where: {
              teamId: ownerTeam[z].teamId
            }
          }),

          prisma.speech.deleteMany({
            where: {
              teamId: ownerTeam[z].teamId
            }
          }),
          prisma.variable.deleteMany({
            where: {
              teamId: ownerTeam[z].teamId
            }
          })
        ])

        await prisma.team.update({
          where: {
            id: ownerTeam[z].teamId
          },
          data: {
            isDelete: true
          }
        })
      })
    }

    await this.prisma.user.delete({
      where: {
        id
      }
    })
  }

  private async deleteAccount(platformAccountId: number) {
    try {
      const platformAccount = await this.prisma.platformAccount.delete({
        where: { id: platformAccountId }
      })

      const res = await this.cacheManager.store.client.hgetall(AutoresponderKeywordKey)
      if (res) {
        Object.keys(res).forEach((key) => {
          const [openId] = key.split(':')
          if (openId === platformAccount.openId) {
            this.cacheManager.store.client.hdel(AutoresponderKeywordKey, key)
          }
        })
      }

      const bindUsers = await this.cacheManager.store.client.hgetall(
        genSocketRedisKey(platformAccount.openId)
      )

      const socketIds = []
      const delTask = []
      if (bindUsers) {
        Object.keys(bindUsers).forEach((socketId) => {
          socketIds.push(socketId)
          delTask.push(
            this.cacheManager.store.client.hdel(genSocketRedisKey(platformAccount.openId), socketId)
          )
        })
      }

      await Promise.all(delTask)

      if (platformAccount.platform === Platform.Wechat) {
        await wechatLogout({
          appId: platformAccount.appId,
          isNew: platformAccount.isNew
        })
      }
    } catch (e) {
      this.logger.error('删除平台账号失败', e)
    }
  }

  parseUserAgent(userAgent: string) {
    let platform = 'android'

    // 检测 android
    if (/android/i.test(userAgent)) {
      platform = 'android'
    }
    // 检测 ios
    else if (/iphone|iPhone|ipad|ipod/i.test(userAgent)) {
      platform = 'ios'
    }
    // 检测 hmos
    else if (/harmonyos/i.test(userAgent)) {
      platform = 'hmos'
    }

    return { platform }
  }

  async getPreSignedUrl() {
    return this.TosManageService.getPreSignedPostSignature({
      objectName: 'abc.jpg'
    })
  }

  static createClient(): captcha20230305 {
    const config = new $OpenApi.Config({
      accessKeyId: process.env.CAPTCHA_ACCESS_KEY_ID,
      accessKeySecret: process.env.CAPTCHA_ACCESS_KEY_SECRET
    })
    config.endpoint = `captcha.cn-shanghai.aliyuncs.com`
    // eslint-disable-next-line new-cap
    return new captcha20230305(config)
  }

  async VerifyAliyunIntelligentCaptchaAsync(
    sceneId: string,
    captchaVerifyParam: string
  ): Promise<boolean> {
    const client = UserService.createClient()
    const verifyCaptchaRequest = new $captcha20230305.VerifyCaptchaRequest({
      sceneId,
      captchaVerifyParam
    })

    try {
      const response = await client.verifyCaptchaWithOptions(
        verifyCaptchaRequest,
        new Util.RuntimeOptions({
          ConnectTimeout: 5000,
          ReadTimeout: 5000
        })
      )
      return response.body.result.verifyResult
    } catch (error) {
      await this.logger.error(null, '滑块验证错误重试', { error: error.message })
      return false
    }
  }
}
