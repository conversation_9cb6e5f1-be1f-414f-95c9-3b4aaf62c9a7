import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsOptional, IsString } from 'class-validator'

export class Groups {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '我的分组'
  })
  name: string

  @ApiResponseProperty({
    type: [String],
    example: '[]'
  })
  accounts: string[]
}

export class PatchGroupsRequest {
  @ApiProperty({
    description: '分组名称',
    default: '张三组',
    required: false,
    type: String
  })
  @IsString()
  @IsOptional()
  name: string

  @ApiProperty({
    description: '绑定账号ID',
    default: [1, 2],
    required: false,
    type: [Number]
  })
  @IsOptional()
  accounts: number[]
}

/**
 * 创建分组
 */
export class PostGroupsRequest {
  @ApiProperty({
    type: String,
    example: '分组',
    required: true
  })
  @IsString()
  name: string
}

/**
 * 分组详情响应体
 */
export class GroupsDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Groups
  })
  data: Groups
}

export class GroupsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [Groups]
  })
  data: Groups[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  totalPage: number
}
