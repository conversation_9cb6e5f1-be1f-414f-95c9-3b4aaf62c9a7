/*
  Warnings:

  - You are about to drop the column `channelName` on the `userscoupons` table. All the data in the column will be lost.
  - You are about to drop the column `teamName` on the `userscoupons` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `userscoupons` DROP COLUMN `channelName`,
    DROP COLUMN `teamName`;

-- AddForeignKey
ALTER TABLE `UsersCoupons` ADD CONSTRAINT `UsersCoupons_channelId_fkey` FOREIGN KEY (`channelId`) REFERENCES `Channel`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UsersCoupons` ADD CONSTRAINT `UsersCoupons_teamId_fkey` FOREIGN KEY (`teamId`) REFERENCES `Team`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
