import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { PatchLabelRequest, PostLabelRequest } from './label.dto'
import { PrismaService } from '@qdy/mysql'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'

@Injectable()
export class LabelService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 添加标签
   * @param body
   * @returns
   */
  async postLabel(body: PostLabelRequest) {
    const { user } = this.request

    const label = await this.prisma.privateMessageUserLabel.findFirst({
      where: {
        title: body.title,
        teamId: user.currentTeamId
      }
    })

    if (label) {
      throw new ForbiddenException('标签名不能重复')
    }

    const total = await this.prisma.privateMessageUserLabel.count({
      where: {
        teamId: user.currentTeamId
      }
    })

    if (total >= 10) {
      throw new ForbiddenException('标签最多添加10个')
    }

    await this.prisma.privateMessageUserLabel.create({
      data: {
        title: body.title,
        teamId: user.currentTeamId
      }
    })
  }

  /**
   * 标签列表
   * @param title
   * @param page
   * @param size
   * @returns
   */
  async getLabel(title: string, page: number, size: number) {
    const { user } = this.request

    const where: Parameters<typeof this.prisma.privateMessageUserLabel.findMany>[0]['where'] = {
      teamId: user.currentTeamId
    }

    if (title) {
      where.title = { contains: title }
    }
    const total = await this.prisma.privateMessageUserLabel.count({
      where
    })

    const Label = await this.prisma.privateMessageUserLabel.findMany({
      where,
      orderBy: {
        createTime: 'desc'
      },
      skip: (page - 1) * size,
      take: size
    })

    return {
      total,
      page,
      size,
      data: Label.map((item) => ({
        id: item.id,
        title: item.title,
        createTime: item.createTime.getTime()
      }))
    }
  }

  /**
   * 修改标签
   * @param id
   * @param body
   */
  async patchLabel(id: number, body: PatchLabelRequest) {
    const { user } = this.request

    const label = await this.prisma.privateMessageUserLabel.findUnique({
      where: {
        id,
        teamId: user.currentTeamId
      }
    })

    if (!label) {
      throw new NotFoundException('标签未找到')
    }

    await this.prisma.privateMessageUserLabel.update({
      where: {
        id,
        teamId: user.currentTeamId
      },
      data: {
        title: body.title
      }
    })
  }

  /**
   * 删除标签
   * @param id
   */
  async deleteLabel(id: number) {
    const { user } = this.request

    const label = await this.prisma.privateMessageUserLabel.findUnique({
      where: {
        id,
        teamId: user.currentTeamId
      }
    })

    if (!label) {
      throw new NotFoundException('标签未找到')
    }

    await this.prisma.privateMessageUserLabel.delete({
      where: {
        id,
        teamId: user.currentTeamId
      }
    })
  }
}
