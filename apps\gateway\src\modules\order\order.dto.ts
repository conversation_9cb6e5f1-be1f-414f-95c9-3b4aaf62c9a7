import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { OrderRecord as OrderRecordType } from 'apps/common/modules/orderManage/orderManage.dto'

export enum OrderStatus {
  PENDING = 'pending', // 待支付
  SUCCESS = 'success', // 成功支付
  CANCELED = 'canceled' // 已取消
}

export enum OrderRecord {
  Create = 'create',
  Upgrade = 'upgrade',
  Diff = 'diff',
  Refund = 'refund',
  Expire = 'expire'
}

export enum PayType {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  CORPORATETRANSFER = 'corporateTransfer'
}

export enum OrderType {
  ONLINE = 'online',
  SYSTEM = 'system'
}

export class OrderRequestCreateOrderDTO {
  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  couponId?: number

  @ApiProperty({
    type: Boolean,
    description: '是否为对公转账',
    required: false
  })
  @IsBoolean()
  isCorporateTransfer: boolean
}

export class UrlInfo {
  @ApiResponseProperty({
    type: String,
    example: 'https://example.com/pay'
  })
  urlInfo: string
}

export class PayInfo {
  @ApiResponseProperty({
    type: UrlInfo
  })
  alipay: UrlInfo

  @ApiResponseProperty({
    type: UrlInfo
  })
  wechatPay: UrlInfo

  @ApiResponseProperty({
    type: UrlInfo
  })
  corporateTransfer: UrlInfo
}

export class PayInfoResponse {
  @ApiResponseProperty({
    type: PayInfo
  })
  payInfo: PayInfo

  @ApiResponseProperty({
    type: Number,
    example: '产品金额'
  })
  price: number

  @ApiResponseProperty({
    type: Number,
    example: '应付金额'
  })
  dueAmount: number

  @ApiResponseProperty({
    type: String
  })
  orderNo: string

  @ApiResponseProperty({
    type: Number
  })
  remainingTimeInSeconds: number
}

export class PayInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: PayInfoResponse
  })
  data: PayInfoResponse
}

export class orderResponseCreateOrder {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  orderNo: string
}

export class OrderResponseCreateOrderDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: orderResponseCreateOrder
  })
  data: orderResponseCreateOrder
}

export class OrderStatusRequestDTO {
  @ApiProperty({
    description: '状态',
    example: 'canceled',
    required: true
  })
  @IsString()
  orderStatus: string
}

export class OrderStatusResponse {
  @ApiResponseProperty({
    enum: Object.values(OrderStatus),
    type: OrderStatus,
    example: OrderStatus.PENDING
  })
  orderStatus: OrderStatus
}

export class OrderResponseQueryOrderDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderStatusResponse
  })
  data: OrderStatusResponse
}

export class Team {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  name: string
}

export class Orders {
  @ApiResponseProperty({
    type: Number,
    example: '1 订单id'
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112 订单编号'
  })
  orderNo: string

  @ApiResponseProperty({
    type: Team,
    example: {
      id: 1,
      name: '张三'
    }
  })
  team: Team

  @ApiResponseProperty({
    type: String,
    example: 'pedding success canceled 订单状态'
  })
  orderStatus: string

  @ApiResponseProperty({
    type: String,
    example: 'online, system 订单类型'
  })
  type: string

  @ApiResponseProperty({
    type: Number,
    example: '创建时间'
  })
  fromTime: number

  @ApiResponseProperty({
    type: Number,
    example: '支付时间'
  })
  payTime: number

  @ApiResponseProperty({
    type: Number,
    example: '过期时间'
  })
  expireTime: number

  @ApiResponseProperty({
    type: Number,
    example: '剩余时间（单位秒）'
  })
  remainingTimeInSeconds: number

  @ApiResponseProperty({
    type: Number,
    example: '订单金额'
  })
  price: number

  @ApiResponseProperty({
    type: Number,
    example: '应付金额'
  })
  dueAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '实付金额'
  })
  payAmount: number

  @ApiResponseProperty({
    type: String,
    example: 'wechat, alipay, corporateTransfer 支付类型'
  })
  payType: string

  @ApiResponseProperty({
    type: String,
    example: '订单类型 create:开通,upgrade:升级,renew:续费,gift:赠送'
  })
  orderType: string
}

export class OrdersResponse {
  @ApiResponseProperty({
    type: [Orders]
  })
  data: Orders[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  total: number
}

export class OrdersResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrdersResponse
  })
  data: OrdersResponse
}

export class VipInfo {
  /**
   * 权益包数量
   */
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  interestCount: number

  /**
   * 账号数
   */
  @ApiResponseProperty({
    type: Number,
    example: 20
  })
  platformAccountCount: number

  /**
   * 成员数
   */
  @ApiResponseProperty({
    type: Number,
    example: 5
  })
  teamMemberCount: number

  /**
   * 消息额度
   */
  @ApiResponseProperty({
    type: Number,
    example: 1000
  })
  messageCount: number

  /**
   * VIP时长
   */
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  month: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  freeMonth: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  expirationTime: number
}

export class OrderInfo {
  @ApiResponseProperty({
    type: String,
    example: '订单编号'
  })
  orderNo: string

  @ApiResponseProperty({
    type: String,
    example: '团队名称'
  })
  teamName: string

  @ApiResponseProperty({
    type: Number,
    example: '创建时间'
  })
  fromTime: number

  @ApiResponseProperty({
    type: String,
    example: 'pending, success, cancaled'
  })
  orderStatus: string

  @ApiResponseProperty({
    type: String,
    example: 'online, system'
  })
  type: string

  @ApiResponseProperty({
    type: Number,
    example: '待支付金额'
  })
  dueAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '已支付金额'
  })
  payAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '支付时间'
  })
  payTime: number

  @ApiResponseProperty({
    type: String,
    example: '支付方式'
  })
  payType: string
}

export class OrderInfoResponse {
  @ApiResponseProperty({
    type: VipInfo,
    example: 1
  })
  vipInfo: VipInfo

  @ApiResponseProperty({
    type: OrderInfo,
    example: 1
  })
  orderInfo: OrderInfo
}

export class OrderInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderInfoResponse,
    example: 1
  })
  data: OrderInfoResponse
}

export class PeddingOrderResponse {
  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  hasPendingOrder: boolean

  @ApiResponseProperty({
    type: Number,
    example: 0
  })
  count: number
}

export class PeddingOrderResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: PeddingOrderResponse
  })
  data: PeddingOrderResponse
}

export class UpgradeOrderRequest {
  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  couponId?: number

  @ApiProperty({
    type: Boolean,
    description: '是否为对公转账',
    required: true
  })
  @IsBoolean()
  isCorporateTransfer: boolean
}

export class RenewOrderRequest {
  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  couponId?: number

  @ApiProperty({
    type: Boolean,
    description: '是否为对公转账',
    required: true
  })
  @IsBoolean()
  isCorporateTransfer: boolean
}

export class orderPriceRequestDTO {
  @ApiProperty({
    enum: OrderRecordType,
    description: '订单类型(create:开通,upgrade:升级,renew:续费)',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(OrderRecordType)
  orderType: OrderRecordType

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    description: '权益包数量',
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  month: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  couponId?: number
}

export class AppPurchasesRequestDTO {
  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  transactionId: string

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  productId: string

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  receiptData: string

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  month: number
}
