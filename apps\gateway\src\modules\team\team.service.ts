import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import type { Cache } from 'cache-manager'
import type { PlatformAccount, TeamMember, User } from '@qdy/mysql'
import { encrypt, inviteCode } from '../../common/utils'
import {
  TeamMemberRole,
  TeamMemberStatus,
  TeamRequestMembersNameDTO,
  TeamRequestMembersPlatformAccountDTO,
  TeamRequestPatchDTO,
  TeamRequestPlatformAccountMembersDTO,
  TeamUpdateMemberRoleDTO,
  TeamWechatMessageLimitRequestDTO
} from './team.dto'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { genSocketRedisKey, Platform } from '@qdy/utils'
import { type RedisStore } from 'cache-manager-ioredis-yet'
import { TeamSocketService } from './team.socket'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { AutoresponderKeywordService } from '../autoresponder/autoresponder.keyword.service'
import { AccountService } from '../account/account.service'
import { InjectModel } from '@nestjs/mongoose'
import { InvitationEntity, LogEntity } from '@qdy/mongo'
import { Model } from 'mongoose'
import { StatusEnum } from '../invitation/invitation.dto'
import dayjs from 'dayjs'

@Injectable()
export class TeamService {
  logger = new Logger('TeamService')

  constructor(
    @InjectModel(InvitationEntity.name) private invitationModel: Model<InvitationEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly socketService: TeamSocketService,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly autoresponderKeywordService: AutoresponderKeywordService,
    private readonly accountService: AccountService,
    @InjectModel(LogEntity.name) private logModel: Model<LogEntity>
  ) {}

  /**
   * 创建团队
   * @param authorization
   * @param name
   * @returns
   */
  async createTeam(name: string) {
    const { user } = this.request

    const teamMembers = await this.prisma.teamMember.count({
      where: {
        userId: user.id
      }
    })

    if (teamMembers >= 10) {
      throw new BadRequestException('当前账号已有10个团队,请先退出/解散团队后再加')
    }

    return this.createBaseTeam(name, user)
  }

  async createBaseTeam(name: string, user: User) {
    const teamInfo = await this.prisma.team.create({
      data: {
        name,
        invitationCode: inviteCode(user.id),
        ownerId: user.id
      }
    })

    const system = await this.prisma.systemDosage.findFirst()

    const orderPrice = 0
    const messageLimit = system.standardMessageLimit
    const teamMemberNumberLimit = system.standardTeamMemberNumberLimit
    const platformAccountNumberLimit = system.standardPlatformAccountNumberLimit

    const vipInfo = await this.prisma.vip.create({
      data: {
        teamId: teamInfo.id,
        messageLimit,
        teamMemberNumberLimit,
        platformAccountNumberLimit,
        douyinCommentNumberLimit: 0,
        interestCount: 0,
        price: orderPrice,
        month: 0,
        freeMonth: 0,
        day: 0
      }
    })

    await this.createTeamMember({
      userId: user.id,
      teamId: teamInfo.id,
      role: TeamMemberRole.Owner
    })

    return {
      teamId: teamInfo.id,
      vipId: vipInfo.id
    }
  }

  /**
   * 加入团队
   * @param authorization
   * @param invitationCode
   * @returns
   */
  async joinTeam(invitationCode: string) {
    const { user } = this.request

    const team = await this.prisma.team.findUnique({
      where: {
        invitationCode
      },
      include: {
        vip: true
      }
    })

    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        userId: user.id
      }
    })

    if (teamMembers.length >= 10) {
      throw new BadRequestException('当前账号已有10个团队,请先退出/解散团队后再加')
    }

    const teamMember = teamMembers.find((item) => item.teamId === team.id)

    if (teamMember) {
      throw new ForbiddenException('已加入该团队')
    }

    const [teamMemberCount, systemDosage] = await Promise.all([
      this.prisma.teamMember.count({
        where: {
          teamId: team.id
        }
      }),
      this.prisma.systemDosage.findFirst()
    ])

    if (team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
      if (team.vip.teamMemberNumberLimit <= teamMemberCount) {
        throw new ForbiddenException('团队成员数量已达上限')
      }
    } else if (systemDosage.standardTeamMemberNumberLimit <= teamMemberCount) {
      throw new ForbiddenException('团队成员数量已达上限')
    }

    await this.createTeamMember({
      userId: user.id,
      teamId: team.id,
      role: TeamMemberRole.Member
    })
  }

  /**
   * 更新团队信息
   * @param authorization
   * @param data
   */
  async updateTeamInfo(data: TeamRequestPatchDTO) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (teamMember.role === TeamMemberRole.Member) {
      throw new ForbiddenException('无权限')
    }

    await this.prisma.team.update({
      where: {
        id: teamMember.teamId
      },
      data: {
        name: data.name,
        avatar: data.avatar
      }
    })
  }

  /**
   * 获取我的团队信息
   * @param authorization
   * @returns
   */
  async getOurTeam({ clientIp, userAgent }: { clientIp: string; userAgent: string }) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      return { checkTeam: false }
    }

    const team = await this.prisma.team.findUnique({
      where: {
        id: teamMember.teamId
      },
      include: {
        vip: true,
        members: {
          where: {
            NOT: {
              status: TeamMemberStatus.Disable
            }
          },
          select: {
            id: true
          }
        },
        platformAccounts: {
          where: {
            NOT: {
              status: TeamMemberStatus.Disable
            }
          },
          select: {
            id: true
          }
        }
      }
    })

    if (!team.deviceInfo) {
      const device = this.parseUserAgent(userAgent)
      const brand = this.getDeviceBrandFromUserAgent(userAgent)
      await this.prisma.team.update({
        where: {
          id: team.id
        },
        data: {
          deviceInfo: {
            device_brand: brand,
            device_platform: device.platform,
            os_version: device.version,
            ip: clientIp,
            device_type: device.platform
          }
        }
      })

      await this.cacheManager.set(
        `device:${team.id}`,
        {
          device_brand: brand,
          device_platform: device.platform,
          os_version: device.version,
          ip: clientIp,
          device_type: device.platform
        },
        0
      )
    }

    if (team.vip.expirationTime && team.vip.expirationTime.getTime() < Date.now()) {
      const systemDosage = await this.prisma.systemDosage.findFirst()
      team.vip.messageLimit = systemDosage.standardMessageLimit
      team.vip.teamMemberNumberLimit = systemDosage.standardTeamMemberNumberLimit
      team.vip.platformAccountNumberLimit = systemDosage.standardPlatformAccountNumberLimit
      team.vip.douyinCommentNumberLimit = systemDosage.standardDouyinCommentCount
    }

    // 剩余消息数
    const residue = (await this.cacheManager.get(`overview:${team.id}`)) as {
      residueCount: number
    }

    const residueCount = residue ? residue.residueCount : 0

    let messageCount = 0

    if (residue) {
      if (residue.residueCount === 0) {
        // 余数为0,则messageCount等于限制数量
        messageCount = team.vip.messageLimit
      } else {
        messageCount = team.vip.messageLimit - residueCount
      }
    }

    const remainingDay = dayjs(team.vip.expirationTime).diff(dayjs(), 'day') + 1

    const diffPrice = (team.vip.price / team.vip.day) * remainingDay || 0

    const { clientKey, secondClientKey } = this.configService.get<RootConfigMap['app']>('app')

    let douyinClientKey = clientKey

    if (team.douyinClientKey) {
      douyinClientKey =
        team.douyinClientKey === clientKey
          ? clientKey
          : team.douyinClientKey === secondClientKey
            ? secondClientKey
            : clientKey
    }

    return {
      id: encrypt(team.id),
      vip: {
        ...team.vip,
        remainingDay,
        diffPrice,
        isInit:
          !team.vip.expirationTime ||
          team.vip.expirationTime.getTime() < team.vip.createTime.getTime()
      },
      name: team.name,
      avatar: team.avatar,
      invitationCode: team.invitationCode,
      ownerId: team.ownerId,
      createTime: team.createTime,
      role: teamMember.role,
      checkTeam: true,
      platformAccountCount: team.platformAccounts.length,
      teamMemberCount: team.members.length,
      douyinClientKey,
      messageCount,
      salesType: team.salesType
    }
  }

  /**
   * 创建团队成员
   * @param data
   * @returns
   * @memberof TeamService
   */
  createTeamMember(data: { userId: number; teamId: number; role: TeamMemberRole }) {
    return this.prisma.teamMember.create({
      data: {
        userId: data.userId,
        teamId: data.teamId,
        role: data.role
      }
    })
  }

  async setMemberRole({ role, memberId }: TeamUpdateMemberRoleDTO & { memberId: number }) {
    const { user } = this.request

    const [actionMember, targetMember] = await Promise.all([
      this.prisma.teamMember.findUnique({
        where: {
          userId_teamId: {
            teamId: user.currentTeamId,
            userId: user.id
          }
        }
      }),
      this.prisma.teamMember.findUnique({
        where: {
          id: memberId
        },
        include: {
          platformAccounts: true,
          user: true
        }
      })
    ])

    if (
      targetMember.role === TeamMemberRole.Owner ||
      actionMember.role === TeamMemberRole.Member ||
      actionMember.teamId !== targetMember.teamId ||
      actionMember.role <= targetMember.role ||
      targetMember.role === TeamMemberRole.Owner
    ) {
      throw new ForbiddenException('无权限')
    }

    if (role === TeamMemberRole.Owner) {
      throw new ForbiddenException('不能设置为团队拥有者')
    }

    if (role === targetMember.role) {
      return
    }

    await this.prisma.teamMember.update({
      where: {
        id: memberId
      },
      data: {
        role
      }
    })

    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        teamId: targetMember.teamId
      }
    })

    const [socketId, socketIdApp] = await Promise.all([
      this.cacheManager.get<string>(genSocketRedisKey(targetMember.userId)),
      this.cacheManager.get<string>(genSocketRedisKey(targetMember.userId + 'app'))
    ])

    const oldPlatformAccounts = new Set(targetMember.platformAccounts.map((item) => item.id))

    await Promise.allSettled([
      (async () => {
        if (socketIdApp) {
          const tasks: Promise<number>[] = []
          const extra = platformAccounts.filter((item) => !oldPlatformAccounts.has(item.id))
          let roleKey = 'add'

          const authApp = (await this.cacheManager.get(targetMember.user.phone + 'app')) as string

          if (targetMember.role === TeamMemberRole.Manager && role === TeamMemberRole.Member) {
            for (let i = 0; i < extra.length; i++) {
              const item = extra[i]
              if (authApp) {
                try {
                  await this.cacheManager.store.client.hdel(genSocketRedisKey(authApp), item.openId)
                } catch (e) {
                  this.logger.error('删除错误', e)
                }
              }

              tasks.push(
                this.cacheManager.store.client.hdel(genSocketRedisKey(item.openId), socketIdApp)
              )
            }

            roleKey = 'remove'
          } else {
            for (let i = 0; i < extra.length; i++) {
              const item = extra[i]
              if (authApp) {
                try {
                  await this.cacheManager.store.client.hset(
                    genSocketRedisKey(authApp),
                    item.openId,
                    item.id
                  )
                } catch (e) {
                  this.logger.error('添加错误', e)
                }
              }

              tasks.push(
                this.cacheManager.store.client.hset(
                  genSocketRedisKey(item.openId),
                  socketIdApp,
                  item.id
                )
              )
            }
          }

          await Promise.all(tasks)
          try {
            //
            return this.socketService.socketService.send({
              list: JSON.stringify([
                {
                  socketIdApp,
                  data: {
                    type: 'changePlatformAccount',
                    data: extra.map((item) => ({
                      action: roleKey,
                      platform: item.platform,
                      name: item.name,
                      avatar: item.avatar,
                      openId: item.openId,
                      accountRole: item.accountRole,
                      id: item.id,
                      teamId: item.teamId,
                      expiresTime: item.expiresIn
                        ? item.expiresIn * 1000 + item.tokenTime.getTime()
                        : 0
                    }))
                  }
                }
              ])
            })
          } catch (e) {
            this.logger.error(e)
            throw new BadRequestException('同步信息失败')
          }
        }
      })(),
      (async () => {
        if (socketId) {
          const tasks: Promise<number>[] = []
          const extra = platformAccounts.filter((item) => !oldPlatformAccounts.has(item.id))
          let roleKey = 'add'

          const auth = (await this.cacheManager.get(targetMember.user.phone)) as string

          if (targetMember.role === TeamMemberRole.Manager && role === TeamMemberRole.Member) {
            for (let i = 0; i < extra.length; i++) {
              const item = extra[i]
              if (auth) {
                try {
                  await this.cacheManager.store.client.hdel(genSocketRedisKey(auth), item.openId)
                } catch (e) {
                  this.logger.error('删除错误', e)
                }
              }

              tasks.push(
                this.cacheManager.store.client.hdel(genSocketRedisKey(item.openId), socketId)
              )
            }

            roleKey = 'remove'
          } else {
            for (let i = 0; i < extra.length; i++) {
              const item = extra[i]
              if (auth) {
                try {
                  await this.cacheManager.store.client.hset(
                    genSocketRedisKey(auth),
                    item.openId,
                    item.id
                  )
                } catch (e) {
                  this.logger.error('添加错误', e)
                }
              }

              tasks.push(
                this.cacheManager.store.client.hset(
                  genSocketRedisKey(item.openId),
                  socketId,
                  item.id
                )
              )
            }
          }

          await Promise.all(tasks)
          try {
            //
            return this.socketService.socketService.send({
              list: JSON.stringify([
                {
                  socketId,
                  data: {
                    type: 'changePlatformAccount',
                    data: extra.map((item) => ({
                      action: roleKey,
                      platform: item.platform,
                      name: item.name,
                      avatar: item.avatar,
                      openId: item.openId,
                      accountRole: item.accountRole,
                      id: item.id,
                      teamId: item.teamId,
                      expiresTime: item.expiresIn
                        ? item.expiresIn * 1000 + item.tokenTime.getTime()
                        : 0
                    }))
                  }
                }
              ])
            })
          } catch (e) {
            this.logger.error(e)
            throw new BadRequestException('同步信息失败')
          }
        }
      })()
    ])
  }

  // /**
  //  * 更新团队成员信息 -- 有风险 TODO
  //  * @param userId
  //  * @param data
  //  * @returns
  //  */
  // updateTeamMember(userId: number, data: Partial<TeamMember>) {
  //   const { user } = this.request
  //   return this.prisma.teamMember.update({
  //     where: {
  //       userId_teamId: {
  //         teamId: user.currentTeamId,
  //         userId
  //       }
  //     },
  //     data
  //   })
  // }

  async removeMember(memberId: number) {
    const { user } = this.request

    const tagetTeamMember = await this.prisma.teamMember.findUnique({
      where: {
        id: memberId
      }
    })

    if (!tagetTeamMember) {
      throw new NotFoundException('团队成员不存在')
    }

    if (tagetTeamMember.teamId !== user.currentTeamId) {
      throw new NotFoundException('无权限')
    }

    if (tagetTeamMember.role === TeamMemberRole.Owner && tagetTeamMember.userId === user.id) {
      throw new ForbiddenException('创建者不能退出团队')
    }

    // 当前用户权限
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    // 当前用户团队成员权限小于目标成员权限,则无权限删除
    if (teamMember.role < tagetTeamMember.role) {
      throw new ForbiddenException('无权限')
    }

    await this.logModel.create({
      type: 'removeMember',
      phone: user.phone,
      teamId: user.currentTeamId,
      teamMemberId: tagetTeamMember.id,
      teamName: teamMember.team.name,
      createTime: dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
      content: {
        ...teamMember.team
      }
    })

    try {
      const { platformAccounts, userId } = await this.prisma.teamMember.delete({
        where: {
          id: memberId,
          teamId: teamMember.teamId
        },
        include: {
          platformAccounts: true
        }
      })

      // 找到团队用户关系表中第一条数据（非当前删除数据）
      const otherTeamMember = await this.prisma.teamMember.findFirst({
        where: {
          userId: user.id,
          teamId: {
            not: user.currentTeamId
          }
        }
      })

      await this.setCurrentTeam(otherTeamMember.teamId)

      const socketId = await this.cacheManager.get<string>(genSocketRedisKey(userId))

      if (socketId) {
        this.cacheManager.del(genSocketRedisKey(userId))

        const tasks: Promise<number>[] = []
        for (let i = 0; i < platformAccounts.length; i++) {
          tasks.push(
            this.cacheManager.store.client.hdel(
              genSocketRedisKey(platformAccounts[i].openId),
              socketId
            )
          )
        }

        await Promise.all(tasks)
      }
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`移除失败`)
    }
  }

  /**
   * 根据邀请码获取团队信息
   * @param invitationCode
   * @returns
   */
  async getTeamInfoByInvitationCode(invitationCode: string) {
    const team = await this.prisma.team.findUnique({
      where: {
        invitationCode
      }
    })

    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    return {
      avatar: team.avatar,
      name: team.name,
      invitationCode: team.invitationCode,
      ownerId: team.ownerId,
      teamId: team.id
    }
  }

  /**
   * 通过手机号码发送邀请
   * @param param
   */
  async invitationUserByPhone(phone) {
    const { user } = this.request

    const inviteeUser = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    if (!inviteeUser) {
      throw new NotFoundException('该用户未注册')
    }

    const team = await this.prisma.team.findUnique({
      where: {
        id: user.currentTeamId
      }
    })

    if (!team) {
      throw new NotFoundException('当前团队不存在')
    }

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: inviteeUser.id
        }
      }
    })

    if (teamMember) {
      throw new ForbiddenException('该用户已在当前团队中')
    }

    try {
      const invitation = await this.invitationModel.create({
        inviterUserId: user.id,
        inviteeUserId: inviteeUser.id,
        teamId: user.currentTeamId,
        status: StatusEnum.Pending,
        createTime: Date.now()
      })

      const socketId = await this.cacheManager.get<string>(genSocketRedisKey(inviteeUser.id))

      if (socketId) {
        return this.socketService.socketService.send({
          list: JSON.stringify([
            {
              socketId,
              data: {
                type: 'invitation',
                data: {
                  id: invitation._id,
                  inviterUserName: user.name,
                  inviterUserAvatar: user.avatar,
                  teamName: team.name,
                  status: invitation.status,
                  createTime: new Date(invitation.createTime)
                }
              }
            }
          ])
        })
      }
    } catch (e) {
      this.logger.error(e)
      throw new BadRequestException('邀请失败')
    }
  }

  /**
   * 获取团队成员列表
   * @param authorization
   * @returns
   */
  async getTeamMembers() {
    const { user } = this.request
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const members = await this.prisma.teamMember.findMany({
      where: {
        teamId: teamMember.teamId
      },
      include: {
        user: true,
        platformAccounts: true
      }
    })

    return members.map((item) => ({
      status: item.status,
      teamId: item.teamId,
      phone: item.user.phone,
      id: item.id,
      name: item.name,
      userName: item.user.name,
      avatar: item.user.avatar,
      role: item.role,
      platformAccountIds: item.platformAccounts.map((platformAccount) => platformAccount.id),
      joinTime: new Date(item.joinTime).getTime()
    }))
  }

  /**
   * 获取团队成员（分页）
   * @param param
   * @returns
   */
  async getMembersByPaged({ page = 1, size = 10 }: { page: number; size: number }) {
    const { user } = this.request
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const total = await this.prisma.teamMember.count({
      where: {
        teamId: teamMember.teamId
      }
    })

    const members = await this.prisma.teamMember.findMany({
      where: {
        teamId: teamMember.teamId
      },
      include: {
        user: true,
        platformAccounts: true
      },
      skip: (page - 1) * size,
      take: size
    })

    return {
      total,
      page,
      size,
      data: members.map((item) => ({
        status: item.status,
        teamId: item.teamId,
        phone: item.user.phone,
        id: item.id,
        name: item.name,
        userName: item.user.name,
        avatar: item.user.avatar,
        role: item.role,
        platformAccountIds: item.platformAccounts.map((platformAccount) => platformAccount.id),
        joinTime: new Date(item.joinTime).getTime()
      }))
    }
  }

  /**
   * 设置团队成员平台账号
   * @param Authorization
   * @param param1
   */
  async setPlatformAccountMembers({
    memberIds,
    platformAccountId
  }: TeamRequestPlatformAccountMembersDTO) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (teamMember.role === TeamMemberRole.Member) {
      throw new ForbiddenException('无权限')
    }

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        teamId: teamMember.teamId,
        id: {
          in: memberIds
        }
      }
    })

    if (teamMembers.length !== memberIds.length) {
      throw new NotFoundException('团队成员不存在')
    }

    let platformAccount: PlatformAccount
    let oldPlatformAccount: PlatformAccount & {
      affiliates: {
        id: number
        name: string
        role: number
        userId: number
        teamId: number
        joinTime: Date
        user: User
      }[]
    }

    try {
      oldPlatformAccount = await this.prisma.platformAccount.findUnique({
        where: {
          id: platformAccountId,
          teamId: teamMember.teamId
        },
        include: {
          affiliates: {
            include: {
              user: true
            }
          }
        }
      })

      platformAccount = await this.prisma.platformAccount.update({
        where: {
          id: platformAccountId,
          teamId: teamMember.teamId
        },
        data: {
          affiliates: {
            set: memberIds.map((id) => ({ id }))
          }
        }
      })
    } catch (e) {
      throw new NotFoundException('平台账号不存在')
    }

    if (!platformAccount) {
      return
    }

    const removedItems = oldPlatformAccount.affiliates.filter(
      (item1) => !memberIds.some((item2) => item2 === item1.id)
    )

    const addedItems = memberIds.filter(
      (item1) => !oldPlatformAccount.affiliates.some((item2) => item2.id === item1)
    )

    this.logger.log('addedItems', addedItems)
    this.logger.log('removedItems', removedItems)

    const getSocketIdAddTasks: Promise<string>[] = []
    const getSocketAddAuthKey = []

    const addTeamMembers = await this.prisma.teamMember.findMany({
      where: {
        teamId: teamMember.teamId,
        id: {
          in: memberIds
        }
      },
      include: {
        user: true
      }
    })

    for (let i = 0; i < addTeamMembers.length; i++) {
      const member = addTeamMembers[i]
      if (member.role === TeamMemberRole.Member) {
        getSocketAddAuthKey.push(this.cacheManager.get<string>(member.user.phone))
        getSocketIdAddTasks.push(this.cacheManager.get<string>(genSocketRedisKey(member.userId)))
      }
    }

    const getSocketIdRemoveTasks: Promise<string>[] = []
    const getSocketRemoveAuthKey = []

    for (let i = 0; i < removedItems.length; i++) {
      const member = removedItems[i]
      if (member.role === TeamMemberRole.Member) {
        getSocketRemoveAuthKey.push(this.cacheManager.get<string>(member.user.phone))
        getSocketIdRemoveTasks.push(this.cacheManager.get<string>(genSocketRedisKey(member.userId)))
      }
    }

    const addSocketIds = (await Promise.all(getSocketIdAddTasks)).filter(Boolean)
    const removeSocketIds = (await Promise.all(getSocketIdRemoveTasks)).filter(Boolean)
    const addSocketAuthKeys = (await Promise.all(getSocketAddAuthKey)).filter(Boolean)
    const removeSocketAuthKeys = (await Promise.all(getSocketRemoveAuthKey)).filter(Boolean)

    this.logger.log('addSocketIds', addSocketIds)
    this.logger.log('removeSocketIds', removeSocketIds)
    this.logger.log('addSocketAuthKeys', addSocketAuthKeys)
    this.logger.log('removeSocketAuthKeys', removeSocketAuthKeys)

    const setRelationTasks: Promise<number>[] = []
    const setAddTasks: Promise<number>[] = []
    const socketChange = []

    const platformAccountData = {
      platform: platformAccount.platform,
      name: platformAccount.name,
      avatar: platformAccount.avatar,
      openId: platformAccount.openId,
      accountRole: platformAccount.accountRole,
      id: platformAccount.id,
      teamId: platformAccount.teamId,
      expiresTime: platformAccount.expiresIn
        ? platformAccount.expiresIn * 1000 + platformAccount.tokenTime.getTime()
        : 0
    }

    for (let i = 0; i < removeSocketAuthKeys.length; i++) {
      const socketAuthKey = removeSocketAuthKeys[i]
      await this.cacheManager.store.client.hdel(
        genSocketRedisKey(socketAuthKey),
        platformAccount.openId
      )
    }

    for (let i = 0; i < addSocketAuthKeys.length; i++) {
      const socketAuthKey = addSocketAuthKeys[i]
      await this.cacheManager.store.client.hset(
        genSocketRedisKey(socketAuthKey),
        platformAccount.openId,
        platformAccount.id
      )
    }

    for (let i = 0; i < removeSocketIds.length; i++) {
      const socketId = removeSocketIds[i]

      setRelationTasks.push(
        this.cacheManager.store.client.hdel(genSocketRedisKey(platformAccount.openId), socketId)
      )

      socketChange.push({
        socketId,
        data: {
          type: 'changePlatformAccount',
          data: [{ ...platformAccountData, action: 'remove' }]
        }
      })
    }

    for (let i = 0; i < addSocketIds.length; i++) {
      const socketId = addSocketIds[i]

      setAddTasks.push(
        this.cacheManager.store.client.hset(
          genSocketRedisKey(platformAccount.openId),
          socketId,
          platformAccount.id
        )
      )

      this.logger.debug(`set add ${socketId} ${platformAccount.id}`)
      socketChange.push({
        socketId,
        data: {
          type: 'changePlatformAccount',
          data: [
            {
              action: 'add',
              ...platformAccountData
            }
          ]
        }
      })
    }

    try {
      const res = await this.socketService.socketService.send({
        list: JSON.stringify(socketChange)
      })

      await Promise.all(setRelationTasks)
      await Promise.all(setAddTasks)

      return res
    } catch (e) {
      this.logger.error(e)
      throw new BadRequestException('同步信息失败')
    }
  }

  async setMemberPlatformAccounts({
    memberId,
    platformAccountIds
  }: TeamRequestMembersPlatformAccountDTO) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true,
        platformAccounts: true
      }
    })

    if (teamMember.role === TeamMemberRole.Member) {
      throw new ForbiddenException('无权限')
    }

    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        teamId: teamMember.teamId,
        id: {
          in: platformAccountIds
        }
      }
    })

    if (platformAccounts.length !== platformAccountIds.length) {
      throw new NotFoundException('平台账号不存在')
    }

    try {
      const tagetTeamMember = await this.prisma.teamMember.findUnique({
        where: {
          id: memberId
        },
        include: {
          platformAccounts: true,
          user: true
        }
      })

      const teamMemberAfter = await this.prisma.teamMember.update({
        where: {
          teamId: teamMember.teamId,
          id: memberId
        },
        data: {
          platformAccounts: {
            set: platformAccountIds.map((id) => ({ id }))
          }
        },
        include: {
          platformAccounts: true
        }
      })

      const socketId = await this.cacheManager.get<string>(
        genSocketRedisKey(teamMemberAfter.userId)
      )

      if (socketId) {
        const tasks: Promise<number>[] = []
        const cleanTasks: Promise<number>[] = []

        const newPlatformAccounts = []

        const auth = await this.cacheManager.get<string>(tagetTeamMember.user.phone)

        for (let i = 0; i < tagetTeamMember.platformAccounts.length; i++) {
          const platformAccount = tagetTeamMember.platformAccounts[i]

          if (!platformAccountIds.includes(platformAccount.id)) {
            if (auth) {
              await this.cacheManager.store.client.hdel(
                genSocketRedisKey(auth),
                platformAccount.openId
              )
            }

            newPlatformAccounts.push({
              action: 'remove',
              name: platformAccount.name,
              platform: platformAccount.platform,
              avatar: platformAccount.avatar,
              openId: platformAccount.openId,
              accountRole: platformAccount.accountRole,
              id: platformAccount.id,
              teamId: platformAccount.teamId,
              expiresTime: platformAccount.expiresIn
                ? platformAccount.expiresIn * 1000 + platformAccount.tokenTime.getTime()
                : 0
            })

            cleanTasks.push(
              this.cacheManager.store.client.hdel(
                genSocketRedisKey(platformAccount.openId),
                socketId
              )
            )
          }
        }

        const oldPlatformAccountIds = new Set(
          tagetTeamMember.platformAccounts.map((item) => item.id)
        )
        for (let i = 0; i < teamMemberAfter.platformAccounts.length; i++) {
          const platformAccount = teamMemberAfter.platformAccounts[i]

          if (
            !oldPlatformAccountIds.has(platformAccount.id) &&
            user.currentTeamId === platformAccount.teamId
          ) {
            if (auth) {
              await this.cacheManager.store.client.hset(
                genSocketRedisKey(auth),
                platformAccount.openId,
                platformAccount.id
              )
            }

            newPlatformAccounts.push({
              action: 'add',
              platform: platformAccount.platform,
              name: platformAccount.name,
              avatar: platformAccount.avatar,
              openId: platformAccount.openId,
              accountRole: platformAccount.accountRole,
              id: platformAccount.id,
              teamId: platformAccount.teamId,
              expiresTime: platformAccount.expiresIn
                ? platformAccount.expiresIn * 1000 + platformAccount.tokenTime.getTime()
                : 0
            })

            tasks.push(
              this.cacheManager.store.client.hset(
                genSocketRedisKey(platformAccount.openId),
                socketId,
                platformAccount.id
              )
            )
          }
        }

        await Promise.all(cleanTasks)
        await Promise.all(tasks)
        return this.socketService.socketService.send({
          list: JSON.stringify([
            {
              socketId,
              data: {
                type: 'changePlatformAccount',
                data: newPlatformAccounts
              }
            }
          ])
        })
      }
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException('设置失败')
    }
  }

  async setMemberName({ memberId, name }: TeamRequestMembersNameDTO & { memberId: number }) {
    const { user } = this.request

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        OR: [{ userId: user.id }, { id: memberId }],
        teamId: user.currentTeamId
      }
    })

    let userTeamMember: TeamMember
    let memberTeamMember: TeamMember

    teamMembers.forEach((item) => {
      if (item.userId === user.id) {
        userTeamMember = item
      }
      if (item.id === memberId) {
        memberTeamMember = item
      }
    })

    if (!userTeamMember || !memberTeamMember) {
      throw new NotFoundException('团队成员不存在')
    }

    if (userTeamMember.role < memberTeamMember.role) {
      throw new ForbiddenException('无权限')
    }

    try {
      await this.prisma.teamMember.update({
        where: {
          userId_teamId: {
            teamId: userTeamMember.teamId,
            userId: memberTeamMember.userId
          }
        },
        data: {
          name
        }
      })
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`设置失败: ${error}`)
    }
  }

  async dissolveTeam(name: string) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        },
        team: {
          name
        }
      },
      include: {
        team: {
          include: {
            vip: true
          }
        }
      }
    })

    const otherTeamMembers = await this.prisma.teamMember.findMany({
      where: {
        userId: user.id,
        team: {
          ownerId: user.id
        },
        NOT: {
          teamId: user.currentTeamId
        }
      },
      include: {
        team: true
      }
    })

    if (!otherTeamMembers.length) {
      throw new ForbiddenException('至少需要保留1个所创建的团队')
    }

    if (!teamMember || teamMember.role !== TeamMemberRole.Owner) {
      throw new ForbiddenException('无权限')
    }

    if (
      teamMember.team.vip &&
      teamMember.team.vip.expirationTime &&
      teamMember.team.vip.expirationTime.getTime() > Date.now()
    ) {
      throw new ForbiddenException('不能解散VIP团队')
    }

    const autoresponders = await this.prisma.autoresponder.findMany({
      where: {
        teamId: teamMember.teamId
      }
    })

    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        teamId: teamMember.teamId
      }
    })

    const platformAccountTasks = []
    const autoTasks = []

    platformAccounts.forEach((item) => {
      platformAccountTasks.push(this.accountService.deleteAccount(item.id))
    })

    autoresponders.forEach((item) => {
      autoTasks.push(this.autoresponderKeywordService.deleteAutoresponder(item.id))
    })

    await this.logModel.create({
      type: 'dissolveTeam',
      phone: user.phone,
      teamId: user.currentTeamId,
      teamName: teamMember.team.name,
      teamMemberId: teamMember.id,
      createTime: dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
      content: {
        ...teamMember.team
      }
    })

    await this.prisma.$transaction(async (prisma) => {
      await Promise.all(platformAccountTasks)
      await Promise.all(autoTasks)

      await prisma.teamMember.deleteMany({
        where: {
          teamId: teamMember.teamId
        }
      })

      await Promise.all([
        prisma.platformAccount.deleteMany({
          where: {
            teamId: teamMember.teamId
          }
        }),

        prisma.speech.deleteMany({
          where: {
            teamId: teamMember.teamId
          }
        }),
        prisma.variable.deleteMany({
          where: {
            teamId: teamMember.teamId
          }
        })
      ])

      await prisma.team.update({
        where: {
          id: teamMember.teamId
        },
        data: {
          isDelete: true
        }
      })
    })

    const [firstTeamMember] = otherTeamMembers

    await this.setCurrentTeam(firstTeamMember.teamId)

    return {
      teamId: firstTeamMember.teamId
    }
  }

  async getTeamList() {
    const { user } = this.request

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        userId: user.id,
        status: TeamMemberStatus.Normal
      },
      include: {
        team: {
          include: {
            vip: true
          }
        }
      }
    })

    return teamMembers.map((item) => ({
      id: item.teamId,
      name: item.team.name,
      avatar: item.team.avatar,
      currentTeam: item.teamId === user.currentTeamId,
      vip: item.team.vip,
      status: item.status
    }))
  }

  async setCurrentTeam(teamId: number) {
    const { user, authorization } = this.request
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')

    if (teamId === user.currentTeamId) {
      return
    }

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('无权限')
    }
    await this.cacheManager.del(genSocketRedisKey(user.id))
    await this.cacheManager.del(genSocketRedisKey(authorization))

    // let platformAccountIdMap: Record<string, number> = {}

    if (teamMember.role === TeamMemberRole.Member) {
      if (teamMember.platformAccounts && teamMember.platformAccounts.length) {
        const platformAccountIdMap = teamMember.platformAccounts.reduce((acc, item) => {
          acc[item.openId] = item.id
          return acc
        }, {})
        await this.cacheManager.store.client.hmset(
          genSocketRedisKey(authorization),
          platformAccountIdMap
        )
      }
    } else {
      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })

      if (platformAccounts && platformAccounts.length) {
        const platformAccountIdMap = platformAccounts.reduce((acc, item) => {
          acc[item.openId] = item.id
          return acc
        }, {})

        await this.cacheManager.store.client.hmset(
          genSocketRedisKey(authorization),
          platformAccountIdMap
        )
      }
    }

    const tasks: Promise<number>[] = []
    // const deleteTasks: Promise<number>[] = []

    // const oldOpenIds = Object.keys(oldPlatformAccountIdMap)
    // for (let i = 0; i < oldOpenIds.length; i++) {
    //   const openId = oldOpenIds[i]
    //   deleteTasks.push(this.cacheManager.store.client.hdel(genSocketRedisKey(openId), socketId))
    // }

    // const openIds = Object.keys(platformAccountIdMap)
    // for (let i = 0; i < openIds.length; i++) {
    //   const openId = openIds[i]
    //   const platformAccountId = platformAccountIdMap[openId]

    //   tasks.push(
    //     this.cacheManager.store.client.hset(genSocketRedisKey(openId), socketId, platformAccountId)
    //   )
    // }

    // await Promise.all(deleteTasks)
    await Promise.all([
      ...tasks,
      this.prisma.user.update({
        where: {
          id: user.id
        },
        data: {
          currentTeamId: teamId
        }
      }),
      this.cacheManager.set(authorization, { ...user, currentTeamId: teamId }, overdueToken)
    ])
  }

  async quitMember() {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    await this.removeMember(teamMember.id)
  }

  /**
   * 解冻/激活成员账号
   * @param memberId 成员id
   */
  async enableMember(memberId: number) {
    const { user } = this.request
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })
    if (!teamMember) {
      throw new ForbiddenException('未加入团队')
    }

    const tagetTeamMember = await this.prisma.teamMember.findUnique({
      where: {
        id: memberId,
        teamId: teamMember.teamId
      }
    })

    // 正常状态的账号直接返回
    if (tagetTeamMember?.status === TeamMemberStatus.Normal) {
      return
    }

    // 要操作的目标成员是否存在
    if (!tagetTeamMember) {
      throw new NotFoundException('团队成员不存在')
    }

    // 判断当前账号是否有权限
    if (teamMember.role <= tagetTeamMember.role) {
      throw new ForbiddenException('无权限')
    }

    const vip = await this.prisma.vip.findUnique({
      where: {
        teamId: user.currentTeamId
      }
    })

    // 判断vip团队成员数量限制
    if (vip?.expirationTime?.getTime() >= Date.now()) {
      const activeMemberCount = await this.prisma.teamMember.count({
        where: {
          teamId: teamMember.teamId,
          status: TeamMemberStatus.Normal
        }
      })
      if (vip.teamMemberNumberLimit <= activeMemberCount) {
        throw new ForbiddenException('成员账号已达上限')
      }
    } else {
      // 非vip情况 只有一个成员，不允许解冻其他成员
      throw new ForbiddenException('免费用户无法启用成员账号')
    }

    // 解冻目标成员的的状态
    try {
      await this.prisma.teamMember.update({
        where: {
          id: memberId,
          teamId: teamMember.teamId
        },
        data: {
          status: TeamMemberStatus.Normal
        }
      })
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`解除冻结失败`)
    }
  }

  async putWechatMessageLimit(body: TeamWechatMessageLimitRequestDTO) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('未加入团队')
    }

    try {
      const vip = await this.prisma.vip.findUnique({
        where: {
          teamId: teamMember.teamId
        }
      })

      const diff = body.wechatMessageLimit - vip.wechatMessageLimit

      await this.prisma.vip.update({
        where: {
          teamId: teamMember.teamId
        },
        data: {
          wechatMessageLimit: body.wechatMessageLimit
        }
      })

      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId,
          platform: Platform.Wechat
        }
      })

      const now = dayjs().tz('Asia/Shanghai')

      let today8AM = now.startOf('day').hour(8)

      if (now.hour() >= 8) {
        today8AM = today8AM.add(1, 'day')
      }

      const ttlMilliseconds = today8AM.diff(now, 'millisecond')

      for (let i = 0; i < platformAccounts.length; i++) {
        const platformAccount = platformAccounts[i]

        const wxResidueKey = `wxResidue:${platformAccount.openId}`
        const wxResidue = (await this.cacheManager.get(wxResidueKey)) as { residueCount: number }

        if (wxResidue) {
          let residueCount
          if (diff < 0) {
            residueCount = Math.max(wxResidue.residueCount + diff, 0)
          } else {
            residueCount = wxResidue.residueCount + diff
          }
          await this.cacheManager.set(
            wxResidueKey,
            {
              residueCount: Number(residueCount)
            },
            ttlMilliseconds
          )
        } else {
          await this.cacheManager.set(
            wxResidueKey,
            {
              residueCount: Number(body.wechatMessageLimit)
            },
            ttlMilliseconds
          )
        }
      }
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`设置失败`)
    }
  }

  parseUserAgent(userAgent: string) {
    let platform = 'Windows'
    let version = '10.0'

    // 检测 Windows
    if (/Windows NT (\d+\.\d+)/.test(userAgent)) {
      const match = userAgent.match(/Windows NT (\d+\.\d+)/)
      platform = 'Windows'
      version = match[1]
    }
    // 检测 macOS
    else if (/Mac OS X (\d+[_\d]*)/.test(userAgent)) {
      const match = userAgent.match(/Mac OS X (\d+[_\d]*)/)
      platform = 'macOS'
      version = match[1].replace(/_/g, '.')
    }
    // 检测 Android
    else if (/Android (\d+(\.\d+)*)/.test(userAgent)) {
      const match = userAgent.match(/Android (\d+(\.\d+)*)/)
      platform = 'Android'
      version = match[1]
    }
    // 检测 iOS
    else if (/iPhone OS (\d+[_\d]*)/.test(userAgent)) {
      const match = userAgent.match(/iPhone OS (\d+[_\d]*)/)
      platform = 'iphone'
      version = match[1].replace(/_/g, '.')
    }

    return { platform, version }
  }

  getDeviceBrandFromUserAgent(userAgent: string) {
    const brandPatterns = {
      Apple: /iPhone|iPad|iPod/,
      Samsung: /SM-[A-Z0-9]+/,
      Huawei: /HUAWEI|Honor/,
      Xiaomi: /Mi|Redmi/,
      OPPO: /OPPO/,
      VIVO: /VIVO/
    }

    // eslint-disable-next-line no-restricted-syntax
    for (const [brand, pattern] of Object.entries(brandPatterns)) {
      if (pattern.test(userAgent)) {
        return brand
      }
    }
    return 'others'
  }
}
