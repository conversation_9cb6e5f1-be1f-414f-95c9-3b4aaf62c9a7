import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsString } from 'class-validator'

export class Channel {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '渠道名称'
  })
  name: string
}

export class MemberResponse {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '手机号码'
  })
  phone: string

  @ApiResponseProperty({
    type: String,
    example: '昵称'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '头像'
  })
  avatar: string

  @ApiResponseProperty({
    type: Number,
    example: '1726626176'
  })
  createTime: number

  @ApiResponseProperty({
    type: Channel,
    example: {}
  })
  channel: Channel
}

export class MemberResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [MemberResponse]
  })
  data: MemberResponse[]
}

export class MemberUpdateChannelDTO {
  @ApiProperty({
    type: String,
    example: '渠道码',
    required: true
  })
  @IsString()
  channelCode: string
}
