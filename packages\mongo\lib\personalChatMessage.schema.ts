import { ModelDefinition, MongooseModule, Prop, SchemaFactory, Schema } from '@nestjs/mongoose'
import { SchemaTypes } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class PersonalChatMessageEntity {
  @Prop({
    type: String,
    required: false,
    unique: true,
    index: true
  })
  uniqueId: string

  /**
   * @description 平台类型
   */
  @Prop({
    type: String,
    required: true
  })
  platformType: string

  /**
   * @description 账号唯一id（私信归属者）
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  openId: string

  /**
   * @description 事件类型
   */
  @Prop({
    type: String,
    required: true
  })
  event: string

  /**
   * @description 发送方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  fromUserId: string

  /**
   * @description 目标方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  toUserId: string

  /**
   * @description 发送方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  fromAvatar: string

  /**
   * @description 发送方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  fromName: string

  /**
   * @description 目标方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  toAvatar: string

  /**
   * @description 目标方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  toName: string

  /**
   * @description 时间戳 毫秒
   */
  @Prop({
    type: Number,
    default: () => Date.now(),
    index: true
  })
  createTime: number

  /**
   * @description 会话id
   */
  @Prop({
    type: String,
    default: '',
    index: true
  })
  sessionId: string

  /**
   * @description 消息id
   */
  @Prop({
    type: String,
    default: '',
    index: true
  })
  messageId: string

  /**
   * @description 是否撤回(默认未撤回)
   */
  @Prop({
    type: Number,
    default: 0
  })
  isRecall: number

  /**
   * @description 是否自动发送(默认手动发送)
   */
  @Prop({
    type: Number,
    default: 0
  })
  isAuto: number

  /**
   * @description 消息内容
   */
  @Prop({
    type: SchemaTypes.Map,
    required: true
  })
  content: Map<string, string>
}

export const PersonalChatMessagesSchema: ModelDefinition = <const>{
  name: PersonalChatMessageEntity.name,
  schema: SchemaFactory.createForClass(PersonalChatMessageEntity)
    .index({
      event: 1,
      fromUserId: 1,
      sessionId: 1
    })
    .index({
      event: 1,
      toUserId: 1,
      sessionId: 1
    })
    .index({
      openId: 1,
      sessionId: 1,
      createTime: -1
    })
}

export const PersonalChatMessagesMongoose = MongooseModule.forFeature([PersonalChatMessagesSchema])
