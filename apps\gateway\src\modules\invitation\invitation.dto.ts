import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsEnum, IsNotEmpty } from 'class-validator'

export enum StatusEnum {
  Pending = 'pending',
  Accepted = 'accepted',
  Rejected = 'rejected'
}

export class InvitationActionRequestBodyDTO {
  @ApiProperty({
    description: '状态类型',
    enum: StatusEnum,
    example: StatusEnum.Accepted,
    required: true
  })
  @IsEnum(StatusEnum)
  @IsNotEmpty({ message: 'status不能为空' })
  status: StatusEnum

  @ApiProperty({
    type: String,
    example: 'id',
    required: true
  })
  @IsNotEmpty({ message: 'id不能为空' })
  id: string
}

export class InvitationQueryDTO {
  @ApiProperty({
    type: Number,
    example: 1,
    required: false
  })
  page: number

  @ApiProperty({
    type: Number,
    example: 10,
    required: false
  })
  size: number

  @ApiProperty({
    type: Number,
    example: 1722938599,
    required: false
  })
  createTime: number
}

export class InvitationResponseType {
  @ApiResponseProperty({
    type: String,
    example: 'id'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '邀请人名称'
  })
  inviterUserName: string

  @ApiResponseProperty({
    type: String,
    example: '团队名称'
  })
  teamName: string

  @ApiResponseProperty({
    type: String,
    example: 'pending(未处理),accepted(同意),rejected(拒绝)'
  })
  status: string

  @ApiResponseProperty({
    type: Number,
    example: 1723082810
  })
  createTime: number
}

export class InvitationResponse {
  @ApiResponseProperty({
    type: [InvitationResponseType]
  })
  data: InvitationResponseType[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class InvitationResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: InvitationResponse
  })
  data: InvitationResponse
}

export class InvitationActionResponseDTO extends BaseResponseDTO {}

export class UserInfoResponseDTO {
  @ApiResponseProperty({
    type: String,
    example: '1300120012DE89D1DE89D'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '13800138000'
  })
  phone: string

  @ApiResponseProperty({
    type: String,
    example: '张三'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'http://example.com/avatar.png'
  })
  avatar: string
}

export class InvitationStatusResponse {
  @ApiResponseProperty({
    type: Boolean,
    example: '是否已加入, true表示已加入,false表示未加入'
  })
  isJoin: boolean

  @ApiResponseProperty({
    type: Boolean,
    example: '是否存在邀请, true表示已存在则不能邀请,false表示不存在可以再邀请'
  })
  isInvitation: boolean

  @ApiResponseProperty({
    type: UserInfoResponseDTO
  })
  user: UserInfoResponseDTO
}

export class InvitationStatusResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: InvitationStatusResponse
  })
  data: InvitationStatusResponse
}
