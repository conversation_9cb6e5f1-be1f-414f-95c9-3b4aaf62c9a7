/* eslint-disable no-case-declarations */
/* eslint-disable no-continue */
import { ForbiddenException, BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import {
  LikeAndFollowActionEntity,
  PersonalChatMessageEntity,
  WorkCommentEntity,
  MessagesByAutoresponderEntity
} from '@qdy/mongo'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { InjectModel } from '@nestjs/mongoose'
import { AnyObject, Model } from 'mongoose'
import { Cache } from 'cache-manager'
import { PrismaService } from '@qdy/mysql'
import { WebhookEvents } from './constant'
import { genSocketRedisKey } from '@qdy/utils'
import { WebhookBody } from './types'
import {
  AutoresponderKeywordKey,
  AutoresponderVariableKey,
  PlatformAccountKeywordKey
} from '../autoresponder/constant'
import { postReplyComment, postSendMessage } from '../interact/external'
import {
  AutoresponderContentChildType,
  AutoresponderContentTextType,
  AutoresponderContentType,
  AutoresponderKeywordContent,
  AutoresponderKeywordRedisValue,
  AutoresponderKeywordRule,
  AutoresponderTriggerType
} from '../autoresponder/autoresponder.dto'
import { InteractRequestSendType, InteractResponseMessageType } from '../interact/interact.dto'
import { generateRandom, wait } from '../../common/utils'
import { Queue, Worker } from 'bullmq'
import { WebHookServiceGrpc } from './webhook.rpc'
import { AccountAccountsStatus } from '../account/account.dto'
import { AlipaySdk } from 'alipay-sdk'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { OrderStatus, PayType } from '../order/order.dto'
import WxPay from 'wechatpay-node-v3'
import fs from 'fs'
import { join } from 'path'
import dayjs from 'dayjs'
import { eventKey, orderEventEmitter } from '../order/order.event'
import { OrderManageService, PlatformAccountManageService } from '@qdy/common'
import { sendJpushMessageEventKey, sendJpushMessageEventEmitter } from './webhook.jpush'

@Injectable()
export class WebhookService {
  logger = new Logger('WebhookService')

  taskQueue: Queue

  taskWorker: Worker

  commentQueue: Queue

  commentWorker: Worker

  callbackTaskQueue: Queue

  callbackTaskWorker: Worker

  constructor(
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(WorkCommentEntity.name) private workCommentModel: Model<WorkCommentEntity>,
    @InjectModel(LikeAndFollowActionEntity.name)
    private likeAndFollowActionModel: Model<LikeAndFollowActionEntity>,
    @InjectModel(MessagesByAutoresponderEntity.name)
    private messagesByAutoresponderModel: Model<MessagesByAutoresponderEntity>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly orderManageService: OrderManageService,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  onModuleInit() {
    this.logger.log('WebhookService init')

    this.taskQueue = new Queue('autoresponder', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'autoresponder',
      async (job) => {
        const { contents, ...others } = job.data

        setTimeout(() => {
          this.autoresponderTask(contents, others).catch((err) => {
            throw new BadRequestException(`task${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.commentQueue = new Queue('commentAutoresponder', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.commentWorker = new Worker(
      'commentAutoresponder',
      async (job) => {
        this.commentAutoresponderSend(job.data).catch((err) => {
          throw new BadRequestException(`commentAutoresponder${err.message}`)
        })
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.callbackTaskQueue = new Queue('callbackevent', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.callbackTaskWorker = new Worker(
      'callbackevent',
      async (job) => {
        const { body, messageId } = job.data

        setTimeout(() => {
          this.callbackEventNewTask(body, messageId).catch((err) => {
            throw new BadRequestException(`callbackeventTask${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async webhook(body: WebhookBody, messageId: string) {
    const { from_user_id: fromUserId } = body
    let { to_user_id: toUserId } = body

    if (!toUserId) {
      toUserId = fromUserId
    }

    if (body.event === WebhookEvents.Authorize || body.event === WebhookEvents.ContractAuthorize) {
      return 'success'
    }

    const job = await this.callbackTaskQueue.getJob(
      `${body.event}-${messageId}-${fromUserId}-${toUserId}`
    )

    if (job) {
      return 'success'
    }

    await this.callbackTaskQueue.add(
      'callbackevent',
      {
        body,
        messageId
      },
      {
        removeOnComplete: true,
        removeOnFail: true,
        jobId: `${body.event}-${messageId}-${fromUserId}-${toUserId}`
      }
    )

    return 'success'
  }

  async callbackEventNewTask(body: WebhookBody, messageId: string) {
    const { from_user_id: fromUserId } = body
    let { to_user_id: toUserId } = body

    if (!toUserId) {
      toUserId = fromUserId
    }

    let sessionId = ''
    let openId = ''
    let userIdMaps: Record<string, string>

    if (body.event === WebhookEvents.Unauthorize) {
      try {
        userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(fromUserId))

        const platformAccount = await this.prisma.platformAccount.update({
          where: {
            openId: fromUserId
          },
          data: {
            expiresIn: 0,
            refreshExpiresIn: 0
          }
        })

        await this.platformAccountManageService.deletePlatformAccountRedisInfo(platformAccount)

        const changePlatformAccounts = []
        Object.keys(userIdMaps).forEach((socketId) => {
          changePlatformAccounts.push({
            socketId,
            data: {
              type: 'changePlatformAccount',
              data: [
                {
                  action: 'timeout',
                  platform: platformAccount.platform,
                  name: platformAccount.name,
                  avatar: platformAccount.avatar,
                  openId: platformAccount.openId,
                  accountRole: platformAccount.accountRole,
                  id: platformAccount.id,
                  teamId: platformAccount.teamId,
                  expiresTime: 0
                }
              ]
            }
          })
        })
        return this.webhookGrpcService.socketService.send({
          list: JSON.stringify(changePlatformAccounts)
        })
      } catch (error) {
        throw new BadRequestException(`解除授权失败 error${error.message}`)
      }
    }

    if (body.event === WebhookEvents.ContractUnauthorize) {
      try {
        const platformAccount = await this.prisma.platformAccount.findUnique({
          where: {
            openId: fromUserId
          }
        })

        if (platformAccount) {
          if (platformAccount.unauthorize) {
            const scopes: Array<string> = JSON.parse(platformAccount.unauthorize)
            const mergedScopes = scopes.concat(body.content.scopes)

            await this.prisma.platformAccount.update({
              where: {
                id: platformAccount.id
              },
              data: {
                unauthorize: JSON.stringify(mergedScopes)
              }
            })
          } else {
            await this.prisma.platformAccount.update({
              where: {
                id: platformAccount.id
              },
              data: {
                unauthorize: JSON.stringify(body.content.scopes)
              }
            })
          }
        }

        return
      } catch (err) {
        throw new BadRequestException(`取消scope授权 error${err.message}`)
      }
    }

    if (body.event === WebhookEvents.ImEnterDirectMessage) {
      try {
        const lookKey = `welcome-look:${fromUserId}-${toUserId}`
        if (await this.cacheManager.get(lookKey)) {
          return
        }
        await this.cacheManager.set(lookKey, true, 60 * 60 * 1000)

        this.autoresponderWelcome(
          toUserId,
          fromUserId,
          body.content.server_message_id as string,
          body.content.conversation_short_id as string
        )
          .then(async (value) => {
            if (!value) {
              await this.cacheManager.del(lookKey)
            }
          })
          .catch((err) => {
            throw new BadRequestException(`autoresponderWelcome error${err.message}`)
          })
      } catch (err) {
        throw new BadRequestException(`cacheManager error${err.message}`)
      }
      return
    }

    switch (body.event) {
      case WebhookEvents.IMSendMessage:
        openId = fromUserId
        sessionId = toUserId
        break
      case WebhookEvents.CommentReply:
        openId = toUserId
        sessionId = body.content.reply_to_item_id as string
        break
      case WebhookEvents.IMReceiveMessage:
        openId = toUserId
        sessionId = fromUserId
        break
      case WebhookEvents.IMGroupSendMessage:
        openId = fromUserId
        sessionId = body.content.conversation_short_id as string
        break
      case WebhookEvents.IMGroupReceiveMessage:
        openId = toUserId
        sessionId = body.content.conversation_short_id as string
        break
      case WebhookEvents.GroupFansEvent:
        openId = toUserId
        sessionId = body.content.im_group_id as string
        break
      case WebhookEvents.EnterGroupAuditChange:
        openId = fromUserId
        sessionId = body.content.group_id as string
        break
      case WebhookEvents.NewVideoDigg:
        openId = toUserId
        sessionId = toUserId
        break
      case WebhookEvents.NewFollowAction:
        openId = toUserId
        sessionId = toUserId
        break
      case WebhookEvents.IMRecallMsg:
        openId = fromUserId
        sessionId = fromUserId
        break
      default:
        break
    }

    const { secondClientKey } = this.configService.get<RootConfigMap['app']>('app')

    if (body.client_key === secondClientKey) {
      // 当应用是蚁小二，找不到账号信息则直接返回
      const account = await this.prisma.platformAccount.findUnique({
        where: {
          openId
        }
      })

      if (!account) {
        return
      }
    }

    switch (body.event) {
      case WebhookEvents.IMReceiveMessage:
      case WebhookEvents.IMGroupReceiveMessage:
      case WebhookEvents.CommentReply:
      case WebhookEvents.GroupFansEvent:
      case WebhookEvents.NewFollowAction:
      case WebhookEvents.NewVideoDigg:
        userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(toUserId))
        break
      case WebhookEvents.IMSendMessage:
      case WebhookEvents.IMGroupSendMessage:
      case WebhookEvents.EnterGroupAuditChange:
        userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(fromUserId))
        break
      default:
        {
          const [fromUserMaps = {}, toUserMaps = {}] = (await Promise.all([
            this.cacheManager.store.client.hgetall(genSocketRedisKey(fromUserId)),
            this.cacheManager.store.client.hgetall(genSocketRedisKey(toUserId))
          ])) as [Record<string, string>, Record<string, string>]

          userIdMaps = { ...fromUserMaps, ...toUserMaps }
        }
        break
    }

    const data = {
      platformType: 'douyin',
      uniqueId: messageId,
      event: body.event,
      openId,
      fromUserId,
      toUserId,
      fromAvatar: '',
      fromName: '',
      toAvatar: '',
      toName: '',
      sessionId,
      content: {},
      createTime: body.content.create_time as number,
      isAuto: 0,
      messageId: ''
    }

    if (body.content.user_infos) {
      body.content.user_infos.forEach((item) => {
        if (item.open_id === fromUserId) {
          data.fromAvatar = item.avatar
          data.fromName = item.nick_name
        } else if (item.open_id === toUserId) {
          data.toAvatar = item.avatar
          data.toName = item.nick_name
        }
      })
    }

    switch (body.event) {
      case WebhookEvents.CommentReply:
        data.createTime *= 1000
        data.fromAvatar = body.content.avatar.toString()
        data.fromName = body.content.nick_name.toString()
        data.messageId = body.content.comment_id.toString()
        data.content = {
          clientKey: body.client_key,
          commentId: body.content.comment_id,
          commentUserId: body.content.comment_user_id,
          content: body.content.content,
          diggCount: body.content.digg_count,
          replyCommentTotal: body.content.reply_comment_total,
          replyToCommentId: body.content.reply_to_comment_id,
          replyToItemId: body.content.reply_to_item_id,
          atUserId: body.content.at_user_id,
          avatar: body.content.avatar,
          name: body.content.nick_name,
          parentId: body.content.parent_id,
          top: body.content.top
        }

        const autoFlag = await this.cacheManager.get(body.log_id)
        data.isAuto = autoFlag ? 1 : 0

        break
      case WebhookEvents.EnterGroupAuditChange:
        data.content = {
          clientKey: body.client_key,
          userAvatar: body.content.user_avatar,
          userNicker: body.content.user_nicker,
          applyStatus: body.content.apply_status,
          applyId: body.content.apply_id,
          groupId: body.content.group_id
        }
        break
      case WebhookEvents.GroupFansEvent:
        data.content = {
          clientKey: body.client_key,
          openId: body.content.open_id,
          groupType: body.content.group_type,
          createGroupType: body.content.create_group_type,
          imGroupId: body.content.im_group_id
        }
        break
      case WebhookEvents.IMGroupSendMessage:
      case WebhookEvents.IMSendMessage:
      case WebhookEvents.IMGroupReceiveMessage:
      case WebhookEvents.IMReceiveMessage:
        data.messageId = body.content.server_message_id.toString()
        data.content = {
          clientKey: body.client_key,
          conversationShortId: body.content.conversation_short_id,
          serverMessageId: body.content.server_message_id,
          messageType: body.content.message_type,
          text: body.content.text,
          sceneType: body.content.scene_type,
          resourceType: body.content.resource_type,
          width: body.content.resource_width,
          height: body.content.resource_height,
          resourceUrl: body.content.resource_url,
          cardId: body.content?.card_id,
          cardStatus: body.content?.card_status,
          cardData: JSON.stringify(body.content?.card_data)
        }
        if (body.event === WebhookEvents.IMSendMessage && body.log_id) {
          const autoFlag = await this.cacheManager.get(body.log_id)
          data.isAuto = autoFlag ? 1 : 0
        }
        break
      case WebhookEvents.NewVideoDigg:
        data.content = {
          actionTime: body.content.action_time,
          actionType: body.content.action_type,
          itemId: body.content.item_id
        }
        delete data.isAuto
        break
      case WebhookEvents.NewFollowAction:
        data.content = {
          actionTime: body.content.action_time,
          actionType: body.content.action_type
        }
        delete data.isAuto
        break
      case WebhookEvents.IMRecallMsg:
        data.messageId = body.content.server_message_id.toString()
        data.uniqueId = body.content.server_message_id.toString()
        data.createTime = Date.now()
        data.content = {
          serverMessageId: body.content.server_message_id
        }
        break
      default:
        return
    }

    switch (body.event) {
      case WebhookEvents.CommentReply:
        try {
          await this.workCommentModel.create(data)
          // this.autoresponderKeywordComment(body).catch((err) => {
          //   throw new BadRequestException(`抖音评论自动回复失败${err.message}`)
          // })

          // this.autoresponderKeywordCommentReceive(body).catch((err) => {
          //   throw new BadRequestException(`抖音评论自动回复私信回复失败${err.message}`)
          // })
        } catch (error) {
          throw new BadRequestException(`抖音评论写入数据库失败:${error.message}`)
        }
        break
      case WebhookEvents.GroupFansEvent:
      case WebhookEvents.EnterGroupAuditChange:
        try {
          await this.personalChatMessageModel.create(data)
        } catch (error) {
          throw new BadRequestException(`抖音用户加群与用户加群申请写入数据库失败:${error.message}`)
        }
        break
      case WebhookEvents.IMGroupSendMessage:
      case WebhookEvents.IMSendMessage:
      case WebhookEvents.IMGroupReceiveMessage:
      case WebhookEvents.IMReceiveMessage:
        try {
          await this.personalChatMessageModel.create(data)

          if (
            body.event === WebhookEvents.IMReceiveMessage &&
            body.content.message_type === 'retain_consult_card'
          ) {
            if (body.content.card_status === 2) {
              // 留资卡片完成态
              const platformAccount = await this.prisma.platformAccount.findUnique({
                where: {
                  openId: toUserId
                }
              })

              if (platformAccount) {
                await this.prisma.cardMessage.create({
                  data: {
                    cardId: body.content.card_id as string,
                    teamId: platformAccount.teamId,
                    fromUserId: data.fromUserId,
                    fromName: data.fromName,
                    fromAvatar: data.fromAvatar,
                    toUserId: data.toUserId,
                    toAvatar: data.toAvatar,
                    toName: data.toName,
                    name: body.content.card_data.find((item) => item.label === '姓名')?.value,
                    phone: body.content.card_data.find((item) => item.label === '手机号')?.value,
                    address: body.content.card_data.find((item) => item.label === '城市')?.value,
                    cardData: body.content.card_data,
                    createTime: new Date(data.createTime)
                  }
                })
              }
            }
          }

          this.autoresponderKeywordChat({
            event: body.event,
            conversationShortId: body.content.conversation_short_id as string,
            serverMessageId: body.content.server_message_id as string,
            fromUserId,
            toUserId,
            itemId: '',
            text: body.content.text as string
          }).catch((err) => {
            throw new BadRequestException(`autoresponderKeywordChat${err.message}`)
          })
        } catch (error) {
          this.logger.log(error)
          throw new BadRequestException(`抖音私信写入数据库失败:${error.message}`)
        }
        break
      case WebhookEvents.NewVideoDigg:
      case WebhookEvents.NewFollowAction:
        try {
          await this.likeAndFollowActionModel.create(data)
          this.autoresponderKeywordChat({
            event: body.event,
            conversationShortId: '',
            serverMessageId: '',
            fromUserId,
            toUserId,
            itemId: body.content.item_id as string,
            text: body.content.text as string
          }).catch((err) => {
            throw new BadRequestException(`抖音点赞关注自动回复失败${err.message}`)
          })
        } catch (error) {
          throw new BadRequestException(`抖音点赞关注写入数据库失败:${error.message}`)
        }
        break
      case WebhookEvents.IMRecallMsg:
        try {
          await this.personalChatMessageModel.updateOne(
            {
              fromUserId: data.fromUserId,
              toUserId: data.toUserId,
              messageId: data.messageId
            },
            {
              isRecall: 1
            }
          )
        } catch (error) {
          throw new BadRequestException(`抖音撤回私信更新数据库失败:${error.message}`)
        }
        break
      default:
        break
    }

    switch (body.event) {
      case WebhookEvents.IMGroupReceiveMessage:
        sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
          openId: data.openId,
          messageId: data.messageId,
          event: data.event,
          content: '收到一条抖音群消息'
        })
        break
      case WebhookEvents.IMReceiveMessage:
        sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
          openId: data.openId,
          messageId: data.messageId,
          event: data.event,
          content: '收到一条抖音私信消息'
        })
        break
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    Object.keys(userIdMaps).forEach((socketId) => {
      dataList.push({
        socketId,
        data: { ...data, platformAccountId: parseInt(userIdMaps[socketId], 10) }
      })
    })

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`发送失败 error${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }

  async autoresponderKeywordComment(body: WebhookBody) {
    try {
      if (body.to_user_id === body.from_user_id) {
        return
      }

      const [res, res2] = await Promise.all([
        this.cacheManager.store.client.hget(AutoresponderKeywordKey, body.to_user_id),
        this.cacheManager.store.client.hget(
          AutoresponderKeywordKey,
          `${body.to_user_id}:${body.content.reply_to_item_id}`
        )
      ])

      if (res || res2) {
        await wait(15000)

        const arr = [
          ...JSON.parse(res || '[]'),
          ...JSON.parse(res2 || '[]')
        ] as AutoresponderKeywordRedisValue[]

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          body.to_user_id
        )

        const { token, accountExpired, platformAccountId, teamId } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const item = arr[i]

          const {
            contents,
            keyword,
            rule,
            state,
            scene,
            autoresponderId,
            stopTime,
            stopInterval,
            isDelay,
            delayTime
          } = item

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-comment:${body.from_user_id}-${autoresponderId}`))
          ) {
            continue
          }

          if (!scene) {
            continue
          }

          if (!state || accountExpired < Date.now()) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (
            rule !== AutoresponderKeywordRule.Match &&
            rule !== AutoresponderKeywordRule.Instantly
          ) {
            // 评论回复只有两种规则（关键字和即刻回复）
            continue
          }

          const content = contents[
            generateRandom(contents.length - 1)
          ] as AutoresponderKeywordContent

          if (!content || !Array.isArray(content.texts)) {
            continue
          }

          const contentText = content.texts.reduce((acc, cur) => {
            acc += cur.text
            return acc
          }, '')

          const prop = {
            openId: body.to_user_id,
            itemId: body.content.reply_to_item_id as string,
            commentId: body.content.comment_id as string,
            content: contentText,
            accessToken: token,
            platformAccountId,
            teamId,
            autoresponderId,
            commentUserId: body.from_user_id
          }

          if (rule === AutoresponderKeywordRule.Match) {
            const value = (keyword as string[]).find((item) => {
              return RegExp(item, 'g').test(body.content.content as string)
            })

            this.logger.debug('autoresponderKeywordCommon matchItem', JSON.stringify(item))

            if (value) {
              await this.commentQueue.add('commentAutoresponder', prop, {
                delay: isDelay && delayTime > 0 ? generateRandom(delayTime) * 1000 : 100,
                removeOnComplete: true,
                removeOnFail: true,
                jobId: `${AutoresponderKeywordKey}-${body.from_user_id}-${body.to_user_id}-comment-${autoresponderId}`
              })
              // await postReplyComment(prop)
            }
          } else {
            this.logger.debug('autoresponderKeywordCommon ', JSON.stringify(item))

            await this.commentQueue.add('commentAutoresponder', prop, {
              delay: isDelay && delayTime > 0 ? generateRandom(delayTime) * 1000 : 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${body.from_user_id}-${body.to_user_id}-comment-${autoresponderId}`
            })

            // await postReplyComment(prop)
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-comment:${body.from_user_id}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }
        }
      }
    } catch (error) {
      throw new BadRequestException(`AutoresponderKeywordKey error${error.message}`)
    }
  }

  /**
   * 评论回复私信 im_receive_msg
   */
  async autoresponderKeywordCommentReceive(body: WebhookBody) {
    try {
      if (body.to_user_id === body.from_user_id) {
        return
      }

      const { from_user_id: fromUserId, to_user_id: toUserId } = body

      const [res, res2] = await Promise.all([
        this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId),
        this.cacheManager.store.client.hget(
          AutoresponderKeywordKey,
          `${toUserId}:${body.content.reply_to_item_id}`
        )
      ])

      const twentyFourHoursAgo = dayjs().subtract(24, 'hours')
      const message = await this.personalChatMessageModel
        .findOne({
          fromUserId,
          toUserId,
          event: 'im_receive_msg',
          createdAt: {
            $gte: twentyFourHoursAgo.toDate()
          }
        })
        .exec()

      if (message && (res || res2)) {
        const messageId = message.content.get('serverMessageId')
        const conversationId = message.content.get('conversationShortId')

        const arr = [
          ...JSON.parse(res || '[]'),
          ...JSON.parse(res2 || '[]')
        ] as AutoresponderKeywordRedisValue[]
        const items = []

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          body.to_user_id
        )

        const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const { contents, contentType, rule, state, stopReply, scene, autoresponderId } = arr[
            i
          ] as AutoresponderKeywordRedisValue

          if (scene || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          const send = rule === AutoresponderKeywordRule.Comment

          if (send && !job) {
            items.push(arr[i])
          }
        }

        const matchItem = items[generateRandom(items.length - 1)]

        this.logger.debug('autoresponderKeywordCommentReceive comment', JSON.stringify(matchItem))

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId,
              toUserId,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'douyin'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponder',
            {
              token,
              openId: toUserId,
              messageId,
              conversationId,
              toUserId: fromUserId,
              platformAccountId,
              teamId,
              autoresponderId,
              contents
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`autoresponderKeywordCommentReceive error${error.message}`)
    }
  }

  async autoresponderWelcome(
    toUserId: string,
    fromUserId: string,
    serverMessageId: string,
    conversationShortId: string
  ) {
    const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

    let isSuc = false

    if (res) {
      const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]

      await wait(1000)

      if (!arr.length) {
        return
      }

      const accountRes = await this.cacheManager.store.client.hget(
        PlatformAccountKeywordKey,
        toUserId
      )

      const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

      for (let i = 0; i < arr.length; i++) {
        const { contents, contentType, state, autoresponderId, executionCount } = arr[i]

        if (
          contentType !== AutoresponderContentType.Greeting ||
          status === AccountAccountsStatus.Disable
        ) {
          continue
        }

        if (!state || accountExpired < Date.now()) {
          continue
        }

        if (!Array.isArray(contents)) {
          continue
        }

        if (!contents.length) {
          continue
        }

        if (executionCount) {
          // 查询是否有对这个账号有发送过消息
          const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
            fromUserId,
            toUserId,
            autoresponderId
          })

          if (messageByAutoresponder) {
            return
          }

          await this.messagesByAutoresponderModel.create({
            fromUserId,
            toUserId,
            autoresponderId,
            platformType: 'douyin'
          })
        }

        for (let i = 0; i < contents.length; i++) {
          const content = contents[i] as AutoresponderKeywordContent

          await wait(content.delay * 1000 || 100)

          await this.autoresponderSend(content, {
            token,
            openId: toUserId,
            messageId: serverMessageId,
            conversationId: conversationShortId,
            toUserId: fromUserId,
            platformAccountId,
            teamId,
            autoresponderId,
            scene: 'im_enter_direct_msg'
          })

          isSuc = true
        }
      }
    }

    return isSuc
  }

  async autoresponderKeywordChat({
    event,
    fromUserId,
    toUserId,
    text,
    itemId,
    serverMessageId,
    conversationShortId
  }: {
    event: WebhookEvents
    fromUserId: string
    toUserId: string
    text: string
    itemId: string
    serverMessageId: string
    conversationShortId: string
  }) {
    if (fromUserId === toUserId) {
      return
    }

    if (event === WebhookEvents.IMReceiveMessage && text) {
      try {
        const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

        if (res) {
          await wait(1000)
          const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]
          const items = []

          const accountRes = await this.cacheManager.store.client.hget(
            PlatformAccountKeywordKey,
            toUserId
          )

          const { token, accountExpired, platformAccountId, teamId, status } =
            JSON.parse(accountRes)

          for (let i = 0; i < arr.length; i++) {
            const {
              contents,
              contentType,
              keyword,
              rule,
              state,
              stopReply,
              scene,
              autoresponderId,
              trigger
            } = arr[i] as AutoresponderKeywordRedisValue

            if (scene || status === AccountAccountsStatus.Disable) {
              continue
            }

            if (!Array.isArray(contents)) {
              continue
            }

            if (!contents.length) {
              continue
            }

            if (contentType !== AutoresponderContentType.Autoresponder) {
              continue
            }

            if (accountExpired < Date.now() || !state) {
              continue
            }

            const job = await this.taskQueue.getJob(
              `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            )

            if (stopReply && job) {
              await job.remove()
              continue
            }

            if (
              rule !== AutoresponderKeywordRule.Match &&
              rule !== AutoresponderKeywordRule.Instantly
            ) {
              // 收到私信回复只有两种规则（关键字和即刻回复）
              continue
            }

            let send = rule !== AutoresponderKeywordRule.Match

            if (!send) {
              send = !!(keyword as string[]).find((item) => {
                return RegExp(item, 'g').test(text)
              })
            }

            const chatMessage = trigger === AutoresponderTriggerType.Chat

            if (send && !job && chatMessage) {
              items.push(arr[i])
            }
          }

          const matchItem =
            items.find((item) => item.rule === AutoresponderKeywordRule.Match) ||
            items[generateRandom(items.length - 1)]

          this.logger.debug(
            'autoresponderKeywordChat matchItem',
            JSON.stringify(matchItem),
            JSON.stringify(items)
          )

          if (matchItem) {
            const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

            if (
              stopInterval &&
              (await this.cacheManager.get(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`
              ))
            ) {
              return
            }

            if (stopInterval && stopTime > 0) {
              await this.cacheManager.set(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
                1,
                stopTime * 1000
              )
            }

            if (executionCount) {
              // 查询是否有对这个账号有发送过消息
              const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
                fromUserId,
                toUserId,
                autoresponderId
              })

              if (messageByAutoresponder) {
                return
              }

              await this.messagesByAutoresponderModel.create({
                fromUserId,
                toUserId,
                autoresponderId,
                platformType: 'douyin'
              })
            }

            const [start] = contents

            await this.taskQueue.add(
              'autoresponder',
              {
                token,
                openId: toUserId,
                messageId: serverMessageId,
                conversationId: conversationShortId,
                toUserId: fromUserId,
                platformAccountId,
                teamId,
                autoresponderId,
                contents
              },
              {
                delay: start.delay * 1000 || 100,
                removeOnComplete: true,
                removeOnFail: true,
                jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
              }
            )
          }
        }
      } catch (error) {
        throw new BadRequestException(`AutoresponderKeywordKey error${error.message}`)
      }
    } else if (event === WebhookEvents.NewVideoDigg) {
      try {
        const [res, res2] = await Promise.all([
          this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId),
          this.cacheManager.store.client.hget(AutoresponderKeywordKey, `${toUserId}:${itemId}`)
        ])

        const twentyFourHoursAgo = dayjs().subtract(24, 'hours')
        const message = await this.personalChatMessageModel
          .findOne({
            fromUserId,
            toUserId,
            event: 'im_receive_msg',
            createdAt: {
              $gte: twentyFourHoursAgo.toDate()
            }
          })
          .exec()

        if (message && (res || res2)) {
          const messageId = message.content.get('serverMessageId')
          const conversationId = message.content.get('conversationShortId')

          if (!messageId || !conversationId) {
            this.logger.log('message', message.content)
            return
          }

          await wait(1000)
          const arr = [
            ...JSON.parse(res || '[]'),
            ...JSON.parse(res2 || '[]')
          ] as AutoresponderKeywordRedisValue[]
          const items = []

          const accountRes = await this.cacheManager.store.client.hget(
            PlatformAccountKeywordKey,
            toUserId
          )

          const { token, accountExpired, platformAccountId, teamId, status } =
            JSON.parse(accountRes)

          for (let i = 0; i < arr.length; i++) {
            const { contents, contentType, state, stopReply, scene, autoresponderId, trigger } =
              arr[i] as AutoresponderKeywordRedisValue

            if (scene || status === AccountAccountsStatus.Disable) {
              continue
            }

            if (!Array.isArray(contents)) {
              continue
            }

            if (!contents.length) {
              continue
            }

            if (contentType !== AutoresponderContentType.Autoresponder) {
              continue
            }

            if (accountExpired < Date.now() || !state) {
              continue
            }

            const job = await this.taskQueue.getJob(
              `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            )

            if (stopReply && job) {
              await job.remove()
              continue
            }

            const send = trigger === AutoresponderTriggerType.Like

            if (send && !job) {
              items.push(arr[i])
            }
          }

          const matchItem = items[generateRandom(items.length - 1)]

          this.logger.debug(
            'autoresponderKeywordChat like',
            JSON.stringify(matchItem),
            JSON.stringify(items)
          )

          if (matchItem) {
            const { contents, autoresponderId, stopTime, stopInterval } = matchItem

            if (
              stopInterval &&
              (await this.cacheManager.get(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`
              ))
            ) {
              return
            }

            if (stopInterval && stopTime > 0) {
              await this.cacheManager.set(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
                1,
                stopTime * 1000
              )
            }

            const [start] = contents

            await this.taskQueue.add(
              'autoresponder',
              {
                token,
                openId: toUserId,
                messageId,
                conversationId,
                toUserId: fromUserId,
                platformAccountId,
                teamId,
                autoresponderId,
                contents
              },
              {
                delay: start.delay * 1000 || 100,
                removeOnComplete: true,
                removeOnFail: true,
                jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
              }
            )
          }
        }
      } catch (error) {
        throw new BadRequestException(`AutoresponderKeywordKey Like error${error.message}`)
      }
    } else if (event === WebhookEvents.NewFollowAction) {
      try {
        const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

        const twentyFourHoursAgo = dayjs().subtract(24, 'hours')
        const message = await this.personalChatMessageModel
          .findOne({
            fromUserId,
            toUserId,
            event: 'im_receive_msg',
            createdAt: {
              $gte: twentyFourHoursAgo.toDate()
            }
          })
          .exec()

        if (message && res) {
          const messageId = message.content.get('serverMessageId')
          const conversationId = message.content.get('conversationShortId')

          if (!messageId || !conversationId) {
            this.logger.log('message', message.content)
            return
          }
          await wait(1000)
          const arr = JSON.parse(res || '[]') as AutoresponderKeywordRedisValue[]
          const items = []

          const accountRes = await this.cacheManager.store.client.hget(
            PlatformAccountKeywordKey,
            toUserId
          )

          const { token, accountExpired, platformAccountId, teamId, status } =
            JSON.parse(accountRes)

          for (let i = 0; i < arr.length; i++) {
            const { contents, contentType, state, stopReply, scene, autoresponderId, trigger } =
              arr[i] as AutoresponderKeywordRedisValue

            if (scene || status === AccountAccountsStatus.Disable) {
              continue
            }

            if (!Array.isArray(contents)) {
              continue
            }

            if (!contents.length) {
              continue
            }

            if (contentType !== AutoresponderContentType.Autoresponder) {
              continue
            }

            if (accountExpired < Date.now() || !state) {
              continue
            }

            const job = await this.taskQueue.getJob(
              `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            )

            if (stopReply && job) {
              await job.remove()
              continue
            }

            const send = trigger === AutoresponderTriggerType.Follow

            if (send && !job) {
              items.push(arr[i])
            }
          }

          const matchItem = items[generateRandom(items.length - 1)]

          this.logger.debug(
            'autoresponderKeywordChat follow',
            JSON.stringify(matchItem),
            JSON.stringify(items)
          )

          if (matchItem) {
            const { contents, autoresponderId, stopTime, stopInterval } = matchItem

            if (
              stopInterval &&
              (await this.cacheManager.get(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`
              ))
            ) {
              return
            }

            if (stopInterval && stopTime > 0) {
              await this.cacheManager.set(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
                1,
                stopTime * 1000
              )
            }

            const [start] = contents

            await this.taskQueue.add(
              'autoresponder',
              {
                token,
                openId: toUserId,
                messageId,
                conversationId,
                toUserId: fromUserId,
                platformAccountId,
                teamId,
                autoresponderId,
                contents
              },
              {
                delay: start.delay * 1000 || 100,
                removeOnComplete: true,
                removeOnFail: true,
                jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
              }
            )
          }
        }
      } catch (error) {
        throw new BadRequestException(`AutoresponderKeywordKey Follow error${error.message}`)
      }
    }
  }

  async autoresponderTask(
    contents: AnyObject[],
    {
      token,
      openId,
      messageId,
      conversationId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId
    }: {
      token: string
      openId: string
      messageId: string
      conversationId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
    }
  ) {
    const [content, ...otherContents] = contents as AutoresponderKeywordContent[]

    if (!content) {
      return
    }

    await this.autoresponderSend(content, {
      token,
      openId,
      messageId,
      conversationId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId
    })

    if (otherContents.length) {
      const [start] = otherContents

      await this.taskQueue.add(
        'autoresponder',
        {
          token,
          openId,
          messageId,
          conversationId,
          toUserId,
          platformAccountId,
          teamId,
          autoresponderId,
          contents: otherContents
        },
        {
          delay: start.delay * 1000 || 100,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${AutoresponderKeywordKey}-${toUserId}-${openId}-${autoresponderId}`
        }
      )
    }
  }

  async autoresponderSend(
    content: AutoresponderKeywordContent,
    {
      token,
      openId,
      messageId,
      conversationId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId,
      scene
    }: {
      token: string
      openId: string
      messageId: string
      conversationId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
      scene?: string
    }
  ) {
    let contentText = ''
    let messageType = InteractResponseMessageType.Text

    if (content.messageType === AutoresponderContentChildType.Text) {
      for (let j = 0; j < content.texts.length; j++) {
        const text = content.texts[j]
        if (text.type === AutoresponderContentTextType.Text) {
          contentText += text.text
        } else if (text.type === AutoresponderContentTextType.Variable) {
          const value = await this.cacheManager.store.client.hget(
            AutoresponderVariableKey,
            `${text.variableId}`
          )
          try {
            const values = JSON.parse(value).value
            contentText += values[generateRandom(values.length - 1)]
          } catch (err) {
            throw new BadRequestException(`autoresponderSend text error${err.message}`)
          }
        }
      }
    } else if (content.messageType === AutoresponderContentChildType.Image) {
      messageType = InteractResponseMessageType.Image
      contentText = content.imageId
    } else if (content.messageType === AutoresponderContentChildType.Card) {
      messageType = InteractResponseMessageType.Card
      contentText = content.cardInfo.find((item) => item.accountId === platformAccountId).cardId
    }

    try {
      const data = await postSendMessage({
        messageType,
        content: contentText,
        openId,
        accessToken: token,
        messageId,
        conversationId,
        toUserId,
        platformAccountId,
        teamId,
        sendType: InteractRequestSendType.Single,
        auto: true,
        autoresponderId,
        redisClient: this.cacheManager,
        scene
      })

      if (data?.extra?.logid) {
        await this.cacheManager.set(data?.extra?.logid, 1, 1000 * 60 * 10)
      }
    } catch (err) {
      throw new BadRequestException(`自动回复失败 ${err.message}`)
    }
  }

  async commentAutoresponderSend({
    openId,
    itemId,
    commentId,
    content,
    accessToken,
    platformAccountId,
    teamId,
    autoresponderId,
    commentUserId
  }: {
    openId: string
    itemId: string
    commentId: string
    content: string
    accessToken: string
    platformAccountId: number
    teamId: number
    autoresponderId: number
    commentUserId: string
  }) {
    try {
      const data = await postReplyComment({
        openId,
        itemId,
        commentId,
        content,
        accessToken,
        platformAccountId,
        teamId,
        auto: true,
        autoresponderId,
        commentUserId,
        redisClient: this.cacheManager
      })
      if (data?.extra?.logid) {
        await this.cacheManager.set(data?.extra?.logid, 1, 1000 * 60 * 10)
      }
    } catch (err) {
      throw new BadRequestException(`自动回复失败 ${err.message}`)
    }
  }

  async alipayWebhook(body: any) {
    try {
      const { alipay } = this.configService.get<RootConfigMap['app']>('app')

      const alipaySdk = new AlipaySdk({
        // 设置应用 ID
        appId: alipay.appId,
        // 设置应用私钥
        privateKey: alipay.privateKey,
        // 设置支付宝公钥
        alipayPublicKey: alipay.alipayPublicKey,
        // 密钥类型，请与生成的密钥格式保持一致，参考平台配置一节
        keyType: 'PKCS8'

        // 设置网关地址，默认是 https://openapi.alipay.com
        // endpoint: 'https://openapi.alipay.com',
      })

      const result = await alipaySdk.checkNotifySign(body)

      if (result) {
        // 验证通过，处理支付成功的逻辑
        const tradeStatus = body.trade_status
        const orderNo = body.out_trade_no
        const tradeNo = body.trade_no
        const receiptAmount = Number(body.receipt_amount) // 实付金额
        const gmtPayment = new Date()

        const order = await this.prisma.order.findUnique({
          where: {
            orderNo
          },
          include: {
            orderInfos: true,
            interest: true,
            vip: true
          }
        })

        if (!order) {
          return 'success'
        }

        if (order.orderStatus === OrderStatus.SUCCESS) {
          return 'success'
        }

        if (tradeStatus === 'TRADE_SUCCESS') {
          const checkOrder = await alipaySdk.curl('POST', '/v3/alipay/trade/query', {
            body: {
              out_trade_no: orderNo,
              trade_no: tradeNo
            }
          })

          if (checkOrder.responseHttpStatus !== 200) {
            throw new ForbiddenException('支付宝查询订单失败')
          }

          if (checkOrder.data.trade_status !== 'TRADE_SUCCESS') {
            throw new ForbiddenException('支付宝查询订单失败')
          }

          // 支付成功，进行相应的业务处理
          if (order.dueAmount === receiptAmount) {
            const startTime = dayjs(gmtPayment)
            const endTime = startTime.add(order.vipMonth, 'month')
            const totalTime = endTime.add(order.freeMonth, 'month')

            const { isUpgrade } = order

            const toTime = isUpgrade
              ? dayjs(order.vip.expirationTime)
                  .add(order.vipMonth + order.freeMonth, 'month')
                  .toDate()
              : totalTime.toDate()

            await this.prisma.order.update({
              where: {
                id: order.id
              },
              data: {
                payTime: gmtPayment,
                orderStatus: OrderStatus.SUCCESS,
                payAmount: receiptAmount,
                payType: PayType.ALIPAY,
                toTime
              }
            })

            const orderInfo = order.orderInfos.find((item) => item.payType === PayType.ALIPAY)

            if (orderInfo) {
              await this.prisma.orderInfo.update({
                where: {
                  id: orderInfo.id
                },
                data: {
                  payTime: gmtPayment,
                  tradeNo
                }
              })
            }

            await this.orderManageService.handleCompletedOrder(order.orderNo)

            await this.cacheManager.del(`overview:${order.teamId}`)

            orderEventEmitter.emit(eventKey, { orderNo: order.orderNo, type: 'close' })
          }

          return 'success'
        }

        return 'fail'
      }

      return 'fail'
    } catch (error) {
      return 'fail'
    }
  }

  async wechatPayWebhook(
    body: any,
    {
      wechatTimestamp,
      wechatNonce,
      wechatSerial,
      wechatSignature
    }: {
      wechatTimestamp: number | string
      wechatNonce: string
      wechatSerial: string
      wechatSignature: string
    }
  ) {
    const { wechatPay } = this.configService.get<RootConfigMap['app']>('app')
    try {
      const pay = new WxPay({
        appid: wechatPay.appId,
        mchid: wechatPay.mchId,
        key: wechatPay.apiV3Key,
        publicKey: fs.readFileSync(join(__dirname, '../cert', 'apiclient_cert.pem')), // 公钥
        privateKey: fs.readFileSync(join(__dirname, '../cert', 'apiclient_key.pem')) // 秘钥
      })

      const result = await pay.verifySign({
        timestamp: wechatTimestamp,
        nonce: wechatNonce,
        body,
        serial: wechatSerial,
        signature: wechatSignature,
        apiSecret: wechatPay.apiV3Key
      })

      if (result) {
        if (body.event_type === 'TRANSACTION.SUCCESS') {
          const data: any = pay.decipher_gcm(
            body.resource.ciphertext,
            body.resource.associated_data,
            body.resource.nonce,
            wechatPay.apiV3Key
          )

          if (data.trade_state === 'SUCCESS') {
            const order = await this.prisma.order.findUnique({
              where: {
                orderNo: data.out_trade_no
              },
              include: {
                orderInfos: true,
                interest: true,
                vip: true
              }
            })

            if (!order) {
              return { code: 'FAIL' }
            }

            const checkOrder = await pay.query({
              transaction_id: data.transaction_id
            })

            if (checkOrder.status !== 200) {
              throw new ForbiddenException('微信查询订单失败')
            }

            if (checkOrder.data.trade_state !== 'SUCCESS') {
              throw new ForbiddenException('微信查询订单失败')
            }

            // 支付成功，进行相应的业务处理
            const receiptAmount = data.amount.total / 100
            if (order.dueAmount === receiptAmount) {
              const gmtPayment = new Date(data.success_time)
              const startTime = dayjs(gmtPayment)
              const endTime = startTime.add(order.vipMonth, 'month')
              const totalTime = endTime.add(order.freeMonth, 'month')

              const { isUpgrade } = order
              const toTime = isUpgrade
                ? dayjs(order.vip.expirationTime)
                    .add(order.vipMonth + order.freeMonth, 'month')
                    .toDate()
                : totalTime.toDate()

              await this.prisma.order.update({
                where: {
                  id: order.id
                },
                data: {
                  payTime: gmtPayment,
                  orderStatus: OrderStatus.SUCCESS,
                  payAmount: receiptAmount,
                  payType: PayType.WECHAT,
                  toTime
                }
              })

              const orderInfo = order.orderInfos.find((item) => item.payType === PayType.WECHAT)

              if (orderInfo) {
                await this.prisma.orderInfo.update({
                  where: {
                    id: orderInfo.id
                  },
                  data: {
                    payTime: gmtPayment,
                    tradeNo: data.transaction_id
                  }
                })
              }

              await this.orderManageService.handleCompletedOrder(order.orderNo)

              await this.cacheManager.del(`overview:${order.teamId}`)

              orderEventEmitter.emit(eventKey, { orderNo: order.orderNo, type: 'close' })
            }

            return { code: 'SUCCESS' }
          }
        }
      }
      return { code: 'FAIL' }
    } catch (error) {
      this.logger.debug(error)
      return { code: 'FAIL' }
    }
  }
}
