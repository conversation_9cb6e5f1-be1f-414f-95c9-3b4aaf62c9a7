import { Controller, Get, Query } from '@nestjs/common'
import { OverviewService } from './overview.service'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import {
  FinancialResponseDTO,
  OverviewDto,
  TeamStatisticListRequest,
  UserRegisterResponesDTO
} from './overview.dto'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('overview')
@ApiTags('数据概览')
export class OverviewController {
  constructor(private readonly overviewService: OverviewService) {}

  @ApiOperation({ summary: '获取 TOP 10' })
  @ApiOkResponse({ description: '操作成功', type: OverviewDto })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @Get('top-ten')
  @ApiQuery({
    name: 'type',
    required: true,
    type: Number,
    description: '事件区间 1:昨天 2:7 天 3:30 天'
  })
  getTopTen(@Query('type') type: number) {
    return this.overviewService.getData(type)
  }

  @ApiOperation({ summary: '获取收益趋势' })
  @ApiOkResponse({ description: '操作成功', type: FinancialResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @Get('income')
  @ApiQuery({
    name: 'type',
    required: true,
    type: Number,
    description: '事件区间 1:昨天 2:7 天 3:30 天'
  })
  getIncome(@Query('type') type: number) {
    return this.overviewService.getFinancial(type)
  }

  @ApiOperation({ summary: '获取30天注册人数' })
  @ApiOkResponse({ description: '操作成功', type: UserRegisterResponesDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @Get('register')
  getRegister() {
    return this.overviewService.getRegister()
  }

  @Get('team/rate')
  @ApiOperation({ summary: '团队转化率/续费率' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiQuery({ type: TeamStatisticListRequest })
  getTeamConverRate(@Query() params: TeamStatisticListRequest) {
    return this.overviewService.getTeamRate(params)
  }
}
