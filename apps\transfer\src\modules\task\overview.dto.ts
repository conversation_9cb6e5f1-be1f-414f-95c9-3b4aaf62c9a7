import { Autoresponder, PlatformAccount } from '@qdy/mysql'
import { AnyObject } from 'mongoose'

export const AutoresponderKeywordKey = 'autoresponder:keyword'

export enum AutoresponderKeywordRule {
  Match = 'match',
  Instantly = 'instantly',
  Comment = 'comment',
  Follow = 'follow',
  Like = 'like'
}

export enum SalesType {
  NotBuy = 'NotBuy',
  FirstBuy = 'FirstBuy',
  ReBuy = 'ReBuy'
}

export enum AutoresponderTriggerType {
  welcome,
  Chat,
  Comment,
  Follow,
  Like
}

export class AutoresponderKeywordRedisValue {
  status: PlatformAccount['status']

  platform: Autoresponder['platform']

  scene: Autoresponder['scene']

  trigger: Autoresponder['trigger']

  keyword: Autoresponder['keywords']

  stopReply: Autoresponder['stopReply']

  stopInterval: Autoresponder['stopInterval']

  isDelay: Autoresponder['isDelay']

  isNew: Autoresponder['isNew']

  delayTime: Autoresponder['delayTime']

  stopTime: Autoresponder['stopTime']

  rule: Autoresponder['rule']

  contents: AnyObject[]

  contentType: Autoresponder['contentType']

  state: Autoresponder['state']

  accountExpired: number

  token: string

  platformAccountId: number

  teamId: number

  autoresponderId: number

  username: string
  // imageId: Autoresponder['imageId']
}
