import axios from 'axios'

const loginApi = 'login'
const loginCount = 'user/stat/loginCount'
const offlineCount = 'user/stat/offlineCount'
const wxList = 'user/wx/list'
const loginRecord = 'user/stat/loginRecord'

const baseUrl = 'http://36.111.200.122:4408/finder/'

export function login({ username, password }: { username: string; password: string }) {
  const url = `${baseUrl}${loginApi}`
  return axios.post(url, {
    username,
    password
  })
}

export function postLoginCount(data: { token: string; startDate: string }) {
  const url = `${baseUrl}${loginCount}`
  const body = {
    startDate: data.startDate
  }
  return axios.post(url, body, {
    headers: {
      Authorization: `Bearer ${data.token}`
    }
  })
}

export function postOfflineCount(data: { token: string; startDate: string }) {
  const url = `${baseUrl}${offlineCount}`
  const body = {
    startDate: data.startDate
  }
  return axios.post(url, body, {
    headers: {
      Authorization: `Bearer ${data.token}`
    }
  })
}

export function postWxList(data: {
  token: string
  wxid: string
  page: number
  size: number
  status: string
  startTime: string
  endTime: string
}) {
  const url = `${baseUrl}${wxList}`
  const body = {
    wxid: data.wxid,
    pageNum: data.page,
    pageSize: data.size,
    status: data.status,
    loginStartTime: data.startTime,
    loginEndTime: data.endTime
  }
  return axios.post(url, body, {
    headers: {
      Authorization: `Bearer ${data.token}`
    }
  })
}

export function postLoginRecord(data: { token: string; wxid: string; page: number; size: number }) {
  const url = `${baseUrl}${loginRecord}`

  return axios.get(url, {
    headers: {
      Authorization: `Bearer ${data.token}`
    },
    params: {
      wxid: data.wxid,
      pageNum: data.page,
      pageSize: data.size
    }
  })
}
