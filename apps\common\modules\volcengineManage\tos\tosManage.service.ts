import { Injectable, Logger } from '@nestjs/common'
import {
  PreSignedPostRequestDTO,
  PreSignedUrlRequestDTO,
  PutObjectByStreamRequestDTO
} from './tosManage.dto'
import { TosClient, TosClientError, TosServerError } from '@volcengine/tos-sdk'
import axios from 'axios'
import { PassThrough } from 'stream'

@Injectable()
export class TosManageService {
  private client: TosClient

  private bucketName = 'qdy-image'

  private host = 'https://qdy-image.tos-cn-shanghai.volces.com'

  logger = new Logger('TosManageService')

  constructor() {
    this.client = new TosClient({
      accessKeyId: process.env.TOS_ACCESS_KEY_ID,
      accessKeySecret: process.env.TOS_ACCESS_KEY_SECRET,
      region: 'cn-shanghai', // 填写 Bucket 所在地域。以华北2（北京)为例，"Provide your region" 填写为 cn-beijing。
      endpoint: 'tos-cn-shanghai.volces.com' // 填写域名地址
    })
  }

  async getPreSignedUrl({ method, objectName }: PreSignedUrlRequestDTO) {
    try {
      if (method !== 'GET' && method !== 'PUT') {
        throw new Error(`Unsupported method: ${method}`)
      }

      const url = this.client.getPreSignedUrl({
        // method 支持 'GET'/'PUT'/'HEAD'/'DELETE'
        method,
        bucket: this.bucketName,
        key: objectName
      })

      return url
    } catch (error) {
      if (error instanceof TosClientError) {
        throw new Error(`Client Err Msg:${error.message}, Client Err Stack:${error.stack}`)
      } else if (error instanceof TosServerError) {
        this.logger.log('Request ID:', error.requestId)
        this.logger.log('Response Status Code:', error.statusCode)
        this.logger.log('Response Header:', error.headers)
        this.logger.log('Response Err Code:', error.code)
        this.logger.log('Response Err Msg:', error.message)
        throw new Error(
          `Request ID:${error.requestId}, Response Status Code:${error.statusCode}, Response Err Code:${error.code}, Response Err Msg:${error.message}`
        )
      } else {
        this.logger.log('unexpected exception, message: ', error)
      }
    }
  }

  async getPreSignedPostSignature({ objectName }: PreSignedPostRequestDTO) {
    try {
      const res = await this.client.preSignedPostSignature({
        bucket: this.bucketName,
        key: objectName,
        expiresIn: 3600
      })

      return { ...res, host: this.host }
    } catch (error) {
      if (error instanceof TosClientError) {
        throw new Error(`Client Err Msg:${error.message}, Client Err Stack:${error.stack}`)
      } else if (error instanceof TosServerError) {
        this.logger.log('Request ID:', error.requestId)
        this.logger.log('Response Status Code:', error.statusCode)
        this.logger.log('Response Header:', error.headers)
        this.logger.log('Response Err Code:', error.code)
        this.logger.log('Response Err Msg:', error.message)
        throw new Error(
          `Request ID:${error.requestId}, Response Status Code:${error.statusCode}, Response Err Code:${error.code}, Response Err Msg:${error.message}`
        )
      } else {
        this.logger.log('unexpected exception, message: ', error)

        throw new Error(`unexpected exception, message: ${error.message}`)
      }
    }
  }

  async putObjectByStream({ url, referer, teamCode }: PutObjectByStreamRequestDTO) {
    try {
      // 使用 axios 请求远程视频文件并获取流
      const response = await axios.get(url, {
        responseType: 'stream',
        headers: {
          Referer: referer
        }
      })

      // 创建一个 PassThrough 流，将远程视频流传递到 OSS 上传
      const passThrough = new PassThrough()
      response.data.pipe(passThrough) // 将下载的数据流输送到 PassThrough 流

      const fileName = this.getFileNameFromUrl(url)

      // 调用 tos 服务上传文件流
      const result = await this.client.putObject({
        bucket: this.bucketName,
        key: `${teamCode}/${fileName}`,
        body: passThrough
      })

      if (result.statusCode === 200) {
        return `${this.host}/${teamCode}/${fileName}`
      }

      return ''
    } catch (error) {
      this.logger.log(error.message)
    }
  }

  getFileNameFromUrl(url: string): string {
    const parsedUrl = new URL(url)
    const { pathname } = parsedUrl
    if (!pathname) {
      return ''
    }
    // 去除问号及后面的部分
    const pathWithoutQuery = pathname.split('?')[0]
    const segments = pathWithoutQuery.split('/')
    const fileName = segments[segments.length - 1]
    return fileName
  }
}
