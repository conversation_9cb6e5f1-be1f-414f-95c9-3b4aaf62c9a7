import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Queue, Worker } from 'bullmq'
import { <PERSON>ron } from '@nestjs/schedule'

import { PrismaService } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { AccountAccountsStatus } from '../account/account.dto'
import { checkOnline, wechatLogout } from '../account/external.wechat'
import { Platform } from '@qdy/utils'

@Injectable()
export class WechantAccountCheckCronService implements OnModuleInit {
  private readonly logger = new Logger(WechantAccountCheckCronService.name)

  wechatAccountCheckQueue: Queue

  wechatAccountCheckWorker: Worker

  wechatToken: string

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async onModuleInit() {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    this.wechatToken = wechatConfig.Token

    this.wechatAccountCheckQueue = new Queue('wechat-platform-account-check-init', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })

    this.wechatAccountCheckWorker = new Worker(
      'wechat-platform-account-check-init',
      async (job) => {
        const { data } = job.data
        this.logger.log(`Running ${job.id} data ${data}`)
        await this.onUpdateWechatAccountOpusCron(data)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.logger.log('wechat-account-check init')
  }

  /**
   * 微信账号检测定时任务
   * 每天2点
   */
  @Cron('0 00 2 * * *', {
    name: 'WechatAccountCheckCron',
    timeZone: 'Asia/Shanghai'
  })
  async WechatAccountCheckCron() {
    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        platform: Platform.Wechat,
        status: AccountAccountsStatus.Normal
      }
    })

    for (let i = 0; i < platformAccounts.length; i++) {
      const { appId, id, isNew } = platformAccounts[i]

      this.wechatAccountCheckQueue.add(
        'wechat-platform-account-check-init',
        {
          data: {
            appId,
            id,
            type: 'check',
            isNew
          }
        },
        {
          delay: 0, // 立即执行
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `wechat-account-init-check-${appId}`
        }
      )
    }
  }

  /**
   * 微信账号检测定时任务
   * 每天3点
   */
  @Cron('0 0 3 * * *', {
    name: 'WechatAllAccountThirdCheckCron',
    timeZone: 'Asia/Shanghai'
  })
  async ThirdWechatAccountCheckCron() {
    this.logger.log('暂时停止检测')
    // if (process.env.NODE_ENV === 'prod') {
    //   const accounts = await getAllAccount(this.wechatToken, false)

    //   if (isArray(accounts) && accounts.length > 0) {
    //     for (let i = 0; i < accounts.length; i++) {
    //       const { appId, wxid, isNew } = accounts[i]

    //       const platformAccount = await this.prisma.platformAccount.findUnique({
    //         where: {
    //           openId: wxid,
    //           status: AccountAccountsStatus.Normal
    //         }
    //       })

    //       if (!platformAccount) {
    //         this.logger.log(`找不到${appId}`)
    //         this.wechatAccountCheckQueue.add(
    //           'wechat-platform-account-check-init',
    //           {
    //             data: {
    //               appId,
    //               id: 0,
    //               type: 'out',
    //               isNew
    //             }
    //           },
    //           {
    //             removeOnComplete: true,
    //             removeOnFail: true,
    //             jobId: `wechat-account-init-check-${appId}`
    //           }
    //         )
    //       }
    //     }
    //   }
    // }
  }

  async onUpdateWechatAccountOpusCron({ id, appId, type, isNew }) {
    if (!appId || !type) {
      return
    }

    try {
      if (type === 'out') {
        await wechatLogout({ appId, isNew })
        return
      }

      if (type === 'check') {
        const data = await checkOnline({ appId, token: this.wechatToken, isNew })

        if (data.data === true) {
          // 在线
          await this.prisma.platformAccount.update({
            where: {
              id
            },
            data: {
              expiresIn: **********
            }
          })
        } else {
          // 离线
          // await this.prisma.platformAccount.update({
          //   where: {
          //     id
          //   },
          //   data: {
          //     expiresIn: 0
          //   }
          // })
        }
      }
    } catch (error) {
      this.logger.error('账号检测数据更新失败', error)
    }
  }
}
