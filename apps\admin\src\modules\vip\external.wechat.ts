import axios from 'axios'

const wechatOutApi = '/api/login/logout'

export function wechatLogout({ appId }: { appId: string }) {
  const url = `${process.env.WECHAT_BASE_URL}${wechatOutApi}`
  const videosApiToken = process.env.WECHAT_TOKEN
  return axios.post(
    url,
    {
      appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )
}
