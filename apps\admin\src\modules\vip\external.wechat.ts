import axios from 'axios'

const wechatOutApi = '/api/login/logout'

const baseUrl = 'http://api.videosapi.com/finder/v2'
const newBaseUrl = 'http://36.111.200.122:4408/finder/v2'

export function wechatLogout({ appId, isNew }: { appId: string; isNew: boolean }) {
  const url = isNew ? `${newBaseUrl}${wechatOutApi}` : `${baseUrl}${wechatOutApi}`
  const videosApiToken = isNew ? process.env.WECHAT_NEW_TOKEN : process.env.WECHAT_TOKEN
  return axios.post(
    url,
    {
      appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )
}
