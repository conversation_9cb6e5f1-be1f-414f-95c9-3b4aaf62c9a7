import { ForbiddenException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { UserLoginRegisterRequestBodyDTO } from './user.dto'
import { ChannelAdminUser, PrismaService } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { nanoid } from 'nanoid'
import { ConfigService } from '@nestjs/config'
import { RedisStore } from 'cache-manager-ioredis-yet'

import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { RootConfigMap } from '@qdy/config'
import crypto from 'crypto'

@Injectable()
export class UserService {
  logger = new Logger('UserService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  verifyPassword(password: string, salt: string, hash: string) {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return hash === hashToVerify
  }

  /**
   * 用户登录
   * @param param
   * @param ip
   * @returns
   */
  async putLoginUser({ username, password }: UserLoginRegisterRequestBodyDTO) {
    const userInfo = await this.prisma.channelAdminUser.findUnique({
      where: {
        username
      }
    })

    if (!userInfo || !this.verifyPassword(password, userInfo.salt, userInfo.password)) {
      throw new NotFoundException('账号或密码错误')
    }

    const authorization = await this.generateAuthorization(userInfo)

    return {
      authorization
    }
  }

  /**
   *  生成 token 并缓存
   * @param phone
   * @param ip
   */
  async generateAuthorization(userInfo: ChannelAdminUser) {
    const authorization = nanoid()
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')
    const authCacheKey = this.getChannelCacheKey(userInfo.username)
    const token = this.getChannelCacheKey(authorization)
    const oldAuthorization = await this.cacheManager.get<string>(authCacheKey)
    if (oldAuthorization) {
      await Promise.all([
        this.cacheManager.del(authCacheKey),
        this.cacheManager.del(oldAuthorization)
      ])
    }

    await Promise.all([
      this.cacheManager.set(authCacheKey, token, overdueToken),
      this.cacheManager.set(token, userInfo, overdueToken)
    ])

    return token
  }

  /**
   * 删除 Userorization
   * @param Userorization
   * @returns
   */
  async deleteAuthorization() {
    const { user, authorization } = this.request

    if (!user) {
      throw new ForbiddenException('登录失效, 请重新登录')
    }

    if (user) {
      await Promise.all([
        this.cacheManager.del(this.getChannelCacheKey(user.username)),
        this.cacheManager.del(authorization)
      ])
    }

    this.logger.log(`delete Authorization: ${authorization} and phone: ${user.phone}`)
  }

  async getUserInfo() {
    const { user } = this.request
    return {
      id: user.id,
      username: user.username,
      role: user.role
    }
  }

  async getAdminUsers(page: number = 1, size: number = 10) {
    if (!page || page < 1) {
      page = 1
    }
    if (!size || size < 1) {
      size = 10
    }

    const [adminUsers, total] = await Promise.all([
      this.prisma.adminUser.findMany({
        orderBy: { id: 'desc' },
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.adminUser.count()
    ])

    return {
      total,
      page,
      size,
      data: adminUsers.map((item) => ({
        id: item.id,
        role: item.role,
        nickname: item.nickname,
        username: item.username,
        createTime: item.createTime.getTime()
      }))
    }
  }

  hasAlphabet(password: string): boolean {
    return /[a-zA-Z]/.test(password)
  }

  hasDigit(password: string): boolean {
    return /\d/.test(password)
  }

  validatePassword(password: string) {
    return this.hasAlphabet(password) && this.hasDigit(password)
  }

  getChannelCacheKey(cacheKey: string) {
    return 'Channel_' + cacheKey
  }
}
