import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString, Length } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class UserLoginRegisterRequestBodyDTO {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    required: true
  })
  @IsString({ message: '用户名格式不正确' })
  @IsNotEmpty({ message: '用户名不能为空' })
  @Length(4, 18, { message: '请输入4-18位用户名' })
  username: string

  @ApiProperty({
    description: '密码',
    example: '123456',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(6, 24, { message: '请输入6-24位密码' })
  password: string
}

export class UserRegisterRequestBodyDTO {
  @ApiProperty({
    description: '用户昵称',
    example: '李力',
    required: true
  })
  @Length(1, 15, { message: '请输入1-15位用户昵称' })
  @IsString({ message: '用户昵称格式不正确' })
  @IsNotEmpty({ message: '用户昵称不能为空' })
  nickname: string

  @ApiProperty({
    description: '用户名',
    example: 'admin',
    required: true
  })
  @IsString({ message: '用户名格式不正确' })
  @IsNotEmpty({ message: '用户名不能为空' })
  username: string

  @ApiProperty({
    description: '密码',
    example: '123456',
    required: true
  })
  @IsOptional()
  @IsString({ message: '密码格式不正确' })
  @Length(8, 15, { message: '请输入8-15位密码' })
  password: string
}

export class UserDeleteResponseDTO extends BaseResponseDTO {}

class UserLoginResphoneDTO {
  /**
   * @description
   * 唯一凭证
   */
  @ApiResponseProperty({
    type: String,
    format: 'nanoid',
    example: '1300120012DE89D1DE89D'
  })
  authorization: string
}

export class UserLoginOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserLoginResphoneDTO
  })
  data: UserLoginResphoneDTO
}

export class UserSendCodeResponseDTO extends BaseResponseDTO {}

class UserInfoResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '张三'
  })
  nickname: string

  @ApiResponseProperty({
    type: String,
    example: '13800138000'
  })
  username: string

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  role: number
}

export class UserOkUserInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserInfoResponseDTO
  })
  data: UserInfoResponseDTO
}

export class UsersResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [UserInfoResponseDTO]
  })
  data: UserInfoResponseDTO[]
}
