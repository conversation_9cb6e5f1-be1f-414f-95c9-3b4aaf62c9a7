import { ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export enum OrderStatus {
  PENDING = 'pending', // 待支付
  SUCCESS = 'success', // 成功支付
  CANCELED = 'canceled', // 已取消
  REFUND = 'refund'
}

export class VipOrder {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'KKJOE982934023KDJIEOW'
  })
  orderNo: string

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  price: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  payAmount: number

  @ApiResponseProperty({
    type: String,
    example: '订单状态'
  })
  orderStatus: string

  @ApiResponseProperty({
    type: String,
    example: 1
  })
  payType: string

  @ApiResponseProperty({
    type: Number,
    example: '支付时间'
  })
  payTime: number
}

export class VipOrdersResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  totalAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [VipOrder]
  })
  data: VipOrder[]
}
