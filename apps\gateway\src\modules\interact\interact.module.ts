import { <PERSON><PERSON><PERSON> } from '@nestjs/common'
import { InteractController } from './interact.controller'
import { InteractService } from './interact.service'
import {
  MessagesMongoose,
  CommentMongoose,
  WechatMessagesMongoose,
  WechatOpusMongoose,
  WechatCommentMongoose,
  PersonalChatMessagesMongoose,
  WorkCommentMongoose,
  UserMessageReadRecordMongoose
} from '@qdy/mongo'
import { InteractWechatService } from './interact.service.wechat'
import { InteractKuaishouService } from './interact.service.kuaishou'
import { InteractWeiboService } from './interact.service.weibo'
import { InteractXiaohongshuService } from './interact.service.xiaohongshu'
import { AccountSocketService } from '../account/account.task'

@Module({
  imports: [
    MessagesMongoose,
    CommentMongoose,
    WechatMessagesMongoose,
    WechatOpusMongoose,
    WechatCommentMongoose,
    PersonalChatMessagesMongoose,
    WorkCommentMongoose,
    UserMessageReadRecordMongoose
  ],
  controllers: [InteractController],
  providers: [
    InteractService,
    InteractWechatService,
    InteractKuaishouService,
    InteractWeiboService,
    InteractXiaohongshuService,
    AccountSocketService
  ]
})
export class InteractModule {}
