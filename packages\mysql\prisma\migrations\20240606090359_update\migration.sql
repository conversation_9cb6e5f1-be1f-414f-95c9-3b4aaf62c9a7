/*
  Warnings:

  - You are about to drop the column `memberAccountSignCardinalNumber` on the `SystemDosage` table. All the data in the column will be lost.
  - You are about to drop the column `standardAccountSignCardinalNumber` on the `SystemDosage` table. All the data in the column will be lost.
  - You are about to drop the `VIP` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `memberAccountSingleCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.
  - Added the required column `standardAccountSingleCardinalNumber` to the `SystemDosage` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `VIP` DROP FOREIGN KEY `VIP_teamId_fkey`;

-- AlterTable
ALTER TABLE `SystemDosage` DROP COLUMN `memberAccountSignCardinalNumber`,
    DROP COLUMN `standardAccountSignCardinalNumber`,
    ADD COLUMN `memberAccountSingleCardinalNumber` INTEGER NOT NULL,
    ADD COLUMN `standardAccountSingleCardinalNumber` INTEGER NOT NULL;

-- DropTable
DROP TABLE `VIP`;

-- CreateTable
CREATE TABLE `Vip` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `teamId` INTEGER NOT NULL,
    `signDosageLimit` INTEGER NOT NULL DEFAULT 0,
    `groupDosageLimit` INTEGER NOT NULL DEFAULT 0,
    `commentDosageLimit` INTEGER NOT NULL DEFAULT 0,
    `platformAccountNumberLimit` INTEGER NOT NULL DEFAULT 0,
    `teamMemberNumberLimit` INTEGER NOT NULL DEFAULT 0,
    `expirationTime` DATETIME(3) NOT NULL,

    UNIQUE INDEX `Vip_teamId_key`(`teamId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Vip` ADD CONSTRAINT `Vip_teamId_fkey` FOREIGN KEY (`teamId`) REFERENCES `Team`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
