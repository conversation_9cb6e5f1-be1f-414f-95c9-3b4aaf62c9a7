import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'

const logger = new Logger('webhook external juguang')

const jiGuangPushApi = 'https://api.jpush.cn/v3/push'

export async function sendJiGuangPush({
  jpush<PERSON>ey,
  jpushSecret,
  jsonData
}: {
  jpushKey: string
  jpushSecret: string
  jsonData: any
}) {
  const keyAndSecret = `${jpushKey}:${jpushSecret}`

  const base64Credentials = Buffer.from(keyAndSecret).toString('base64')

  const res = (await axios.post(jiGuangPushApi, jsonData, {
    headers: {
      Authorization: 'Basic ' + base64Credentials
    }
  })) as {
    data: {
      error: {
        code: number
        message: string
      }
      sendno: string
      msg_id: string
    }
  }

  if (res.data.error) {
    logger.debug({
      code: res.data.error.code,
      description: res.data.error.message
    })

    throw new BadRequestException(`[极光官方]:${res.data.error.message}`)
  }

  return res.data
}
