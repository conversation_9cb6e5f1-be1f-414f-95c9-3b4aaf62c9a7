import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common'
import { VipService } from './vip.service'

import {
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiQuery
} from '@nestjs/swagger'
import {
  GiftVipDTO,
  OrderInfoResponseDTO,
  OrderInterestResponseDTO,
  orderPriceRequestDTO,
  OrderStatusRequestDTO,
  UpgradeOrderRequest,
  VipCreateDTO,
  VipOrdersResponseDTO,
  VipRenewDTO
} from './vip.dto'

import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { OrderPriceResponseDto } from 'apps/common/modules/orderManage/orderManage.dto'

@Controller('orders')
@ApiTags('订单管理')
export class VipController {
  constructor(private readonly vipService: VipService) {}

  @Post('price')
  @ApiOperation({ summary: '订单计算金额' })
  @ApiOkResponse({ type: OrderPriceResponseDto })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  calculateOrderPrice(@Body() body: orderPriceRequestDTO) {
    return this.vipService.calculateOrderPrice(body)
  }

  @Post()
  @ApiOperation({ summary: '创建 订单' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization'
  })
  async createVip(@Body() data: VipCreateDTO) {
    return this.vipService.createVip(data)
  }

  @Post('upgrade')
  @ApiOperation({ summary: '升级订单' })
  @ApiOkResponse({ type: BaseBadRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createUpgradeOrder(@Body() body: UpgradeOrderRequest) {
    return this.vipService.createUpgradeOrder(body)
  }

  @Post('renew')
  @ApiOperation({ summary: '续费vip' })
  @ApiOkResponse({ type: BaseBadRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  renewVip(@Body() body: VipRenewDTO) {
    return this.vipService.renewOrder(body)
  }

  @Post('gift')
  @ApiOperation({ summary: '赠送VIP' })
  @ApiOkResponse({ type: BaseBadRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  giftVip(@Body() body: GiftVipDTO) {
    return this.vipService.giftOrder(body)
  }

  @Get()
  @ApiOperation({ summary: '获取 订单列表' })
  @ApiOkResponse({ description: '操作成功', type: VipOrdersResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码 <默认 1>'
  })
  @ApiQuery({
    name: 'size',
    required: false,
    type: Number,
    description: '每页数量 <默认 10>'
  })
  @ApiQuery({
    name: 'phone',
    required: false,
    type: String,
    description: '手机号码'
  })
  @ApiQuery({
    name: 'orderNo',
    required: false,
    type: String,
    description: '订单号'
  })
  @ApiQuery({
    name: 'payAmount',
    required: false,
    type: Number,
    description: '付款金额'
  })
  @ApiQuery({
    name: 'orderStartTime',
    required: false,
    type: Number,
    description: '开始时间'
  })
  @ApiQuery({
    name: 'orderEndTime',
    required: false,
    type: Number,
    description: '到期时间'
  })
  @ApiQuery({
    name: 'payStartTime',
    required: false,
    type: Number,
    description: '支付开始时间'
  })
  @ApiQuery({
    name: 'payEndTime',
    required: false,
    type: Number,
    description: '支付开始时间'
  })
  @ApiQuery({
    name: 'orderStatus',
    required: false,
    example: 'pending | success | canceled',
    type: String,
    description: '订单状态'
  })
  @ApiQuery({
    name: 'orderType',
    required: false,
    example: 'create | upgrade | renew',
    type: String,
    description: '订单类型'
  })
  @ApiQuery({
    name: 'saleType',
    required: false,
    example: 'FirstBuy | ReBuy | NotBuy | Buy',
    type: String,
    description: '销售类型'
  })
  @ApiQuery({
    name: 'channelId',
    required: false,
    example: '1',
    type: Number,
    description: '渠道id'
  })
  @ApiQuery({
    name: 'invitationCode',
    required: false,
    example: '3VqJ7K2',
    type: String,
    description: '团队code'
  })
  async getOrders(
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number,
    @Query('phone', {
      transform: (value) => value || ''
    })
    phone: string,
    @Query('orderNo', {
      transform: (value) => value || ''
    })
    orderNo: string,
    @Query('payAmount', {
      transform: (value) => value || 0
    })
    payAmount: number,
    @Query('orderStartTime', {
      transform: (value) => value || 0
    })
    orderStartTime: number,
    @Query('orderEndTime', {
      transform: (value) => value || 0
    })
    orderEndTime: number,
    @Query('payStartTime', {
      transform: (value) => value || 0
    })
    payStartTime: number,
    @Query('payEndTime', {
      transform: (value) => value || 0
    })
    payEndTime: number,
    @Query('orderStatus', {
      transform: (value) => value || ''
    })
    orderStatus: string,
    @Query('orderType', {
      transform: (value) => value || ''
    })
    orderType: string,
    @Query('salesType', {
      transform: (value) => value || ''
    })
    salesType: string,
    @Query('channelId', {
      transform: (value) => value || 0
    })
    channelId: number,
    @Query('invitationCode', {
      transform: (value) => value || ''
    })
    invitationCode: string
  ) {
    return this.vipService.getOrders({
      page,
      size,
      phone,
      orderNo,
      payAmount,
      orderStartTime,
      orderEndTime,
      payStartTime,
      payEndTime,
      orderStatus,
      orderType,
      salesType,
      channelId,
      invitationCode
    })
  }

  @Patch(':orderNo/status')
  @ApiOperation({ summary: '对公转账开通' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization'
  })
  async putOrderStatus(@Param('orderNo') orderNo: string, @Body() body: OrderStatusRequestDTO) {
    return this.vipService.putOrderStatus(orderNo, body)
  }

  @Get(':orderNo')
  @ApiOperation({ summary: '订单详情' })
  @ApiOkResponse({ type: OrderInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getOrder(@Param('orderNo') orderNo: string) {
    return this.vipService.getOrderInfo(orderNo)
  }

  @Get('interest')
  @ApiOperation({ summary: '获取订单VIP规格' })
  @ApiOkResponse({ type: OrderInterestResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getInterest() {
    return this.vipService.getInterest()
  }
}
