import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  Matches
} from 'class-validator'

export class PublicRequestNotifyDTO {
  @ApiProperty({
    type: String,
    example: 'Hello, World!'
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 15)
  title: string

  @ApiProperty({
    type: String,
    example: 'Hello, World!'
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 500)
  content: string

  @ApiProperty({
    type: Boolean
  })
  @IsBoolean()
  @IsNotEmpty()
  isToast: boolean

  @ApiProperty({
    type: Number
  })
  createTime: number

  @ApiProperty({
    type: Number
  })
  id: string
}

export class PublicResponseNotify {
  @ApiResponseProperty({
    type: String,
    example: 'Hello, World!'
  })
  title: string

  @ApiResponseProperty({
    type: String,
    example: 'Hello, World!'
  })
  content: string

  @ApiProperty({
    type: Date,
    example: '2021-06-01 12:00:00'
  })
  createTime: number
}

export class PublicRequestNotifyListDTO {
  @ApiResponseProperty({
    type: [PublicRequestNotifyDTO]
  })
  data: PublicRequestNotifyDTO[]

  @ApiResponseProperty({
    type: Number
  })
  total: number

  @ApiResponseProperty({
    type: Number
  })
  size: number

  @ApiResponseProperty({
    type: Number
  })
  page: number
}

export class PublicResponseNotifyDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: PublicRequestNotifyListDTO
  })
  data: PublicRequestNotifyListDTO
}

export enum AppPlatformType {
  Android = 'android',
  IOS = 'ios'
}

export class CreateAppVersionRequestBodyDTO {
  @ApiProperty({
    description: '下载地址',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  url: string

  @ApiProperty({
    description: '版本号',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+\.\d+\.\d+$/, { message: '版本号格式错误，应为 x.x.x，例如 1.0.0' })
  version: string

  @ApiProperty({
    enum: Object.values(AppPlatformType).filter((v) => typeof v === 'string'),
    description: '类型',
    example: ' ios/android',
    required: true
  })
  @IsEnum(AppPlatformType)
  @IsNotEmpty()
  type: AppPlatformType

  @ApiProperty({
    type: Boolean,
    description: '是否强制更新',
    required: true
  })
  @IsBoolean()
  @IsNotEmpty()
  force: boolean

  @ApiProperty({
    type: String,
    description: '描述',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  desc: string
}

export class AppVersionResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: CreateAppVersionRequestBodyDTO
  })
  data: CreateAppVersionRequestBodyDTO

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number
}

export class AppVersionListRequestDTO {
  @ApiProperty({
    type: Number,
    description: '起始日期时间戳',
    required: true
  })
  @IsNumber()
  @IsOptional()
  releaseTimeMin?: number

  @ApiProperty({
    type: Number,
    description: '结束日期时间戳',
    required: false
  })
  @IsNumber()
  @IsOptional()
  releaseTimeMax?: number

  @ApiProperty({
    enum: Object.values(AppPlatformType).filter((v) => typeof v === 'string'),
    description: '类型',
    example: ' ios/android',
    required: false
  })
  @IsEnum(AppPlatformType)
  @IsOptional()
  type?: AppPlatformType

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsOptional()
  page?: number = 1

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsOptional()
  size?: number = 10
}

export class PublicCreateUploadUrlResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: String,
    example:
      'eyJleHBpcmF0aW9uIjoiMjAyNC0wNS0wNlQxMDo0ODo1MS41MTNaIiwiY29uZGl0aW9ucyI6W1siY29udGVudC1sZW5ndGgtcmFuZ2UiLDAsMTA0ODU3NjAwMF0seyJidWNrZXQiOiJxZHktaW1hZ2UifV19'
  })
  policy: string

  @ApiResponseProperty({
    type: String,
    example: 'Ao4nnTaft83zHUBwX5ZXw1i3GNA'
  })
  signature: string

  @ApiResponseProperty({
    type: String,
    example: 'LTAI5tD3QdxUQZbJAeHeX7Ma'
  })
  ossAccessKeyId: string

  @ApiResponseProperty({
    type: String,
    example: 'https://oss-cn-shanghai.aliyuncs.com'
  })
  host: string

  @ApiResponseProperty({
    type: String,
    example: 'avatar'
  })
  dir: string

  @ApiResponseProperty({
    type: String,
    example: 'abc123'
  })
  fileKey: string
}

export enum PublicRequestUploadType {
  Avatar = 'avatar',
  Autoresponder = 'autoresponder',
  Image = 'image',
  App = 'app'
}

export class PublicCreateUploadUrlRequestBodyDTO {
  @ApiProperty({
    enum: Object.values(PublicRequestUploadType).filter((v) => typeof v === 'string'),
    description: '文件类型',
    example: ' avatar: 头像 image: 图片',
    required: true
  })
  @IsEnum(PublicRequestUploadType)
  type: PublicRequestUploadType

  @ApiProperty({
    type: String,
    example: 'objectName: "avatar/2023-10-01/1234567890.png"',
    required: true
  })
  @IsString()
  objectName: string
}
