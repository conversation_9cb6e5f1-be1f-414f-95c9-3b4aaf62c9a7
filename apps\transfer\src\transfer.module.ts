import { Module } from '@nestjs/common'
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core'
import { GlobalExceptionFilter } from './common/filters'

import { ResponseTransformInterceptor } from './common/interceptors'
import { TokenGuard } from './common/guards'

import { RedisModule } from '@qdy/redis'
import { MongoModule } from '@qdy/mongo'
import { PrismaModule } from '@qdy/mysql'
import { ConfigModule } from '@qdy/config'
import { TaskModule } from './modules/task/task.module'

@Module({
  imports: [ConfigModule, RedisModule, MongoModule, PrismaModule, TaskModule],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor
    },
    {
      provide: APP_GUARD,
      useClass: TokenGuard
    }
  ]
})
export class TransferModule {}
