import { ModelD<PERSON>inition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { AnyObject } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class LogEntity {
  @Prop({
    type: String
  })
  type: string

  @Prop({
    type: Object
  })
  content: AnyObject

  @Prop({
    type: Number
  })
  teamMemberId: number

  @Prop({
    type: String
  })
  teamName: string

  @Prop({
    type: Number
  })
  teamId: number

  @Prop({
    type: String
  })
  phone: string

  @Prop({
    type: String
  })
  createTime: string
}

export const LogSchema: ModelDefinition = <const>{
  name: LogEntity.name,
  schema: SchemaFactory.createForClass(LogEntity)
}

export const LogMongoose = MongooseModule.forFeature([LogSchema])
