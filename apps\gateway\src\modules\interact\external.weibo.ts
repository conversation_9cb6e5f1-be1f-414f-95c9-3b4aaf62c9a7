import { BadRequestException } from '@nestjs/common'
import axios from 'axios'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { sendMessageEventEmitter, sendMessageEventKey } from '../webhook/webhook.event'
import { sendEvent } from '../overview/event'

const sendMessageApi = 'https://m.api.weibo.com/2/messages/reply.json'

export async function weiboSendMessages({
  fromUserId,
  toUserId,
  accessToken,
  type,
  content,
  teamId,
  autoresponderId,
  fromName,
  fromAvatar,
  toName,
  toAvatar,
  auto,
  redisClient,
  platformAccountId
}: {
  fromUserId: string
  toUserId: string
  accessToken: string
  type: string
  content: string
  teamId: number
  platformAccountId: number
  autoresponderId?: number
  fromName?: string
  fromAvatar?: string
  toName?: string
  toAvatar?: string
  auto?: boolean
  redisClient: Cache<RedisStore>
}) {
  const teamInfo = (await redisClient.get(`overview:${teamId}`)) as {
    residueCount: number
  }

  if (teamInfo && teamInfo.residueCount <= 0) {
    throw new BadRequestException('剩余回复次数不足')
  }

  if (autoresponderId) {
    const key = `postSendMessage:weibo-${toUserId}`
    const toUserCount = parseInt((await redisClient.get(key)) || '0', 10)

    // 1小时内发送次数超过3次
    if (toUserCount && toUserCount >= 100) {
      return
    }
    // 1小时内发送次数
    if (toUserCount) {
      const ttl = await redisClient.store.ttl(key)

      await redisClient.set(key, toUserCount + 1, ttl)
    } else {
      await redisClient.set(key, 1, 1000 * 60 * 60 * 24)
    }
  }

  const res = await axios
    .post(sendMessageApi, null, {
      params: {
        type,
        access_token: accessToken,
        data: JSON.stringify({
          text: content
        }),
        receiver_id: toUserId
      },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    .catch(function (error) {
      if (error.response) {
        throw new BadRequestException(`[微博官方]:${error.response.data.error}`)
      } else {
        throw new BadRequestException(`[微博官方]:请求报错`)
      }
    })

  const messageId = `${Date.now()}-${res.data.msg_id}`

  sendMessageEventEmitter.emit(sendMessageEventKey, {
    platformType: 'weibo',
    uniqueId: messageId,
    openId: fromUserId,
    fromUserId,
    toUserId,
    sessionId: toUserId,
    fromName,
    fromAvatar,
    toName,
    toAvatar,
    messageId,
    isAuto: auto ? 1 : 0,
    content: {
      messageType: 'text',
      text: content
    }
  })

  sendEvent({
    platformType: 'weibo',
    teamId,
    platformAccountId,
    autoresponderId,
    autoCommentCount: 0,
    commentCount: 0,
    groupCount: 0,
    openId: fromUserId,
    ...(autoresponderId
      ? { autoSingleCount: 1, singleCount: 0 }
      : { singleCount: 1, autoSingleCount: 0 })
  })

  return { ...res.data, messageId }
}
