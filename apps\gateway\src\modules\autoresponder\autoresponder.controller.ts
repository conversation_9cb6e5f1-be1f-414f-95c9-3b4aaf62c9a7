import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'

import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import {
  AutoresponderKeywordResponseDTO,
  AutoresponderKeywordCreateDTO,
  AutoresponderRequestUpdateStateDTO,
  AutoresponderKeywordsResponseDTO,
  AutoresponderVariable,
  AutoresponderResponesVariableDTO,
  AutoresponderType,
  AutoresponderKeywordRule,
  AutoresponderTriggerType
} from './autoresponder.dto'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { AutoresponderKeywordService } from './autoresponder.keyword.service'
import { AutoresponderVariableService } from './autoresponder.variable.service'
import { Platform } from '@apple/app-store-server-library'

@Controller('autoresponder')
@ApiTags('自动回复')
export class AutoresponderController {
  constructor(
    private readonly autoresponderKeywordService: AutoresponderKeywordService,
    private readonly autoresponderVariableService: AutoresponderVariableService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取策略列表' })
  @ApiOkResponse({ type: AutoresponderKeywordsResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '策略名称搜索'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: AutoresponderType,
    description: '选择类型 1 评论自动回复， 2 私信自动回复 3 私信欢迎语'
  })
  @ApiQuery({
    name: 'rule',
    required: false,
    enum: AutoresponderKeywordRule,
    description: '选择规则  match 关键词, instantly 即可'
  })
  @ApiQuery({
    name: 'platform',
    required: true,
    enum: Platform,
    description: '选择平台 0 抖音, 1 微信, 2 微博, 3 快手, 4 小红书'
  })
  @ApiQuery({
    name: 'trigger',
    required: true,
    enum: AutoresponderTriggerType,
    description: '触发条件 <0:欢迎语 1：私信，2：评论，3：关注，4：点赞>'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getKeywords(
    @Query('name') name: string,
    @Query('page', {
      transform(value) {
        return value || 1
      }
    })
    page: number,
    @Query('size', {
      transform(value) {
        return value || 10
      }
    })
    size: number,
    @Query('type') type: number,
    @Query('platform', {
      transform(value) {
        return value || 0
      }
    })
    platform: number,
    @Query('trigger', {
      transform(value) {
        return value || 0
      }
    })
    trigger: number,
    @Query('rule') rule: string
  ) {
    return this.autoresponderKeywordService.getAutoresponder({
      name,
      page,
      size,
      type,
      rule,
      platform,
      trigger
    })
  }

  @Get('/:autoresponderId')
  @ApiOperation({ summary: '获取单个策略' })
  @ApiOkResponse({ type: AutoresponderKeywordResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiParam({ name: 'autoresponderId', description: '类型ID' })
  @ApiHeader({
    name: 'authorization'
  })
  getKeyword(@Param('autoresponderId') autoresponderId: number) {
    return this.autoresponderKeywordService.getAutoresponderById(autoresponderId)
  }

  @ApiOperation({ summary: '删除策略' })
  @Delete('/:autoresponderId')
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiParam({ name: 'autoresponderId', description: '类型ID' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  deleteKeywords(@Param('autoresponderId') autoresponderId: number) {
    return this.autoresponderKeywordService.deleteAutoresponder(autoresponderId)
  }

  @Post()
  @ApiOperation({ summary: '创建策略' })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createAutoresponder(@Body() body: AutoresponderKeywordCreateDTO) {
    return this.autoresponderKeywordService.createAutoresponder(body)
  }

  @Put('/:autoresponderId')
  @ApiOperation({ summary: '修改策略' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiParam({ name: 'autoresponderId', description: '关键词ID' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateKeywords(
    @Param('autoresponderId') autoresponderId: number,
    @Body() body: AutoresponderKeywordCreateDTO
  ) {
    return this.autoresponderKeywordService.updateAutoresponder({ ...body, id: autoresponderId })
  }

  @Patch('/:autoresponderId')
  @ApiOperation({ summary: '启用/禁用策略' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiParam({ name: 'autoresponderId', description: '关键词ID' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateKeywordsStatus(
    @Param('autoresponderId') autoresponderId: number,
    @Body() body: AutoresponderRequestUpdateStateDTO
  ) {
    return this.autoresponderKeywordService.updateAutoresponderState(autoresponderId, body.state)
  }

  @Get('variables')
  @ApiOperation({ summary: '获取变量列表' })
  @ApiOkResponse({ type: AutoresponderResponesVariableDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '策略名称搜索'
  })
  getVariables(
    @Query('name') name: string,
    @Query('page', {
      transform(value) {
        return value || 1
      }
    })
    page: number,
    @Query('size', {
      transform(value) {
        return value || 10
      }
    })
    size: number
  ) {
    return this.autoresponderVariableService.getAll({ size, page, name })
  }

  @Delete('variables/:id')
  @ApiOperation({ summary: '删除变量' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  deleteVariables(@Param('id') id: number) {
    return this.autoresponderVariableService.delete(id)
  }

  @Post('variables')
  @ApiOperation({ summary: '新增变量' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createVariables(@Body() body: AutoresponderVariable) {
    return this.autoresponderVariableService.create(body)
  }

  @Put('variables/:id')
  @ApiOperation({ summary: '修改变量' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateVariables(@Param('id') id: number, @Body() body: AutoresponderVariable) {
    return this.autoresponderVariableService.update(id, body)
  }

  @Get('autoresponderCountByPlatform')
  @ApiOperation({ summary: '根据平台获取策略数量' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'platform', required: true, type: Number, description: '平台' })
  getAutoresponderCountByPlatform(
    @Query('platform')
    platform: number
  ) {
    return this.autoresponderKeywordService.getAutoresponderCountByPlatform(platform)
  }
}
