import { Modu<PERSON> } from '@nestjs/common'
import { WebhookController } from './webhook.controller'
import { WebhookService } from './webhook.service'
import {
  CommentMongoose,
  MessagesMongoose,
  WechatMessagesEntity,
  WechatMessagesMongoose,
  WechatCommentEntity,
  WechatCommentMongoose,
  WechatOpusMongoose,
  WechatOpusEntity,
  PersonalChatMessagesMongoose,
  WorkCommentMongoose,
  LikeAndFollowActionMongoose,
  MessagesByAutoresponderMongoose
} from '@qdy/mongo'
import { WebHookServiceWechat } from './webhook.service.wechat'
import { WebHookServiceGrpc } from './webhook.rpc'
import {
  OrderManageModule,
  AutoresponderManageModule,
  TosManageModule,
  TlsManageModule
} from '@qdy/common'
import { WebhookServiceKuaishou } from './webhook.service.kuaishou'
import { WebhookEventService } from './webhook.event'
import { WebhookServiceWeibo } from './webhook.service.weibo'
import { WebhookJpushService } from './webhook.jpush'
import { WebhookServiceXiaohongshu } from './webhook.service.xiaohongshu'
import { WebHookServiceWechatNew } from './webhook.service.wechatNew'
import { XhsBindAccountEventService } from './xhsBindAccount.event'

@Module({
  imports: [
    MessagesMongoose,
    CommentMongoose,
    WechatMessagesMongoose,
    WechatMessagesEntity,
    WechatCommentEntity,
    WechatCommentMongoose,
    WechatOpusMongoose,
    WechatOpusEntity,
    PersonalChatMessagesMongoose,
    WorkCommentMongoose,
    LikeAndFollowActionMongoose,
    MessagesByAutoresponderMongoose,
    OrderManageModule,
    AutoresponderManageModule,
    TosManageModule,
    TlsManageModule
  ],
  controllers: [WebhookController],
  providers: [
    WebHookServiceGrpc,
    WebhookService,
    WebHookServiceWechat,
    WebhookServiceKuaishou,
    WebhookEventService,
    WebhookJpushService,
    WebhookServiceWeibo,
    WebhookServiceXiaohongshu,
    XhsBindAccountEventService,
    WebHookServiceWechatNew
  ]
})
export class WebhookModule {}
