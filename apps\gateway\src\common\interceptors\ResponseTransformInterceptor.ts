import { Injectable, NestInterceptor, Execution<PERSON>ontext, Call<PERSON><PERSON><PERSON> } from '@nestjs/common'
import { Observable } from 'rxjs'
import { map } from 'rxjs/operators'
import { FastifyRequest } from 'fastify'

@Injectable()
export class ResponseTransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<FastifyRequest>()
    const { url } = request

    if (
      url === `/${process.env.WEBHOOK_URL}` ||
      url === `/${process.env.WECHAT_WEBHOOK_URL}` ||
      url === `/${process.env.ALIPAY_WEBHOOK_URL}` ||
      url === `/${process.env.WECHATPAY_WEBHOOK_URL}` ||
      url === `/${process.env.KUAISHOU_WEBHOOK_URL}` ||
      url.includes(`/${process.env.WEIBO_WEBHOOK_URL}`) ||
      url.includes(`/open/im/`) ||
      url.includes(`/open/intent/`)
    ) {
      return next.handle()
    }

    return next.handle().pipe(
      map((data) => {
        return {
          statusCode: 0,
          data
        }
      })
    )
  }
}
