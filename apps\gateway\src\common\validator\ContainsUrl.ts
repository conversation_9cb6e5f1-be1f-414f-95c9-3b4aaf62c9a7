import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface
} from 'class-validator'

@ValidatorConstraint({ async: false })
class ContainsUrlConstraint implements ValidatorConstraintInterface {
  validate(text: string) {
    const urlRegex =
      /(https?:\/\/)([a-zA-Z0-9_]+:[a-zA-Z0-9_]+@)?([a-zA-Z0-9.-]+\.[A-Za-z]{2,4})(:[0-9]+)?(\/.*)?/g
    return !urlRegex.test(text)
  }

  defaultMessage() {
    return '文本不能包含 URL'
  }
}

export function ContainsUrl(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: ContainsUrlConstraint
    })
  }
}
