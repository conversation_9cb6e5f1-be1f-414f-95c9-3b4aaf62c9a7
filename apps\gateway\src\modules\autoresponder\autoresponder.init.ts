import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { PlatformAccount } from '@qdy/mysql/client'
import { AutoresponderKeywordKey, AutoresponderVariableKey } from './constant'
import { AutoresponderKeywordRedisValue } from './autoresponder.dto'
import { AnyObject } from 'mongoose'

@Injectable()
export class AutoresponderInitService implements OnModuleInit {
  logger = new Logger('AutoresponderInitService')

  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  onModuleInit() {
    // this.onInite()
  }

  async onInite() {
    const lock = ((await this.cacheManager.get('init-autoresponder')) || 0) as number
    if (!lock) {
      this.onInitVariable()
      try {
        this.cacheManager.set('init-autoresponder', 1, 1000 * 60)
        const platformAccount = await this.prisma.platformAccount.findMany()
        const platformAccountMap: Record<number, PlatformAccount> = {}

        const autoresponders = await this.prisma.autoresponder.findMany({
          where: {
            isNew: true
          }
        })

        const keywordMaps: Record<string, AutoresponderKeywordRedisValue[]> = {}

        platformAccount.forEach((account) => {
          platformAccountMap[account.id] = account
          keywordMaps[account.openId] = []
        })

        autoresponders.forEach((autoresponder) => {
          const { platformAccountIds, opusers } = autoresponder as unknown as {
            platformAccountIds: number[]
            opusers: { id: number; platformAccountId: number }[]
          }

          opusers.forEach((opuser) => {
            const platformAccount = platformAccountMap[opuser.platformAccountId]
            if (platformAccount) {
              const opuserKey = `${platformAccount.openId}:${opuser.id}`
              if (!keywordMaps[opuserKey]) {
                keywordMaps[opuserKey] = []
              }
              // autoresponder-update-value 全局搜索需要修改的地方
              keywordMaps[opuserKey].push({
                keyword: autoresponder.keywords,
                rule: autoresponder.rule,
                contents: autoresponder.contents as AnyObject[],
                contentType: autoresponder.contentType,
                scene: autoresponder.scene,
                trigger: autoresponder.trigger,
                stopReply: autoresponder.stopReply,
                stopInterval: autoresponder.stopInterval,
                stopTime: autoresponder.stopTime,
                isDelay: autoresponder.isDelay,
                delayTime: autoresponder.delayTime,
                state: autoresponder.state,
                executionCount: autoresponder.executionCount,
                isNew: autoresponder.isNew,
                platform: autoresponder.platform,
                autoresponderId: autoresponder.id
              })
            } else {
              this.logger.error(`${opuser.platformAccountId} is 无效`)
            }
          })

          platformAccountIds.forEach((pid) => {
            const platformAccount = platformAccountMap[pid]
            if (platformAccount) {
              // autoresponder-update-value 全局搜索需要修改的地方
              keywordMaps[platformAccount.openId].push({
                scene: autoresponder.scene,
                trigger: autoresponder.trigger,
                keyword: autoresponder.keywords,
                rule: autoresponder.rule,
                contents: autoresponder.contents as AnyObject[],
                contentType: autoresponder.contentType,
                state: autoresponder.state,
                executionCount: autoresponder.executionCount,
                stopReply: autoresponder.stopReply,
                stopInterval: autoresponder.stopInterval,
                stopTime: autoresponder.stopTime,
                isDelay: autoresponder.isDelay,
                isNew: autoresponder.isNew,
                delayTime: autoresponder.delayTime,
                platform: autoresponder.platform,
                autoresponderId: autoresponder.id
              })
            } else {
              this.logger.error(`${pid} is 无效`)
            }
          })
        })

        await this.cacheManager.store.client.del(AutoresponderKeywordKey)
        const newKeywordMaps = {}
        if (Object.keys(keywordMaps).length) {
          Object.keys(keywordMaps).forEach((key) => {
            newKeywordMaps[key] = JSON.stringify(keywordMaps[key])
          })
          this.cacheManager.store.client.hmset(AutoresponderKeywordKey, newKeywordMaps)
        }
      } catch (e) {
        this.logger.error(e)
      } finally {
        await this.cacheManager.del('init-autoresponder')
      }
    }
  }

  async onInitVariable() {
    try {
      await this.cacheManager.del(AutoresponderVariableKey)
    } catch (e) {
      this.logger.error(e)
    }

    const variables = await this.prisma.variable.findMany()
    if (!variables.length) {
      return
    }
    const variablesMap = variables.reduce((memo, current) => {
      memo[current.id] = JSON.stringify(current)
      return memo
    }, {})

    this.cacheManager.store.client.hset(AutoresponderVariableKey, variablesMap)
  }
}
