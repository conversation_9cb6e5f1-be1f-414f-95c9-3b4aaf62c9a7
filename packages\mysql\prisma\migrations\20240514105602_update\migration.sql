/*
  Warnings:

  - The primary key for the `SessionConfig` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `SessionConfig` table. The data in that column could be lost. The data in that column will be cast from `VarChar(191)` to `Int`.
  - A unique constraint covering the columns `[sessionId]` on the table `SessionConfig` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `sessionId` to the `SessionConfig` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `SessionConfig` DROP PRIMARY KEY,
    ADD COLUMN `sessionId` VARCHAR(191) NOT NULL,
    MODIFY `id` INTEGER NOT NULL AUTO_INCREMENT,
    ADD PRIMARY KEY (`id`);

-- CreateIndex
CREATE UNIQUE INDEX `SessionConfig_sessionId_key` ON `SessionConfig`(`sessionId`);
