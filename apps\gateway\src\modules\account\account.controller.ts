import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Res
} from '@nestjs/common'
import { AccountService } from './account.service'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  AccountAccountsResponseDTO,
  AccountAccountAuthorizeRequestBodyDTO,
  AccountAccountAuthorizeResponseDTO,
  AccountUpdateAccountAuthorizationDTO,
  AccountMemberAccountsResponseDTO,
  AccountUpdateAccountConfigsDTO,
  AccountResponseConfigsDTO,
  AccountsRequestDto,
  WechatQrCodeResponseDTO,
  WechatQrCodeRequestDTO,
  WechatCheckLoginRequestDTO,
  WechatAuthInfoResponseDTO,
  AccountAdditionPermissionDTO,
  AccountUpdateRemarkRequestDTO,
  AccountMessageCardRequestDTO,
  AccountInfoResponseDTO,
  CardMessageResponseDTO,
  CardMessageListRequestDTO,
  KuaishouCardMessageListResponseDTO,
  XiaohongshuCommentListRequestDTO,
  XiaohongshuCommentListResponseDTO,
  XiaohongshuPageListResponseDTO
} from './account.dto'
import type { FastifyReply } from 'fastify'

import {
  BaseBadRequestDTO,
  BaseUnauthorizedResponseDTO,
  BaseNotFoundRequestDTO
} from '../../common/dto/BaseRequestDTO'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { validate } from 'class-validator'
import { AccountWechatService } from './account.service.wechat'
import { AccountKuaishouService } from './account.service.kuaishou'
import { AccountWeiboService } from './account.service.weibo'
import { AccountXiaohongshuService } from './account.service.xiaohongshu'
import { Platform } from '@qdy/utils'

@Controller('platform-accounts')
@ApiTags('账号管理')
export class AccountController {
  constructor(
    private readonly accountService: AccountService,
    private readonly accountWechatService: AccountWechatService,
    private readonly accountKuaishouService: AccountKuaishouService,
    private readonly accountWeiboService: AccountWeiboService,
    private readonly accountXiaohongshuService: AccountXiaohongshuService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取账号列表' })
  @ApiOkResponse({ description: '操作成功', type: AccountAccountsResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '按帐户名称搜索'
  })
  @ApiQuery({
    name: 'operatorID',
    required: false,
    type: String,
    description: '按操作员ID称搜索'
  })
  @ApiQuery({
    name: 'platform',
    required: false,
    type: Number,
    enum: Platform,
    description: '按平台搜索(0是抖音,1是视频号)'
  })
  @ApiQuery({
    name: 'groupID',
    required: false,
    type: Number,
    description: '分组ID'
  })
  @ApiQuery({
    name: 'isExpired',
    required: false,
    type: Boolean,
    description: '是否已过期(true是已过期,目前只对微信视频号平台有用)'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getAccounts(
    @Query('name') name: string,
    @Query('operatorID') operatorId: number,
    @Query('groupID') groupId: number,
    @Query('size') size: number,
    @Query('page') page: number,
    @Query('platform') platform: number,
    @Query('isExpired') isExpired: boolean
  ) {
    const querys = {
      platform,
      groupId,
      name,
      operatorId,
      isExpired,
      page,
      size
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    if (!querys.platform && querys.platform !== 0) {
      delete querys.platform
    } else {
      const accountsRequest = new AccountsRequestDto()
      accountsRequest.platform = platform
      const validationErrors = await validate(accountsRequest)
      if (validationErrors.length > 0) {
        throw new BadRequestException('平台参数错误')
      }
    }

    return this.accountService.getAccounts(querys)
  }

  @Get('/:platformAccountId')
  @ApiOperation({ summary: '获取账号详情' })
  @ApiOkResponse({ description: '操作成功', type: AccountInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getAccountById(@Param('platformAccountId') platformAccountId: number) {
    return this.accountService.getAccountById(platformAccountId)
  }

  @Get('/my')
  @ApiOperation({ summary: '获取成员账号列表' })
  @ApiOkResponse({ description: '操作成功', type: AccountMemberAccountsResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({
    name: 'autoresponder',
    required: false,
    type: String,
    description: '是否查询欢迎语关系'
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '按帐户名称搜索'
  })
  @ApiQuery({
    name: 'platform',
    required: false,
    type: Number,
    enum: Platform,
    description: '按平台搜索(0是抖音,1是视频号)'
  })
  @ApiQuery({
    name: 'groupId',
    required: false,
    type: Number,
    description: '按分组搜索'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getMembersPlatformAccounts(
    @Query('autoresponder') autoresponder: number,
    @Query('platform') platform: number,
    @Query('groupID') groupId: number,
    @Query('name') name: string
  ) {
    if (platform) {
      const accountsRequest = new AccountsRequestDto()
      accountsRequest.platform = platform
      const validationErrors = await validate(accountsRequest)
      if (validationErrors.length > 0) {
        throw new BadRequestException('平台参数错误')
      }
    }

    return this.accountService.getMembersPlatformAccounts(!!autoresponder, platform, groupId, name)
  }

  @Post('authorize')
  @ApiOperation({ summary: '获取账号授权' })
  @ApiOkResponse({ type: AccountAccountAuthorizeResponseDTO })
  @ApiBadRequestResponse({ description: '无效code' })
  @ApiUnauthorizedResponse({ description: '用户未登录' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async authorize(@Res() req: FastifyReply, @Body() body: AccountAccountAuthorizeRequestBodyDTO) {
    return this.accountService.authorizeAccount(body, req)
  }

  @Put('/authorize')
  @ApiOperation({ summary: '更新账号授权信息' })
  @ApiOkResponse({ type: AccountUpdateAccountAuthorizationDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async updateAccountAuthorization(@Body() { code }: AccountAccountAuthorizeRequestBodyDTO) {
    return this.accountService.updateAccountAuthorization(code)
  }

  @Post('kuaishou/authorize')
  @ApiOperation({ summary: '获取快手账号授权' })
  @ApiOkResponse({ type: AccountAccountAuthorizeResponseDTO })
  @ApiBadRequestResponse({ description: '无效code' })
  @ApiUnauthorizedResponse({ description: '用户未登录' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async kuaishouAuthorize(
    @Res() req: FastifyReply,
    @Body() body: AccountAccountAuthorizeRequestBodyDTO
  ) {
    return this.accountKuaishouService.authorizeAccount(body, req)
  }

  @Put('/:platformAccountId/config')
  @ApiOperation({ summary: '更新账号配置' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiParam({
    name: 'platformAccountId',
    required: true,
    type: Number,
    description: '平台账号ID'
  })
  @ApiOkResponse({ type: BaseResponseDTO })
  async updateAccountConfigs(
    @Param('platformAccountId') platformAccountId: number,
    @Body() body: AccountUpdateAccountConfigsDTO
  ) {
    return this.accountService.updateAccountConfigs(platformAccountId, body)
  }

  @Get('/configs')
  @ApiOperation({ summary: '获取账号配置' })
  @ApiOkResponse({ type: AccountResponseConfigsDTO })
  @ApiHeader({
    name: 'authorization'
  })
  async getAccountConfigs() {
    return this.accountService.getAccountConfigs()
  }

  @Delete(':platformAccountId')
  @ApiOperation({ summary: '删除账号' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async deleteAccount(@Param('platformAccountId') platformAccountId: number) {
    return this.accountService.deleteAccount(platformAccountId)
  }

  @Post('wechat/qrcode')
  @ApiOperation({ summary: '微信视频号二维码' })
  @ApiOkResponse({ type: WechatQrCodeResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async loginQrCodeByWechat(@Body() { regionId, appId }: WechatQrCodeRequestDTO) {
    const response = this.accountWechatService.getLoginQrCode(regionId, appId)
    return response
  }

  @Post('wechat/auth')
  @ApiOperation({ summary: '视频号登录' })
  @ApiOkResponse({ type: WechatAuthInfoResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async checkLoginByWechat(@Body() body: WechatCheckLoginRequestDTO) {
    const response = this.accountWechatService.checkLoginByWechat(body)
    return response
  }

  @Get('addition/permission')
  @ApiOperation({ summary: '检测是否可以添加账号' })
  @ApiOkResponse({ type: AccountAdditionPermissionDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async additionPermission() {
    const response = this.accountService.additionPermission()
    return response
  }

  @Put(':platformAccountId/enable')
  @ApiOperation({ summary: '解除媒体账号冻结状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async enableAccount(@Param('platformAccountId') platformAccountId: number) {
    return this.accountService.enableAccount(platformAccountId)
  }

  @Put(':platformAccountId/remark')
  @ApiOperation({ summary: '设置账号备注' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async accountRemark(
    @Param('platformAccountId') platformAccountId: number,
    @Body() body: AccountUpdateRemarkRequestDTO
  ) {
    return this.accountService.updateRemark(platformAccountId, body)
  }

  @Get(':platformAccountId/account-message-card')
  @ApiOperation({ summary: '查询留资卡片' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getAccountMessageCard(@Param('platformAccountId') platformAccountId: number) {
    return this.accountService.getAccountMessageCard(platformAccountId)
  }

  @Post(':platformAccountId/account-message-card')
  @ApiOperation({ summary: '创建/更新留资卡片' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async postAccountMessageCard(
    @Param('platformAccountId') platformAccountId: number,
    @Body() body: AccountMessageCardRequestDTO
  ) {
    return this.accountService.postAccountMessageCard(platformAccountId, body)
  }

  @Delete(':platformAccountId/account-message-card')
  @ApiOperation({ summary: '删除留资卡片' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'cardId', required: true, type: String, description: '留资id' })
  async deleteAccountMessageCard(
    @Param('platformAccountId') platformAccountId: number,
    @Query('cardId') cardId: string
  ) {
    return this.accountService.deleteAccountMessageCard(platformAccountId, cardId)
  }

  @Post(`cards`)
  @ApiOperation({ summary: '获取留资卡片数据列表' })
  @ApiOkResponse({ description: '操作成功', type: CardMessageResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getCardMessages(@Body() body: CardMessageListRequestDTO) {
    return this.accountService.getCardMessages(body)
  }

  @Get(':platformAccountId/kuaishou/account-message-card')
  @ApiOperation({ summary: '查询快手留资卡片' })
  @ApiOkResponse({ type: KuaishouCardMessageListResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getKuaishouAccountMessageCard(@Param('platformAccountId') platformAccountId: number) {
    return this.accountService.getKuaishouAccountMessageCard(platformAccountId)
  }

  @Post('weibo/authorize')
  @ApiOperation({ summary: '获取微博账号授权' })
  @ApiOkResponse({ type: AccountAccountAuthorizeResponseDTO })
  @ApiBadRequestResponse({ description: '无效code' })
  @ApiUnauthorizedResponse({ description: '用户未登录' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async weiboAuthorize(
    @Res() req: FastifyReply,
    @Body() body: AccountAccountAuthorizeRequestBodyDTO
  ) {
    return this.accountWeiboService.authorizeAccount(body, req)
  }

  @Post(`:platformAccountId/weibo/revokeoauth`)
  @ApiOperation({ summary: '微博账号下线' })
  @ApiOkResponse({ type: AccountAccountAuthorizeResponseDTO })
  @ApiBadRequestResponse({ description: '无效账号id' })
  @ApiUnauthorizedResponse({ description: '用户未登录' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async weiboRevokeoauth(@Param('platformAccountId') platformAccountId: number) {
    return this.accountWeiboService.weiboRevokeoauth(platformAccountId)
  }

  @Post('xiaohongshu/authorize')
  @ApiOperation({ summary: '获取小红书开放平台账号授权' })
  @ApiOkResponse({ type: AccountAccountAuthorizeResponseDTO })
  @ApiBadRequestResponse({ description: '无效code' })
  @ApiUnauthorizedResponse({ description: '用户未登录' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async xiaohongshuAuthorize(
    @Res() req: FastifyReply,
    @Body() body: AccountAccountAuthorizeRequestBodyDTO
  ) {
    return this.accountXiaohongshuService.authorizeAccount(body, req)
  }

  @Get(':platformAccountId/xiaohongshu/account-message-card')
  @ApiOperation({ summary: '查询小红书留资卡片' })
  @ApiOkResponse({ type: KuaishouCardMessageListResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'type',
    required: true,
    type: Number,
    description: '类型 <4-名片 5-留资卡 7-交易卡>'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getXiaohongshuAccountMessageCard(
    @Param('platformAccountId') platformAccountId: number,
    @Query('size') size: number,
    @Query('page') page: number,
    @Query('type') type: number
  ) {
    return this.accountService.getXiaohongshuAccountMessageCard(
      platformAccountId,
      page ?? 1,
      size ?? 20,
      type
    )
  }

  @Get(':platformAccountId/xiaohongshu/page-list')
  @ApiOperation({ summary: '查询小红书落地页' })
  @ApiOkResponse({ type: XiaohongshuPageListResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getXiaohongshuAccountPageList(
    @Param('platformAccountId') platformAccountId: number,
    @Query('size') size: number,
    @Query('page') page: number
  ) {
    return this.accountService.getXiaohongshuAccountPageList(
      platformAccountId,
      page ?? 1,
      size ?? 20
    )
  }

  @Post('xiaohongshu/comments')
  @ApiOperation({ summary: '查询小红书意向评论' })
  @ApiOkResponse({ type: XiaohongshuCommentListResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async postXiaohongshuAccountCommentList(@Body() body: XiaohongshuCommentListRequestDTO) {
    return this.accountXiaohongshuService.postCommentList(body)
  }

  @Post(`:platformAccountId/wechat/reconnection`)
  @ApiOperation({ summary: '视频号重连' })
  @ApiUnauthorizedResponse({ description: '用户未登录' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async reconnectionByWechat(@Param('platformAccountId') platformAccountId: number) {
    return this.accountWechatService.reconnectionByWechat({ id: platformAccountId })
  }
}
