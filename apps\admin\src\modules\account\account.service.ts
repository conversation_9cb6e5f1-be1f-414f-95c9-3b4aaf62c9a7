import { ForbiddenException, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { AnyObject } from 'mongoose'
import { wechatLogout } from './external.wechat'
import { postRevokeoAuthWeiboAccount } from './external.weibo'
import { Platform } from '@qdy/utils'
import { PlatformAccountManageService } from '@qdy/common'

@Injectable()
export class AccountService {
  logger = new Logger('AccountService')

  constructor(
    private readonly prisma: PrismaService,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  /**
   * 获取账号列表
   * @param param0
   * @returns
   */
  async getAccounts({
    platform,
    name,
    invitationCode,
    status,
    startTime,
    endTime,
    page = 1,
    size = 10
  }: {
    platform: Platform
    name: string
    invitationCode: string
    status: string
    startTime: number
    endTime: number
    page: number
    size: number
  }) {
    const where: Parameters<typeof this.prisma.platformAccount.findMany>[0]['where'] = {
      platform,
      createTime: {
        gte: new Date(startTime),
        lte: new Date(endTime)
      }
    }

    if (!(platform === 0 || platform)) {
      delete where.platform
    }

    if (invitationCode) {
      const team = await this.prisma.team.findUnique({
        where: {
          invitationCode
        }
      })

      if (!team) {
        return {
          total: 0,
          page,
          size,
          data: []
        }
      }

      where.teamId = team.id
    }

    if (name) {
      where.OR = [{ name: { contains: name } }, { remark: { contains: name } }]
    }

    if (status) {
      where.OR = where.OR || [] // 确保 OR 条件存在

      if (status === 'normal') {
        where.OR.push({
          expiresIn: {
            gt: 2
          }
        })
        where.OR.push({
          status: 0
        })
      }

      if (status === 'abnormal') {
        where.OR.push({
          expiresIn: {
            lt: 2
          }
        })
        where.OR.push({
          status: 1
        })
      }
    }

    if (!startTime || !endTime) {
      delete where.createTime
    }

    if (where.OR && where.OR.length <= 0) {
      delete where.OR
    }

    const total = await this.prisma.platformAccount.count({ where })

    const accounts = await this.prisma.platformAccount.findMany({
      where,
      include: {
        Team: true
      },
      orderBy: {
        createTime: 'desc'
      },
      skip: (page - 1) * size,
      take: size
    })

    const transformAccounts: AnyObject[] = []

    const platformAccountIds: number[] = []

    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i]
      platformAccountIds.push(account.id)

      transformAccounts.push({
        regionId: account.regionId,
        platform: account.platform,
        name: account.name,
        avatar: account.avatar,
        openId: account.openId,
        accountRole: account.accountRole,
        expiresIn: account.expiresIn,
        id: account.id,
        teamId: account.teamId,
        appId: account.appId,
        username: account.username,
        teamName: account.Team.name,
        invitationCode: account.Team.invitationCode,
        createTime: account.createTime.getTime(),
        expiresTime: account.expiresIn ? account.expiresIn * 1000 + account.tokenTime.getTime() : 0,
        status: account.status,
        scopes: account.unauthorize,
        remark: account.remark,
        parentOpenId: account.parentOpenId,
        isBind: account.isBind
      })
    }

    return {
      total,
      page,
      size,
      data: transformAccounts
    }
  }

  async unAuthorize(platformAccountId: number) {
    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId
      }
    })

    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }

    if (
      platformAccount.platform !== Platform.Wechat &&
      platformAccount.platform !== Platform.Weibo
    ) {
      throw new ForbiddenException('只有微信与微博账号才能解除授权')
    }

    let expiresIn = 0

    try {
      if (platformAccount.platform === Platform.Wechat) {
        await wechatLogout({
          appId: platformAccount.appId,
          isNew: platformAccount.isNew
        })
      }

      if (platformAccount.platform === Platform.Weibo) {
        await postRevokeoAuthWeiboAccount(platformAccount.accessToken)
        expiresIn = 1
      }
    } catch (error) {
      throw new ForbiddenException('退出账号失败')
    }

    await this.prisma.platformAccount.update({
      where: {
        id: platformAccount.id
      },
      data: {
        expiresIn
      }
    })

    await this.platformAccountManageService.deletePlatformAccountRedisInfo(platformAccount)
  }
}
