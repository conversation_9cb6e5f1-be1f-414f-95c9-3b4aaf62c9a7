import { Injectable, Logger } from '@nestjs/common'
import { tlsOpenapi } from '@volcengine/openapi'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { TlsDTO } from './tlsManage.dto'

@Injectable()
export class TlsManageService {
  logger = new Logger('TlsManageService')

  private tlsOpenapiService = tlsOpenapi.defaultService

  constructor(private readonly configService: ConfigService<{ app: RootConfigMap }, true>) {
    const { tlsConfig } = this.configService.get<RootConfigMap['app']>('app')

    this.tlsOpenapiService.setSecretKey(tlsConfig.accessKeySecret)
    this.tlsOpenapiService.setAccessKeyId(tlsConfig.accessKeyId)
    this.tlsOpenapiService.setHost(tlsConfig.host)
    this.tlsOpenapiService.setRegion(tlsConfig.region)
  }

  async putLogs(context: TlsDTO) {
    const logsBuffer = await tlsOpenapi.TlsService.objToProtoBuffer({
      LogGroups: [
        {
          Logs: [
            {
              Time: Math.floor(Date.now() / 1000),
              Contents: [
                { Key: 'timestamp', Value: new Date().toISOString() },
                ...(context
                  ? Object.entries(context).map(([k, v]) => ({ Key: k, Value: JSON.stringify(v) }))
                  : [])
              ]
            }
          ],
          Source: '',
          LogTags: [],
          FileName: '',
          ContextFlow: ''
        }
      ]
    })

    await this.tlsOpenapiService.PutLogs({
      TopicId: process.env.LOG_TOPIC_ID,
      LogGroupList: Buffer.from(logsBuffer)
    })
  }
}
