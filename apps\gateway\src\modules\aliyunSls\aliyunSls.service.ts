import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { GenerateSLSDto, SlsDTO } from './aliyunSls.dto'
import * as ALY from 'aliyun-sdk'

@Injectable()
export class AliyunSlsService {
  logger = new Logger('aliyunSlsService')

  constructor(private readonly configService: ConfigService<{ app: RootConfigMap }, true>) {}

  putLogs(slsDto: SlsDTO) {
    try {
      const { slsConfig } = this.configService.get<RootConfigMap['app']>('app')

      const sls = new ALY.SLS({
        // 本示例从环境变量中获取AccessKey ID和AccessKey Secret。
        accessKeyId: slsConfig.accessKeyId,
        secretAccessKey: slsConfig.accessKeySecret,
        endpoint: 'http://cn-shanghai.log.aliyuncs.com',
        apiVersion: '2015-06-01'
      })

      const param = {
        projectName: slsConfig.project,
        logStoreName: process.env.LOGSTORE,
        logGroup: {
          // 必选，写入的日志数据。
          logs: [this.dtoToGenerateSLSDto(slsDto)]
        }
      }

      sls.putLogs(param, (err) => {
        if (err) {
          this.logger.error(err)
        }
      })
    } catch (error) {
      this.logger.error(error)
    }
  }

  dtoToGenerateSLSDto(slsDto: SlsDTO): GenerateSLSDto {
    const generateSLSDto: GenerateSLSDto = {
      time: Math.floor(Date.now() / 1000),
      contents: [
        {
          key: 'logData',
          value: slsDto.logData
        },
        {
          key: 'requestUri',
          value: slsDto.requestUri
        },
        {
          key: 'jobStatus',
          value: slsDto.jobStatus
        },
        {
          key: 'logLevel',
          value: slsDto.logLevel
        }
      ]
    }

    return generateSLSDto
  }
}
