FROM node:20

ARG DATABASE_URL_MYSQL
ARG ENV_TYPE

CMD echo $DATABASE_URL_MYSQL
ENV DATABASE_URL_MYSQL=$DATABASE_URL_MYSQL
ENV ENV_TYPE=$ENV_TYPE
ENV NODE_ENV=$ENV_TYPE

COPY . /app
WORKDIR /app

RUN echo "Current directory:"
RUN ls -al

RUN npm config set registry https://registry.npmmirror.com/
RUN npm install -g pnpm@9.0.6 && npm install -g pm2
RUN pnpm install --frozen-lockfile
RUN pnpm run generate-mysql

RUN pnpm run build:gateway

EXPOSE 3000

ENTRYPOINT pnpm run start-gateway:$ENV_TYPE
