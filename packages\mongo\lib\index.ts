import { MongooseModule } from '@nestjs/mongoose'
import { MongoService } from './mongo.service'
import { MongoModule as _MongoModule } from './mongo.module'

export { UsageMongoose, UsageEntity } from './usage.schema'
export { MessagesMongoose, MessagesEntity } from './message.schema'
export { CommentMongoose, CommentEntity, CommentContent } from './comment.schema'
export { OverviewMongoose, OverviewEntity } from './overview.schema'
export { NoticeMongoose, NoticeEntity } from './notice.schema'
export { InvitationMongoose, InvitationEntity } from './invitation.schema'
export { WechatMessagesMongoose, WechatMessagesEntity } from './wechatMessage.schema'
export { MessageStatisticsMongoose, MessageStatisticEntity } from './messageStatistic.schema'
export { WechatOpusEntity, WechatOpusMongoose } from './wechatOpus.schema'
export {
  UserRegisterStatisticsMongoose,
  UserRegisterStatisticEntity
} from './userRegisterStatistic.schema'
export { WechatCommentEntity, WechatCommentMongoose } from './wechatComment.schema'
export { LogMongoose, LogEntity } from './log.schema'
export {
  LikeAndFollowActionEntity,
  LikeAndFollowActionMongoose
} from './likeAndFollowAction.schema'
export {
  PersonalChatMessageEntity,
  PersonalChatMessagesMongoose
} from './personalChatMessage.schema'
export { WorkCommentEntity, WorkCommentMongoose } from './workComment.schema'
export {
  DailyMessageStatisticMongoose,
  DailyMessageStatisticEntity
} from './dailyMessageStatistic.schema'
export { DailyOverviewMongoose, DailyOverviewEntity } from './dailyOverview.schema'

export { AppVersionMongoose, AppVersionEntity } from './appVersion.schema'
export {
  UserMessageReadRecordMongoose,
  UserMessageReadRecordEntity
} from './userMessageRead.schema'

export {
  MessagesByAutoresponderEntity,
  MessagesByAutoresponderMongoose
} from './messageByAutoresponder.schema'

export const MongoModule = MongooseModule.forRootAsync({
  useFactory: (mongoService: MongoService) => mongoService.createOptions(),
  imports: [_MongoModule],
  inject: [MongoService]
})
