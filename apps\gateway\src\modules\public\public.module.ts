import { Module } from '@nestjs/common'
import { PublicController } from './public.controller'
import { PublicService } from './public.service'
import { AppVersionMongoose, NoticeMongoose } from '@qdy/mongo'
import { TosManageModule } from '@qdy/common'

@Module({
  imports: [NoticeMongoose, AppVersionMongoose, TosManageModule],
  controllers: [PublicController],
  providers: [PublicService]
})
export class PublicModule {}
