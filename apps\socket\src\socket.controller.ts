import { Controller } from '@nestjs/common'
import { GrpcMethod } from '@nestjs/microservices'
import { SocketParam } from '@qdy/proto'
import { EventsGateway } from './events.gateway'

@Controller()
export class SocketController {
  constructor(private readonly eventsGateway: EventsGateway) {}

  @GrpcMethod('Socket', 'send')
  async send({ list }: SocketParam) {
    this.eventsGateway.send(JSON.parse(list))
  }
}
