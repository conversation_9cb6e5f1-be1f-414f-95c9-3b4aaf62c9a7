import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { PrismaService, type PlatformAccount } from '@qdy/mysql'
import {
  delRetainConsultCard,
  getRetainConsultCard,
  postAuthorizeAccount,
  postAuthorizeAccountInfoApi,
  postAuthorizeRole,
  postRetainConsultCard
} from './external'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { type Cache } from 'cache-manager'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { type RedisStore } from 'cache-manager-ioredis-yet'
import { AutoresponderKeywordKey, genSocketRedisKey, Platform } from '@qdy/utils'
import {
  AccountAccountAuthorizeRequestBodyDTO,
  AccountAccountsStatus,
  AccountMessageCardRequestDTO,
  AccountUpdateAccountConfigsDTO,
  AccountUpdateRemarkRequestDTO,
  CardMessageListRequestDTO
} from './account.dto'
import { TeamMemberRole } from '../team/team.dto'
import { AutoresponderContentType } from '../autoresponder/autoresponder.dto'
import { getCommentList, getVideoList, postClientToken } from '../interact/external'
import { wait } from '../../common/utils'
import { AnyObject, Model } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { CommentEntity, MessagesEntity, WechatOpusEntity } from '@qdy/mongo'
import { type FastifyReply } from 'fastify'
import { AccountSocketService } from './account.task'
import { WebhookEvents } from '../webhook/constant'
import { wechatLogout } from './external.wechat'
import { postMarketToolList } from './external.kuaishou'
import { decrypt } from '../webhook/external.kuaishou'
import { PlatformAccountManageService } from '@qdy/common'
import { postMaterialList, postPageList } from '../webhook/external.xiaohongshu'

@Injectable()
export class AccountService {
  logger = new Logger('AccountService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly socketService: AccountSocketService,
    @InjectModel(CommentEntity.name) private commentModel: Model<CommentEntity>,
    @InjectModel(MessagesEntity.name) private messagesModel: Model<MessagesEntity>,
    @InjectModel(WechatOpusEntity.name) private wechatOpusModel: Model<WechatOpusEntity>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  /**
   * 授权账号
   * @param authorization
   * @param code
   */
  async authorizeAccount(
    { code, isRegister }: AccountAccountAuthorizeRequestBodyDTO,
    req: FastifyReply
  ) {
    const { clientKey, clientSecret, secondClientKey, secondClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const [team, platformAccountCount, systemDosage] = await Promise.all([
      this.prisma.team.findUnique({
        where: {
          id: teamMember.teamId
        },
        include: {
          vip: true
        }
      }),
      this.prisma.platformAccount.count({ where: { teamId: teamMember.teamId } }),
      this.prisma.systemDosage.findFirst()
    ])

    if (isRegister) {
      if (team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
        if (team.vip.platformAccountNumberLimit <= platformAccountCount) {
          throw new ForbiddenException('账号数量已达上限')
        }
      } else if (systemDosage.standardPlatformAccountNumberLimit <= platformAccountCount) {
        throw new ForbiddenException('账号数量已达上限')
      }
    }

    let accountByClientKey = clientKey
    let accountByClientSecret = clientSecret
    let keyFlag = 'clientToken'

    if (team.douyinClientKey) {
      if (team.douyinClientKey === secondClientKey) {
        accountByClientKey = secondClientKey
        accountByClientSecret = secondClientSecret
        keyFlag = 'secondClientToken'
      }
    }

    const res = await postAuthorizeAccount({
      clientKey: accountByClientKey,
      clientSecret: accountByClientSecret,
      code
    })

    const oldPlatformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId: res.openId
      },
      include: {
        affiliates: true
      }
    })

    if (!isRegister) {
      if (oldPlatformAccount && oldPlatformAccount.teamId !== user.currentTeamId) {
        // 重新授权，登录的账号在当前团队无法找到，并且账号已超了提示错误
        if (team.vip.platformAccountNumberLimit <= platformAccountCount) {
          throw new ForbiddenException('账号数量已达上限')
        }
      }
      if (!oldPlatformAccount && team.vip.platformAccountNumberLimit <= platformAccountCount) {
        throw new ForbiddenException('账号数量已达上限')
      }
    }

    if (oldPlatformAccount) {
      // false 没有过期 true
      const isoverdue =
        oldPlatformAccount.expiresIn * 1000 + oldPlatformAccount.createTime.getTime() < Date.now()

      if (!isoverdue && oldPlatformAccount.teamId !== teamMember.teamId) {
        await this.prisma.platformAccount.update({
          where: {
            id: oldPlatformAccount.id
          },
          data: {
            accessToken: res.accessToken,
            refreshToken: res.refreshToken,
            expiresIn: res.expiresIn,
            refreshExpiresIn: res.refreshExpiresIn,
            refreshTime: new Date(),
            createTime: new Date(),
            tokenTime: new Date(),
            status: AccountAccountsStatus.Normal,
            unauthorize: ''
          }
        })
        throw new ForbiddenException('账号已授权过，如需重新授权，请在手机上解除授权后再次授权')
      }
    }

    let clientToken = await this.cacheManager.get(keyFlag)

    if (!clientToken) {
      const clientTokenInfo = await postClientToken({
        clientSecret: accountByClientSecret,
        clientKey: accountByClientKey
      })

      await this.cacheManager.set(
        keyFlag,
        clientTokenInfo.access_token,
        clientTokenInfo.expires_in * 1000
      )

      clientToken = clientTokenInfo.access_token
    }

    let role: string

    try {
      role = await postAuthorizeRole({
        accessToken: clientToken as string,
        openId: res.openId
      })
    } catch (err) {
      this.logger.error('获取角色失败', err)
    }

    const info = await postAuthorizeAccountInfoApi(res)
    info.accountRole = role || info.accountRole

    const value = {
      ...info,
      openId: res.openId,
      accessToken: res.accessToken,
      refreshToken: res.refreshToken,
      expiresIn: res.expiresIn,
      refreshExpiresIn: res.refreshExpiresIn,
      teamId: teamMember.teamId,
      status: AccountAccountsStatus.Normal,
      unauthorize: '',
      affiliates: {
        connect: {
          id: teamMember.id
        }
      }
    }

    if (oldPlatformAccount && oldPlatformAccount.teamId !== teamMember.teamId) {
      await this.prisma.platformAccount.update({
        where: {
          id: oldPlatformAccount.id
        },
        data: {
          affiliates: {
            disconnect: oldPlatformAccount.affiliates.map((item) => ({ id: item.id }))
          }
        }
      })

      const userIds = oldPlatformAccount.affiliates.map((item) => item.userId)
      const teamMemberByOldPlatformAccount = await this.prisma.teamMember.findMany({
        where: {
          teamId: oldPlatformAccount.teamId,
          role: {
            not: TeamMemberRole.Member
          }
        }
      })

      const manageUserIds = teamMemberByOldPlatformAccount.map((item) => item.userId)

      const combinedAndUniqueIds = [...new Set([...userIds, ...manageUserIds])]

      const users = await this.prisma.user.findMany({
        where: {
          id: {
            in: combinedAndUniqueIds
          }
        }
      })
      if (users.length > 0) {
        for (let i = 0; i < users.length; i++) {
          const userInfo = users[i]
          const auth = await this.cacheManager.get<string>(userInfo.phone)
          if (auth) {
            await this.cacheManager.store.client.hdel(
              genSocketRedisKey(auth),
              oldPlatformAccount.openId
            )
          }
        }
      }

      // 删除旧账号的socketids
      await this.cacheManager.store.client.del(genSocketRedisKey(oldPlatformAccount.openId))

      // 该账号之前团队对应的自动回复移除该账号
      const autoresponderList = await this.prisma.autoresponder.findMany({
        where: {
          platformAccountIds: {
            array_contains: oldPlatformAccount.id
          }
        }
      })

      autoresponderList.forEach(async (item) => {
        if (item.platformAccountIds) {
          const accountIds = (item.platformAccountIds as number[]).filter(
            (id) => id !== oldPlatformAccount.id
          )
          await this.prisma.autoresponder.update({
            where: { id: item.id },
            data: { platformAccountIds: accountIds }
          })

          // 删除账号策略缓存
          await this.cacheManager.store.client.hdel(
            AutoresponderKeywordKey,
            oldPlatformAccount.openId
          )
        }
      })
    }

    const platformAccount = await this.prisma.platformAccount.upsert({
      create: value,
      where: {
        openId: res.openId
      },
      update: {
        ...value,
        createTime: new Date(),
        refreshTime: new Date(),
        tokenTime: new Date()
      }
    })

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        teamId: teamMember.teamId,
        role: {
          not: TeamMemberRole.Member
        },
        userId: {
          not: user.id
        }
      },
      include: {
        user: true
      }
    })

    const socketIds = []

    for (let i = 0; i < teamMembers.length; i++) {
      const item = teamMembers[i]
      const [socketId, appSocketId] = await Promise.all([
        this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
        this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
      ])

      this.logger.debug(`teamMembers-item-${item.name}`, {
        socketId,
        reidsKey: genSocketRedisKey(item.userId),
        openId: platformAccount.id
      })

      await Promise.allSettled([
        (async () => {
          if (appSocketId) {
            socketIds.push(appSocketId)
            try {
              const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

              if (authApp) {
                await this.cacheManager.store.client.hset(
                  genSocketRedisKey(authApp),
                  platformAccount.openId,
                  platformAccount.id
                )
              }
            } catch (error) {
              this.logger.error('更新缓存错误', error)
            }

            await this.cacheManager.set(genSocketRedisKey(item.userId + 'app'), appSocketId, 0)
          }
        })(),
        (async () => {
          if (socketId) {
            socketIds.push(socketId)
            try {
              const auth = await this.cacheManager.get<string>(item.user.phone)

              if (auth) {
                await this.cacheManager.store.client.hset(
                  genSocketRedisKey(auth),
                  platformAccount.openId,
                  platformAccount.id
                )
              }
            } catch (error) {
              this.logger.error('更新缓存错误', error)
            }

            await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
          }
        })()
      ])
    }

    const [socketId, appSocketId] = await Promise.all([
      this.cacheManager.get<string>(genSocketRedisKey(user.id)),
      this.cacheManager.get<string>(genSocketRedisKey(user.id + 'app'))
    ])

    await this.platformAccountManageService.updatePlatformAccountRedisInfo(platformAccount)

    await Promise.allSettled([
      (async () => {
        if (appSocketId) {
          socketIds.push(appSocketId)
          try {
            const authApp = await this.cacheManager.get<string>(`${user.phone}app`)

            if (authApp) {
              await this.cacheManager.store.client.hset(
                genSocketRedisKey(authApp),
                platformAccount.openId,
                platformAccount.id
              )
            }
          } catch (error) {
            this.logger.error('更新缓存错误', error)
          }

          await this.cacheManager.store.client.hset(
            genSocketRedisKey(res.openId),
            appSocketId,
            platformAccount.id
          )
        }
      })(),
      (async () => {
        if (socketId) {
          socketIds.push(socketId)
          try {
            const auth = await this.cacheManager.get<string>(user.phone)

            if (auth) {
              await this.cacheManager.store.client.hset(
                genSocketRedisKey(auth),
                platformAccount.openId,
                platformAccount.id
              )
            }
          } catch (error) {
            this.logger.error('更新缓存错误', error)
          }

          await this.cacheManager.store.client.hset(
            genSocketRedisKey(res.openId),
            socketId,
            platformAccount.id
          )
        }
      })()
    ])

    req.status(201).send({
      statusCode: 0,
      data: {
        name: info.name,
        avatar: info.avatar,
        accountRole: info.accountRole
      }
    })

    return this.initPlatformAccount(platformAccount, socketIds, !!oldPlatformAccount)
  }

  /**
   * 获取团队成员的平台账号
   * @returns
   */
  async getMembersPlatformAccounts(
    autoresponder: boolean,
    platform: Platform,
    groupId: number,
    name: string
  ) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const someAffiliates =
      teamMember.role === TeamMemberRole.Member
        ? {
            affiliates: {
              some: {
                id: teamMember.id
              }
            }
          }
        : {}

    let platformWhere = {}

    if (platform === 0 || platform) {
      platformWhere = {
        platform
      }
    }
    if (groupId) {
      platformWhere = {
        ...platformWhere,
        groups: {
          some: {
            id: groupId
          }
        }
      }
    }

    let nameWhere = {}
    if (name) {
      nameWhere = {
        OR: [
          {
            remark: {
              contains: name
            }
          },
          {
            name: {
              contains: name
            }
          }
        ]
      }
    }

    try {
      const accounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId,
          ...someAffiliates,
          ...platformWhere,
          ...nameWhere
        },
        select: {
          name: true,
          avatar: true,
          openId: true,
          accountRole: true,
          expiresIn: true,
          createTime: true,
          tokenTime: true,
          id: true,
          teamId: true,
          platform: true,
          remark: true,
          appId: true,
          regionId: true,
          status: true,
          unauthorize: true,
          isBind: true,
          parentOpenId: true,
          groups: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          createTime: 'desc'
        }
      })
      const platformAccountConfig = await this.prisma.platformAccountConfig.findMany({
        where: {
          userId: user.id
        }
      })

      const platformAccountConfigMap = new Map(
        platformAccountConfig.map((item) => [item.platformAccountId, item])
      )

      const platformAccountIds = accounts.map((item) => item.id)

      const newAccounts = []

      if (autoresponder) {
        const greetings = await this.prisma.autoresponder.findMany({
          where: {
            contentType: AutoresponderContentType.Greeting,
            OR: platformAccountIds.map((id) => ({
              platformAccountIds: {
                array_contains: id
              }
            }))
          }
        })

        const greetingInPlatformAccount = new Set(
          greetings.map((item) => item.platformAccountIds).flat()
        )

        accounts.forEach((item) => {
          newAccounts.push({
            ...item,
            scopes: item.unauthorize,
            top: platformAccountConfigMap.get(item.id)?.top || false,
            isGreeting: greetingInPlatformAccount.has(item.id),
            expiresTime: !item.expiresIn ? 0 : item.expiresIn * 1000 + item.tokenTime.getTime()
          })
        })
      } else {
        accounts.forEach((item) => {
          newAccounts.push({
            ...item,
            scopes: item.unauthorize,
            top: platformAccountConfigMap.get(item.id)?.top || false,
            expiresTime: !item.expiresIn ? 0 : item.expiresIn * 1000 + item.tokenTime.getTime()
          })
        })
      }

      return newAccounts
    } catch (error) {
      this.logger.error(error)
    }
  }

  async getInvalidPlatformAccounts() {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const isManager = teamMember.role !== TeamMemberRole.Member

    let someAffiliates = {}

    if (isManager) {
      someAffiliates = {}
    } else {
      someAffiliates = {
        affiliates: {
          some: {
            teamId: teamMember.teamId,
            id: teamMember.id
          }
        }
      }
    }

    try {
      const accounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId,
          platform: Platform.Wechat,
          status: AccountAccountsStatus.Normal,
          expiresIn: {
            lte: Math.floor(Date.now() / 1000)
          },
          ...someAffiliates
        },
        select: {
          name: true,
          avatar: true,
          openId: true,
          expiresIn: true,
          createTime: true,
          id: true,
          teamId: true,
          platform: true,
          appId: true,
          regionId: true,
          status: true
        },
        orderBy: {
          createTime: 'desc'
        }
      })

      return accounts
    } catch (error) {
      this.logger.error(error)
    }
  }

  /**
   * 获取账号列表
   * @param param0
   * @returns
   */
  async getAccounts({
    platform,
    groupId,
    name,
    operatorId,
    isExpired,
    page = 1,
    size = 10
  }: {
    platform: Platform
    name: string
    groupId: number
    operatorId: number
    isExpired: boolean
    page: number
    size: number
  }) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('权限错误')
    }

    const isManager = teamMember.role !== TeamMemberRole.Member

    let someAffiliates = {}

    if (operatorId && !isManager) {
      someAffiliates = {
        affiliates: {
          some: {
            AND: [
              {
                teamId: teamMember.teamId,
                id: teamMember.id
              },
              {
                teamId: teamMember.teamId,
                id: operatorId
              }
            ]
          }
        }
      }
    } else if (operatorId && isManager) {
      someAffiliates = {
        affiliates: {
          some: {
            teamId: teamMember.teamId,
            id: operatorId
          }
        }
      }
    } else if (isManager) {
      someAffiliates = {}
    } else {
      someAffiliates = {
        affiliates: {
          some: {
            teamId: teamMember.teamId,
            id: teamMember.id
          }
        }
      }
    }

    let platformWhere = {}

    if (platform === 0 || platform) {
      platformWhere = {
        platform
      }
    }

    let groupWhere = {}

    if (groupId) {
      groupWhere = {
        groups: {
          some: {
            id: groupId
          }
        }
      }
    }

    let expiredWhere = {}
    if (isExpired && platform === Platform.Wechat) {
      expiredWhere = {
        expiresIn: {
          lte: Math.floor(Date.now() / 1000)
        }
      }
    }

    let nameWhere = {}
    if (name) {
      nameWhere = {
        OR: [
          {
            remark: {
              contains: name
            }
          },
          {
            name: {
              contains: name
            }
          }
        ]
      }
    }

    const total = await this.prisma.platformAccount.count({
      where: {
        teamId: teamMember.teamId,
        ...groupWhere,
        ...someAffiliates,
        ...platformWhere,
        ...expiredWhere,
        ...nameWhere
      }
    })

    const accounts = await this.prisma.platformAccount.findMany({
      where: {
        teamId: teamMember.teamId,
        ...groupWhere,
        ...someAffiliates,
        ...platformWhere,
        ...expiredWhere,
        ...nameWhere
      },
      include: {
        affiliates: {
          select: {
            id: true,
            name: true,
            teamId: true,
            user: {
              select: {
                name: true,
                avatar: true
              }
            }
          }
        }
      },
      orderBy: {
        createTime: 'desc'
      },
      skip: (page - 1) * size,
      take: size
    })

    const transformAccounts: AnyObject[] = []

    const platformAccountIds: number[] = []

    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i]
      const affiliates = account.affiliates
        .filter((item) => item.teamId === teamMember.teamId)
        .map((item) => ({
          ...item.user,
          name: item.name || item.user.name,
          id: item.id
        }))

      platformAccountIds.push(account.id)

      transformAccounts.push({
        regionId: account.regionId,
        platform: account.platform,
        name: account.name,
        avatar: account.avatar,
        openId: account.openId,
        accountRole: account.accountRole,
        expiresIn: account.expiresIn,
        affiliates,
        id: account.id,
        teamId: account.teamId,
        appId: account.appId,
        username: account.username,
        wechatInfo: account.wechatInfo,
        expiresTime: account.expiresIn ? account.expiresIn * 1000 + account.tokenTime.getTime() : 0,
        status: account.status,
        scopes: account.unauthorize,
        remark: account.remark,
        isBind: account.isBind,
        parentOpenId: account.parentOpenId,
        isNew: account.isNew
      })
    }

    return {
      total,
      page,
      size,
      data: transformAccounts
    }
  }

  async getAccountById(id: number) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('权限错误')
    }

    const account = await this.prisma.platformAccount.findUnique({
      where: {
        id
      },
      include: {
        affiliates: {
          select: {
            id: true,
            name: true,
            teamId: true,
            user: {
              select: {
                name: true,
                avatar: true
              }
            }
          }
        }
      }
    })

    if (!account) {
      throw new NotFoundException('账号不存在')
    }

    const affiliates = account.affiliates
      .filter((item) => item.teamId === teamMember.teamId)
      .map((item) => ({
        ...item.user,
        name: item.name || item.user.name,
        id: item.id
      }))

    return {
      regionId: account.regionId,
      platform: account.platform,
      name: account.name,
      avatar: account.avatar,
      openId: account.openId,
      accountRole: account.accountRole,
      expiresIn: account.expiresIn,
      affiliates,
      id: account.id,
      teamId: account.teamId,
      appId: account.appId,
      username: account.username,
      accessToken: account.accessToken,
      wechatInfo: account.wechatInfo,
      expiresTime: account.expiresIn ? account.expiresIn * 1000 + account.tokenTime.getTime() : 0,
      status: account.status,
      scopes: account.unauthorize,
      remark: account.remark,
      isBind: account.isBind,
      parentOpenId: account.parentOpenId
    }
  }

  async updateAccountAuthorization(code: string) {
    const { clientKey, clientSecret, secondClientKey, secondClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    let accountByClientKey = clientKey
    let accountByClientSecret = clientSecret

    if (teamMember.team.douyinClientKey) {
      if (teamMember.team.douyinClientKey === secondClientKey) {
        accountByClientKey = secondClientKey
        accountByClientSecret = secondClientSecret
      }
    }

    const res = await postAuthorizeAccount({
      clientKey: accountByClientKey,
      clientSecret: accountByClientSecret,
      code
    })

    const info = await postAuthorizeAccountInfoApi(res)

    try {
      const newValue = await this.prisma.platformAccount.update({
        where: {
          teamId: teamMember.teamId,
          openId: res.openId
        },
        data: {
          name: info.name,
          avatar: info.avatar,
          openId: res.openId,
          accessToken: res.accessToken,
          refreshToken: res.refreshToken,
          expiresIn: res.expiresIn,
          refreshExpiresIn: res.refreshExpiresIn,
          createTime: new Date(),
          refreshTime: new Date(),
          tokenTime: new Date()
        }
      })
      await this.platformAccountManageService.updatePlatformAccountRedisInfo(newValue)
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException('账号不存在')
      }

      this.logger.error(error)
    }
  }

  async getAccountConfigs() {
    const { user } = this.request
    const res = await this.prisma.platformAccountConfig.findMany({
      where: {
        userId: user.id
      }
    })
    return res.map((item) => ({
      top: item.top,
      mute: item.mute,
      platformAccountId: item.platformAccountId
    }))
  }

  async updateAccountConfigs(platformAccountId: number, body: AccountUpdateAccountConfigsDTO) {
    const { user } = this.request
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (teamMember.role === TeamMemberRole.Member) {
      const account = await this.prisma.platformAccount.findUnique({
        where: {
          id: platformAccountId,
          teamId: teamMember.teamId,
          affiliates: {
            some: {
              userId: user.id
            }
          }
        }
      })

      if (!account) {
        throw new ForbiddenException('你没有此账号权限')
      }
    } else {
      const account = await this.prisma.platformAccount.findUnique({
        where: {
          id: platformAccountId,
          teamId: teamMember.teamId
        }
      })

      if (!account) {
        throw new ForbiddenException('你没有此账号权限')
      }
    }

    try {
      await this.prisma.platformAccountConfig.upsert({
        where: {
          platformAccountId_userId: {
            platformAccountId,
            userId: user.id
          }
        },
        create: {
          ...body,
          userId: user.id,
          platformAccountId
        },
        update: body
      })
    } catch (error) {
      this.logger.error(error)
      throw new ForbiddenException(`更新失败: ${error.message}`)
    }
  }

  async initPlatformAccount(platformAccount: PlatformAccount, socketIds: string[], old: boolean) {
    if (!old) {
      const res = await getVideoList({
        accessToken: platformAccount.accessToken,
        openId: platformAccount.openId,
        cursor: 0,
        count: 10
      })

      if (!res.list) {
        res.list = []
      }

      const comments = []

      this.logger.debug('初始化账号 socketIds', socketIds)

      const videoList = res.list.filter((item: AnyObject) => item.video_status !== 2)

      for (let i = 0; i < videoList.length; i++) {
        const video = videoList[i]
        try {
          const commentList = await getCommentList({
            accessToken: platformAccount.accessToken,
            openId: platformAccount.openId,
            itemId: video.item_id,
            cursor: 0,
            count: 19
          })

          if (!commentList.list) {
            break
          }

          comments.push(
            ...commentList.list.map((item: AnyObject) => {
              return {
                uniqueId: item.comment_id,
                event: WebhookEvents.CommentReply,
                fromUserId: item.comment_user_id,
                toUserId: platformAccount.openId,
                fromAvatar: item.avatar,
                fromName: item.nick_name,
                toAvatar: '',
                toName: '',
                sessionId: platformAccount.openId,
                content: {
                  commentId: item.comment_id,
                  commentUserId: item.comment_user_id,
                  content: item.content,
                  diggCount: item.digg_count,
                  replyCommentTotal: item.reply_comment_total,
                  replyToCommentId: item.reply_to_comment_id,
                  replyToItemId: video.item_id,
                  atUserId: item.at_user_id,
                  avatar: item.avatar,
                  name: item.nick_name,
                  parentId: item.parent_id,
                  top: item.top
                },
                createTime: (item.create_time * 1000) as number
              }
            })
          )
        } catch (e) {
          this.logger.error('initAccount commont', e)
        }

        await wait(500)
      }

      try {
        await this.commentModel.insertMany(comments)
      } catch (e) {
        this.logger.error('initAccount commont', e)
      }
    }

    this.logger.debug('初始化平台账号成功', socketIds)
    if (socketIds.length) {
      try {
        this.socketService.socketService
          .send({
            list: JSON.stringify(
              socketIds.map((socketId) => ({
                socketId,
                data: {
                  type: 'changePlatformAccount',
                  data: [
                    {
                      action: 'add',
                      platform: platformAccount.platform,
                      name: platformAccount.name,
                      avatar: platformAccount.avatar,
                      openId: platformAccount.openId,
                      accountRole: platformAccount.accountRole,
                      id: platformAccount.id,
                      teamId: platformAccount.teamId,
                      expiresTime: platformAccount.expiresIn
                        ? platformAccount.expiresIn * 1000 + platformAccount.tokenTime.getTime()
                        : 0
                    }
                  ]
                }
              }))
            )
          })
          .subscribe({
            next: () => {},
            error: (err) => {
              throw new BadRequestException(`发送失败 error${err.message}`)
            },
            complete: () => {}
          })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }

  async deleteAccount(platformAccountId: number) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        },
        role: {
          not: TeamMemberRole.Member
        }
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('你没有此账号权限')
    }

    const account = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId
      }
    })

    if (account && account.platform === Platform.Xiaohongshu && account.isBind) {
      throw new ForbiddenException('未解绑聚光的小红书账号无法删除')
    }

    try {
      const platformAccount = await this.prisma.platformAccount.delete({
        where: { id: platformAccountId }
      })

      const autoresponderList = await this.prisma.autoresponder.findMany({
        where: {
          platformAccountIds: {
            array_contains: platformAccount.id
          }
        }
      })

      autoresponderList.forEach(async (item) => {
        if (item.platformAccountIds) {
          const accountIds = (item.platformAccountIds as number[]).filter(
            (id) => id !== platformAccount.id
          )
          await this.prisma.autoresponder.update({
            where: { id: item.id },
            data: { platformAccountIds: accountIds }
          })
        }
      })

      await this.commentModel.deleteMany({
        toUserId: platformAccount.openId
      })

      await this.messagesModel.deleteMany({
        $and: [
          { $or: [{ fromUserId: platformAccount.openId }, { toUserId: platformAccount.openId }] },
          { event: { $in: [WebhookEvents.IMReceiveMessage, WebhookEvents.IMSendMessage] } }
        ]
      })

      this.platformAccountManageService.deletePlatformAccountRedisInfo(platformAccount)

      const bindUsers = await this.cacheManager.store.client.hgetall(
        genSocketRedisKey(platformAccount.openId)
      )

      const socketIds = []
      const delTask = []
      if (bindUsers) {
        Object.keys(bindUsers).forEach((socketId) => {
          socketIds.push(socketId)
          delTask.push(
            this.cacheManager.store.client.hdel(genSocketRedisKey(platformAccount.openId), socketId)
          )
        })
      }

      await Promise.all(delTask)
      this.logger.debug('删除平台账号成功', socketIds)

      if (platformAccount.platform === Platform.Wechat) {
        await wechatLogout({
          appId: platformAccount.appId
        })

        await this.wechatOpusModel.deleteMany({
          wxid: platformAccount.openId
        })
      }

      return this.socketService.socketService.send({
        list: JSON.stringify(
          socketIds.map((socketId) => ({
            socketId,
            data: {
              type: 'changePlatformAccount',
              data: [
                {
                  action: 'remove',
                  platform: platformAccount.platform,
                  name: platformAccount.name,
                  avatar: platformAccount.avatar,
                  openId: platformAccount.openId,
                  accountRole: platformAccount.accountRole,
                  id: platformAccount.id,
                  teamId: platformAccount.teamId
                }
              ]
            }
          }))
        )
      })
    } catch (e) {
      this.logger.error('删除平台账号失败', e)
    }
  }

  async additionPermission() {
    const { user } = this.request
    let data = true

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const [team, platformAccountCount, systemDosage] = await Promise.all([
      this.prisma.team.findUnique({
        where: {
          id: teamMember.teamId
        },
        include: {
          vip: true
        }
      }),
      this.prisma.platformAccount.count({ where: { teamId: teamMember.teamId } }),
      this.prisma.systemDosage.findFirst()
    ])

    if (team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
      if (team.vip.platformAccountNumberLimit <= platformAccountCount) {
        data = false
      }
    } else if (systemDosage.standardPlatformAccountNumberLimit <= platformAccountCount) {
      data = false
    }

    return data
  }

  async enableAccount(platformAccountId: number) {
    const { user } = this.request

    // 查询当前团队下的媒体号
    const account = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId,
        teamId: user.currentTeamId
      }
    })

    if (!account) {
      throw new NotFoundException('媒体号不存在')
    }

    if (account.status === AccountAccountsStatus.Normal) {
      return
    }

    const vip = await this.prisma.vip.findUnique({
      where: {
        teamId: user.currentTeamId
      }
    })

    // 已启用的媒体账号数量
    const activeMemberCount = await this.prisma.platformAccount.count({
      where: {
        teamId: user.currentTeamId,
        status: AccountAccountsStatus.Normal
      }
    })

    // 判断vip团队媒体账号数量限制
    if (vip?.expirationTime?.getTime() >= Date.now()) {
      if (vip.platformAccountNumberLimit <= activeMemberCount) {
        throw new ForbiddenException('启用媒体账号数已达上限')
      }
    } else {
      const systemDosage = await this.prisma.systemDosage.findFirst()
      // 非vip情况 只允许启用2个抖音号
      if (account.platform !== Platform.Douyin) {
        throw new ForbiddenException('免费用户只能启用抖音号')
      }
      if (activeMemberCount >= systemDosage.standardPlatformAccountNumberLimit) {
        throw new ForbiddenException('免费用户只能启用2个抖音号')
      }
    }
    try {
      const platformAccount = await this.prisma.platformAccount.update({
        where: {
          id: platformAccountId,
          teamId: user.currentTeamId
        },
        data: {
          status: AccountAccountsStatus.Normal
        }
      })

      await this.platformAccountManageService.updatePlatformAccountRedisInfo(platformAccount)
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`解除媒体号冻结状态失败`)
    }
  }

  async updateRemark(platformAccountId: number, { remark }: AccountUpdateRemarkRequestDTO) {
    const { user } = this.request

    const account = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId,
        teamId: user.currentTeamId
      }
    })

    if (!account) {
      throw new NotFoundException('媒体账号不存在')
    }

    await this.prisma.platformAccount.update({
      where: {
        id: platformAccountId
      },
      data: {
        remark
      }
    })
  }

  async getAccountMessageCard(platformAccountId: number) {
    try {
      const platformAccount = await this.prisma.platformAccount.findUnique({
        where: { id: platformAccountId, platform: Platform.Douyin }
      })

      if (!platformAccount) {
        throw new NotFoundException('账号不存在')
      }

      const result = await getRetainConsultCard({
        openId: platformAccount.openId,
        accessToken: platformAccount.accessToken
      })

      return result || []
    } catch (e) {
      throw new ForbiddenException(e)
    }
  }

  async getKuaishouAccountMessageCard(platformAccountId: number) {
    try {
      const { kuaishouClientKey, kuaishouClientSecret } =
        this.configService.get<RootConfigMap['app']>('app')
      const platformAccount = await this.prisma.platformAccount.findUnique({
        where: { id: platformAccountId, platform: Platform.Kuaishou }
      })

      if (!platformAccount) {
        throw new NotFoundException('账号不存在')
      }

      let result: AnyObject[]

      result = await postMarketToolList({
        openId: platformAccount.openId,
        accessToken: platformAccount.accessToken,
        appId: kuaishouClientKey
      })

      result = result.filter((item) => item.type === 1)

      const newResult = result.map((item) => ({
        ...item,
        id: decrypt(item.id, kuaishouClientSecret)
      }))

      return newResult || []
    } catch (e) {
      throw new ForbiddenException(e)
    }
  }

  async postAccountMessageCard(platformAccountId: number, data: AccountMessageCardRequestDTO) {
    try {
      const platformAccount = await this.prisma.platformAccount.findUnique({
        where: { id: platformAccountId }
      })

      if (!platformAccount) {
        throw new NotFoundException('账号不存在')
      }

      if (platformAccount.platform !== Platform.Douyin) {
        throw new ForbiddenException('无法创建/编辑留资卡片')
      }

      if (!data.cardId) {
        const cardList = await getRetainConsultCard({
          openId: platformAccount.openId,
          accessToken: platformAccount.accessToken
        })

        if (cardList && cardList.length > 0) {
          throw new ForbiddenException('留资卡片创建数量已超过限制')
        }
      }

      return await postRetainConsultCard({
        openId: platformAccount.openId,
        accessToken: platformAccount.accessToken,
        mediaId: data.mediaId,
        cardId: data.cardId,
        title: data.title,
        components: data.components
      })
    } catch (e) {
      throw new ForbiddenException(e)
    }
  }

  async deleteAccountMessageCard(platformAccountId: number, cardId: string) {
    try {
      const platformAccount = await this.prisma.platformAccount.findUnique({
        where: { id: platformAccountId }
      })

      if (!platformAccount) {
        throw new NotFoundException('账号不存在')
      }

      if (platformAccount.platform !== Platform.Douyin) {
        throw new NotFoundException('无法删除留资卡片')
      }

      const autoresponder = await this.prisma.autoresponder.findMany({
        where: {
          teamId: platformAccount.teamId,
          contents: {
            path: '$[*].cardInfo[*].cardId',
            array_contains: cardId
          }
        }
      })

      if (autoresponder.length > 0) {
        const autoresponderName = []
        autoresponder.forEach((element) => {
          autoresponderName.push(element.name)
        })

        throw new NotFoundException(
          `该留资卡片已被【${autoresponderName.join(',')}】策略使用，请先将该策略解除后删除。`
        )
      }

      await delRetainConsultCard({
        openId: platformAccount.openId,
        accessToken: platformAccount.accessToken,
        cardId
      })
    } catch (e) {
      throw new ForbiddenException(e)
    }
  }

  async getCardMessages({ cardId, page, size }: CardMessageListRequestDTO) {
    const { user } = this.request
    const total = await this.prisma.cardMessage.count({
      where: {
        teamId: user.currentTeamId,
        cardId
      }
    })

    if (!page) {
      page = 1
    }

    if (!size) {
      size = 10
    }

    const cards = await this.prisma.cardMessage.findMany({
      where: {
        teamId: user.currentTeamId,
        cardId
      },
      orderBy: {
        createTime: 'desc'
      },
      skip: (page - 1) * size,
      take: size
    })

    return {
      total,
      page,
      size,
      data: cards.map((item) => ({
        id: item.id,
        cardId: item.cardId,
        teamId: item.teamId,
        fromUserId: item.fromUserId,
        fromName: item.fromName,
        fromAvatar: item.fromAvatar,
        toUserId: item.toUserId,
        toName: item.toName,
        toAvatar: item.toAvatar,
        name: item.name,
        phone: item.phone,
        address: item.address,
        createTime: item.createTime.getTime(),
        cardData: item.cardData
      }))
    }
  }

  async getXiaohongshuAccountMessageCard(
    platformAccountId: number,
    page: number,
    size: number,
    type: number
  ) {
    try {
      const platformAccount = await this.prisma.platformAccount.findUnique({
        where: { id: platformAccountId, platform: Platform.Xiaohongshu }
      })

      if (!platformAccount) {
        throw new NotFoundException('账号不存在')
      }

      const result = await postMaterialList({
        openId: platformAccount.openId,
        accessToken: platformAccount.accessToken,
        page,
        size,
        type
      })

      return {
        data: result.list || [],
        total: result.total || 0,
        page,
        size
      }
    } catch (e) {
      throw new ForbiddenException(e)
    }
  }

  async getXiaohongshuAccountPageList(platformAccountId: number, page: number, size: number) {
    try {
      const platformAccount = await this.prisma.platformAccount.findUnique({
        where: { id: platformAccountId, platform: Platform.Xiaohongshu }
      })

      if (!platformAccount) {
        throw new NotFoundException('账号不存在')
      }

      const result = await postPageList({
        openId: platformAccount.openId,
        accessToken: platformAccount.accessToken,
        page,
        size
      })

      return {
        data: result.list || [],
        total: result.total || 0,
        page,
        size
      }
    } catch (e) {
      throw new ForbiddenException(e)
    }
  }
}
