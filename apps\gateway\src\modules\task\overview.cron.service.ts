import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Queue, Worker } from 'bullmq'
import { PrismaService } from '@qdy/mysql'
import { Cron } from '@nestjs/schedule'
import dayjs from 'dayjs'
import { Model } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { DailyMessageStatisticEntity, DailyOverviewEntity } from '@qdy/mongo'
import { TlsManageService } from '@qdy/common'

@Injectable()
export class OverviewCronService implements OnModuleInit {
  private readonly logger = new Logger(OverviewCronService.name)

  overViewQueue: Queue

  overViewWorker: Worker

  constructor(
    private readonly prisma: PrismaService,
    @InjectModel(DailyMessageStatisticEntity.name)
    private dailyMessageStatisticModel: Model<DailyMessageStatisticEntity>,
    @InjectModel(DailyOverviewEntity.name) private dailyOverviewModel: Model<DailyOverviewEntity>,
    private readonly tlsManageService: TlsManageService
  ) {}

  async onModuleInit() {
    this.overViewQueue = new Queue('overview-cron', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.overViewWorker = new Worker(
      'overview-cron',
      async (job) => {
        const { teamId, yesterday } = job.data
        this.logger.log(`Running task ${teamId} ${yesterday}`)
        await this.onUpdateOverViewCron({ teamId, yesterday })
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.logger.log('OverviewCronService init')
  }

  /**
   * 团队的评论人数和私信人数概览数据汇总
   * 每天凌晨4点
   */
  @Cron('0 0 4 * * *', {
    name: 'overViewCron',
    timeZone: 'Asia/Shanghai'
  })
  async overViewCron() {
    const teams = await this.prisma.team.findMany({
      where: {
        isDelete: false,
        platformAccounts: {
          some: {}
        }
      },
      select: {
        id: true
      }
    })

    const yesterday = dayjs().format('YYYY-MM-DD')

    for (let i = 0; i < teams.length; i++) {
      const teamId = teams[i].id

      this.overViewQueue.add(
        'overview-cron',
        {
          teamId,
          yesterday
        },
        {
          delay: 1 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `overview-cron-${teamId}`
        }
      )
    }
  }

  async onUpdateOverViewCron({ teamId, yesterday }) {
    if (!teamId || !yesterday) {
      return
    }

    try {
      // 获取抖音评论人数
      const accountMessageStatistic = await this.dailyMessageStatisticModel.aggregate([
        {
          $match: {
            createTime: yesterday,
            teamId
          }
        },
        {
          $group: {
            _id: null,
            commentPeopleCount: {
              $sum: { $ifNull: ['$commentPeopleCount', 0] }
            },
            singlePeopleCount: {
              $sum: { $ifNull: ['$singlePeopleCount', 0] }
            }
          }
        },
        {
          $project: {
            _id: 0,
            commentPeopleCount: 1,
            singlePeopleCount: 1
          }
        }
      ])

      const dailyOverview = await this.dailyOverviewModel.findOne({
        createTime: yesterday,
        teamId
      })

      const commentPeopleCount = accountMessageStatistic[0]?.commentPeopleCount || 0
      const singlePeopleCount = accountMessageStatistic[0]?.singlePeopleCount || 0
      if (!dailyOverview) {
        await this.dailyOverviewModel.create({
          createTime: yesterday,
          teamId,
          commentPeopleCount,
          singlePeopleCount
        })
      } else {
        await this.dailyOverviewModel.findByIdAndUpdate(dailyOverview.id, {
          commentPeopleCount,
          singlePeopleCount
        })
      }

      if (commentPeopleCount > 0 || singlePeopleCount > 0) {
        this.tlsManageService.putLogs({
          logData: `概览数据更新成功 teamId: ${teamId} 时间：${yesterday} 评论人数：${commentPeopleCount} 私信人数：${singlePeopleCount}`,
          logLevel: 'info',
          requestUri: 'onUpdateMessageStatisticCron',
          jobStatus: 'overview-cron'
        })
      }
    } catch (error) {
      this.logger.error('概览数据更新失败', error)
    }
  }
}
