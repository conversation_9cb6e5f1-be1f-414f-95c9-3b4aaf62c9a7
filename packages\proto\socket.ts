/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { Empty } from "./google/protobuf/empty";

export const protobufPackage = "socket";

export interface SocketParam {
  list: string;
}

function createBaseSocketParam(): SocketParam {
  return { list: "" };
}

export const SocketParam = {
  encode(message: SocketParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.list !== "") {
      writer.uint32(10).string(message.list);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SocketParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSocketParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.list = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SocketParam {
    return { list: isSet(object.list) ? globalThis.String(object.list) : "" };
  },

  toJSON(message: SocketParam): unknown {
    const obj: any = {};
    if (message.list !== "") {
      obj.list = message.list;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SocketParam>, I>>(base?: I): SocketParam {
    return SocketParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SocketParam>, I>>(object: I): SocketParam {
    const message = createBaseSocketParam();
    message.list = object.list ?? "";
    return message;
  },
};

export interface Socket {
  send(request: SocketParam): Observable<Empty>;
}

export const SocketServiceName = "socket.Socket";
export class SocketClientImpl implements Socket {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || SocketServiceName;
    this.rpc = rpc;
    this.send = this.send.bind(this);
  }
  send(request: SocketParam): Observable<Empty> {
    const data = SocketParam.encode(request).finish();
    const result = this.rpc.serverStreamingRequest(this.service, "send", data);
    return result.pipe(map((data) => Empty.decode(_m0.Reader.create(data))));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
  clientStreamingRequest(service: string, method: string, data: Observable<Uint8Array>): Promise<Uint8Array>;
  serverStreamingRequest(service: string, method: string, data: Uint8Array): Observable<Uint8Array>;
  bidirectionalStreamingRequest(service: string, method: string, data: Observable<Uint8Array>): Observable<Uint8Array>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
