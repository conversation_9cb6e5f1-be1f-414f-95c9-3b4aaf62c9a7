/*
  Warnings:

  - You are about to drop the column `opuserIds` on the `Autoresponder` table. All the data in the column will be lost.
  - You are about to drop the column `groupIds` on the `Greeting` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `Autoresponder` DROP COLUMN `opuserIds`;

-- AlterTable
ALTER TABLE `Greeting` DROP COLUMN `groupIds`;

-- CreateTable
CREATE TABLE `AutoresponderOpuser` (
    `id` VARCHAR(191) NOT NULL,
    `autoresponderId` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(191) NOT NULL,
    `avatar` VARCHAR(191) NOT NULL,
    `createTime` DATETIME(3) NOT NULL,
    `commentCount` INTEGER NOT NULL,
    `teamId` INTEGER NOT NULL,

    UNIQUE INDEX `AutoresponderOpuser_id_key`(`id`),
    PRIMARY KEY (`autoresponderId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `GreetingGroup` (
    `id` INTEGER NOT NULL,
    `greetingId` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `avatar` VARCHAR(191) NOT NULL,
    `teamId` INTEGER NOT NULL,

    PRIMARY KEY (`greetingId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `AutoresponderOpuser` ADD CONSTRAINT `AutoresponderOpuser_autoresponderId_fkey` FOREIGN KEY (`autoresponderId`) REFERENCES `Autoresponder`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `GreetingGroup` ADD CONSTRAINT `GreetingGroup_greetingId_fkey` FOREIGN KEY (`greetingId`) REFERENCES `Greeting`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
