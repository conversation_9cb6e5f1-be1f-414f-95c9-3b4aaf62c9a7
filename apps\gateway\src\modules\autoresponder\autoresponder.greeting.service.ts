// import { ForbiddenException, HttpException, Inject, Injectable, Logger } from '@nestjs/common'
// import {
//   AutoresponderGreetingCreateDTO,
//   AutoresponderGreetingGroup,
//   AutoresponderPlatformAccountType
// } from './autoresponder.dto'
// import { FastifyRequest } from 'fastify'
// import { REQUEST } from '@nestjs/core'
// import { Greeting, PlatformAccount, PrismaService } from '@qdy/mysql'
// import { TeamMemberRole } from '../team/team.dto'
// import { disableGroupGreeting, setGroupGreeting } from './external'
// import { AnyObject } from 'mongoose'
// import { CACHE_MANAGER } from '@nestjs/cache-manager'
// import { RedisStore } from 'cache-manager-ioredis-yet'
// import { Cache } from 'cache-manager'
// import { AutoresponderGreetingKey } from './constant'

// @Injectable()
// export class AutoresponderGreetingService {
//   logger = new Logger('InteractService.greeting')

//   constructor(
//     @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
//     @Inject(REQUEST) private request: FastifyRequest,
//     private readonly prisma: PrismaService
//   ) {}

//   async createAutoresponder(data: AutoresponderGreetingCreateDTO) {
//     const { user } = this.request

//     const { teamMember, platformAccountIds, platformAccounts } = await this.checkRole({
//       userId: user.id,
//       propPlatformAccountIds: data.platformAccountIds
//     })

//     const newPlatformAccountIds =
//       data.platformAccountType === AutoresponderPlatformAccountType.All
//         ? [...platformAccountIds]
//         : data.platformAccountIds

//     await this.checkLock({
//       teamId: teamMember.teamId,
//       platformAccountIds: newPlatformAccountIds,
//       groupIds: data.groups.map((item) => item.id)
//     })

//     const newGreeting = await this.prisma.greeting.create({
//       data: {
//         ...data,
//         groups: data.groups as AnyObject[],
//         platformAccountIds: newPlatformAccountIds,
//         teamId: teamMember.teamId,
//         ownerTeamMemberId: user.id
//       }
//     })

//     if (data.state && data.groups && data.groups.length) {
//       const platformAccountMaps: Record<number, PlatformAccount> = {}
//       platformAccounts.forEach((platformAccount) => {
//         platformAccountMaps[platformAccount.id] = platformAccount
//       })
//       const tasks = []
//       data.groups.forEach((group) => {
//         const platformAccount = platformAccountMaps[group.platformAccountId]
//         tasks.push(
//           setGroupGreeting({
//             accessToken: platformAccount.accessToken,
//             openId: platformAccount.openId,
//             groupId: group.id,
//             greeting: data.content
//           })
//         )
//       })

//       await Promise.all(tasks)
//     }

//     if (data.state && data.platformAccountIds && data.platformAccountIds.length) {
//       const platformAccountMap = {}
//       platformAccounts.forEach((platformAccount) => {
//         platformAccountMap[platformAccount.id] = platformAccount
//       })
//       data.platformAccountIds.forEach((id) => {
//         this.cacheManager.store.client.hset(
//           AutoresponderGreetingKey,
//           platformAccountMap[id].openId,
//           JSON.stringify({
//             contentType: data.contentType,
//             state: data.state,
//             accountExpired:
//               platformAccountMap[id].expiresIn * 1000 + platformAccountMap[id].createTime.getTime(),
//             token: platformAccountMap[id].accessToken,
//             content: data.content,
//             platformAccountId: id,
//             teamId: platformAccountMap[id].teamId,
//             autoresponderId: newGreeting.id,
//             imageId: data.imageId
//           })
//         )
//       })
//     }
//   }

//   async getAutoresponderById(id: number) {
//     const { user } = this.request

//     const { teamMember } = await this.checkRole({
//       userId: user.id,
//       id
//     })

//     const [greeting, platformAccounts] = await Promise.all([
//       this.prisma.greeting.findUnique({
//         where: {
//           id
//         }
//       }),
//       this.prisma.platformAccount.findMany({
//         where: {
//           teamId: teamMember.teamId
//         }
//       })
//     ])

//     if (!greeting) {
//       throw new ForbiddenException('自动回复不存在')
//     }

//     const platformAccountIds = []
//     const itemPlatformAccountIds = new Set<number>(greeting.platformAccountIds as number[])
//     platformAccounts.forEach((platformAccount) => {
//       if (itemPlatformAccountIds.has(platformAccount.id)) {
//         platformAccountIds.push({
//           name: platformAccount.name,
//           avatar: platformAccount.avatar,
//           id: platformAccount.id
//         })
//       }
//     })

//     return {
//       ...greeting,
//       platformAccountIds
//     }
//   }

//   async getAutoresponder({ content, page, size }: { content: string; page: number; size: number }) {
//     const { user } = this.request

//     const teamMember = await this.prisma.teamMember.findUnique({
//       where: {
//         userId: user.id
//       },
//       include: {
//         platformAccounts: true
//       }
//     })

//     if (!teamMember) {
//       throw new HttpException('用户未加入团队', -1)
//     }

//     const where = {
//       teamId: teamMember.teamId,
//       content: {
//         contains: content
//       }
//     }

//     const [greetings, total, platformAccounts] = await Promise.all([
//       this.prisma.greeting.findMany({
//         where,
//         skip: (page - 1) * size,
//         take: size
//       }),
//       this.prisma.greeting.count({
//         where
//       }),
//       this.prisma.platformAccount.findMany({
//         where: {
//           teamId: teamMember.id
//         }
//       })
//     ])

//     return {
//       data: greetings.map((item) => {
//         const platformAccountIds = []

//         const itemPlatformAccountIds = new Set<number>(item.platformAccountIds as number[])
//         platformAccounts.forEach((platformAccount) => {
//           if (itemPlatformAccountIds.has(platformAccount.id)) {
//             platformAccountIds.push({
//               name: platformAccount.name,
//               avatar: platformAccount.avatar,
//               id: platformAccount.id
//             })
//           }
//         })
//         return {
//           ...item,
//           platformAccountsLength: (item.platformAccountIds as AnyObject[]).length,
//           groupsLength: (item.groups as AnyObject[]).length,
//           platformAccountIds
//         }
//       }),
//       total,
//       page,
//       size
//     }
//   }

//   async deleteAutoresponder(id: number) {
//     const { user } = this.request

//     const { teamMember, platformAccounts } = await this.checkRole({
//       userId: user.id,
//       id
//     })

//     try {
//       const res = await this.prisma.greeting.delete({
//         where: {
//           id,
//           teamId: teamMember.teamId
//         }
//       })

//       const platformAccountMap = {}
//       platformAccounts.forEach((platformAccount) => {
//         platformAccountMap[platformAccount.id] = platformAccount
//       })

//       if (res.platformAccountIds && (res.platformAccountIds as number[]).length) {
//         ;(res.platformAccountIds as number[]).forEach((id) => {
//           this.cacheManager.store.client.hdel(
//             AutoresponderGreetingKey,
//             platformAccountMap[id].openId
//           )
//         })
//       }
//     } catch (error) {
//       this.logger.error(error)
//       throw new ForbiddenException('删除失败')
//     }
//   }

//   async updateAutoresponder(data: AutoresponderGreetingCreateDTO & { id: number }) {
//     const { user } = this.request

//     const { teamMember, platformAccountIds, platformAccounts } = await this.checkRole({
//       userId: user.id,
//       id: data.id,
//       propPlatformAccountIds: data.platformAccountIds
//     })

//     const newPlatformAccountIds =
//       data.platformAccountType === AutoresponderPlatformAccountType.All
//         ? [...platformAccountIds]
//         : data.platformAccountIds

//     await this.checkLock({
//       oldId: data.id,
//       platformAccountIds: newPlatformAccountIds,
//       groupIds: data.groups.map((item) => item.id),
//       teamId: teamMember.teamId
//     })

//     try {
//       const updateData = {
//         ...data,
//         groups: data.groups as AnyObject[],
//         platformAccountIds: newPlatformAccountIds
//       }
//       const res = await this.prisma.greeting.update({
//         data: updateData,
//         where: {
//           id: data.id,
//           teamId: teamMember.teamId
//         }
//       })

//       this.updateRedis({
//         res: { ...res, ...updateData },
//         oldRes: res,
//         platformAccounts
//       })
//     } catch (error) {
//       this.logger.error(error)
//       throw new ForbiddenException('更新失败')
//     }
//   }

//   async updateAutoresponderState(id: number, state: boolean) {
//     const { user } = this.request

//     const { teamMember, platformAccounts } = await this.checkRole({
//       userId: user.id,
//       id
//     })

//     try {
//       const res = await this.prisma.greeting.update({
//         data: {
//           state
//         },
//         where: {
//           id,
//           teamId: teamMember.teamId
//         }
//       })

//       this.updateRedis({
//         res: { ...res, state },
//         oldRes: res,
//         platformAccounts
//       })
//     } catch (error) {
//       this.logger.error(error)
//       throw new ForbiddenException('更新失败')
//     }
//   }

//   async checkRole({
//     userId,
//     id,
//     propPlatformAccountIds
//   }: {
//     propPlatformAccountIds?: number[]
//     id?: number
//     userId: number
//   }) {
//     const teamMember = await this.prisma.teamMember.findUnique({
//       where: {
//         userId
//       },
//       include: {
//         platformAccounts: true
//       }
//     })

//     if (!teamMember) {
//       throw new HttpException('用户未加入团队', -1)
//     }

//     if (teamMember.role === TeamMemberRole.Member && id) {
//       const autoresponder = await this.prisma.autoresponder.findUnique({
//         where: {
//           id
//         }
//       })

//       if (!autoresponder) {
//         throw new ForbiddenException('自动回复不存在')
//       }

//       const memberPlatformAccoutIds = new Set(teamMember.platformAccounts.map((item) => item.id))
//       ;(autoresponder.platformAccountIds as number[]).forEach((id) => {
//         if (!memberPlatformAccoutIds.has(id)) {
//           throw new ForbiddenException('权限不足')
//         }
//       })
//     }

//     if (propPlatformAccountIds) {
//       if (id) {
//         const platformAccounts = await this.prisma.platformAccount.findMany({
//           where: {
//             teamId: teamMember.teamId
//           }
//         })

//         const platformAccountIds = new Set(platformAccounts.map((item) => item.id))

//         propPlatformAccountIds.forEach((id) => {
//           if (!platformAccountIds.has(id)) {
//             throw new ForbiddenException('平台账号不存在')
//           }
//         })

//         return { teamMember, platformAccountIds, platformAccounts }
//       }

//       if (teamMember.role === TeamMemberRole.Member) {
//         const platformAccountIds = new Set(teamMember.platformAccounts.map((item) => item.id))

//         propPlatformAccountIds.forEach((id) => {
//           if (!platformAccountIds.has(id)) {
//             throw new ForbiddenException('平台账号不存在')
//           }
//         })

//         return { teamMember, platformAccountIds, platformAccounts: teamMember.platformAccounts }
//       }
//       const platformAccounts = await this.prisma.platformAccount.findMany({
//         where: {
//           teamId: teamMember.teamId
//         }
//       })

//       const platformAccountIds = new Set(platformAccounts.map((item) => item.id))

//       propPlatformAccountIds.forEach((id) => {
//         if (!platformAccountIds.has(id)) {
//           throw new ForbiddenException('平台账号不存在')
//         }
//       })

//       return { teamMember, platformAccountIds, platformAccounts }
//     }

//     if (teamMember.role === TeamMemberRole.Member) {
//       return { teamMember, platformAccounts: teamMember.platformAccounts }
//     }

//     const platformAccounts = await this.prisma.platformAccount.findMany({
//       where: {
//         teamId: teamMember.teamId
//       }
//     })

//     return { teamMember, platformAccounts }
//   }

//   async checkLock({
//     platformAccountIds,
//     groupIds,
//     oldId,
//     teamId
//   }: {
//     teamId: number
//     platformAccountIds: number[]
//     groupIds: string[]
//     oldId?: number
//   }) {
//     if (platformAccountIds.length === 0 && groupIds.length === 0) {
//       throw new ForbiddenException('请选择账号或者群聊')
//     }

//     const isLock = await this.prisma.greeting.findFirst({
//       where: {
//         teamId,
//         NOT: {
//           id: oldId
//         },
//         OR: [
//           ...platformAccountIds.map((id) => ({
//             platformAccountIds: {
//               array_contains: id
//             }
//           })),
//           ...(groupIds.length
//             ? [
//                 {
//                   groups: {
//                     path: '$[*].id',
//                     array_contains: groupIds
//                   }
//                 }
//               ]
//             : [])
//         ]
//       }
//     })

//     console.log('isLock', isLock)

//     if (isLock) {
//       throw new ForbiddenException('账号/群聊已经创建了欢迎语')
//     }
//   }

//   async updateRedis({
//     res,
//     oldRes,
//     platformAccounts
//   }: {
//     res: Greeting
//     oldRes: Greeting
//     platformAccounts: PlatformAccount[]
//   }) {
//     if (!platformAccounts) {
//       this.logger.error('没有获取到平台账号, ', res)
//       return
//     }

//     if (res.groups && (res.groups as unknown as AutoresponderGreetingGroup[]).length) {
//       if (oldRes.state !== res.state) {
//         const platformAccountMaps: Record<number, PlatformAccount> = {}
//         platformAccounts.forEach((platformAccount) => {
//           platformAccountMaps[platformAccount.id] = platformAccount
//         })
//         const tasks = []
//         ;(res.groups as unknown as AutoresponderGreetingGroup[]).forEach((group) => {
//           const platformAccount = platformAccountMaps[group.platformAccountId]
//           if (res.state) {
//             tasks.push(
//               setGroupGreeting({
//                 accessToken: platformAccount.accessToken,
//                 openId: platformAccount.openId,
//                 groupId: group.id,
//                 greeting: res.content
//               })
//             )
//           } else {
//             tasks.push(
//               disableGroupGreeting({
//                 accessToken: platformAccount.accessToken,
//                 openId: platformAccount.openId,
//                 groupId: group.id
//               })
//             )
//           }
//         })

//         await Promise.all(tasks)
//       }
//     }

//     // 更新 redis 信息
//     if (
//       res.platformAccountIds &&
//       (res.platformAccountIds as number[]).length &&
//       res.state !== oldRes.state
//     ) {
//       const platformAccountMap = {}
//       platformAccounts.forEach((platformAccount) => {
//         platformAccountMap[platformAccount.id] = platformAccount
//       })
//       ;(res.platformAccountIds as number[]).forEach((id) => {
//         this.cacheManager.store.client.hset(
//           AutoresponderGreetingKey,
//           platformAccountMap[id].openId,
//           JSON.stringify({
//             contentType: res.contentType,
//             state: res.state,
//             accountExpired:
//               platformAccountMap[id].expiresIn * 1000 + platformAccountMap[id].createTime.getTime(),
//             token: platformAccountMap[id].accessToken,
//             content: res.content,
//             platformAccountId: id,
//             teamId: platformAccountMap[id].teamId,
//             autoresponderId: res.id,
//             imageId: res.imageId
//           })
//         )
//       })
//     }
//   }
// }
