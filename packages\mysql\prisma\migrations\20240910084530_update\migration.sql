-- CreateTable
CREATE TABLE `Coupons` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `minimumSpendingAmount` INTEGER NOT NULL DEFAULT 0,
    `discountAmount` INTEGER NOT NULL DEFAULT 0,
    `creatorId` INTEGER NOT NULL,
    `creatorName` VARCHAR(191) NOT NULL,
    `creatTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `postAmount` INTEGER NOT NULL DEFAULT 0,
    `activeAmount` INTEGER NOT NULL DEFAULT 0,
    `status` INTEGER NOT NULL DEFAULT 0,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UsersCoupons` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `couponsId` INTEGER NOT NULL,
    `phone` VARCHAR(191) NOT NULL,
    `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `expireTime` DATETIME(3) NOT NULL,
    `minimumSpendingAmount` INTEGER NOT NULL,
    `discountAmount` INTEGER NOT NULL,
    `status` INTEGER NOT NULL DEFAULT 0,

    INDEX `UsersCoupons_phone_createTime_expireTime_idx`(`phone`, `createTime`, `expireTime`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
