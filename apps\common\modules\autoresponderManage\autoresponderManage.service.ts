import { BadRequestException, Inject, Injectable } from '@nestjs/common'
import { PlatformAccount, PrismaService } from '@qdy/mysql'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { AutoresponderKeywordRedisValue, AutoresponderOpuser } from './autoresponderManage.dto'
import { AutoresponderKeywordKey } from '@qdy/utils'

@Injectable()
export class AutoresponsederManageService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  async updateAutoresponderInfo(platformAccount: PlatformAccount) {
    const commentAutoresponder = await this.prisma.autoresponder.findMany({
      where: {
        opusers: {
          path: '$[*].platformAccountId',
          array_contains: [platformAccount.id]
        }
      }
    })

    commentAutoresponder.forEach((item) => {
      ;(item.opusers as unknown as AutoresponderOpuser[]).forEach(async (opuser) => {
        const key = `${platformAccount.openId}:${opuser.id}`
        const opuserValueString = await this.cacheManager.store.client.hget(
          AutoresponderKeywordKey,
          key
        )
        try {
          if (opuserValueString) {
            const opuserValue = JSON.parse(opuserValueString) as AutoresponderKeywordRedisValue[]
            for (let i = 0; i < opuserValue.length; i++) {
              const value = opuserValue[i]
              opuserValue[i] = {
                ...value
              }
            }
            await this.cacheManager.store.client.hset(
              AutoresponderKeywordKey,
              key,
              JSON.stringify(opuserValue)
            )
          }
        } catch (e) {
          throw new BadRequestException(`更新出错 error ${e.msg}`)
        }
      })
    })

    const autoresponder = await this.cacheManager.store.client.hget(
      AutoresponderKeywordKey,
      platformAccount.openId
    )

    if (autoresponder) {
      const arr = JSON.parse(autoresponder) as AutoresponderKeywordRedisValue[]
      // autoresponder-update-value 全局搜索需要修改的地方
      for (let i = 0; i < arr.length; i++) {
        const value = arr[i]
        arr[i] = {
          platform: platformAccount.platform,
          scene: value.scene,
          trigger: value.trigger,
          keyword: value.keyword,
          rule: value.rule,
          stopReply: value.stopReply,
          stopInterval: value.stopInterval,
          stopTime: value.stopTime,
          isDelay: value.isDelay,
          delayTime: value.delayTime,
          contents: value.contents,
          contentType: value.contentType,
          state: value.state,
          isNew: value.isNew,
          executionCount: value.executionCount,
          autoresponderId: value.autoresponderId
        }
      }

      this.cacheManager.store.client.hset(
        AutoresponderKeywordKey,
        platformAccount.openId,
        JSON.stringify(arr)
      )
    }
  }

  async deleteAutoresponder(platformAccount: PlatformAccount) {
    // 查找所有符合条件的记录
    const autoresponders = await this.prisma.autoresponder.findMany({
      where: {
        opusers: {
          path: '$[*].platformAccountId',
          array_contains: [platformAccount.id]
        }
      }
    })

    const keysToDelete = autoresponders.flatMap((item) =>
      (item.opusers as unknown as AutoresponderOpuser[]).map(
        (opuser) => `${platformAccount.openId}:${opuser.id}`
      )
    )

    try {
      if (keysToDelete.length > 0) {
        await this.cacheManager.store.client.hdel(AutoresponderKeywordKey, ...keysToDelete)
      }
      await this.cacheManager.store.client.hdel(AutoresponderKeywordKey, platformAccount.openId)
    } catch {
      // ignore
    }

    // 如果找到了记录，进行更新
    if (autoresponders.length > 0) {
      // 获取需要更新的 ID 列表
      const updates = autoresponders.map((responder) => ({
        id: responder.id,
        updatedIds: (responder.platformAccountIds as number[]).filter(
          (id) => id !== platformAccount.id
        ) // 移除指定的 ID
      }))

      // 批量更新所有符合条件的记录
      const updatePromises = updates.map((update) =>
        this.prisma.autoresponder.update({
          where: { id: update.id },
          data: {
            platformAccountIds: update.updatedIds // 使用更新后的 ID 列表
          }
        })
      )

      // 等待所有更新完成
      await Promise.all(updatePromises)
    }
  }
}
