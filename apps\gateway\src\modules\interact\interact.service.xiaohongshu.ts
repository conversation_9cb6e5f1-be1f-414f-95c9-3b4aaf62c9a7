import { ForbiddenException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { PrismaService } from '@qdy/mysql'
import { FastifyRequest } from 'fastify'
import { RecallMessageRequestDTO, XiaohongshuMessagesRequestDTO } from './interact.dto'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { TeamMemberRole } from '../team/team.dto'
import { AccountAccountsStatus } from '../account/account.dto'
import { Platform } from '@qdy/utils'
import { ContentType, postSendMessage } from '../webhook/external.xiaohongshu'
import { InjectModel } from '@nestjs/mongoose'
import { PersonalChatMessageEntity } from 'packages/mongo/lib'
import { Model } from 'mongoose'

@Injectable()
export class InteractXiaohongshuService {
  logger = new Logger('InteractXiaohongshuService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async sendXiaohongshuMessage({
    toUserId,
    platformAccountId,
    content,
    messageType,
    width,
    height,
    contentLenght,
    toName,
    toAvatar,
    title,
    subTitle,
    image,
    socialType,
    linkPlatform,
    noteTitle,
    cover,
    commentContent,
    commentId,
    link,
    desc
  }: XiaohongshuMessagesRequestDTO) {
    const { user } = this.request

    const { platformAccount, teamMember } = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId
    })

    if (platformAccount.isBind === false) {
      throw new ForbiddenException('账号未绑定聚光，发送失败')
    }

    let contentType = ContentType.Text

    switch (messageType) {
      case 'text':
        contentType = ContentType.Text
        break
      case 'image':
        contentType = ContentType.Image
        break
      case 'retain_consult_card':
        contentType = ContentType.Card
        break
      case 'consult_card':
        contentType = ContentType.BusinessCard
        break
      case 'trade_business_card':
        contentType = ContentType.TradeBusinessCard
        break
      case 'comment':
        contentType = ContentType.Comment
        break
      case 'common':
        contentType = ContentType.Common
        break
    }

    const res = await postSendMessage({
      messageType: contentType,
      content,
      secret: process.env.XIAOHONGSHU_SECRET,
      openId: platformAccount.openId,
      accessToken: platformAccount.accessToken,
      fromUserId: platformAccount.openId,
      toUserId,
      platformAccountId,
      teamId: platformAccount.teamId,
      width,
      height,
      contentLenght,
      redisClient: this.cacheManager,
      scene: 'im_replay_msg',
      fromName: platformAccount.name,
      fromAvatar: platformAccount.avatar,
      toName,
      toAvatar,
      socialType,
      linkPlatform,
      title,
      subTitle,
      image,
      cover,
      commentContent,
      noteTitle,
      commentId,
      link,
      desc
    })

    await this.prisma.teamMember.update({
      where: {
        id: teamMember.id
      },
      data: {
        replyMessage: {
          increment: 1
        }
      }
    })

    return {
      messageId: res.messageId
    }
  }

  private async checkPlatformAccount({
    teamId,
    userId,
    platformAccountId
  }: {
    teamId: number
    userId: number
    platformAccountId: number
  }) {
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('用户未加入此团队')
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    const platformAccount = platformAccounts.find((item) => item.id === platformAccountId)

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在或你不属于运营人员')
    }

    if (platformAccount.platform !== Platform.Xiaohongshu) {
      throw new ForbiddenException('此账号不是小红书账号')
    }

    if (platformAccount.status === AccountAccountsStatus.Disable) {
      throw new ForbiddenException('账号已被冻结')
    }

    return { platformAccount, teamMember }
  }

  async postRevokeMessage({ uniqueId, platformAccountId }: RecallMessageRequestDTO) {
    const account = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId
      }
    })

    if (!account) {
      throw new NotFoundException('账号不存在')
    }

    if (account.status === AccountAccountsStatus.Disable) {
      throw new ForbiddenException('此账号已被冻结')
    }

    const message = await this.personalChatMessageModel.findOne({
      openId: account.openId,
      uniqueId
    })

    if (!message) {
      throw new NotFoundException('消息不存在')
    }

    if (message.isRecall) {
      throw new NotFoundException('此消息已删除')
    }

    if (account.openId !== message.fromUserId) {
      throw new NotFoundException('只能撤回自己发送的消息')
    }

    await postSendMessage({
      messageType: ContentType.Revoke,
      content: message.messageId,
      secret: process.env.XIAOHONGSHU_SECRET,
      openId: message.openId,
      accessToken: account.accessToken,
      fromUserId: message.fromUserId,
      toUserId: message.toUserId,
      teamId: account.teamId,
      platformAccountId: account.id,
      fromName: message.fromName,
      fromAvatar: message.fromAvatar,
      toName: message.toName,
      toAvatar: message.toAvatar,
      auto: false,
      autoresponderId: 0,
      redisClient: this.cacheManager
    })

    await this.personalChatMessageModel.findByIdAndUpdate(message.id, { isRecall: 1 })
  }
}
