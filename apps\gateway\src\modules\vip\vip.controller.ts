import { Controller, Get } from '@nestjs/common'
import { VipService } from './vip.service'

import { ApiOperation, ApiTags, ApiOkResponse, ApiHeader } from '@nestjs/swagger'
import { OrderInterestResponseDTO } from './vip.dto'

@Controller('vip')
@ApiTags('VIP')
export class VipController {
  constructor(private readonly vipService: VipService) {}

  @Get('interest')
  @ApiOperation({ summary: '获取VIP规格' })
  @ApiOkResponse({ type: OrderInterestResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getInterest() {
    return this.vipService.getInterest()
  }
}
