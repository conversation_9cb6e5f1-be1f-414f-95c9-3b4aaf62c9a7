import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { LastAppVersionRequestDTO, PublicCreateUploadUrlRequestBodyDTO } from './public.dto'
import { InjectModel } from '@nestjs/mongoose'
import { AppVersionEntity, NoticeEntity } from '@qdy/mongo'
import { Model } from 'mongoose'
import { PrismaService } from 'packages/mysql/lib'
import { KUAIDIALI_AREAS } from './kuaidaili-areas'
import { TosManageService } from '@qdy/common'

@Injectable()
export class PublicService implements OnModuleInit {
  private logger = new Logger('PublicService')

  async onModuleInit() {}

  constructor(
    @InjectModel(NoticeEntity.name) private noticeModel: Model<NoticeEntity>,
    @InjectModel(AppVersionEntity.name) private appVersionModel: Model<AppVersionEntity>,
    private readonly prisma: PrismaService,
    private readonly tosManageService: TosManageService
  ) {}

  async createUploadUrl({ objectName, type }: PublicCreateUploadUrlRequestBodyDTO) {
    const result = await this.tosManageService.getPreSignedPostSignature({
      objectName: `${type}/${objectName}`
    })

    return result
  }

  async getSystemNotice() {
    return this.noticeModel.find().sort({ createTime: -1 }).limit(5)
  }

  async getProvice() {
    const provice = await this.prisma.city.findMany({
      where: {
        type: 1
      }
    })

    return provice
  }

  async getCity(proviceId: number) {
    const city = await this.prisma.city.findMany({
      where: {
        parentId: proviceId,
        type: 2
      }
    })

    return city
  }

  async getAreas() {
    return KUAIDIALI_AREAS
  }

  async getLastAppVersions({ version, type }: LastAppVersionRequestDTO) {
    const currentVersion = await this.appVersionModel.findOne({
      type,
      version
    })

    if (currentVersion) {
      const appVersions = await this.appVersionModel
        .find({
          type,
          releaseTime: { $gt: currentVersion.releaseTime }
        })
        .sort({ releaseTime: -1 })
        .limit(10)
        .lean()

      let force = false

      appVersions.forEach((item) => {
        if (item.force) {
          force = true
        }
      })

      if (!appVersions.length) {
        return null
      }

      return { ...appVersions[0], force }
    }
    const appVersions = await this.appVersionModel
      .findOne({
        type
      })
      .sort({ releaseTime: -1 })
      .lean()

    if (!appVersions) {
      return null
    }

    appVersions.force = true
    return appVersions
  }

  async getLastAppVersionWebSite() {
    const ios = this.appVersionModel
      .findOne({
        type: 'ios'
      })
      .sort({ releaseTime: -1 })
      .lean()

    const android = this.appVersionModel
      .findOne({
        type: 'android'
      })
      .sort({ releaseTime: -1 })
      .lean()

    const [i, a] = await Promise.all([ios, android])
    return {
      ios: i
        ? {
            version: i.version,
            url: i.url
          }
        : null,
      android: a
        ? {
            version: a.version,
            url: a.url
          }
        : null
    }
  }
}
