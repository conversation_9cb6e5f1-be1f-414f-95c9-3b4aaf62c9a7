import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Length,
  MinLength
} from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

/**
 * @description 用户状态
 */
export enum UserState {
  Normal,
  Ban
}

export enum TeamState {
  NotJoin = 0,
  Join = 1
}

export enum SMSCodeSence {
  Auth = 'auth',
  Password = 'password',
  Juguang = 'juguang'
}

export class UserLoginRegisterRequestBodyDTO {
  @ApiProperty({
    description: '手机号',
    example: '13800138000',
    required: true
  })
  @IsString({ message: '手机号码格式不正确' })
  @IsNotEmpty({ message: '手机号不能为空' })
  @Length(11, 11, { message: '请输入11位手机号' })
  phone: string

  @ApiProperty({
    description: '验证码',
    example: '123456',
    required: true
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  @Length(6, 6, { message: '请输入6位验证码' })
  code: string

  @ApiProperty({
    description: '邀请码',
    example: '123456',
    required: false
  })
  @IsOptional()
  @IsString({ message: '邀请码格式不正确' })
  @MinLength(5, { message: '请输入不少于5位邀请码' })
  invitationCode?: string

  @ApiProperty({
    description: '渠道码',
    example: '123456',
    required: false
  })
  @IsOptional()
  @IsString({ message: '渠道码格式不正确' })
  channelCode?: string

  @ApiProperty({
    description: '是否是投放注册',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isAdvertisement?: boolean
}

export class AgreeAuthRequestBodyDTO {
  @ApiProperty({
    description: '手机号',
    example: '13800138000',
    required: true
  })
  @IsString({ message: '手机号码格式不正确' })
  @IsNotEmpty({ message: '手机号不能为空' })
  @Length(11, 11, { message: '请输入11位手机号' })
  phone: string

  @ApiProperty({
    description: '验证码',
    example: '123456',
    required: true
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  @Length(6, 6, { message: '请输入6位验证码' })
  code: string
}

export class UserPasswordLoginRequestBodyDTO {
  @ApiProperty({
    description: '手机号',
    example: '13800138000',
    required: true
  })
  @IsString({ message: '手机号码格式不正确' })
  @IsNotEmpty({ message: '手机号不能为空' })
  @Length(11, 11, { message: '请输入11位手机号' })
  phone: string

  @ApiProperty({
    description: '密码',
    example: 'Ad234@sdf',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string
}

export class UserDeleteResponseDTO extends BaseResponseDTO {}

class CouponInfo {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '满2000减200'
  })
  // 优惠券名称
  name: string

  @ApiResponseProperty({
    type: Number,
    example: 5000
  })
  // 最低金额，满减额
  minimumSpendingAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 200
  })
  // 优惠金额
  discountAmount: number

  @ApiResponseProperty({
    type: Date,
    example: '2025-09-09T00:00:00Z'
  })
  // 过期时间
  expireTime: Date
}

class VipRegisterResponse {
  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  platformAccountNumberLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  messageLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  teamMemberNumberLimit: number

  @ApiResponseProperty({
    type: Date,
    example: '2025-09-09T00:00:00Z'
  })
  // 过期时间
  expirationTime: Date
}

class UserLoginResphoneDTO {
  /**
   * @description
   * 唯一凭证
   */
  @ApiResponseProperty({
    type: String,
    format: 'nanoid',
    example: '1300120012DE89D1DE89D'
  })
  authorization: string

  /**
   * @description
   * 团队状态
   * 0: 未加入
   * 1: 已加入
   */
  @ApiResponseProperty({
    type: Number,
    example: TeamState.NotJoin,
    format: '0: 未加入, 1: 已加入 <enum>'
  })
  teamState: TeamState

  /**
   * 优惠券信息
   */
  @ApiResponseProperty({
    type: CouponInfo
  })
  coupons: CouponInfo

  /**
   * vip信息
   */
  @ApiResponseProperty({
    type: VipRegisterResponse
  })
  vipInfo: VipRegisterResponse

  /**
   * 是否是新用户注册
   */
  @ApiResponseProperty({
    type: Boolean
  })
  isNewUser: boolean
}

export class UserLoginOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserLoginResphoneDTO
  })
  data: UserLoginResphoneDTO
}

class AgreeAuthResphoneDTO {
  /**
   * @description
   */
  @ApiResponseProperty({
    type: String,
    example: '1300120012DE89D1DE89D'
  })
  token: string

  /**
   * @description
   */
  @ApiResponseProperty({
    type: Number,
    example: 2307
  })
  appId: number
}

export class AgreeAuthOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AgreeAuthResphoneDTO
  })
  data: AgreeAuthResphoneDTO
}

export class UserSendCodeResponseDTO extends BaseResponseDTO {}

export class UserSendCodeRequestBodyDTO {
  /**
   * @description
   * phone number
   */
  @ApiProperty({
    description: 'Phone number',
    example: '13800138000',
    required: true
  })
  @IsString({ message: 'Phone number format is incorrect' })
  @Length(11, 11, { message: 'Please enter an 11-digit phone number' })
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string

  @ApiProperty({
    description: '验证码场景(auth登录,password修改密码,juguang聚光授权)',
    enum: Object.values(SMSCodeSence),
    example: 'auth',
    required: true
  })
  @IsEnum(SMSCodeSence, { message: '验证码场景不正确' })
  sence: SMSCodeSence
}

export class UserSendCodeRequestBodyDTOV2 {
  /**
   * @description
   * phone number
   */
  @ApiProperty({
    description: 'Phone number',
    example: '13800138000',
    required: true
  })
  @IsString({ message: 'Phone number format is incorrect' })
  @Length(11, 11, { message: 'Please enter an 11-digit phone number' })
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string

  @ApiProperty({
    description: '验证码场景(auth登录,password修改密码,juguang聚光授权)',
    enum: Object.values(SMSCodeSence),
    example: 'auth',
    required: true
  })
  @IsEnum(SMSCodeSence, { message: '验证码场景不正确' })
  sence: SMSCodeSence

  @ApiProperty({
    type: String,
    description: '验证参数',
    example: '123123213',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '验证参数不能为空' })
  captchaVerifyParam: string

  @ApiProperty({
    type: Boolean,
    description: '',
    example: 'true'
  })
  @IsBoolean()
  @IsOptional()
  isApp?: boolean
}

class UserInfoResponseDTO {
  @ApiResponseProperty({
    type: String,
    example: '1300120012DE89D1DE89D'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '13800138000'
  })
  phone: string

  @ApiResponseProperty({
    type: String,
    example: '张三'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'http://example.com/avatar.png'
  })
  avatar: string
}

export class UserOkUserInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserInfoResponseDTO
  })
  data: UserInfoResponseDTO
}

export class UserRequestUpdateDTO {
  @ApiProperty({
    description: '昵称',
    example: '张三',
    required: false
  })
  @IsString({ message: '昵称格式不正确' })
  @Length(2, 18)
  @IsOptional()
  name?: string

  @ApiProperty({
    description: '头像',
    example: 'http://example.com/avatar.png',
    required: false
  })
  @IsUrl({ protocols: ['https'] }, { message: '头像格式不正确' })
  @IsString({ message: '头像格式不正确' })
  @IsOptional()
  avatar?: string
}

export class UserPasswordRegisterBodyDto {
  @ApiProperty({
    description: '密码',
    example: 'Ad234@sdf',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string
}

export class UserPasswordUpdateBodyDto {
  @ApiProperty({
    description: '验证码',
    example: '123456',
    required: true
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  @Length(6, 6, { message: '请输入6位验证码' })
  code: string

  @ApiProperty({
    description: '密码',
    example: 'Ad234@sdf',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string
}

export class UserPasswordResetBodyDto {
  @ApiProperty({
    description: 'Phone number',
    example: '13800138000',
    required: true
  })
  @IsString({ message: 'Phone number format is incorrect' })
  @Length(11, 11, { message: 'Please enter an 11-digit phone number' })
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string

  @ApiProperty({
    description: '验证码',
    example: '123456',
    required: true
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  @Length(6, 6, { message: '请输入6位验证码' })
  code: string

  @ApiProperty({
    description: '密码',
    example: 'Ad234@sdf',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string
}

export class UserInfoExchangeKeyRequestBodyDTO {
  @ApiProperty({
    description: '兑换密钥',
    example: 'Ad234@sdf',
    required: true
  })
  @IsString({ message: '兑换密钥格式不正确' })
  exchangeKey: string
}

export class BaiduOcpcRequestBodyDTO {
  @ApiProperty({
    description: '落地页地址',
    example: 'http://www.bb123.com/12345?XX=XXX&bd_vid=1111',
    required: true
  })
  @IsString({ message: '落地页地址' })
  logidUrl: string
}
