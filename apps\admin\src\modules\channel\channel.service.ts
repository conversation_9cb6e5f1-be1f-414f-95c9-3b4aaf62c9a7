import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  OnModuleInit
} from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import {
  ChannelPasswordUpdateRequestDTO,
  ChannelRequestBodyDTO,
  ChannelRequestUpdateDTO,
  ChannelStatusRequestDTO
} from './channel.dto'
import { OrderStatus } from '../vip/vip.dto'
import { CouponsStatus } from '../coupons/coupons.dto'
import crypto from 'crypto'
import { SalesType } from '../overview/overview.dto'

@Injectable()
export class ChannelService implements OnModuleInit {
  logger = new Logger('ChannelService')

  constructor(private readonly prisma: PrismaService) {}

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  hasAlphabet(password: string): boolean {
    return /[a-zA-Z]/.test(password)
  }

  hasDigit(password: string): boolean {
    return /\d/.test(password)
  }

  validatePassword(password: string) {
    return this.hasAlphabet(password) && this.hasDigit(password)
  }

  async onModuleInit() {
    await this.compensateForDataLoss()
  }

  async createChannel({ name, code, couponId, username, password }: ChannelRequestBodyDTO) {
    const countByName = await this.prisma.channel.count({
      where: {
        name
      }
    })

    if (countByName > 0) {
      throw new BadRequestException(`渠道名称已存在`)
    }

    const countByCode = await this.prisma.channel.count({
      where: {
        code
      }
    })

    if (countByCode > 0) {
      throw new BadRequestException(`渠道码已存在`)
    }

    if (couponId) {
      const coupon = await this.prisma.coupons.findUnique({
        where: {
          id: couponId
        }
      })

      if (!coupon) {
        throw new BadRequestException(`优惠券不存在`)
      }

      if (coupon.status === CouponsStatus.Deleted) {
        throw new BadRequestException(`优惠券不存在`)
      }
    }

    const channelAdminUser = await this.prisma.channelAdminUser.findFirst({
      where: {
        username
      }
    })
    if (channelAdminUser) {
      throw new ForbiddenException('该管理账号已存在')
    }
    const result = this.validatePassword(password)
    if (!result) {
      throw new ForbiddenException('密码至少包含一个大写字母、一个小写字母、一位数字和一个特殊字符')
    }

    const channel = await this.prisma.channel.create({
      data: {
        name,
        code,
        couponId
      }
    })
    const { salt, hash } = this.hashPassword(password)
    await this.prisma.channelAdminUser.create({
      data: {
        username,
        password: hash,
        channelId: channel.id,
        salt,
        role: 0
      }
    })
  }

  async getChannels({
    page,
    size,
    name,
    startTime,
    endTime
  }: {
    page: number
    size: number
    name: string
    startTime: number
    endTime: number
  }) {
    const where: Parameters<typeof this.prisma.channel.findMany>[0]['where'] = {
      name: {
        contains: name
      },
      createTime: {
        gte: new Date(startTime),
        lte: new Date(endTime)
      }
    }

    if (!name) {
      delete where.name
    }

    if (!startTime || !endTime) {
      delete where.createTime
    }

    const [channels, total] = await Promise.all([
      this.prisma.channel.findMany({
        where,
        include: {
          User: true,
          ChannelAdminUser: {
            select: {
              id: true,
              username: true
            },
            take: 1
          },
          Order: {
            where: {
              orderStatus: OrderStatus.SUCCESS,
              salesType: {
                in: [SalesType.FirstBuy, SalesType.ReBuy]
              }
            }
          }
        },
        orderBy: { createTime: 'desc' },
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.channel.count({ where })
    ])

    return {
      total,
      page,
      size,
      data: channels.map((item) => ({
        ...item,
        ChannelAdminUser: item.ChannelAdminUser[0]?.username,
        channelUserId: item.ChannelAdminUser[0]?.id,
        userCount: item.User.length,
        orderCount: item.Order.length,
        orderTotal: item.Order.reduce((acc, od) => acc + od.payAmount, 0),
        createTime: item.createTime.getTime()
      }))
    }
  }

  async updateChannel(id: number, data: ChannelRequestUpdateDTO) {
    const channel = await this.prisma.channel.findUnique({
      where: {
        id
      }
    })

    if (!channel) {
      throw new BadRequestException(`渠道不存在`)
    }

    const countByName = await this.prisma.channel.count({
      where: {
        name: data.name,
        id: {
          not: id
        }
      }
    })

    if (countByName > 0) {
      throw new BadRequestException(`渠道名称已存在`)
    }

    const countByCode = await this.prisma.channel.count({
      where: {
        code: data.code,
        id: {
          not: id
        }
      }
    })

    if (countByCode > 0) {
      throw new BadRequestException(`渠道码已存在`)
    }

    const countByChannelAdminUser = await this.prisma.channelAdminUser.count({
      where: {
        id: {
          not: data.channelUserId
        },
        username: data.username
      }
    })
    if (countByChannelAdminUser) {
      throw new ForbiddenException('该管理账号已存在')
    }

    if (data.couponId) {
      const coupon = await this.prisma.coupons.findUnique({
        where: {
          id: data.couponId
        }
      })

      if (!coupon) {
        throw new BadRequestException(`优惠券不存在`)
      }
    }

    await this.prisma.channel.update({
      where: {
        id
      },
      data: {
        name: data.name,
        code: data.code,
        couponId: data.couponId
      }
    })

    await this.prisma.channelAdminUser.update({
      where: {
        id: data.channelUserId
      },
      data: {
        username: data.username
      }
    })
  }

  async updateChannelStatus(id: number, data: ChannelStatusRequestDTO) {
    const channel = await this.prisma.channel.findUnique({
      where: {
        id
      }
    })

    if (!channel) {
      throw new BadRequestException(`渠道不存在`)
    }

    await this.prisma.channel.update({
      where: {
        id
      },
      data: {
        status: data.status
      }
    })
  }

  /**
   * 更新密码
   */
  async updateChannelUserPassword(id: number, data: ChannelPasswordUpdateRequestDTO) {
    const channel = await this.prisma.channel.findUnique({
      where: {
        id
      },
      include: {
        ChannelAdminUser: true
      }
    })

    if (!channel) {
      throw new BadRequestException(`渠道不存在`)
    }

    const channelAdminUser = channel.ChannelAdminUser.find((item) => item.id === data.channelUserId)

    if (!channelAdminUser) {
      throw new BadRequestException(`渠道用户不存在`)
    }

    const result = this.validatePassword(data.password)

    if (!result) {
      throw new ForbiddenException('密码至少包含一个大写字母、一个小写字母、一位数字和一个特殊字符')
    }

    const { salt, hash } = this.hashPassword(data.password)
    await this.prisma.channelAdminUser.update({
      where: {
        id: channelAdminUser.id
      },
      data: {
        salt,
        password: hash
      }
    })
  }

  async compensateForDataLoss() {
    const channelList = await this.prisma.channel.findMany({
      include: {
        ChannelAdminUser: true
      }
    })

    const unChannelAdminUserList = channelList.filter(
      (channel) => channel.ChannelAdminUser.length === 0
    )

    for (let j = 0; j < unChannelAdminUserList.length; j++) {
      const username = `channel${j}`
      const { salt, hash } = this.hashPassword('123456')
      await this.prisma.channelAdminUser.create({
        data: {
          username,
          password: hash,
          channelId: unChannelAdminUserList[j].id,
          salt,
          role: 0
        }
      })
    }
  }
}
