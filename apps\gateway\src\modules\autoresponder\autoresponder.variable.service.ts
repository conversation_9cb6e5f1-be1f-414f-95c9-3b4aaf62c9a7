import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { PrismaService } from '@qdy/mysql'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { FastifyRequest } from 'fastify'
import { AutoresponderVariable } from './autoresponder.dto'
import { AutoresponderVariableKey } from './constant'

@Injectable()
export class AutoresponderVariableService {
  logger = new Logger('AutoresponderVariableService')

  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getAll({ name, page, size }: { name: string; page: number; size: number }) {
    const teamMember = await this.checkTeamMember()

    const where: Parameters<typeof this.prisma.variable.findMany>[0]['where'] = {
      teamId: teamMember.teamId,
      name: {
        contains: name
      }
    }

    if (!name) {
      delete where.name
    }

    const [variables, total] = await Promise.all([
      this.prisma.variable.findMany({
        orderBy: {
          id: 'desc'
        },
        where,
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.variable.count({
        where
      })
    ])

    return {
      total,
      page,
      size,
      data: variables
    }
  }

  async create(item: any) {
    const teamMember = await this.checkTeamMember()

    const res = await this.prisma.variable.create({
      data: {
        ...item,
        teamId: teamMember.teamId
      }
    })

    this.cacheManager.store.client.hset(AutoresponderVariableKey, res.id, JSON.stringify(res))
  }

  async delete(id: number) {
    const teamMember = await this.checkTeamMember()
    try {
      const autoresponder = await this.prisma.autoresponder.findMany({
        where: {
          teamId: teamMember.teamId,
          contents: {
            path: '$[*].texts[*].variableId',
            array_contains: id
          }
        }
      })

      if (autoresponder.length > 0) {
        throw new ForbiddenException('该变量在策略中已使用，请先到策略中解除后再删除')
      }

      await this.prisma.variable.delete({
        where: {
          teamId: teamMember.teamId,
          id
        }
      })

      this.cacheManager.store.client.hdel(AutoresponderVariableKey, `${id}`)
    } catch (e) {
      this.logger.error(e)
      throw new ForbiddenException(e.message)
    }
  }

  async update(id: number, data: AutoresponderVariable) {
    const teamMember = await this.checkTeamMember()
    const res = await this.prisma.variable.update({
      where: {
        teamId: teamMember.teamId,
        id
      },
      data
    })

    this.cacheManager.store.client.hset(AutoresponderVariableKey, res.id, JSON.stringify(res))
  }

  async checkTeamMember() {
    const { user } = this.request
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('用户未加入团队')
    }

    return teamMember
  }
}
