import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Inject, Logger } from '@nestjs/common'
import {
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect
} from '@nestjs/websockets'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Server, Socket } from 'socket.io'
import { genSocketRedisKey } from '@qdy/utils'
import { type User } from '@qdy/mysql'

@WebSocketGateway(3002, {
  transports: ['websocket'],
  allowUpgrades: false,
  pingTimeout: 10000,
  pingInterval: 8000,
  cors: {
    origin: '*',
    credentials: true
  }
})
export class EventsGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  logger = new Logger('EventsGateway')

  /**
   * socket服务实例
   */
  @WebSocketServer()
  server: Server

  /**
   * 构造函数
   * @param cacheManager
   */
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>) {}

  /**
   * 初始化
   */
  afterInit() {
    this.logger.log('Init')
  }

  /**
   * 处理断开连接
   * @param socket
   */
  async handleDisconnect(socket: Socket) {
    const { authorization } = socket.handshake.query as { authorization: string }
    if (!authorization) {
      return
    }
    const user = await this.cacheManager.get<User>(authorization)

    if (user) {
      this.cacheManager.store.client.hdel(
        genSocketRedisKey(socket.handshake.query.authorization as string),
        socket.id
      )
      const platformAccountIdMap =
        (await this.cacheManager.store.client.hgetall(genSocketRedisKey(authorization))) ?? {}

      const openIds = Object.keys(platformAccountIdMap)

      for (let i = 0; i < openIds.length; i++) {
        const openId = openIds[i]
        try {
          const oldSocketId = await this.cacheManager.store.client.hget(
            genSocketRedisKey(openId),
            socket.id
          )
          if (oldSocketId) {
            await this.cacheManager.store.client.hdel(genSocketRedisKey(openId), socket.id)
          }
        } catch (err) {
          this.logger.debug('删除错误', err)
        }
      }

      // this.cacheManager.del(genSocketRedisKey(user.id))
    }
  }

  /**
   * 测试socket连接
   * @returns
   */
  @SubscribeMessage('each')
  doStuff() {
    return { event: 'each' }
  }

  /**
   * 发送消息
   * @param socketId
   * @param data
   * @returns
   */
  async send(list: Record<string, unknown>[]) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      const { socketId, data } = item
      if (socketId && typeof socketId === 'string') {
        this.server.to(socketId).emit('messages', data)
      }

      this.logger.debug(`send to ${socketId} ${JSON.stringify(data)}`)
    }
  }

  /**
   * 处理连接
   * @param socket
   * @returns
   */
  async handleConnection(socket: Socket) {
    const { authorization } = socket.handshake.query as { authorization: string }

    if (authorization) {
      const user = await this.cacheManager.get<User>(authorization)
      if (!user) {
        socket.disconnect()
        return
      }

      const socketKey = `${user.id}${socket.handshake.query['device-type'] || ''}`
      const oldSocketId = await this.cacheManager.get<string>(genSocketRedisKey(socketKey))
      this.logger.log(`user ${user.id} old socketId ${socket.id}`)
      await this.cacheManager.set(genSocketRedisKey(socketKey), socket.id, 0)

      const platformAccountIdMap = await this.cacheManager.store.client.hgetall(
        genSocketRedisKey(authorization)
      )

      const tasks: Promise<number>[] = []
      const deleteTasks: Promise<number>[] = []
      const openIds = Object.keys(platformAccountIdMap)
      for (let i = 0; i < openIds.length; i++) {
        const openId = openIds[i]
        const platformAccountId = platformAccountIdMap[openId]

        if (oldSocketId) {
          this.logger.debug(openId)
          deleteTasks.push(
            this.cacheManager.store.client.hdel(genSocketRedisKey(openId), oldSocketId)
          )
        }

        tasks.push(
          this.cacheManager.store.client.hset(
            genSocketRedisKey(openId),
            socket.id,
            platformAccountId
          )
        )
      }
      await Promise.all(deleteTasks)
      await Promise.all(tasks)
      return
    }

    socket.disconnect()
  }
}
