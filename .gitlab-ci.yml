variables:
  DOCKER_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_IMAGE_NAME}'
  DOCKER_SOCKET_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_SOCKET_IMAGE_NAME}'
  DOCKER_ADMIN_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_ADMIN_IMAGE_NAME}'
  DOCKER_CHANNEL_ADMIN_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_CHANNEL_ADMIN_IMAGE_NAME}'
  CI_ADMIN_NAME: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_ADMIN_NAME}'
  DOCKER_TRANSFER_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_TRANSFER_IMAGE_NAME}'


stages:
  - build-gateway-dev
  - deploy-gateway-dev

  - build-admin-dev
  - deploy-admin-dev

  - build-socket-dev
  - deploy-socket-dev

  - build-channel-admin-dev
  - deploy-channel-admin-dev

  - build-transfer-dev
  - deploy-transfer-dev

  - build-gateway-test
  - deploy-gateway-test

  - build-socket-test
  - deploy-socket-test

  - build-admin-test
  - deploy-admin-test

  - build-channel-admin-test
  - deploy-channel-admin-test

  - build-transfer-test
  - deploy-transfer-test

  - build-gateway-prod
  - deploy-gateway-prod

  - build-admin-prod
  - deploy-admin-prod

  - build-socket-prod
  - deploy-socket-prod

  - build-channel-admin-prod
  - deploy-channel-admin-prod

  - build-transfer-prod
  - deploy-transfer-prod

development:build-gateway:
  image: docker:stable
  stage: build-gateway-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/gateway/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - main
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/gateway/Dockerfile" -t $DOCKER_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev --build-arg DATABASE_URL_MYSQL=$(cat .env.dev | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_IMAGE:$TAG_NAME
      docker tag $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME
      docker push $DOCKER_IMAGE:$ENV_NAME
      docker rmi $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME --force

# -------------------------------admin-dev-----------------------------
development:build-admin:
  image: docker:stable
  stage: build-admin-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/admin/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - main
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/admin/Dockerfile" -t $DOCKER_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev --build-arg DATABASE_URL_MYSQL=$(cat .env.dev | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_ADMIN_IMAGE:$TAG_NAME
      docker tag $DOCKER_ADMIN_IMAGE:$TAG_NAME $DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker push $DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker rmi $DOCKER_ADMIN_IMAGE:$TAG_NAME $DOCKER_ADMIN_IMAGE:$ENV_NAME --force

# --------------------------socket-----------------------#

development:build-socket:
  image: docker:stable
  stage: build-socket-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/socket/**/*
      - app/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - main
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/socket/Dockerfile" -t $DOCKER_SOCKET_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev
      docker push $DOCKER_SOCKET_IMAGE:$TAG_NAME
      docker tag $DOCKER_SOCKET_IMAGE:$TAG_NAME $DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker push $DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker rmi $DOCKER_SOCKET_IMAGE:$TAG_NAME $DOCKER_SOCKET_IMAGE:$ENV_NAME --force

# -------------------------------channel-admin-dev-----------------------------
development:build-channel-admin:
  image: docker:stable
  stage: build-channel-admin-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/channel_admin/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - main
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/channel_admin/Dockerfile" -t $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev --build-arg DATABASE_URL_MYSQL=$(cat .env.dev | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME
      docker tag $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME
      docker push $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME
      docker rmi $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME --force

# -------------------------- test -------------------------------

test:build-gateway:
  image: docker:stable
  stage: build-gateway-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/gateway/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/gateway/Dockerfile" -t $DOCKER_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test --build-arg DATABASE_URL_MYSQL=$(cat .env.test | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_IMAGE:$TAG_NAME
      docker tag $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME
      docker push $DOCKER_IMAGE:$ENV_NAME
      docker rmi $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME --force

    # ------------------------admin-test------------------------#
test:build-admin:
  image: docker:stable
  stage: build-admin-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/admin/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/admin/Dockerfile" -t $DOCKER_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test --build-arg DATABASE_URL_MYSQL=$(cat .env.test | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_ADMIN_IMAGE:$TAG_NAME
      docker tag $DOCKER_ADMIN_IMAGE:$TAG_NAME $DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker push $DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker rmi $DOCKER_ADMIN_IMAGE:$TAG_NAME $DOCKER_ADMIN_IMAGE:$ENV_NAME --force

# ----------------------------test-transfer---------------------------#
test:build-transfer:
  image: docker:stable
  stage: build-transfer-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/transfer/**/*
      - app/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/transfer/Dockerfile" -t $DOCKER_TRANSFER_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test --build-arg DATABASE_URL_MYSQL=$(cat .env.test | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_TRANSFER_IMAGE:$TAG_NAME
      docker tag $DOCKER_TRANSFER_IMAGE:$TAG_NAME $DOCKER_TRANSFER_IMAGE:$ENV_NAME
      docker push $DOCKER_TRANSFER_IMAGE:$ENV_NAME
      docker rmi $DOCKER_TRANSFER_IMAGE:$TAG_NAME $DOCKER_TRANSFER_IMAGE:$ENV_NAME --force


# --------------------------socket-----------------------#

test:build-socket:
  image: docker:stable
  stage: build-socket-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/socket/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/socket/Dockerfile" -t $DOCKER_SOCKET_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test
      docker push $DOCKER_SOCKET_IMAGE:$TAG_NAME
      docker tag $DOCKER_SOCKET_IMAGE:$TAG_NAME $DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker push $DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker rmi $DOCKER_SOCKET_IMAGE:$TAG_NAME $DOCKER_SOCKET_IMAGE:$ENV_NAME --force

# ----------------------------test-channel-admin---------------------------#
test:build-channel-admin:
  image: docker:stable
  stage: build-channel-admin-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/channel_admin/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/channel_admin/Dockerfile" -t $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test --build-arg DATABASE_URL_MYSQL=$(cat .env.test | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME
      docker tag $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME
      docker push $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME
      docker rmi $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME --force

# ------------------------------prod-gateway--------------------------------

prod:build-gateway:
  image: docker:stable
  stage: build-gateway-prod
  variables:
    ENV_NAME: prod
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/gateway/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.prod
    refs:
      - production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/gateway/Dockerfile" -t $DOCKER_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=prod --build-arg DATABASE_URL_MYSQL=$(cat .env.prod | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_IMAGE:$TAG_NAME
      docker tag $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME
      docker push $DOCKER_IMAGE:$ENV_NAME
      docker rmi $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME --force

# ----------------------------prod-admin---------------------------#
prod:build-admin:
  image: docker:stable
  stage: build-admin-prod
  variables:
    ENV_NAME: prod
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/admin/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.prod
    refs:
      - production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/admin/Dockerfile" -t $DOCKER_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=prod --build-arg DATABASE_URL_MYSQL=$(cat .env.prod | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_ADMIN_IMAGE:$TAG_NAME
      docker tag $DOCKER_ADMIN_IMAGE:$TAG_NAME $DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker push $DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker rmi $DOCKER_ADMIN_IMAGE:$TAG_NAME $DOCKER_ADMIN_IMAGE:$ENV_NAME --force

# ----------------------------prod-socket---------------------------#

prod:build-socket:
  image: docker:stable
  stage: build-socket-prod
  variables:
    ENV_NAME: prod
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/socket/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.prod
    refs:
      - production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/socket/Dockerfile" -t $DOCKER_SOCKET_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=prod
      docker push $DOCKER_SOCKET_IMAGE:$TAG_NAME
      docker tag $DOCKER_SOCKET_IMAGE:$TAG_NAME $DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker push $DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker rmi $DOCKER_SOCKET_IMAGE:$TAG_NAME $DOCKER_SOCKET_IMAGE:$ENV_NAME --force

# ----------------------------prod-channel-admin---------------------------#
prod:build-channel-admin:
  image: docker:stable
  stage: build-channel-admin-prod
  variables:
    ENV_NAME: prod
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/channel_admin/**/*
      - apps/common/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.prod
    refs:
      - production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/channel_admin/Dockerfile" -t $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=prod --build-arg DATABASE_URL_MYSQL=$(cat .env.prod | grep 'DATABASE_URL_MYSQL' | cut -d '=' -f2)
      docker push $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME
      docker tag $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME
      docker push $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME
      docker rmi $DOCKER_CHANNEL_ADMIN_IMAGE:$TAG_NAME $DOCKER_CHANNEL_ADMIN_IMAGE:$ENV_NAME --force