import { Inject, Injectable, Logger } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { PrismaService } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import {
  login,
  postLoginCount,
  postLoginRecord,
  postOfflineCount,
  postWxList
} from './external.wechat'

@Injectable()
export class WechatService {
  logger = new Logger('WechatService')

  username = 'admin'

  password = 'Yixiaoer888999'

  cacheKey = 'wx_system_login_token'

  cacheTime = 1000 * 60 * 25 // 25 minutes

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async getWechatList(querys: {
    wxid: string
    size: number
    page: number
    startTime: string
    endTime: string
  }) {
    try {
      let wxToken = (await this.cacheManager.get(this.cacheKey)) as string

      if (!wxToken) {
        const wxLogin = await login({
          username: this.username,
          password: this.password
        })
        await this.cacheManager.set(this.cacheKey, wxLogin.data.token, this.cacheTime)
        wxToken = wxLogin.data.token
      }

      const regions = [
        {
          regionId: '110000',
          regionName: '北京市'
        },
        {
          regionId: '310000',
          regionName: '上海市'
        },
        {
          regionId: '320000',
          regionName: '江苏省'
        },
        {
          regionId: '440000',
          regionName: '广东省'
        },
        {
          regionId: '500000',
          regionName: '重庆市'
        },
        {
          regionId: '510000',
          regionName: '四川省'
        },
        {
          regionId: '330000',
          regionName: '浙江省'
        },
        {
          regionId: '340000',
          regionName: '安徽省'
        },
        {
          regionId: '130000',
          regionName: '河北省'
        },
        {
          regionId: '430000',
          regionName: '湖南省'
        },
        {
          regionId: '420000',
          regionName: '湖北省'
        },
        {
          regionId: '120000',
          regionName: '天津市'
        },
        {
          regionId: '610000',
          regionName: '陕西省'
        },
        {
          regionId: '410000',
          regionName: '河南省'
        }
      ]

      const { data } = await postWxList({
        token: wxToken,
        wxid: querys.wxid,
        page: querys.page,
        size: querys.size,
        status: '1',
        startTime: querys.startTime,
        endTime: querys.endTime
      })

      return {
        page: querys.page,
        size: querys.size,
        total: data.total,
        data: data.rows.map((item) => {
          const region = regions.find((region) => region.regionId === item.regionId)
          return {
            ...item,
            regionName: region ? region.regionName : ''
          }
        })
      }
    } catch (error) {
      this.logger.error('获取视频号列表失败', error)
      throw new Error(error.message)
    }
  }

  async getWechatOnlineCount(startDate: string) {
    try {
      let wxToken = (await this.cacheManager.get(this.cacheKey)) as string

      if (!wxToken) {
        const wxLogin = await login({
          username: this.username,
          password: this.password
        })
        await this.cacheManager.set(this.cacheKey, wxLogin.data.token, this.cacheTime)
        wxToken = wxLogin.data.token
      }

      const { data } = await postLoginCount({
        token: wxToken,
        startDate
      })

      if (data.ret !== 200) {
        this.logger.error('获取视频号在线账号数量失败', data)
        throw new Error(data.msg)
      }

      return {
        onlineCount: data.data.onlineCount,
        statData: data.data.statData
      }
    } catch (error) {
      this.logger.error('获取视频号在线账号数量失败', error)
      throw new Error(error.message)
    }
  }

  async getWechatOfflineCount(startDate: string) {
    try {
      let wxToken = (await this.cacheManager.get(this.cacheKey)) as string

      if (!wxToken) {
        const wxLogin = await login({
          username: this.username,
          password: this.password
        })
        await this.cacheManager.set(this.cacheKey, wxLogin.data.token, this.cacheTime)
        wxToken = wxLogin.data.token
      }

      const { data } = await postOfflineCount({
        token: wxToken,
        startDate
      })

      if (data.ret !== 200) {
        this.logger.error('获取视频号在线账号数量失败', data)
        throw new Error(data.msg)
      }

      return {
        offlineCount: data.data.offlineCount,
        statData: data.data.statData
      }
    } catch (error) {
      this.logger.error('获取视频号离线账号数量失败', error)
      throw new Error(error.message)
    }
  }

  async getWechatLoginRecord(querys: { wxid: string; size: number; page: number }) {
    try {
      let wxToken = (await this.cacheManager.get(this.cacheKey)) as string

      if (!wxToken) {
        const wxLogin = await login({
          username: this.username,
          password: this.password
        })
        await this.cacheManager.set(this.cacheKey, wxLogin.data.token, this.cacheTime)
        wxToken = wxLogin.data.token
      }

      const { data } = await postLoginRecord({
        token: wxToken,
        wxid: querys.wxid,
        page: querys.page,
        size: querys.size
      })

      return {
        page: querys.page,
        size: querys.size,
        total: data.total,
        data: data.rows
      }
    } catch (error) {
      this.logger.error('获取视频号登录记录失败', error)
      throw new Error(error.message)
    }
  }
}
