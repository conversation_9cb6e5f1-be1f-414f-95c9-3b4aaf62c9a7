import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class WechatMessagesEntity {
  @Prop({
    type: String,
    required: false
  })
  typeName: string

  @Prop({
    type: String,
    required: false
  })
  appid: string

  @Prop({
    type: String,
    required: false,
    index: true
  })
  wxid: string

  @Prop({
    type: String,
    required: false
  })
  msgId: string

  @Prop({
    type: String,
    required: false,
    index: true
  })
  fromUserName: string

  @Prop({
    type: String,
    required: false,
    index: true
  })
  toUserName: string

  @Prop({
    type: Number,
    required: false
  })
  msgType: number

  @Prop({
    type: String,
    required: false
  })
  content: string

  @Prop({
    type: Number,
    required: false
  })
  status: number

  @Prop({
    type: Number,
    required: false
  })
  imgStatus: number

  @Prop({
    type: Number,
    required: false,
    index: true
  })
  createTime: number

  @Prop({
    type: String,
    required: false
  })
  newMsgId: string

  @Prop({
    type: String,
    required: false
  })
  msgSessionId: string

  /**
   * @description 发送方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  fromAvatar: string

  /**
   * @description 发送方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  fromName: string

  /**
   * @description 目标方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  toAvatar: string

  /**
   * @description 目标方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  toName: string

  @Prop({
    type: Number
  })
  isSender: number

  @Prop({
    type: String,
    default: 'wechat'
  })
  platformType: string

  @Prop({
    type: String,
    index: true
  })
  sessionId: string

  @Prop({
    type: String
  })
  imageUrl: string

  @Prop({
    type: String
  })
  videoUrl: string

  /**
   * @description 是否自动发送(默认手动发送)
   */
  @Prop({
    type: Number,
    default: 0
  })
  isAuto: number
}

export const WechatMessagesSchema: ModelDefinition = {
  name: WechatMessagesEntity.name,
  schema: SchemaFactory.createForClass(WechatMessagesEntity).index({
    wxid: 1,
    createTime: -1,
    sessionId: 1
  })
}

export const WechatMessagesMongoose = MongooseModule.forFeature([WechatMessagesSchema])
