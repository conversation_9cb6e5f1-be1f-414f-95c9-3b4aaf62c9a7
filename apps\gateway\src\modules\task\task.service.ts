import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { <PERSON>ron } from '@nestjs/schedule'
import { PlatformAccount, PrismaService } from '@qdy/mysql'
import { postAuthorizeAccountRefresh, postAuthorizeAccountRefreshAcc } from '../account/external'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { Queue, Worker } from 'bullmq'
import { TeamMemberStatus } from '../team/team.dto'
import { AccountAccountsStatus } from '../account/account.dto'
import { wechatLogout } from '../account/external.wechat'
import { getAuthorizeAccountRefresh } from '../account/external.kuaishou'
import { postAccountTokenInfo } from '../account/external.weibo'
import { Platform } from '@qdy/utils'
import { postXiaohongshuAuthorizeAccountRefresh } from '../account/external.xiaohongshu'
import { OrderRecord } from '../order/order.dto'
import dayjs from 'dayjs'
import { PlatformAccountManageService, TlsManageService } from '@qdy/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'

type PlatformAccountCronType = PlatformAccount & {
  tokenTime: string
  refreshTime: string
  douyinClientKey: string
}

@Injectable()
export class TasksService implements OnModuleInit {
  private readonly logger = new Logger(TasksService.name)

  taskQueue: Queue

  taskWorker: Worker

  refreshTokenTaskQueue: Queue

  refreshTokenTaskWorker: Worker

  xhsRefreshTokenTaskQueue: Queue

  xhsRefreshTokenTaskWorker: Worker

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly tlsManageService: TlsManageService,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  async onModuleInit() {
    this.taskQueue = new Queue('account-init', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'account-init',
      async (job) => {
        const { type, data } = job.data
        this.logger.log(`Running task ${type} ${job.id}`)
        switch (type) {
          case 'platformAccount':
            await this.onUpdatePlatformAccount(data as PlatformAccountCronType)
            break
        }
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.refreshTokenTaskQueue = new Queue('refresh-account-token-update', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.refreshTokenTaskWorker = new Worker(
      'refresh-account-token-update',
      async (job) => {
        const { type, data } = job.data
        this.logger.log(`Running task ${type} ${job.id}`)
        await this.onRefreshToken(data as PlatformAccountCronType)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.xhsRefreshTokenTaskQueue = new Queue('xhs-refresh-account-token-update', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.xhsRefreshTokenTaskWorker = new Worker(
      'xhs-refresh-account-token-update',
      async (job) => {
        const { type, data } = job.data

        this.logger.log(`Running task ${type} ${job.id}`)
        await this.onXhsRefreshToken(data as PlatformAccountCronType)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // this.onRefreshTokenCron()

    this.logger.log('AccountInitService init')
  }

  @Cron('0 0 9 * * *', {
    name: 'dailyReport-n',
    timeZone: 'Asia/Shanghai'
  })
  async taskN() {
    this.onUpdateDouyinPlatformAccountCron()
  }

  @Cron('0 0 23 * * *', {
    name: 'dailyReport-t',
    timeZone: 'Asia/Shanghai'
  })
  async taskT() {
    this.onUpdateDouyinPlatformAccountCron()
  }

  async onUpdateDouyinPlatformAccountCron() {
    this.logger.debug('Called every day at 5 AM')
    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        platform: Platform.Douyin
      },
      include: {
        Team: true
      }
    })

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]

      this.taskQueue.add(
        'account-init',
        {
          type: 'platformAccount',
          data: {
            ...platformAccount,
            tokenTime: platformAccount.tokenTime.getTime(),
            refreshTime: platformAccount.refreshTime.getTime(),
            douyinClientKey: platformAccount.Team.douyinClientKey ?? ''
          }
        },
        {
          delay: 5 * 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `platformAccount-init-move-${platformAccount.id}`
        }
      )
    }

    this.logger.log('AccountInitService 刷新初始化')
  }

  async onUpdatePlatformAccount(platformAccount: PlatformAccountCronType) {
    const { clientKey, secondClientKey } = this.configService.get<RootConfigMap['app']>('app')

    if (
      platformAccount.platform !== Platform.Douyin ||
      !platformAccount.expiresIn ||
      !platformAccount.refreshExpiresIn
    ) {
      return
    }

    const platformAccountTokenTime = new Date(platformAccount.tokenTime).getTime()
    const platformAccountRefreshTime = new Date(platformAccount.refreshTime).getTime()

    const timeX = platformAccount.expiresIn * 1000 + platformAccountTokenTime - Date.now()

    const expiresTime = platformAccount.refreshExpiresIn * 1000 + platformAccountRefreshTime

    const expiresX = expiresTime - Date.now()

    const refreshClientKey =
      platformAccount.douyinClientKey === secondClientKey ? secondClientKey : clientKey

    if (expiresX < 48 * 60 * 60 * 1000) {
      if (expiresX > 0) {
        try {
          const res = await postAuthorizeAccountRefresh({
            clientKey: refreshClientKey,
            refreshToken: platformAccount.refreshToken
          })

          const updateValue = {
            refreshToken: res.refreshToken,
            refreshExpiresIn: res.refreshExpiresIn,
            refreshTime: new Date()
          }

          platformAccount.refreshToken = updateValue.refreshToken

          const updateAccount = await this.prisma.platformAccount.update({
            where: { id: platformAccount.id },
            data: updateValue
          })

          await this.platformAccountManageService.updatePlatformAccountRedisInfo(updateAccount)

          this.logger.debug('刷新成功 RefreshToken', updateValue.refreshTime)
        } catch (error) {
          this.logger.error('刷新失败 RefreshToken', error)
        }
      } else if (platformAccount.refreshExpiresIn > 1) {
        await this.prisma.platformAccount.update({
          where: { id: platformAccount.id },
          data: {
            refreshExpiresIn: 1
          }
        })
      }
    }

    if (timeX < 36 * 60 * 60 * 1000 && Date.now() < expiresTime) {
      try {
        const res = await postAuthorizeAccountRefreshAcc({
          clientKey: refreshClientKey,
          refreshToken: platformAccount.refreshToken
        })

        const updateValue = {
          refreshToken: res.refreshToken,
          expiresIn: res.expiresIn,
          refreshExpiresIn: res.refreshExpiresIn,
          accessToken: res.accessToken,
          tokenTime: new Date()
        }

        const updateAccount = await this.prisma.platformAccount.update({
          where: { id: platformAccount.id },
          data: updateValue
        })

        await this.platformAccountManageService.updatePlatformAccountRedisInfo(updateAccount)

        this.logger.debug('刷新成功 accessToken')
      } catch (error) {
        this.logger.error('刷新失败 accessToken', error)

        await this.prisma.platformAccount.update({
          where: { id: platformAccount.id },
          data: {
            expiresIn: 1,
            refreshExpiresIn: 1
          }
        })
      }
    }

    this.logger.log(`AccountInitService 刷新 ${platformAccount.id}`)
  }

  @Cron('0 15 1 * * *', {
    name: 'TeamVipCheck-t',
    timeZone: 'Asia/Shanghai'
  })
  async onTeamVipCheckCron() {
    const now = dayjs().tz('Asia/Shanghai')

    const startDay = now.subtract(2, 'day').format('YYYY-MM-DD')
    const endDay = now.subtract(1, 'day').format('YYYY-MM-DD')

    const startOfYesterday = new Date(startDay + 'T16:00:00') // 获取昨天的开始时间
    const endOfYesterday = new Date(endDay + 'T16:00:00') // 获取昨天的结束时间，0时区的时间

    this.tlsManageService.putLogs({
      logData: JSON.stringify({ startOfYesterday, endOfYesterday }),
      logLevel: 'info',
      requestUri: 'onTeamVipCheckCron',
      jobStatus: 'onTeamVipCheckCron'
    })

    const teams = await this.prisma.team.findMany({
      where: {
        vip: {
          expirationTime: {
            gte: startOfYesterday,
            lte: endOfYesterday
          }
        }
      },
      include: {
        vip: true
      }
    })

    const tasks = []
    const orderRecordTask: Promise<void>[] = []
    const platformAccountTasks: Promise<PlatformAccount[]>[] = []

    for (let i = 0; i < teams.length; i++) {
      const team = teams[i]

      orderRecordTask.push(
        new Promise<void>((resolve) => {
          this.prisma.orderRecord
            .aggregate({
              where: {
                teamId: team.id
              },
              _sum: {
                price: true
              }
            })
            .then((res) => {
              this.prisma.orderRecord
                .create({
                  data: {
                    teamId: team.id,
                    price: -res._sum.price,
                    type: OrderRecord.Expire
                  }
                })
                .then(() => {
                  resolve()
                })
            })
        })
      )

      tasks.push(
        this.prisma.teamMember.updateMany({
          where: {
            teamId: team.id,
            NOT: {
              userId: team.ownerId
            }
          },
          data: {
            status: TeamMemberStatus.Disable
          }
        })
      )

      platformAccountTasks.push(
        this.prisma.platformAccount.findMany({
          where: {
            teamId: team.id
          }
        })
      )
      // vip里相关的条数限制更改为初始值
      const limitData = await this.prisma.systemDosage.findFirst()
      await this.prisma.vip.update({
        where: {
          teamId: team.id
        },
        data: {
          messageLimit: limitData?.standardMessageLimit ?? 0,
          teamMemberNumberLimit: limitData?.standardTeamMemberNumberLimit ?? 0,
          platformAccountNumberLimit: limitData?.standardPlatformAccountNumberLimit ?? 0
        }
      })
    }

    await Promise.all(tasks)
    await Promise.all(orderRecordTask)
    const teamPlatformAccounts = await Promise.all(platformAccountTasks)
    for (let i = 0; i < teamPlatformAccounts.length; i++) {
      const platformAccounts = teamPlatformAccounts[i]
      for (let j = 0; j < platformAccounts.length; j++) {
        const platformAccount = platformAccounts[j]

        const updateAccount = await this.prisma.platformAccount.update({
          where: { id: platformAccount.id },
          data: {
            status: AccountAccountsStatus.Disable,
            expiresIn: 0
          }
        })

        await this.platformAccountManageService.updatePlatformAccountRedisInfo(updateAccount)

        if (platformAccount.platform === Platform.Wechat) {
          await wechatLogout({
            appId: platformAccount.appId,
            isNew: platformAccount.isNew
          })
        }
      }
    }

    this.logger.log('AccountInitService vip 检查')
  }

  @Cron('0 30 23 * * *', {
    name: 'Refresh-t',
    timeZone: 'Asia/Shanghai'
  })
  async onRefreshTokenCron() {
    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        platform: Platform.Kuaishou
      }
    })

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]

      this.refreshTokenTaskQueue.add(
        'refresh-account-token-update',
        {
          type: 'platformAccount',
          data: {
            ...platformAccount,
            tokenTime: platformAccount.tokenTime.getTime(),
            refreshTime: platformAccount.refreshTime.getTime()
          }
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `account-update-move-${platformAccount.platform}-${platformAccount.id}`
        }
      )
    }
  }

  async onRefreshToken(platformAccount: PlatformAccountCronType) {
    const { kuaishouClientKey, kuaishouClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')
    try {
      if (
        platformAccount.refreshExpiresIn * 1000 + new Date(platformAccount.refreshTime).getTime() >
        Date.now()
      ) {
        let refreshResult: {
          expiresIn: number
          accessToken: string
          refreshToken: string
          refreshExpiresIn: number
        }

        if (platformAccount.platform === Platform.Kuaishou) {
          refreshResult = await getAuthorizeAccountRefresh({
            clientKey: kuaishouClientKey,
            clientSecret: kuaishouClientSecret,
            refreshToken: platformAccount.refreshToken
          })
        }

        const tokenTime = new Date()

        const updateAccount = await this.prisma.platformAccount.update({
          where: {
            id: platformAccount.id
          },
          data: {
            accessToken: refreshResult.accessToken,
            expiresIn: refreshResult.expiresIn,
            refreshToken: refreshResult.refreshToken,
            refreshExpiresIn: refreshResult.refreshExpiresIn,
            tokenTime,
            refreshTime: tokenTime
          }
        })

        await this.platformAccountManageService.updatePlatformAccountRedisInfo(updateAccount)
      }
    } catch (error) {
      this.tlsManageService.putLogs({
        logData: JSON.stringify({ ...platformAccount, error: error.message }),
        logLevel: 'info',
        requestUri: 'taskService',
        jobStatus: 'kuaishouRefrshToken'
      })

      if (
        new Date().getTime() - new Date(platformAccount.createTime).getTime() <
          30 * 24 * 60 * 60 * 1000 &&
        platformAccount.platform === Platform.Xiaohongshu
      ) {
        this.logger.log('Platform account creation time is less than 30 days, skipping update.')
        return
      }
      await this.prisma.platformAccount.update({
        where: { id: platformAccount.id },
        data: {
          expiresIn: 1,
          refreshExpiresIn: 1
        }
      })
    }
  }

  @Cron('0 0 8 * * *', {
    name: 'Refresh-xhs-n',
    timeZone: 'Asia/Shanghai'
  })
  async refreshXhsTokenN() {
    this.onXhsRefreshTokenCron()
  }

  @Cron('0 0 22 * * *', {
    name: 'Refresh-xhs-t',
    timeZone: 'Asia/Shanghai'
  })
  async refreshXhsTokenT() {
    this.onXhsRefreshTokenCron()
  }

  async onXhsRefreshTokenCron() {
    const lockKey = 'xhs-refresh-token-cron-lock'
    const lockValue = `${process.pid}-${Date.now()}`
    const lockTimeout = 5 * 60 * 1000 // 5分钟锁定时间

    try {
      // 尝试获取分布式锁
      const acquired = await this.tryAcquireDistributedLock(lockKey, lockValue, lockTimeout)

      if (!acquired) {
        this.tlsManageService.putLogs({
          logData: `小红书账号刷新任务已被其他实例执行，跳过`,
          logLevel: 'info',
          requestUri: 'taskService',
          jobStatus: 'xhsRefrshToken'
        })
        return
      }

      this.logger.log('获取到小红书刷新令牌任务锁，开始执行')

      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          platform: Platform.Xiaohongshu,
          status: AccountAccountsStatus.Normal,
          refreshExpiresIn: {
            gt: 100
          },
          parentOpenId: {
            equals: ''
          }
        }
      })

      this.tlsManageService.putLogs({
        logData: `小红书账号刷新任务开始执行，账号数量: ${platformAccounts.length} - ${JSON.stringify(platformAccounts.map((item) => item.openId))}`,
        logLevel: 'info',
        requestUri: 'taskService',
        jobStatus: 'xhsRefrshToken'
      })

      for (let i = 0; i < platformAccounts.length; i++) {
        const platformAccount = platformAccounts[i]

        this.xhsRefreshTokenTaskQueue.add(
          'xhs-refresh-account-token-update',
          {
            type: 'platformAccount',
            data: {
              ...platformAccount,
              tokenTime: platformAccount.tokenTime.getTime(),
              refreshTime: platformAccount.refreshTime.getTime()
            }
          },
          {
            delay: 5 * 60 * 1000, // 错开执行时间
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `xhs-account-update-move-${platformAccount.openId}`
          }
        )
      }

      this.logger.log(`小红书刷新令牌任务执行完成，处理了 ${platformAccounts.length} 个账号`)
    } catch (error) {
      this.logger.error('小红书刷新令牌任务执行失败', error)
      this.tlsManageService.putLogs({
        logData: `小红书刷新令牌任务执行失败: ${error.message}`,
        logLevel: 'error',
        requestUri: 'taskService',
        jobStatus: 'xhsRefrshToken'
      })
    } finally {
      // 释放锁
      await this.releaseDistributedLock(lockKey, lockValue)
    }
  }

  async onXhsRefreshToken(platformAccount: PlatformAccountCronType) {
    const { xiaohongshuClientKey, xiaohongshuClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')

    try {
      this.tlsManageService.putLogs({
        logData: JSON.stringify({
          platformAccount
        }),
        logLevel: 'info',
        requestUri: 'taskService',
        jobStatus: 'xhsRefrshToken'
      })

      const refreshResult = await postXiaohongshuAuthorizeAccountRefresh({
        clientKey: xiaohongshuClientKey,
        clientSecret: xiaohongshuClientSecret,
        refreshToken: platformAccount.refreshToken
      })

      this.tlsManageService.putLogs({
        logData: JSON.stringify({
          refreshResult
        }),
        logLevel: 'info',
        requestUri: 'taskService',
        jobStatus: 'xhsRefrshToken'
      })

      const tokenTime = new Date()

      const updateAccount = await this.prisma.platformAccount.update({
        where: {
          id: platformAccount.id
        },
        data: {
          accessToken: refreshResult.accessToken,
          expiresIn: refreshResult.expiresIn,
          refreshToken: refreshResult.refreshToken,
          refreshExpiresIn: refreshResult.refreshExpiresIn,
          tokenTime,
          refreshTime: tokenTime
        }
      })

      // 小红书子账号token更新
      await this.prisma.platformAccount.updateMany({
        where: {
          parentOpenId: platformAccount.openId,
          platform: Platform.Xiaohongshu,
          teamId: platformAccount.teamId
        },
        data: {
          accessToken: refreshResult.accessToken,
          expiresIn: refreshResult.expiresIn,
          refreshToken: refreshResult.refreshToken,
          refreshExpiresIn: refreshResult.refreshExpiresIn,
          tokenTime,
          refreshTime: tokenTime
        }
      })

      await this.platformAccountManageService.updatePlatformAccountRedisInfo(updateAccount)

      const childAccount = await this.prisma.platformAccount.findMany({
        where: {
          parentOpenId: platformAccount.openId,
          platform: Platform.Xiaohongshu,
          teamId: platformAccount.teamId
        }
      })

      for (let i = 0; i < childAccount.length; i++) {
        const child = childAccount[i]
        await this.platformAccountManageService.updatePlatformAccountRedisInfo(child)
      }
    } catch (error) {
      this.tlsManageService.putLogs({
        logData: JSON.stringify({ ...platformAccount, error: error.message }),
        logLevel: 'info',
        requestUri: 'taskService',
        jobStatus: 'xhsRefrshToken'
      })

      const updatePlatformAccount = await this.prisma.platformAccount.update({
        where: { id: platformAccount.id },
        data: {
          expiresIn: 1,
          refreshExpiresIn: 1
        }
      })

      await this.prisma.platformAccount.updateMany({
        where: {
          parentOpenId: platformAccount.openId,
          platform: Platform.Xiaohongshu
        },
        data: {
          expiresIn: 1,
          refreshExpiresIn: 1
        }
      })

      await this.platformAccountManageService.updatePlatformAccountRedisInfo(updatePlatformAccount)

      const childAccount = await this.prisma.platformAccount.findMany({
        where: {
          parentOpenId: platformAccount.openId,
          platform: Platform.Xiaohongshu,
          teamId: platformAccount.teamId
        }
      })

      for (let i = 0; i < childAccount.length; i++) {
        const child = childAccount[i]
        await this.platformAccountManageService.updatePlatformAccountRedisInfo(child)
      }

      this.logger.log(error.message)
    }
  }

  @Cron('0 0 23 * * *', {
    name: 'Check-Weibo-Token',
    timeZone: 'Asia/Shanghai'
  })
  async onCheckWeiboTokenCron() {
    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        platform: Platform.Weibo
      }
    })

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]

      try {
        if (platformAccount.expiresIn * 1000 + platformAccount.createTime.getTime() > Date.now()) {
          const refreshResult = await postAccountTokenInfo({
            accessToken: platformAccount.accessToken
          })

          if (refreshResult.expiresIn < 0) {
            const updateAccount = await this.prisma.platformAccount.update({
              where: {
                id: platformAccount.id
              },
              data: {
                expiresIn: 1,
                tokenTime: new Date()
              }
            })

            await this.platformAccountManageService.updatePlatformAccountRedisInfo(updateAccount)
          }
        }
      } catch (error) {
        const updateAccount = await this.prisma.platformAccount.update({
          where: { id: platformAccount.id },
          data: {
            expiresIn: 1,
            refreshExpiresIn: 1
          }
        })

        await this.platformAccountManageService.updatePlatformAccountRedisInfo(updateAccount)
        this.logger.log(error.message)
      }
    }

    this.logger.log('onCheckWeiboTokenCron vip 检查')
  }

  /**
   * 尝试获取分布式锁
   * @param lockKey 锁的键名
   * @param lockValue 锁的值（用于标识持有锁的实例）
   * @param timeout 锁的超时时间（毫秒）
   * @returns 是否成功获取锁
   */
  private async tryAcquireDistributedLock(
    lockKey: string,
    lockValue: string,
    timeout: number
  ): Promise<boolean> {
    try {
      // 使用 Redis SET 命令的 NX 和 PX 选项实现原子性锁获取
      // NX: 只在键不存在时设置
      // PX: 设置过期时间（毫秒）
      const redisStore = this.cacheManager.store as RedisStore
      const result = await redisStore.client.set(lockKey, lockValue, 'PX', timeout, 'NX')

      return result === 'OK'
    } catch (error) {
      this.logger.error(`获取分布式锁失败: ${lockKey}`, error)
      return false
    }
  }

  /**
   * 释放分布式锁
   * @param lockKey 锁的键名
   * @param lockValue 锁的值（用于验证锁的所有权）
   */
  private async releaseDistributedLock(lockKey: string, lockValue: string): Promise<void> {
    try {
      const redisStore = this.cacheManager.store as RedisStore

      // 使用 Lua 脚本确保原子性释放锁
      // 只有当锁的值匹配时才删除，防止误删其他实例的锁
      const luaScript = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("DEL", KEYS[1])
        else
          return 0
        end
      `

      const result = await redisStore.client.eval(luaScript, 1, lockKey, lockValue)

      if (result === 1) {
        this.logger.log(`成功释放分布式锁: ${lockKey}`)
      } else {
        this.logger.warn(`锁已过期或被其他实例持有: ${lockKey}`)
      }
    } catch (error) {
      this.logger.error(`释放分布式锁失败: ${lockKey}`, error)
    }
  }
}
