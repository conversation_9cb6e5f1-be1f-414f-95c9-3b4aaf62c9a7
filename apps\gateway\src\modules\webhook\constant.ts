import { Transport } from '@nestjs/microservices'
import { socketConfig } from 'packages/proto'

export enum WebhookEvents {
  /**
   * 验证 webhook
   */
  VerifyWebhook = 'verify_webhook',
  /**
   * 接收私信消息
   */
  IMReceiveMessage = 'im_receive_msg',
  /**
   * 发送私信消息
   */
  IMSendMessage = 'im_send_msg',
  /**
   * 群消息接收
   */
  IMGroupReceiveMessage = 'im_group_receive_msg',
  /**
   * 发送群消息
   */
  IMGroupSendMessage = 'im_group_send_msg',
  /**
   * 用户解除授权
   */
  Unauthorize = 'unauthorize',
  /**
   * 用户授权
   */
  Authorize = 'authorize',
  /**
   * 用户加群申请
   */
  EnterGroupAuditChange = 'enter_group_audit_change',
  /**
   * 用户加群成功
   */
  GroupFansEvent = 'group_fans_event',
  /**
   * 评论回复
   */
  CommentReply = 'item_comment_reply',

  /**
   * 合同授权
   */
  ContractAuthorize = 'contract_authorize',

  /**
   * 合同取消授权
   */
  ContractUnauthorize = 'contract_unauthorize',

  /**
   * 接收用户进入私信会话页事件，用户主动进入私信会话页触发
   */
  ImEnterDirectMessage = 'im_enter_direct_msg',

  /**
   * 视频点赞
   */
  NewVideoDigg = 'new_video_digg',

  /**
   * 用户关注
   */
  NewFollowAction = 'new_follow_action',

  /**
   * 私信撤回
   */
  IMRecallMsg = 'im_recall_msg'
}

export enum WebhookWechatEvents {
  /**
   * 视频号私信信息
   */
  FinderMsg = 'FinderBypMsg',

  /**
   * 微信消息
   */
  AddMsg = 'AddMsg',

  /**
   * 下线
   */
  Offline = 'Offline',

  /**
   * 消息通知（点赞、评论、小红心）
   */
  FinderSyncMsg = 'FinderSyncMsg'
}

export const GrpcOption = <const>{
  transport: Transport.GRPC,
  options: {
    ...socketConfig,
    channelOptions: {
      'grpc.keepalive_time_ms': 10 * 1000,
      'grpc.keepalive_timeout_ms': 5 * 1000,
      'grpc.keepalive_permit_without_calls': 1,
      'grpc.service_config': JSON.stringify({
        methodConfig: [
          {
            name: [{ service: 'Socket' }],
            timeout: '10s',
            retryPolicy: {
              maxAttempts: 3,
              initialBackoff: '0.5s',
              maxBackoff: '10s',
              backoffMultiplier: 1.5,
              retryableStatusCodes: ['UNAVAILABLE', 'INTERNAL']
            }
          }
        ]
      })
    }
  }
}

export enum WebhookKuaishouEvents {
  Test = 'TEST',

  /**
   * 接收私信
   */
  ReceiveMsg = 'RECEIVE_MSG',

  /**
   * 进入消息
   */
  ImEnterSession = 'IM_ENTER_SESSION',

  /**
   * 互动信息对外推送事件  interact_type  （1 直播评论，2 短视频评论，3进入私信页未发消息，4用户关注）
   */
  PushPotentialCustomerMsg = 'PUSH_POTENTIAL_CUSTOMER_MSG',

  /**
   * 进入消息
   */
  Unauthorize = 'UN_AUTHORIZE',

  Authorize = 'AUTHORIZE'
}

export enum BindEventXiaohongshu {
  BindAccount = 'BindAccount',

  UnBindAccount = 'UnBindAccount',

  BindKosUser = 'BindKosUser'
}
