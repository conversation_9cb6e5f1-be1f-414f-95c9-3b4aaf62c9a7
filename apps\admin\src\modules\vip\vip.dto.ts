import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { OrderRecord as OrderRecordType } from 'apps/common/modules/orderManage/orderManage.dto'

export enum OrderStatus {
  PENDING = 'pending', // 待支付
  SUCCESS = 'success', // 成功支付
  CANCELED = 'canceled', // 已取消
  REFUND = 'refund'
}

export enum OrderRecord {
  Create = 'create',
  Upgrade = 'upgrade',
  Diff = 'diff',
  Refund = 'refund',
  Expire = 'expire'
}

export enum UserCouponsStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * 已使用
   */
  used = 1,

  /**
   * 已过期
   */
  expired
}

export const AutoresponderKeywordKey = 'autoresponder:keyword'

export enum TeamMemberStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * vip过期冻结
   */
  Disable = 1
}

export enum AccountAccountsStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * vip过期冻结/禁用
   */
  Disable = 1
}

export enum PayType {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  CORPORATETRANSFER = 'corporateTransfer'
}

export enum OrderType {
  ONLINE = 'online',
  SYSTEM = 'system'
}

export class VipCreateDTO {
  @ApiProperty({
    type: Number,
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  teamId: number

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  interestId: number

  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @IsNumber()
  month: number

  @ApiProperty({
    description: '自定义天数',
    example: 1,
    required: true
  })
  @IsNumber()
  days: number

  @ApiProperty({
    description: '实付金额',
    type: Number,
    example: 100,
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  payAmount: number

  @ApiProperty({
    type: Boolean,
    description: '是否需要付费',
    required: true
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class VipRenewDTO {
  @ApiProperty({
    type: Number,
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  teamId: number

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  interestId: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @IsNumber()
  month: number

  @ApiProperty({
    description: '自定义天数',
    example: 1,
    required: true
  })
  @IsNumber()
  days: number

  @ApiProperty({
    description: '实付金额',
    type: Number,
    example: 100,
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  payAmount: number

  @ApiProperty({
    type: Boolean,
    description: '是否需要付费',
    required: true
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class GiftVipDTO {
  @ApiProperty({
    type: Number,
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  teamId: number

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  interestId: number

  @ApiProperty({
    description: '赠送天数',
    example: 1,
    required: true
  })
  @IsNumber()
  giftDays: number

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class TeamInfo {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String
  })
  name: string
}

export class Channel {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '渠道名称'
  })
  name: string
}

export class VipOrder {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: TeamInfo
  })
  team: TeamInfo

  @ApiResponseProperty({
    type: String
  })
  phone: string

  @ApiResponseProperty({
    type: String,
    example: 'KKJOE982934023KDJIEOW'
  })
  orderNo: string

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  teamId: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  vipId: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  price: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  payAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  createTime: number

  @ApiResponseProperty({
    type: String,
    example: 1
  })
  payType: string

  @ApiResponseProperty({
    type: String,
    example: 'Normal'
  })
  type: string

  @ApiResponseProperty({
    type: String,
    example: 'VIP'
  })
  remark: string

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  month: number

  @ApiResponseProperty({
    type: Number,
    example: '创建时间'
  })
  fromTime: number

  @ApiResponseProperty({
    type: Number,
    example: '支付时间'
  })
  payTime: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  toTime: number

  @ApiResponseProperty({
    type: String,
    example: '订单状态'
  })
  orderStatus: string

  @ApiResponseProperty({
    type: String,
    example: '订单类型(create:开通,upgrade:升级,renew:续费,gift:赠送)'
  })
  orderType: string

  @ApiResponseProperty({
    type: Number,
    example: '购买数量'
  })
  interestCount: number

  @ApiResponseProperty({
    type: Channel,
    example: {}
  })
  channel: Channel
}

export class VipVip {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  teamId: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  signDosageLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  groupDosageLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  commentDosageLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  platformAccountNumberLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  teamMemberNumberLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  uploadImageLimit: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  fansGroupManageLimit: number

  @ApiResponseProperty({
    type: Date
  })
  createTime: Date

  @ApiResponseProperty({
    type: Date
  })
  expirationTime: Date
}

export class VipOrdersResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  totalAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [VipOrder]
  })
  data: VipOrder[]
}

export class VipInfo {
  /**
   * 权益包数量
   */
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  interestCount: number

  /**
   * 账号数
   */
  @ApiResponseProperty({
    type: Number,
    example: 20
  })
  platformAccountCount: number

  /**
   * 成员数
   */
  @ApiResponseProperty({
    type: Number,
    example: 5
  })
  teamMemberCount: number

  /**
   * 消息额度
   */
  @ApiResponseProperty({
    type: Number,
    example: 1000
  })
  messageCount: number

  /**
   * VIP时长
   */
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  month: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  freeMonth: number

  /**
   * 赠送的天数
   */
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  giftDays: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  expirationTime: number
}

export class OrderInfo {
  @ApiResponseProperty({
    type: String,
    example: '订单编号'
  })
  orderNo: string

  @ApiResponseProperty({
    type: String,
    example: '团队名称'
  })
  teamName: string

  @ApiResponseProperty({
    type: String,
    example: '创建人'
  })
  creatorName: string

  @ApiResponseProperty({
    type: Number,
    example: '创建时间'
  })
  fromTime: number

  @ApiResponseProperty({
    type: String,
    example: 'pending, success, cancaled'
  })
  orderStatus: string

  @ApiResponseProperty({
    type: String,
    example: 'online, system'
  })
  type: string

  @ApiResponseProperty({
    type: Number,
    example: '待支付金额'
  })
  dueAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '已支付金额'
  })
  payAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '支付时间'
  })
  payTime: number

  @ApiResponseProperty({
    type: Number,
    example: '到期日期'
  })
  toTime: number

  @ApiResponseProperty({
    type: String,
    example: '支付方式'
  })
  payType: string

  @ApiResponseProperty({
    type: String,
    example: '备注'
  })
  remark: string

  @ApiResponseProperty({
    type: String,
    example: '订单类型(create:开通,upgrade:升级,renew:续费,gift:赠送)'
  })
  orderType: string

  @ApiResponseProperty({
    type: Number,
    example: '折扣金额'
  })
  discountAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '赠送天数'
  })
  giftDays: number

  @ApiResponseProperty({
    type: Number,
    example: '升级天数'
  })
  remainingDays: number

  @ApiResponseProperty({
    type: Number,
    example: '月份'
  })
  vipMonth: number

  @ApiResponseProperty({
    type: Number,
    example: '赠送月份'
  })
  freeMonth: number
}

export class OrderInfoResponse {
  @ApiResponseProperty({
    type: VipInfo,
    example: 1
  })
  vipInfo: VipInfo

  @ApiResponseProperty({
    type: OrderInfo,
    example: 1
  })
  orderInfo: OrderInfo
}

export class OrderInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderInfoResponse,
    example: 1
  })
  data: OrderInfoResponse
}

class OrderInterestResponse {
  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  id: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  platformAccountCount: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  messageCount: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  memberCount: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  price: number
}

class OrderInterestVipOftenDTO {
  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  mount: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  present: number
}

class OrderInterestData {
  @ApiResponseProperty({
    type: [OrderInterestResponse]
  })
  interests: OrderInterestResponse[]

  @ApiResponseProperty({
    type: [OrderInterestVipOftenDTO]
  })
  vipOften: OrderInterestVipOftenDTO[]
}

export class OrderInterestResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderInterestData
  })
  data: OrderInterestData
}

export class UpgradeOrderRequest {
  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  teamId: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsOptional()
  @IsNumber()
  payAmount: number

  @ApiProperty({
    type: Boolean,
    description: '是否需要付费',
    required: true
  })
  @IsNotEmpty()
  @IsBoolean()
  isPay: boolean

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class OrderStatusRequestDTO {
  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class orderPriceRequestDTO {
  @ApiProperty({
    enum: OrderRecordType,
    description: '订单类型(create:开通,upgrade:升级,renew:续费)',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(OrderRecordType)
  orderType: OrderRecordType

  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  teamId: number

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    description: '权益包数量',
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  month: number

  @ApiProperty({
    description: '天数',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  days: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  couponId?: number
}
