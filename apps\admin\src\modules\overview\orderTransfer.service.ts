import { Inject, Injectable, Logger } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Queue, Worker } from 'bullmq'
import { OrderStatus } from '../vip/vip.dto'
import { OrderRecord } from 'apps/common/modules/orderManage/orderManage.dto'
import dayjs from 'dayjs'

@Injectable()
export class OrderTransferService {
  dataQueue: Queue

  dataWorker: Worker

  serverNumber: number

  LOCK_TIMEOUT = 10 * 60 * 1000

  logger = new Logger('OrderTransferService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService
  ) {}

  async onModuleInit() {
    this.dataQueue = new Queue('order-data-transfer', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.dataWorker = new Worker(
      'order-data-transfer',
      async (job) => {
        const { teamId } = job.data

        await this.migrateUserTableData(teamId)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // this.onDataTransferByTeam()
    // this.serverNumber = 8
    // this.cacheManager.set('order-data-transfer', 1, 1000 * 60 * 60 * 24)
  }

  async onDataTransferByTeam() {
    const teamList = await this.prisma.team.findMany({
      where: {
        isDelete: false
      },
      orderBy: {
        createTime: 'desc'
      }
    })

    // console.log(teamList)

    for (let i = 0; i < teamList.length; i++) {
      const team = teamList[i]
      await this.onTransferTeamData(team.id)
    }

    // const teamIdList = teamList.map((team) => team.id)

    // const activeArray = teamIdList.slice(0, this.serverNumber)

    // if (activeArray.length > 0) {
    //   for (let j = 0; j < activeArray.length; j++) {
    //     const teamId = activeArray[j]
    //     await this.dataQueue.add(
    //       'order-data-transfer',
    //       {
    //         teamId
    //       },
    //       {
    //         removeOnComplete: true,
    //         removeOnFail: true,
    //         jobId: `order-data-transfer-${teamId}`
    //       }
    //     )
    //   }
    // }

    // const otherContents = teamIdList.slice(this.serverNumber)

    // if (otherContents.length) {
    //   await this.cacheManager.store.client.hset(
    //     'order:transfer',
    //     'teamIds',
    //     JSON.stringify(otherContents)
    //   )
    // }
  }

  async migrateUserTableData(teamId: number) {
    const isLock = await this.tryAcquireLock(`order-transfer-${teamId}`)

    if (!isLock) {
      // 数据迁移逻辑
      await this.onTransferTeamData(teamId)
    }

    const teamIds = await this.cacheManager.store.client.hget('order:transfer', 'teamIds')

    if (teamIds) {
      const teamListArray = JSON.parse(teamIds)

      const activeArray = teamListArray.slice(0, this.serverNumber)

      if (activeArray.length > 0) {
        for (let j = 0; j < activeArray.length; j++) {
          const teamId = activeArray[j]
          await this.dataQueue.add(
            'order-data-transfer',
            {
              teamId
            },
            {
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `order-data-transfer-${teamId}`
            }
          )
        }
      }

      const otherContents = teamListArray.slice(this.serverNumber)

      if (otherContents.length) {
        await this.cacheManager.store.client.hset(
          'order:transfer',
          'teamIds',
          JSON.stringify(otherContents)
        )
      } else {
        await this.cacheManager.store.client.hdel('order:transfer', 'teamIds')
      }
    }
  }

  async onTransferTeamData(teamId: number) {
    this.logger.log(teamId)
    const orderList = await this.prisma.order.findMany({
      where: {
        teamId,
        orderStatus: OrderStatus.SUCCESS
      },
      orderBy: {
        fromTime: 'asc'
      }
    })

    const teamInfo = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })

    let lastDate = teamInfo?.vip?.expirationTime ?? new Date()

    for (let i = 0; i < orderList.length; i++) {
      const order = orderList[i]
      if (!order.toTime) {
        if (order.orderType !== '') {
          let toTime = lastDate
          if (order.isGiftOrder) {
            toTime = dayjs(order.fromTime).add(order.giftDays, 'day').toDate()
            await this.prisma.order.update({
              where: {
                id: order.id
              },
              data: {
                toTime
              }
            })
          } else {
            // eslint-disable-next-line no-lonely-if
            if (order.orderType === OrderRecord.Create) {
              toTime = dayjs(order.fromTime).add(order.vipMonth, 'month').toDate()
              await this.prisma.order.update({
                where: {
                  id: order.id
                },
                data: {
                  toTime
                }
              })
            }

            if (order.orderType === OrderRecord.Upgrade) {
              toTime = dayjs(order.fromTime).add(order.remainingDays, 'day').toDate()
              await this.prisma.order.update({
                where: {
                  id: order.id
                },
                data: {
                  toTime
                }
              })
            }
          }
        } else if (order.orderType === '') {
          // eslint-disable-next-line no-lonely-if
          let orderType: OrderRecord
          let toTime = lastDate
          if (order.isGiftOrder) {
            toTime = dayjs(order.fromTime).add(order.giftDays, 'day').toDate()
            orderType = OrderRecord.Create
            await this.prisma.order.update({
              where: {
                id: order.id
              },
              data: {
                orderType,
                toTime
              }
            })
          } else {
            // eslint-disable-next-line no-lonely-if
            if (order.isUpgrade) {
              // 此订单有可能是升级，有可能是续费
              orderType = OrderRecord.Upgrade
              toTime = dayjs(order.fromTime).add(order.vipMonth, 'month').toDate()
              await this.prisma.order.update({
                where: {
                  id: order.id
                },
                data: {
                  orderType,
                  toTime
                }
              })
            } else {
              orderType = OrderRecord.Create
              toTime = dayjs(order.fromTime)
                .add(order.vipMonth >= 10 ? 12 : order.vipMonth, 'month')
                .toDate()
            }
            await this.prisma.order.update({
              where: {
                id: order.id
              },
              data: {
                orderType,
                toTime
              }
            })
          }
        }
      } else {
        lastDate = order.toTime
      }
    }

    await this.onOrderTransferData(teamId)
  }

  async onOrderTransferData(teamId: number) {
    const orderList = await this.prisma.order.findMany({
      where: {
        teamId,
        orderStatus: OrderStatus.SUCCESS
      },
      orderBy: {
        fromTime: 'desc'
      }
    })

    const teamInfo = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })

    let lastDate = teamInfo?.vip?.expirationTime ?? new Date()

    for (let i = 0; i < orderList.length; i++) {
      const order = orderList[i]
      if (!order.toTime) {
        if (order.orderType !== '') {
          if (i === 0) {
            await this.prisma.order.update({
              where: {
                id: order.id
              },
              data: {
                toTime: lastDate
              }
            })
          } else {
            // eslint-disable-next-line no-lonely-if
            if (order.orderType === OrderRecord.Gift) {
              lastDate = dayjs(lastDate).subtract(order.giftDays, 'day').toDate()
              await this.prisma.order.update({
                where: {
                  id: order.id
                },
                data: {
                  toTime: lastDate
                }
              })
            }

            if (order.orderType === OrderRecord.Renew) {
              lastDate = dayjs(lastDate)
                .subtract(order.vipMonth + order.freeMonth, 'month')
                .toDate()
              await this.prisma.order.update({
                where: {
                  id: order.id
                },
                data: {
                  toTime: lastDate
                }
              })
            }
          }
        } else {
          lastDate = order.toTime
        }
      } else {
        lastDate = order.toTime
      }
    }
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(`team-transfer-${key}`, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
