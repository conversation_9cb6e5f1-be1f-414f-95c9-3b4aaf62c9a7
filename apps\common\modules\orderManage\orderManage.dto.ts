import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'

export enum OrderStatus {
  PENDING = 'pending', // 待支付
  SUCCESS = 'success', // 成功支付
  CANCELED = 'canceled', // 已取消
  REFUND = 'refund' // 已退款
}

export enum UserCouponsStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * 已使用
   */
  used = 1,

  /**
   * 已过期
   */
  expired
}

export enum ContractStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * 已退款
   */
  isRefund = 1
}

export enum SalesType {
  NotBuy = 'NotBuy',
  FirstBuy = 'FirstBuy',
  ReBuy = 'ReBuy'
}

export enum OrderRecord {
  Create = 'create',
  Upgrade = 'upgrade',
  Renew = 'renew',
  Gift = 'gift'
}

export enum PayType {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  CORPORATETRANSFER = 'corporateTransfer'
}

export enum OrderType {
  ONLINE = 'online',
  SYSTEM = 'system'
}

export class OrderBaseRequestDTO {
  @ApiProperty({
    description: '用户id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  userId: number

  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  teamId: number

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  couponId?: number

  @ApiProperty({
    type: Boolean,
    description: '是否支付',
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isPay?: boolean

  @ApiProperty({
    description: '创建人',
    required: false
  })
  @IsNumber()
  @IsOptional()
  creatorId?: number

  @ApiProperty({
    description: '创建人昵称',
    required: false
  })
  @IsString()
  @IsOptional()
  creatorName?: string

  @ApiProperty({
    description: '备注',
    required: false
  })
  @IsString()
  @IsOptional()
  remark?: string

  @ApiProperty({
    description: '待付款金额',
    required: false
  })
  @IsNumber()
  @IsOptional()
  payAmount?: number

  @ApiProperty({
    type: Boolean,
    description: '是否对公转账',
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isCorporateTransfer?: boolean
}

export class PostCreateOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: '1',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: true
  })
  @IsNumber()
  days: number

  @ApiProperty({
    description: 'ios查询id',
    required: false
  })
  @IsString()
  @IsOptional()
  transactionId?: string
}

export class PostUpgradeOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number
}

export class PostRenewOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    description: '月份数量',
    example: '1',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: true
  })
  @IsNumber()
  days: number
}

export class PostGiftOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: true
  })
  @IsNumber()
  giftDays: number

  @ApiProperty({
    type: Boolean,
    description: '是否是注册赠送',
    required: true
  })
  @IsBoolean()
  isRegisterOrder: boolean
}

export class PostRefundRequestDTO {
  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  teamId: number

  @ApiProperty({
    description: '退费金额',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  refundAmount: number

  @ApiProperty({
    description: '实际退费金额',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  actualRefundAmount: number

  @ApiProperty({
    description: '备注',
    required: false
  })
  @IsString()
  @IsOptional()
  remark: string
}

export class orderInfo {
  @ApiProperty({
    description: '订单编号',
    example: 'U041YQS3M0OJ5D1ZW3',
    required: true
  })
  @IsString()
  orderNo: string

  @ApiProperty({
    enum: Number,
    description: '订单金额',
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  payAmount: number

  @ApiProperty({
    description: '退费金额',
    example: 1,
    required: false
  })
  @IsNotEmpty()
  @IsNumber()
  refundAmount: number
}

export class PostCalculateOrderPriceRequestDTO {
  @ApiProperty({
    description: '用户id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  userId: number

  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  teamId: number

  @ApiProperty({
    enum: OrderRecord,
    description: '订单类型(create:开通,upgrade:升级,renew:续费)',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(OrderRecord)
  orderType: OrderRecord

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestId: number

  @ApiProperty({
    description: '权益包数量',
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: '1',
    required: false
  })
  @IsNumber()
  @IsOptional()
  month: number

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: true
  })
  @IsNumber()
  days: number

  @ApiProperty({
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  couponId?: number
}

export class OrderPriceResponseDto {
  @ApiResponseProperty({
    type: Number,
    example: '产品金额'
  })
  orderAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '折扣金额'
  })
  discountAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '优惠券金额'
  })
  couponAmount: number

  @ApiResponseProperty({
    type: String,
    example: '升级公式'
  })
  tips: string

  @ApiResponseProperty({
    type: String,
    example: '升级公式(中文 )'
  })
  tipsCn: string
}
