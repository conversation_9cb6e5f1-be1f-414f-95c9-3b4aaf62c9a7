import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { LabelService } from './label.service'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { LabelResponseDTO, PatchLabelRequest, PostLabelRequest } from './label.dto'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('labels')
@ApiTags('标签管理')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({
  name: 'authorization',
  required: true
})
export class LabelController {
  constructor(private readonly labelService: LabelService) {}

  @Get()
  @ApiOperation({ summary: '标签列表' })
  @ApiOkResponse({ description: '操作成功', type: LabelResponseDTO })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '标签名称'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '单页数量 <默认 10>' })
  async getLabel(
    @Query('name') name: string,
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number
  ) {
    const response = await this.labelService.getLabel(name, page, size)
    return response
  }

  @Post()
  @ApiOperation({ summary: '创建标签' })
  @ApiOkResponse({ description: '操作成功', type: LabelResponseDTO })
  async postLabel(@Body() body: PostLabelRequest) {
    const response = await this.labelService.postLabel(body)
    return response
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新标签' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async patchLabel(@Param('id') id: number, @Body() body: PatchLabelRequest) {
    return this.labelService.patchLabel(id, body)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除标签' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async deleteLabel(@Param('id') id: number) {
    await this.labelService.deleteLabel(id)
  }
}
