import { ApiProperty } from '@nestjs/swagger'
import { IsString } from 'class-validator'

export class PreSignedUrlRequestDTO {
  @ApiProperty({
    description: 'method 支持 GET/PUT/HEAD/DELETE',
    required: true,
    enum: ['GET', 'PUT', 'HEAD', 'DELETE']
  })
  method: 'GET' | 'PUT' | 'HEAD' | 'DELETE'

  @ApiProperty({
    description: '对象名称'
  })
  @IsString()
  objectName: string
}

export class PreSignedPostRequestDTO {
  @ApiProperty({
    description: '对象名称'
  })
  @IsString()
  objectName: string
}

export class PutObjectByStreamRequestDTO {
  @ApiProperty({
    description: '远端地址'
  })
  @IsString()
  url: string

  @ApiProperty({
    description: '团队code'
  })
  @IsString()
  teamCode: string

  @ApiProperty({
    description: 'referer'
  })
  @IsString()
  referer: string
}
