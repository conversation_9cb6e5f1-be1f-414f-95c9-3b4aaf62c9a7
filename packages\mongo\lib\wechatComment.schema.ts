import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { SchemaTypes } from 'mongoose'

export interface WechatCommentContent {
  /**
   * @description 评论者昵称
   */

  nickname: string

  /**
   * @description 评论者头像
   */
  headUrl: string

  /**
   * @description 回复内容
   */
  mentionContent: string

  /**
   * @description 该评论回复的评论
   */
  refContent: string

  /**
   * @description 该评论回复的评论对应的昵称
   */
  replyNickname: string

  /**
   * @description 该评论回复的评论对应的头像
   */
  replyHeadUrl: string

  /**
   * @description 评论id
   */
  refCommentId: string
}

export interface CommentReply {
  /**
   * @description 回复者微信id
   */
  wxid: string

  /**
   * @description 回复者昵称
   */
  nickname: string

  /**
   * @description 回复者头像
   */
  headUrl: string

  /**
   * @description 回复内容
   */
  replyContent: string

  /**
   * @description 回复内容
   */
  createTime: number
}

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class WechatCommentEntity {
  @Prop({
    type: String,
    required: true,
    unique: true,
    index: true
  })
  uniqueId: string

  @Prop({
    type: String,
    required: true
  })
  typeName: string

  @Prop({
    type: String,
    required: true,
    index: true
  })
  fromUserName: string

  @Prop({
    type: String,
    required: true,
    index: true
  })
  toUserName: string

  @Prop({
    type: String,
    required: true,
    index: true
  })
  wxid: string

  @Prop({
    type: String,
    required: true,
    index: true
  })
  appid: string

  @Prop({
    type: Number,
    required: false
  })
  mentionType: number

  @Prop({
    type: SchemaTypes.Map,
    required: false
  })
  content: WechatCommentContent

  @Prop({
    type: Number,
    required: false,
    index: true
  })
  createTime: number

  @Prop({
    type: String,
    default: 'wechat'
  })
  platformType: string

  @Prop({
    type: String,
    index: true
  })
  refObjectId: string

  @Prop({
    type: String
  })
  refObjectNonceId: string

  @Prop({
    type: String
  })
  thumbUrl: string

  @Prop({
    type: String
  })
  description: string

  @Prop({
    type: String,
    required: true
  })
  sessionBuffer: string

  @Prop({ type: String })
  jsonData: string

  @Prop([
    {
      content: String,
      wxid: String,
      createTime: Number
    }
  ])
  commentReply: {
    content: string
    wxid: string
    createTime: number
  }[]
}

export const WechatCommentSchema: ModelDefinition = {
  name: WechatCommentEntity.name,
  schema: SchemaFactory.createForClass(WechatCommentEntity).index({
    wxid: 1,
    createTime: -1
  })
}

export const WechatCommentMongoose = MongooseModule.forFeature([WechatCommentSchema])
