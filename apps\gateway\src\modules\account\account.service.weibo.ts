import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger
} from '@nestjs/common'
import { PrismaService, type PlatformAccount } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { type Cache } from 'cache-manager'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { type RedisStore } from 'cache-manager-ioredis-yet'
import { AutoresponderKeywordKey, genSocketRedisKey, Platform } from '@qdy/utils'
import { AccountAccountAuthorizeRequestBodyDTO, AccountAccountsStatus } from './account.dto'
import { TeamMemberRole } from '../team/team.dto'
import { type FastifyReply } from 'fastify'
import { AccountSocketService } from './account.task'
import {
  postAuthorizeWeiboAccount,
  getAuthorizeWeiboAccountInfoApi,
  postRevokeoAuthWeiboAccount
} from './external.weibo'
import { PlatformAccountManageService, TosManageService } from '@qdy/common'

@Injectable()
export class AccountWeiboService {
  logger = new Logger('AccountWeiboService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly socketService: AccountSocketService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly tosManageService: TosManageService,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  /**
   * 授权账号
   * @param authorization
   * @param code
   */
  async authorizeAccount(
    { code, isRegister }: AccountAccountAuthorizeRequestBodyDTO,
    req: FastifyReply
  ) {
    const { user } = this.request

    const { weiboClientKey, weiboClientSecret, weiboRedirectUri } =
      this.configService.get<RootConfigMap['app']>('app')

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const [team, platformAccountCount, systemDosage] = await Promise.all([
      this.prisma.team.findUnique({
        where: {
          id: teamMember.teamId
        },
        include: {
          vip: true
        }
      }),
      this.prisma.platformAccount.count({ where: { teamId: teamMember.teamId } }),
      this.prisma.systemDosage.findFirst()
    ])

    if (isRegister) {
      if (team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
        if (team.vip.platformAccountNumberLimit <= platformAccountCount) {
          throw new ForbiddenException('账号数量已达上限')
        }
      } else if (systemDosage.standardPlatformAccountNumberLimit <= platformAccountCount) {
        throw new ForbiddenException('账号数量已达上限')
      }
    }

    const res = await postAuthorizeWeiboAccount({
      clientKey: weiboClientKey,
      clientSecret: weiboClientSecret,
      code,
      redirectUri: weiboRedirectUri
    })

    const oldPlatformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId: res.openId,
        platform: Platform.Weibo
      },
      include: {
        affiliates: true
      }
    })

    if (!isRegister) {
      if (oldPlatformAccount && oldPlatformAccount.teamId !== user.currentTeamId) {
        // 重新授权，登录的账号在当前团队无法找到，并且账号已超了提示错误
        if (team.vip.platformAccountNumberLimit <= platformAccountCount) {
          throw new ForbiddenException('账号数量已达上限')
        }
      }
      if (!oldPlatformAccount && team.vip.platformAccountNumberLimit <= platformAccountCount) {
        throw new ForbiddenException('账号数量已达上限')
      }
    }
    if (oldPlatformAccount) {
      // false 没有过期 true
      const isoverdue =
        oldPlatformAccount.expiresIn * 1000 + oldPlatformAccount.createTime.getTime() < Date.now()

      if (!isoverdue && oldPlatformAccount.teamId !== teamMember.teamId) {
        await this.prisma.platformAccount.update({
          where: {
            id: oldPlatformAccount.id
          },
          data: {
            accessToken: res.accessToken,
            expiresIn: res.expiresIn,
            refreshTime: new Date(),
            createTime: new Date(),
            tokenTime: new Date(),
            status: AccountAccountsStatus.Normal,
            unauthorize: ''
          }
        })
        req.status(201).send({
          statusCode: 0,
          data: {
            name: oldPlatformAccount.name,
            avatar: oldPlatformAccount.avatar
          }
        })
      }
    }

    const info = await getAuthorizeWeiboAccountInfoApi({
      accessToken: res.accessToken,
      uid: res.openId
    })

    const accountAvatar = await this.uploadFromUrl({
      url: info.profile_image_url,
      teamCode: team.invitationCode
    })

    const value = {
      name: info.name,
      avatar: accountAvatar,
      openId: res.openId,
      accessToken: res.accessToken,
      expiresIn: res.expiresIn,
      refreshExpiresIn: 0,
      refreshToken: '',
      teamId: teamMember.teamId,
      status: AccountAccountsStatus.Normal,
      accountRole: info.verified ? 'COMPANY_BAND' : '',
      wechatInfo: JSON.stringify(info),
      unauthorize: '',
      platform: Platform.Weibo,
      affiliates: {
        connect: {
          id: teamMember.id
        }
      }
    }

    if (oldPlatformAccount && oldPlatformAccount.teamId !== teamMember.teamId) {
      await this.prisma.platformAccount.update({
        where: {
          id: oldPlatformAccount.id
        },
        data: {
          affiliates: {
            disconnect: oldPlatformAccount.affiliates.map((item) => ({ id: item.id }))
          }
        }
      })

      const userIds = oldPlatformAccount.affiliates.map((item) => item.userId)
      const teamMemberByOldPlatformAccount = await this.prisma.teamMember.findMany({
        where: {
          teamId: oldPlatformAccount.teamId,
          role: {
            not: TeamMemberRole.Member
          }
        }
      })

      const manageUserIds = teamMemberByOldPlatformAccount.map((item) => item.userId)

      const combinedAndUniqueIds = [...new Set([...userIds, ...manageUserIds])]

      const users = await this.prisma.user.findMany({
        where: {
          id: {
            in: combinedAndUniqueIds
          }
        }
      })
      if (users.length > 0) {
        for (let i = 0; i < users.length; i++) {
          const userInfo = users[i]
          this.logger.log(userInfo)
          const auth = await this.cacheManager.get<string>(userInfo.phone)
          if (auth) {
            await this.cacheManager.store.client.hdel(
              genSocketRedisKey(auth),
              oldPlatformAccount.openId
            )
          }
        }
      }

      // 删除旧账号的socketids
      await this.cacheManager.store.client.del(genSocketRedisKey(oldPlatformAccount.openId))

      // 该账号之前团队对应的自动回复移除该账号
      const autoresponderList = await this.prisma.autoresponder.findMany({
        where: {
          platformAccountIds: {
            array_contains: oldPlatformAccount.id
          }
        }
      })

      autoresponderList.forEach(async (item) => {
        if (item.platformAccountIds) {
          const accountIds = (item.platformAccountIds as number[]).filter(
            (id) => id !== oldPlatformAccount.id
          )
          await this.prisma.autoresponder.update({
            where: { id: item.id },
            data: { platformAccountIds: accountIds }
          })

          // 删除账号策略缓存
          await this.cacheManager.store.client.hdel(
            AutoresponderKeywordKey,
            oldPlatformAccount.openId
          )
        }
      })
    }

    const platformAccount = await this.prisma.platformAccount.upsert({
      create: value,
      where: {
        openId: res.openId
      },
      update: {
        ...value,
        createTime: new Date(),
        refreshTime: new Date(),
        tokenTime: new Date()
      }
    })

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        teamId: teamMember.teamId,
        role: {
          not: TeamMemberRole.Member
        },
        userId: {
          not: user.id
        }
      },
      include: {
        user: true
      }
    })

    const socketIds = []

    for (let i = 0; i < teamMembers.length; i++) {
      const item = teamMembers[i]
      const [socketId, appSocketId] = await Promise.all([
        this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
        this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
      ])
      this.logger.debug(`teamMembers-item-${item.name}`, {
        socketId,
        reidsKey: genSocketRedisKey(item.userId),
        openId: platformAccount.id
      })
      await Promise.allSettled([
        (async () => {
          if (appSocketId) {
            socketIds.push(appSocketId)
            try {
              const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

              if (authApp) {
                await this.cacheManager.store.client.hset(
                  genSocketRedisKey(authApp),
                  platformAccount.openId,
                  platformAccount.id
                )
              }
            } catch (error) {
              this.logger.error('更新缓存错误', error)
            }

            await this.cacheManager.set(genSocketRedisKey(item.userId + 'app'), appSocketId, 0)
          }
        })(),
        (async () => {
          if (socketId) {
            socketIds.push(socketId)
            try {
              const auth = await this.cacheManager.get<string>(item.user.phone)

              if (auth) {
                await this.cacheManager.store.client.hset(
                  genSocketRedisKey(auth),
                  platformAccount.openId,
                  platformAccount.id
                )
              }
            } catch (error) {
              this.logger.error('更新缓存错误', error)
            }

            await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
          }
        })()
      ])
    }

    const [socketId, appSocketId] = await Promise.all([
      this.cacheManager.get<string>(genSocketRedisKey(user.id)),
      this.cacheManager.get<string>(genSocketRedisKey(user.id + 'app'))
    ])

    await this.platformAccountManageService.updatePlatformAccountRedisInfo(platformAccount)

    await Promise.allSettled([
      (async () => {
        if (appSocketId) {
          socketIds.push(appSocketId)
          try {
            const authApp = await this.cacheManager.get<string>(`${user.phone}app`)

            if (authApp) {
              await this.cacheManager.store.client.hset(
                genSocketRedisKey(authApp),
                platformAccount.openId,
                platformAccount.id
              )
            }
          } catch (error) {
            this.logger.error('更新缓存错误', error)
          }

          await this.cacheManager.store.client.hset(
            genSocketRedisKey(res.openId),
            appSocketId,
            platformAccount.id
          )
        }
      })(),
      (async () => {
        if (socketId) {
          socketIds.push(socketId)
          try {
            const auth = await this.cacheManager.get<string>(user.phone)

            if (auth) {
              await this.cacheManager.store.client.hset(
                genSocketRedisKey(auth),
                platformAccount.openId,
                platformAccount.id
              )
            }
          } catch (error) {
            this.logger.error('更新缓存错误', error)
          }

          await this.cacheManager.store.client.hset(
            genSocketRedisKey(res.openId),
            socketId,
            platformAccount.id
          )
        }
      })()
    ])

    req.status(201).send({
      statusCode: 0,
      data: {
        name: info.name,
        avatar: info.avatar
      }
    })

    return this.initPlatformAccount(platformAccount, socketIds)
  }

  async weiboRevokeoauth(platformAccountId: number) {
    const { user } = this.request
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        },
        role: {
          not: TeamMemberRole.Member
        }
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('你没有此账号权限')
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: { id: platformAccountId }
    })

    try {
      const result = await postRevokeoAuthWeiboAccount(platformAccount.accessToken)
      if (result.result) {
        await this.prisma.platformAccount.update({
          where: {
            id: platformAccount.id
          },
          data: {
            expiresIn: 0
          }
        })

        const userIdMaps = await this.cacheManager.store.client.hgetall(
          genSocketRedisKey(platformAccount.openId)
        )

        const changePlatformAccounts = []
        Object.keys(userIdMaps).forEach((socketId) => {
          changePlatformAccounts.push({
            socketId,
            data: {
              type: 'changePlatformAccount',
              data: [
                {
                  action: 'timeout',
                  platform: platformAccount.platform,
                  name: platformAccount.name,
                  avatar: platformAccount.avatar,
                  openId: platformAccount.openId,
                  accountRole: platformAccount.accountRole,
                  id: platformAccount.id,
                  teamId: platformAccount.teamId,
                  expiresTime: 0
                }
              ]
            }
          })
        })
      }

      return result
    } catch (error) {
      await this.prisma.platformAccount.update({
        where: {
          id: platformAccount.id
        },
        data: {
          expiresIn: 1
        }
      })
      throw new ForbiddenException(error.message)
    }
  }

  async initPlatformAccount(platformAccount: PlatformAccount, socketIds: string[]) {
    if (socketIds.length) {
      try {
        this.socketService.socketService
          .send({
            list: JSON.stringify(
              socketIds.map((socketId) => ({
                socketId,
                data: {
                  type: 'changePlatformAccount',
                  data: [
                    {
                      action: 'add',
                      platform: platformAccount.platform,
                      name: platformAccount.name,
                      avatar: platformAccount.avatar,
                      openId: platformAccount.openId,
                      accountRole: platformAccount.accountRole,
                      id: platformAccount.id,
                      teamId: platformAccount.teamId,
                      expiresTime: platformAccount.expiresIn
                        ? platformAccount.expiresIn * 1000 + platformAccount.createTime.getTime()
                        : 0
                    }
                  ]
                }
              }))
            )
          })
          .subscribe({
            next: () => {},
            error: (err) => {
              throw new BadRequestException(`发送失败 error${err.message}`)
            },
            complete: () => {}
          })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }

  async uploadFromUrl(data: { url: string; teamCode: string }) {
    try {
      // 使用 axios 请求远程视频文件并获取流
      return await this.tosManageService.putObjectByStream({
        url: data.url,
        referer: 'https://weibo.com',
        teamCode: data.teamCode
      })
    } catch (error) {
      this.logger.log(error.message)
    }
  }

  getFileNameFromUrl(url: string): string {
    const parsedUrl = new URL(url)
    const { pathname } = parsedUrl
    if (!pathname) {
      return ''
    }
    // 去除问号及后面的部分
    const pathWithoutQuery = pathname.split('?')[0]
    const segments = pathWithoutQuery.split('/')
    const fileName = segments[segments.length - 1]
    return fileName
  }
}
