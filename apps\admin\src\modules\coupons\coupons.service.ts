import { BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { Prisma, PrismaService } from '@qdy/mysql'
import {
  CouponsCreateDTO,
  CouponsStatus,
  SendCouponsRequestDTO,
  UpdateCouponsRequestDTO
} from './coupons.dto'
import { UserCouponsStatus } from '../vip/vip.dto'

@Injectable()
export class CouponsService {
  logger = new Logger('CouponService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async createCoupons(data: CouponsCreateDTO) {
    if (data.discountAmount >= data.minimumSpendingAmount) {
      throw new BadRequestException('优惠金额只能小于消费金额')
    }

    if (!data.expireDaysNum) {
      throw new BadRequestException('优惠券有效天数不能小于1')
    }
    const { user } = this.request

    try {
      await this.prisma.coupons.create({
        data: {
          creatorId: user.id,
          creatorName: user.username,
          name: data.name,
          minimumSpendingAmount: data.minimumSpendingAmount,
          discountAmount: data.discountAmount,
          expireDaysNum: data.expireDaysNum
        }
      })
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`创建优惠券失败`)
    }
  }

  async getCoupons(page: number = 1, size: number = 10) {
    if (!page || page < 1) {
      page = 1
    }
    if (!size || size < 1) {
      size = 10
    }

    const whereDic: Prisma.CouponsWhereInput = { status: CouponsStatus.Normal }

    // 总数
    const total = await this.prisma.coupons.count({
      where: whereDic
    })

    const reData =
      total > 0
        ? await this.prisma.coupons.findMany({
            where: whereDic,
            skip: (page - 1) * size,
            take: size,
            orderBy: {
              createTime: 'desc' // 按 createTime 字段降序排序
            }
          })
        : null

    return {
      total,
      page,
      size: reData?.length ?? 0,
      data:
        reData?.map((obj) => ({
          id: obj.id,
          name: obj.name,
          minimumSpendingAmount: obj.minimumSpendingAmount,
          discountAmount: obj.discountAmount,
          creatorName: obj.creatorName,
          createTime: obj.createTime,
          postAmount: obj.postAmount,
          activeAmount: obj.activeAmount,
          expireDaysNum: obj.expireDaysNum
        })) ?? []
    }
  }

  async sendCoupons(data: SendCouponsRequestDTO) {
    const { user } = this.request

    const whereDic: Prisma.CouponsWhereUniqueInput = {
      id: data.couponsId,
      status: CouponsStatus.Normal
    }

    const coupons = await this.prisma.coupons.findUnique({
      where: whereDic
    })

    if (!coupons) {
      throw new BadRequestException('优惠券不存在')
    }

    const targetUser = await this.prisma.user.findUnique({
      where: {
        phone: data.phone
      }
    })

    if (!targetUser) {
      throw new BadRequestException('当前手机号未注册')
    }

    try {
      const result = new Date()
      result.setDate(result.getDate() + coupons.expireDaysNum)
      const expireDate = result
      await this.prisma.$transaction(async () => {
        // 插入发放记录
        await this.prisma.usersCoupons.create({
          data: {
            couponsId: data.couponsId,
            phone: data.phone,
            creatorId: user.id,
            userId: targetUser.id,
            expireTime: expireDate,
            minimumSpendingAmount: coupons.minimumSpendingAmount,
            discountAmount: coupons.discountAmount,
            name: coupons.name
          }
        })

        // 更新发放次数 (优惠券表中发放次数 +1)
        await this.prisma.coupons.update({
          where: { id: data.couponsId, status: CouponsStatus.Normal },
          data: { postAmount: { increment: 1 } }
        })
      })
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`发放优惠券失败`)
    }
  }

  async deleteCoupons(couponsId: number) {
    const whereDic: Prisma.CouponsWhereUniqueInput = {
      id: couponsId,
      status: CouponsStatus.Normal
    }

    const coupons = await this.prisma.coupons.findUnique({
      where: whereDic
    })
    if (!coupons) {
      throw new BadRequestException('优惠券不存在')
    }

    try {
      await this.prisma.coupons.update({
        where: {
          id: couponsId,
          status: CouponsStatus.Normal
        },
        data: {
          status: CouponsStatus.Deleted
        }
      })
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`删除优惠券失败`)
    }
  }

  async updateCoupons(data: UpdateCouponsRequestDTO) {
    if (data.discountAmount >= data.minimumSpendingAmount) {
      throw new BadRequestException('优惠金额只能小于消费金额')
    }

    const whereDic: Prisma.CouponsWhereUniqueInput = {
      id: data.id,
      status: CouponsStatus.Normal
    }

    try {
      await this.prisma.coupons.update({
        where: whereDic,
        data: {
          name: data.name,
          minimumSpendingAmount: data.minimumSpendingAmount,
          discountAmount: data.discountAmount,
          expireDaysNum: data.expireDaysNum
        }
      })
    } catch (error) {
      this.logger.error(error)
      throw new BadRequestException(`更新优惠券失败`)
    }
  }

  async getCoupon(couponsId: number) {
    const coupons = await this.prisma.coupons.findUnique({
      where: {
        id: couponsId,
        status: CouponsStatus.Normal
      }
    })

    if (!coupons) {
      throw new BadRequestException('优惠券不存在')
    }

    return {
      id: coupons.id,
      name: coupons.name
    }
  }

  async getUserCouponsRecord(
    couponId: number = 0,
    status: number = -1,
    page: number = 1,
    size: number = 10,
    phone: string = null,
    teamId: number = 0,
    channelId: number = 0,
    createTimeS: number = 0,
    createTimeE: number = 0,
    castTimeS: number = 0,
    castTimeE: number = 0
  ) {
    if (!page || page < 1) {
      page = 1
    }
    if (!size || size < 1) {
      size = 10
    }

    const now = new Date()

    const whereDiction: Prisma.UsersCouponsWhereInput = {}

    if (couponId) {
      whereDiction.couponsId = couponId
    }

    // 指定状态
    if (status >= 0) {
      if (status === UserCouponsStatus.expired) {
        // 已过期的
        whereDiction.expireTime = { lt: now }
      } else {
        // 未使用和已使用的
        whereDiction.status = status
      }
    }

    // 电话筛选
    if (phone) {
      whereDiction.phone = phone
    }

    // 团队筛选
    if (teamId) {
      whereDiction.teamId = teamId
    }

    // 渠道筛选
    if (channelId) {
      whereDiction.channelId = channelId
    }

    // 发放/领取时间筛选
    if (createTimeS && createTimeE) {
      whereDiction.createTime = { gte: new Date(createTimeS), lt: new Date(createTimeE) }
    }

    // 使用时间筛选
    if (castTimeS && castTimeE) {
      whereDiction.castTime = { gte: new Date(castTimeS), lt: new Date(castTimeE) }
    }

    // 总数
    const total = await this.prisma.usersCoupons.count({
      where: whereDiction
    })

    const reData =
      total > 0
        ? await this.prisma.usersCoupons.findMany({
            where: whereDiction,
            include: {
              channel: { select: { name: true } },
              team: { select: { name: true } }
            },
            skip: (page - 1) * size,
            take: size,
            orderBy: [
              {
                status: 'asc'
              },
              {
                createTime: 'desc'
              }
            ]
          })
        : null

    return {
      total,
      page,
      size: reData?.length ?? 0,
      data:
        reData?.map((obj) => ({
          id: obj.id,
          phone: obj.phone,
          channelId: obj.channelId,
          channelName: obj.channel?.name ?? '',
          teamId: obj.teamId,
          teamName: obj.team?.name ?? '',
          createTime: obj.createTime,
          expireTime: obj.expireTime,
          castTime: obj.castTime,
          orderNo: obj.orderNo,
          status:
            obj.status === UserCouponsStatus.Normal && obj.expireTime < now
              ? UserCouponsStatus.expired
              : obj.status
        })) ?? []
    }
  }
}
