/* eslint-disable no-console */
import { BadRequestException } from '@nestjs/common'
import axios from 'axios'

const wechatOutApi = '/api/login/logout'
const authorQrCodeApi = '/api/login/getLoginQrCode'
const checkLoginApi = '/api/login/checkLogin'
const getProfile = '/api/finder/getProfile'
const accountCheckOnline = '/api/login/checkOnline'
const queryLoginWx = '/api/login/queryLoginWx'

export function wechatLogout({ appId }: { appId: string }) {
  const videosApiToken = process.env.WECHAT_TOKEN
  const url = `${process.env.WECHAT_BASE_URL}${wechatOutApi}`
  return axios.post(
    url,
    {
      appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )
}

export async function postAuthorizeByWechat(data: {
  appId: string
  regionId: string
  token: string
  type: string
}) {
  const url = `${process.env.WECHAT_BASE_URL}${authorQrCodeApi}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const type = data.type ?? 'ipad'

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      regionId: data.regionId,
      useProxy: true,
      type
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      qrData: string
      qrImgBase64: string
      uuid: string
      appId: string
    }
    msg: string
    ret: number
  } as { data: Record<string, any> }

  if (res.data.ret !== 200) {
    console.log(url)

    console.log(
      JSON.stringify({
        appId: data.appId,
        regionId: data.regionId,
        useProxy: true
      })
    )
    throw new BadRequestException(`[视频号]:${res.data.msg}`)
  }

  return {
    qrData: res.data.data.qrData,
    qrImgBase64: res.data.data.qrImgBase64,
    uuid: res.data.data.uuid,
    appId: res.data.data.appId
  }
}

export async function postCheckLoginByWechat(data: {
  appId: string
  uuid: string
  captchCode: string
  token: string
}) {
  const url = `${process.env.WECHAT_BASE_URL}${checkLoginApi}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      uuid: data.uuid,
      captchCode: data.captchCode,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      uuid: string
      headImgUrl: string
      nickName: string
      expiredTime: number
      status: number
      loginInfo: {
        uin: number
        wxid: string
        nickName: string
        mobile: string
        alias: string
      }
      code: string
      msg: string
    }
    msg: string
    ret: number
  } as { data: Record<string, any> }

  console.log(url)

  console.log(
    JSON.stringify({
      appId: data.appId,
      uuid: data.uuid,
      captchCode: data.captchCode,
      useProxy: true
    })
  )

  console.log(res.data)

  return res.data
}

export async function postWechatProfile(data: { appId: string; token: string }) {
  const url = `${process.env.WECHAT_BASE_URL}${getProfile}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      mainFinderUsername: string
      aliasInfo: Array<{
        nickname: string
        headImgUrl: string
        roleType: number
      }>
    }
    msg: string
    ret: number
  } as { data: Record<string, any> }

  if (res.data.ret !== 200) {
    throw new BadRequestException(`[视频号]:${res.data.msg}`)
  }

  return {
    mainFinderUsername: res.data.data.mainFinderUsername,
    aliasInfo: res.data.data.aliasInfo
  }
}

export async function checkOnline({ appId }: { appId: string }) {
  const url = `${process.env.WECHAT_BASE_URL}${accountCheckOnline}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = await axios.post(
    url,
    {
      appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )

  return res.data
}

export async function getAllAccount() {
  const url = `${process.env.WECHAT_BASE_URL}${queryLoginWx}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = await axios.post(url, null, {
    headers: {
      'VideosApi-token': videosApiToken
    }
  })

  return res.data.data
}
