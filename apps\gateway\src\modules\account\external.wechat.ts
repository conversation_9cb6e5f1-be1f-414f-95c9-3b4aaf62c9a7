/* eslint-disable no-console */
import { BadRequestException } from '@nestjs/common'
import axios from 'axios'

const wechatOutApi = '/api/login/logout'
const authorQrCodeApi = '/api/login/getLoginQrCode'
const checkLoginApi = '/api/login/checkLogin'
const getProfile = '/api/finder/getProfile'
const accountCheckOnline = '/api/login/checkOnline'
const queryLoginWx = '/api/login/queryLoginWx'

const baseUrl = 'http://api.videosapi.com/finder/v2'
const newBaseUrl = 'http://36.111.200.122:4408/finder/v2'

export function wechatLogout({ appId, isNew }: { appId: string; isNew: boolean }) {
  const videosApiToken = isNew ? process.env.WECHAT_NEW_TOKEN : process.env.WECHAT_TOKEN
  const url = isNew ? `${newBaseUrl}${wechatOutApi}` : `${baseUrl}${wechatOutApi}`
  return axios.post(
    url,
    {
      appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )
}

export async function postAuthorizeByWechat(data: {
  appId: string
  regionId: string
  token: string
  isNew: boolean
}) {
  const url = data.isNew ? `${newBaseUrl}${authorQrCodeApi}` : `${baseUrl}${authorQrCodeApi}`

  const videosApiToken = data.isNew ? process.env.WECHAT_NEW_TOKEN : process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      regionId: data.regionId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      qrData: string
      qrImgBase64: string
      uuid: string
      appId: string
    }
    msg: string
    ret: number
  } as { data: Record<string, any> }

  if (res.data.ret !== 200) {
    console.log(url)

    console.log(
      JSON.stringify({
        appId: data.appId,
        regionId: data.regionId,
        useProxy: true
      })
    )
    throw new BadRequestException(`[视频号]:${res.data.msg}`)
  }

  return {
    qrData: res.data.data.qrData,
    qrImgBase64: res.data.data.qrImgBase64,
    uuid: res.data.data.uuid,
    appId: res.data.data.appId
  }
}

export async function postCheckLoginByWechat(data: {
  appId: string
  uuid: string
  captchCode: string
  token: string
  isNew: boolean
}) {
  const url = data.isNew ? `${newBaseUrl}${checkLoginApi}` : `${baseUrl}${checkLoginApi}`

  const videosApiToken = data.isNew ? process.env.WECHAT_NEW_TOKEN : process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      uuid: data.uuid,
      captchCode: data.captchCode,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      uuid: string
      headImgUrl: string
      nickName: string
      expiredTime: number
      status: number
      loginInfo: {
        uin: number
        wxid: string
        nickName: string
        mobile: string
        alias: string
      }
      code: string
      msg: string
    }
    msg: string
    ret: number
  } as { data: Record<string, any> }

  console.log(url)

  console.log(
    JSON.stringify({
      appId: data.appId,
      uuid: data.uuid,
      captchCode: data.captchCode,
      useProxy: true
    })
  )

  console.log(res.data)

  return res.data
}

export async function postWechatProfile(data: { appId: string; token: string; isNew: boolean }) {
  const url = data.isNew ? `${newBaseUrl}${getProfile}` : `${baseUrl}${getProfile}`

  const videosApiToken = data.isNew ? process.env.WECHAT_NEW_TOKEN : process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      mainFinderUsername: string
      aliasInfo: Array<{
        nickname: string
        headImgUrl: string
        roleType: number
      }>
    }
    msg: string
    ret: number
  } as { data: Record<string, any> }

  if (res.data.ret !== 200) {
    throw new BadRequestException(`[视频号]:${res.data.msg}`)
  }

  return {
    mainFinderUsername: res.data.data.mainFinderUsername,
    aliasInfo: res.data.data.aliasInfo
  }
}

export async function checkOnline({
  appId,
  isNew
}: {
  appId: string
  token: string
  isNew: boolean
}) {
  const url = isNew ? `${newBaseUrl}${accountCheckOnline}` : `${baseUrl}${accountCheckOnline}`

  const videosApiToken = isNew ? process.env.WECHAT_NEW_TOKEN : process.env.WECHAT_TOKEN

  const res = await axios.post(
    url,
    {
      appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )

  return res.data
}

export async function getAllAccount(token: string, isNew: boolean) {
  const url = isNew ? `${newBaseUrl}${queryLoginWx}` : `${baseUrl}${queryLoginWx}`

  const videosApiToken = isNew ? process.env.WECHAT_NEW_TOKEN : process.env.WECHAT_TOKEN

  const res = await axios.post(url, null, {
    headers: {
      'VideosApi-token': videosApiToken
    }
  })

  return res.data.data
}
