import { Controller, Get } from '@nestjs/common'
import { ApiHeader, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'
import { OverviewService } from './overview.service'
import { OverviewDosageResponseDTO, OverviewResponseDTO } from './overview.dto'

@Controller('overview')
@ApiTags('概览')
export class OverviewController {
  constructor(private readonly overviewService: OverviewService) {}

  @Get()
  @ApiOperation({ summary: '获取概览信息' })
  @ApiOkResponse({ description: '操作成功', type: OverviewResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getOverview() {
    return this.overviewService.getOverview()
  }

  @ApiOperation({ summary: '获取团队使用额度' })
  @Get('dosage')
  @ApiOkResponse({ description: '操作成功', type: OverviewDosageResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getDosage() {
    return this.overviewService.getDosage()
  }
}
