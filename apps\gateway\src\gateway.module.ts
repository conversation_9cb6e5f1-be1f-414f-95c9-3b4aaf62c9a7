import { Module } from '@nestjs/common'
import { UserModule } from './modules/user/user.module'

import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core'
import { GlobalExceptionFilter } from './common/filters'

import { ResponseTransformInterceptor } from './common/interceptors'
import { TeamModule } from './modules/team/team.module'
import { AccountModule } from './modules/account/account.module'
import { AutoresponderModule } from './modules/autoresponder/autoresponder.module'
import { PublicModule } from './modules/public/public.module'
import { InteractModule } from './modules/interact/interact.module'
import { OrderModule } from './modules/order/order.module'
import { WebhookModule } from './modules/webhook/webhook.module'
import { OverviewModule } from './modules/overview/overview.module'
import { TokenGuard } from './common/guards'
import { baseModule } from './common/baseModule'
import { TaskModule } from './modules/task/task.module'
import { InvitationModule } from './modules/invitation/invitation.module'
import { VipModule } from './modules/vip/vip.module'
import { UserCouponsModule } from './modules/coupons/coupons.module'
import { GroupsModule } from './modules/group/group.module'
import { LabelModule } from './modules/label/label.module'
import { AliyunSlsModule } from './modules/aliyunSls/aliyunSls.module'
import { AliyunSlsService } from './modules/aliyunSls/aliyunSls.service'
import { OrderManageModule, TlsManageModule } from '@qdy/common'

@Module({
  imports: [
    ...baseModule,
    PublicModule,
    OverviewModule,
    OrderModule,
    VipModule,
    InteractModule,
    TeamModule,
    AutoresponderModule,
    AccountModule,
    UserModule,
    WebhookModule,
    TaskModule,
    InvitationModule,
    UserCouponsModule,
    GroupsModule,
    LabelModule,
    AliyunSlsModule,
    OrderManageModule,
    TlsManageModule
  ],
  controllers: [],
  providers: [
    AliyunSlsService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor
    },
    {
      provide: APP_GUARD,
      useClass: TokenGuard
    }
  ]
})
export class GatewayModule {}
