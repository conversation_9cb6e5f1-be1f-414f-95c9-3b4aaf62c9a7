import { Catch, ArgumentsHost, HttpException } from '@nestjs/common'
import { FastifyReply } from 'fastify'

@Catch(HttpException)
export class GlobalExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp()

    const reply = ctx.getResponse<FastifyReply>()

    const status = exception.getStatus()

    try {
      // @ts-expect-error - this is a private property
      const { message } = exception.response
      reply.code(status).send({
        statusCode: status,
        message: (Array.isArray(message) ? message[0] : message) || 'Internal Server Error'
      })
    } catch {
      reply.code(status).send({
        statusCode: status,
        message: exception.message || 'Internal Server Error'
      })
    }
  }
}
