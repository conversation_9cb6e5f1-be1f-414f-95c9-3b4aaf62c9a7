-- CreateTable
CREATE TABLE `ChannelAdminUser` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `nickname` VA<PERSON>HAR(191) NOT NULL DEFAULT '',
    `username` VA<PERSON><PERSON><PERSON>(191) NOT NULL,
    `password` VARCHAR(191) NOT NULL,
    `role` INTEGER NOT NULL,
    `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `salt` VARCHAR(191) NOT NULL,
    `channelId` INTEGER NULL,

    UNIQUE INDEX `ChannelAdminUser_username_key`(`username`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ChannelAdminUser` ADD CONSTRAINT `ChannelAdminUser_channelId_fkey` FOREIGN KEY (`channelId`) REFERENCES `Channel`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
