/* eslint-disable no-case-declarations */
import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from 'packages/mysql/lib'
import {
  ContractStatus,
  OrderPriceResponseDto,
  OrderRecord,
  OrderStatus,
  OrderType,
  PayType,
  PostCalculateOrderPriceRequestDTO,
  PostCreateOrderRequestDTO,
  PostGiftOrderRequestDTO,
  PostRefundRequestDTO,
  PostRenewOrderRequestDTO,
  PostUpgradeOrderRequestDTO,
  SalesType,
  UserCouponsStatus
} from './orderManage.dto'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { AlipaySdk } from 'alipay-sdk'
import WxPay from 'wechatpay-node-v3'
import fs from 'fs'
import { join } from 'path'
import axios from 'axios'
import { VIPOften } from './constant'
import { customAlphabet } from 'nanoid'
import dayjs from 'dayjs'
import { TeamMemberStatus } from 'apps/gateway/src/modules/team/team.dto'
import { AccountAccountsStatus } from 'apps/admin/src/modules/vip/vip.dto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { type Cache } from 'cache-manager'
import { type RedisStore } from 'cache-manager-ioredis-yet'

@Injectable()
export class OrderManageService {
  private firstMonthOrderPrice = 398 // 首月权益包单价

  private yearOrderPrice = 3980 // 年付权益包单价

  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 18)

  async calculateOrderPrice({
    userId,
    teamId,
    orderType,
    interestId,
    interestCount,
    month,
    days,
    couponId
  }: PostCalculateOrderPriceRequestDTO): Promise<OrderPriceResponseDto> {
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        user: true
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const interest = await this.prisma.interest.findUnique({
      where: {
        id: interestId
      }
    })

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })

    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    let orderAmount: number
    let couponAmount: any
    let discountAmount: any
    let tipsCn = ''
    let tips = ''

    const dayPrice =
      orderType === OrderRecord.Upgrade
        ? ((interestCount - team.vip.interestCount) * interest.price) / 30
        : (interestCount * interest.price) / 30

    switch (orderType) {
      case OrderRecord.Create: {
        if (team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
          throw new ForbiddenException('VIP未过期无需开通VIP')
        }

        if (month === 0 && days === 0) {
          throw new NotFoundException('参数错误')
        }

        if (month) {
          const vipOften = VIPOften.find((item) => item.mount === month)

          if (!vipOften) {
            throw new NotFoundException('权益月份不存在')
          }

          orderAmount = Math.trunc(interest.price * interestCount * vipOften.mount)

          switch (vipOften.mount) {
            case 1:
              if (team.salesType === SalesType.NotBuy) {
                // 首月购买
                discountAmount = orderAmount - Math.trunc(this.firstMonthOrderPrice * interestCount)
              }
              break
            case 12:
              // 年付优惠
              discountAmount = orderAmount - Math.trunc(this.yearOrderPrice * interestCount)
              break
          }
        } else {
          orderAmount = Math.trunc(dayPrice * days)
        }

        break
      }

      case OrderRecord.Upgrade: {
        if (!team.vip.expirationTime || team.vip.expirationTime.getTime() < Date.now()) {
          throw new ForbiddenException('您的VIP已过期')
        }

        const remainingDay = dayjs(team.vip.expirationTime).diff(dayjs(), 'day') + 1

        const needPayInterestCount = interestCount - team.vip.interestCount

        const needPayAmount = Math.trunc(
          Math.floor(remainingDay / 365) * this.yearOrderPrice * needPayInterestCount +
            Math.min(this.yearOrderPrice * needPayInterestCount, (remainingDay % 365) * dayPrice)
        )

        // 升级需要的费用, 需要升级到的数量-当前VIP的权益包的数量 * 权益包单价 / 30天 * 剩余天数
        orderAmount = Math.trunc(dayPrice * remainingDay)
        discountAmount = orderAmount - needPayAmount

        tipsCn = '升级权益包金额/30*剩余时长'
        tips = `${interest.price}*${interestCount - team.vip.interestCount}/30*${remainingDay}=￥${orderAmount}`

        break
      }

      case OrderRecord.Renew: {
        if (!team.vip.expirationTime || team.vip.expirationTime.getTime() < Date.now()) {
          throw new ForbiddenException('您的VIP已过期')
        }

        if (month === 0 && days === 0) {
          throw new NotFoundException('参数错误')
        }

        if (month) {
          const vipOften = VIPOften.find((item) => item.mount === month)

          if (!vipOften) {
            throw new NotFoundException('权益月份不存在')
          }

          orderAmount = Math.trunc(interest.price * vipOften.mount * team.vip.interestCount)
          switch (vipOften.mount) {
            case 1:
              if (team.salesType === SalesType.NotBuy) {
                // 首月购买
                discountAmount =
                  orderAmount - Math.trunc(this.firstMonthOrderPrice * team.vip.interestCount)
              }
              break
            case 12:
              // 年付优惠
              discountAmount =
                orderAmount - Math.trunc(this.yearOrderPrice * team.vip.interestCount)
              break
          }
        } else {
          orderAmount = Math.trunc(dayPrice * days)
        }

        break
      }
    }

    if (couponId) {
      const coupon = await this.prisma.usersCoupons.findFirst({
        where: {
          userId,
          id: couponId
        }
      })

      if (!coupon) {
        throw new NotFoundException('优惠券不存在')
      }

      if (coupon.status !== UserCouponsStatus.Normal) {
        throw new NotFoundException('该优惠券已经使用过')
      }

      if (new Date(coupon.expireTime).getTime() < Date.now()) {
        throw new NotFoundException('优惠券已过期')
      }

      if (orderAmount >= coupon.minimumSpendingAmount) {
        couponAmount = coupon.discountAmount
      }
    }

    return {
      orderAmount,
      discountAmount: discountAmount ?? 0,
      couponAmount: couponAmount ?? 0,
      tips,
      tipsCn
    }
  }

  /**
   * 开通订单
   */
  async createOrder({
    teamId,
    userId,
    interestCount,
    interestId,
    month,
    days,
    isPay,
    couponId,
    creatorId,
    creatorName,
    remark,
    payAmount
  }: PostCreateOrderRequestDTO) {
    if (month <= 0 && days <= 0) {
      throw new ForbiddenException('参数不合法')
    }

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        user: true,
        team: true
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const interest = await this.prisma.interest.findUnique({
      where: {
        id: interestId
      }
    })

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    let vipOften: {
      mount: number
      present: number
    } = { mount: 0, present: 0 }

    if (month > 0) {
      vipOften = VIPOften.find((item) => item.mount === month)

      if (!vipOften) {
        throw new NotFoundException('权益月份不存在')
      }
    }

    const vip = await this.prisma.vip.findUnique({
      where: {
        teamId: teamMember.teamId
      }
    })

    if (!vip) {
      throw new NotFoundException('VIP信息无法找到')
    }

    if (vip.expirationTime && vip.expirationTime.getTime() > Date.now()) {
      throw new ForbiddenException('VIP未过期无需开通VIP')
    }

    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 2 * 60 * 60 * 1000)
    const orderNo = this.nanoid()

    const dayPrice = (interestCount * interest.price) / 30

    const price = month > 0 ? interest.price * vipOften.mount * interestCount : dayPrice * days
    let dueAmount = price

    let usersCouponId: null | number = null
    let channelId: number

    switch (vipOften.mount) {
      case 1:
        if (teamMember.team.salesType === SalesType.NotBuy) {
          // 首月购买
          dueAmount = Math.trunc(this.firstMonthOrderPrice * interestCount)
        }
        break
      case 12:
        // 年付优惠
        dueAmount = Math.trunc(this.yearOrderPrice * interestCount)
        break
    }

    if (couponId) {
      const coupon = await this.prisma.usersCoupons.findUnique({
        where: {
          id: couponId,
          userId
        }
      })

      if (!coupon) {
        throw new NotFoundException('优惠券不存在')
      }

      if (coupon.status !== UserCouponsStatus.Normal) {
        throw new NotFoundException('该优惠券已经使用过')
      }

      if (new Date(coupon.expireTime).getTime() < Date.now()) {
        throw new NotFoundException('优惠券已过期')
      }
      if (dueAmount >= coupon.minimumSpendingAmount) {
        dueAmount -= coupon.discountAmount
        usersCouponId = coupon.id
      } else {
        throw new NotFoundException('优惠券不满足使用条件')
      }

      ;({ channelId } = coupon)
    }

    ;({ channelId } = teamMember.user)

    if (payAmount && creatorId) {
      // 后台管理系统创建订单
      dueAmount = Math.trunc(payAmount)
    }

    const salesType = await this.getTeamOrderSalesTypeCheck(teamId, isPay)

    await this.prisma.$transaction(async () => {
      const orderInfo = await this.prisma.order.create({
        data: {
          phone: teamMember.user.phone,
          channelId,
          orderNo,
          teamId: teamMember.teamId,
          vipId: vip.id,
          type: creatorId ? OrderType.SYSTEM : OrderType.ONLINE,
          price: Math.trunc(price),
          usersCouponId,
          dueAmount: Math.trunc(dueAmount),
          fromTime: currentDate,
          expireTime: towHours,
          orderStatus: OrderStatus.PENDING,
          vipMonth: vipOften.mount,
          freeMonth: vipOften.present,
          isPay,
          interestId,
          interestCount,
          creatorId,
          creatorName,
          remark,
          giftDays: days,
          days,
          salesType,
          orderType: OrderRecord.Create
        }
      })

      if (isPay && orderInfo.dueAmount > 0) {
        const alipayInfo = await this.alipayTradePagePay(
          orderNo,
          '青豆云权益开通VIP',
          towHours,
          orderInfo.dueAmount.toString()
        )

        const wechatPayInfo = await this.wechatNativeOrder(
          orderNo,
          '青豆云权益开通VIP',
          towHours,
          orderInfo.dueAmount
        )

        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.ALIPAY,
            payStatus: OrderStatus.PENDING,
            urlInfo: alipayInfo,
            callbackInfo: null
          }
        })

        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.WECHAT,
            payStatus: OrderStatus.PENDING,
            urlInfo: wechatPayInfo,
            callbackInfo: null
          }
        })
      }
    })

    return orderNo
  }

  /**
   * 升级订单
   */
  async upgradeOrder({
    teamId,
    userId,
    interestCount,
    interestId,
    isPay,
    couponId,
    creatorId,
    creatorName,
    remark,
    payAmount
  }: PostUpgradeOrderRequestDTO) {
    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })
    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    if (team.vip.expirationTime && team.vip.expirationTime.getTime() < Date.now()) {
      throw new ForbiddenException('您的VIP已过期')
    }

    if (team.vip.interestCount >= interestCount) {
      throw new ForbiddenException('权益包数量必须大于升级前的数量')
    }

    const interest = await this.prisma.interest.findUnique({
      where: {
        id: interestId
      }
    })

    const dayPrice = ((interestCount - team.vip.interestCount) * interest.price) / 30

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    const userInfo = await this.prisma.user.findUnique({
      where: {
        id: userId
      }
    })

    if (!userInfo) {
      throw new NotFoundException('用户信息不存在')
    }

    const remainingDay = dayjs(team.vip.expirationTime).diff(dayjs(), 'day') + 1

    const needPayInterestCount = interestCount - team.vip.interestCount

    // 升级需要的费用, 需要升级到的数量-当前VIP的权益包的数量 * 权益包单价 / 30天 * 剩余天数
    const price = Math.trunc(
      Math.floor(remainingDay / 365) * this.yearOrderPrice * needPayInterestCount +
        Math.min(this.yearOrderPrice * needPayInterestCount, (remainingDay % 365) * dayPrice)
    )

    let dueAmount = price

    let usersCouponId: null | number = null
    let channelId: number

    if (couponId) {
      const coupon = await this.prisma.usersCoupons.findFirst({
        where: {
          userId,
          id: couponId
        }
      })

      if (!coupon) {
        throw new NotFoundException('优惠券不存在')
      }

      if (coupon.status !== UserCouponsStatus.Normal) {
        throw new NotFoundException('该优惠券已经使用过')
      }

      if (new Date(coupon.expireTime).getTime() < Date.now()) {
        throw new NotFoundException('优惠券已过期')
      }

      if (dueAmount >= coupon.minimumSpendingAmount) {
        dueAmount -= coupon.discountAmount
        usersCouponId = coupon.id
      } else {
        throw new ForbiddenException('优惠券不满足使用条件')
      }
      ;({ channelId } = coupon)
    }

    ;({ channelId } = userInfo)

    if (payAmount && creatorId) {
      // 后台管理系统创建订单
      dueAmount = Math.trunc(payAmount)
    }

    const salesType = await this.getTeamOrderSalesTypeCheck(teamId, isPay)

    const orderNo = this.nanoid()
    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 2 * 60 * 60 * 1000)

    await this.prisma.$transaction(async () => {
      const orderInfo = await this.prisma.order.create({
        data: {
          isUpgrade: true,
          phone: userInfo.phone,
          orderNo,
          channelId,
          teamId: team.id,
          vipId: team.vip.id,
          type: creatorId ? OrderType.SYSTEM : OrderType.ONLINE,
          price: Math.trunc(price),
          usersCouponId,
          dueAmount: Math.trunc(dueAmount),
          fromTime: currentDate,
          expireTime: towHours,
          orderStatus: OrderStatus.PENDING,
          vipMonth: 0,
          freeMonth: 0,
          isPay,
          interestId,
          interestCount,
          creatorId,
          creatorName,
          remark,
          salesType,
          priceDiff: 0,
          orderType: OrderRecord.Upgrade,
          remainingDays: remainingDay
        }
      })

      if (isPay && orderInfo.dueAmount > 0) {
        const alipayInfo = await this.alipayTradePagePay(
          orderNo,
          '青豆云权益升级',
          towHours,
          orderInfo.dueAmount.toString()
        )
        const wechatPayInfo = await this.wechatNativeOrder(
          orderNo,
          '青豆云权益升级',
          towHours,
          orderInfo.dueAmount
        )

        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.ALIPAY,
            payStatus: OrderStatus.PENDING,
            urlInfo: alipayInfo,
            callbackInfo: null
          }
        })
        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.WECHAT,
            payStatus: OrderStatus.PENDING,
            urlInfo: wechatPayInfo,
            callbackInfo: null
          }
        })
      }
    })

    return orderNo
  }

  /**
   * 续费订单
   */
  async renewOrder({
    teamId,
    userId,
    month,
    days,
    interestId,
    isPay,
    isCorporateTransfer,
    couponId,
    creatorId,
    creatorName,
    remark,
    payAmount
  }: PostRenewOrderRequestDTO) {
    if (month <= 0 && days <= 0) {
      throw new ForbiddenException('参数不合法')
    }

    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })
    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    if (team.vip.expirationTime && team.vip.expirationTime.getTime() < Date.now()) {
      throw new ForbiddenException('您的VIP已过期')
    }

    let vipOften: {
      mount: number
      present: number
    } = { mount: 0, present: 0 }

    if (month > 0) {
      vipOften = VIPOften.find((item) => item.mount === month)

      if (!vipOften) {
        throw new NotFoundException('权益月份不存在')
      }
    }

    const interest = await this.prisma.interest.findUnique({
      where: {
        id: interestId
      }
    })

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    const userInfo = await this.prisma.user.findUnique({
      where: {
        id: userId
      }
    })

    if (!userInfo) {
      throw new NotFoundException('用户信息不存在')
    }

    const dayPrice = (team.vip.interestCount * interest.price) / 30

    const price =
      month > 0 ? interest.price * vipOften.mount * team.vip.interestCount : dayPrice * days
    let dueAmount = price

    let usersCouponId: null | number = null
    let channelId: number

    switch (vipOften.mount) {
      case 1:
        if (team.salesType === SalesType.NotBuy) {
          // 首月购买
          dueAmount = Math.trunc(this.firstMonthOrderPrice * team.vip.interestCount)
        }
        break
      case 12:
        // 年付优惠
        dueAmount = Math.trunc(this.yearOrderPrice * team.vip.interestCount)
        break
    }

    if (couponId) {
      const coupon = await this.prisma.usersCoupons.findFirst({
        where: {
          userId,
          id: couponId
        }
      })

      if (!coupon) {
        throw new NotFoundException('优惠券不存在')
      }

      if (coupon.status !== UserCouponsStatus.Normal) {
        throw new NotFoundException('该优惠券已经使用过')
      }

      if (new Date(coupon.expireTime).getTime() < Date.now()) {
        throw new NotFoundException('优惠券已过期')
      }

      if (dueAmount >= coupon.minimumSpendingAmount) {
        dueAmount -= coupon.discountAmount
        usersCouponId = coupon.id
      } else {
        throw new ForbiddenException('优惠券不满足使用条件')
      }
      ;({ channelId } = coupon)
    }

    ;({ channelId } = userInfo)

    if (payAmount && creatorId) {
      // 后台管理系统创建订单
      dueAmount = payAmount
    }

    const salesType = await this.getTeamOrderSalesTypeCheck(teamId, isPay)

    const orderNo = this.nanoid()
    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 2 * 60 * 60 * 1000)

    await this.prisma.$transaction(async () => {
      const orderInfo = await this.prisma.order.create({
        data: {
          phone: userInfo.phone,
          orderNo,
          channelId,
          teamId: team.id,
          vipId: team.vip.id,
          type: creatorId ? OrderType.SYSTEM : OrderType.ONLINE,
          price: Math.trunc(price),
          usersCouponId,
          dueAmount: Math.trunc(dueAmount),
          fromTime: currentDate,
          expireTime: towHours,
          orderStatus: OrderStatus.PENDING,
          vipMonth: vipOften.mount,
          freeMonth: vipOften.present,
          payType: isCorporateTransfer ? PayType.CORPORATETRANSFER : '',
          isPay,
          interestId,
          creatorId,
          creatorName,
          remark,
          giftDays: days,
          days,
          salesType,
          interestCount: team.vip.interestCount,
          priceDiff: 0,
          orderType: OrderRecord.Renew
        }
      })

      if (isPay && orderInfo.dueAmount > 0) {
        const alipayInfo = await this.alipayTradePagePay(
          orderNo,
          '青豆云权益续费',
          towHours,
          orderInfo.dueAmount.toString()
        )
        const wechatPayInfo = await this.wechatNativeOrder(
          orderNo,
          '青豆云权益续费',
          towHours,
          orderInfo.dueAmount
        )

        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.ALIPAY,
            payStatus: OrderStatus.PENDING,
            urlInfo: alipayInfo,
            callbackInfo: null
          }
        })
        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.WECHAT,
            payStatus: OrderStatus.PENDING,
            urlInfo: wechatPayInfo,
            callbackInfo: null
          }
        })
      }
    })

    return orderNo
  }

  async giftOrder({
    teamId,
    userId,
    interestId,
    creatorId,
    creatorName,
    remark,
    giftDays,
    isRegisterOrder
  }: PostGiftOrderRequestDTO) {
    let interestCount = 1
    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })

    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    const interest = await this.prisma.interest.findUnique({
      where: {
        id: interestId
      }
    })

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    let orderType = OrderRecord.Create

    if (!isRegisterOrder) {
      if (team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
        orderType = OrderRecord.Renew
      }
      ;({ interestCount } = team.vip)
    }

    const price = (interest.price / 30) * interestCount * giftDays

    const userInfo = await this.prisma.user.findUnique({
      where: {
        id: userId
      }
    })

    const startTime = team.vip.expirationTime ? team.vip.expirationTime : new Date()

    const toTime = dayjs(startTime).add(giftDays, 'day').toDate()

    const orderNo = this.nanoid()
    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 2 * 60 * 60 * 1000)

    await this.prisma.order.create({
      data: {
        phone: userInfo.phone,
        orderNo,
        teamId: team.id,
        vipId: team.vip.id,
        type: OrderType.SYSTEM,
        price: Math.trunc(price),
        dueAmount: 0,
        payAmount: 0,
        payTime: currentDate,
        payType: PayType.CORPORATETRANSFER,
        isPay: false,
        fromTime: currentDate,
        toTime,
        expireTime: towHours,
        orderStatus: OrderStatus.SUCCESS,
        vipMonth: 0,
        freeMonth: 0,
        interestId: interest.id,
        interestCount,
        priceDiff: 0,
        isGiftOrder: true,
        giftDays,
        orderType,
        creatorId,
        creatorName,
        remark
      }
    })

    return orderNo
  }

  async iosOrder({
    teamId,
    userId,
    interestCount,
    interestId,
    month,
    isPay,
    couponId,
    creatorId,
    creatorName,
    remark,
    payAmount,
    transactionId
  }: PostCreateOrderRequestDTO) {
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        user: true,
        team: true
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const interest = await this.prisma.interest.findUnique({
      where: {
        id: interestId
      }
    })

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    const vipOften = VIPOften.find((item) => item.mount === month)

    if (!vipOften) {
      throw new NotFoundException('权益月份不存在')
    }

    const vip = await this.prisma.vip.findUnique({
      where: {
        teamId: teamMember.teamId
      }
    })

    if (!vip) {
      throw new NotFoundException('VIP信息无法找到')
    }

    if (vip.expirationTime && vip.expirationTime.getTime() > Date.now()) {
      throw new ForbiddenException('VIP未过期无需开通VIP')
    }

    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 2 * 60 * 60 * 1000)
    const orderNo = this.nanoid()
    const price = interest.price * vipOften.mount * interestCount
    let dueAmount = price

    let usersCouponId: null | number = null
    let channelId: number

    if (couponId) {
      const coupon = await this.prisma.usersCoupons.findUnique({
        where: {
          id: couponId,
          userId
        }
      })

      if (!coupon) {
        throw new NotFoundException('优惠券不存在')
      }

      if (coupon.status !== UserCouponsStatus.Normal) {
        throw new NotFoundException('该优惠券已经使用过')
      }

      if (new Date(coupon.expireTime).getTime() < Date.now()) {
        throw new NotFoundException('优惠券已过期')
      }
      if (price >= coupon.minimumSpendingAmount) {
        dueAmount = price - coupon.discountAmount
        usersCouponId = coupon.id
      } else {
        throw new NotFoundException('优惠券不满足使用条件')
      }

      ;({ channelId } = coupon)
    }

    ;({ channelId } = teamMember.user)

    if (payAmount && creatorId) {
      // 后台管理系统创建订单
      dueAmount = payAmount
    }

    const salesType = await this.getTeamOrderSalesTypeCheck(teamId, true)

    await this.prisma.$transaction(async () => {
      const orderInfo = await this.prisma.order.create({
        data: {
          phone: teamMember.user.phone,
          channelId,
          orderNo,
          teamId: teamMember.teamId,
          vipId: vip.id,
          type: creatorId ? OrderType.SYSTEM : OrderType.ONLINE,
          price: Math.trunc(price),
          usersCouponId,
          dueAmount: parseFloat(dueAmount.toFixed(2)),
          fromTime: currentDate,
          expireTime: towHours,
          orderStatus: OrderStatus.PENDING,
          vipMonth: vipOften.mount,
          freeMonth: vipOften.present,
          interestId,
          interestCount,
          creatorId,
          creatorName,
          remark,
          salesType,
          orderType: OrderRecord.Create,
          transactionId
        }
      })

      if (isPay && orderInfo.dueAmount > 0) {
        const alipayInfo = await this.alipayTradePagePay(
          orderNo,
          '青豆云权益开通VIP',
          towHours,
          orderInfo.dueAmount.toString()
        )

        const wechatPayInfo = await this.wechatNativeOrder(
          orderNo,
          '青豆云权益开通VIP',
          towHours,
          orderInfo.dueAmount
        )

        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.ALIPAY,
            payStatus: OrderStatus.PENDING,
            urlInfo: alipayInfo,
            callbackInfo: null
          }
        })

        await this.prisma.orderInfo.create({
          data: {
            orderId: orderInfo.id,
            orderNo,
            payType: PayType.WECHAT,
            payStatus: OrderStatus.PENDING,
            urlInfo: wechatPayInfo,
            callbackInfo: null
          }
        })
      }
    })

    return orderNo
  }

  async orderListByRefund(teamId: number) {
    // 已经使用过的订单
    const useOrder = await this.prisma.contract.findMany({
      where: {
        teamId,
        status: ContractStatus.Normal,
        startTime: {
          lt: new Date()
        },
        endTime: {
          gt: new Date()
        }
      },
      include: {
        order: {
          select: {
            orderNo: true,
            payAmount: true
          }
        }
      }
    })

    const orderList = []

    useOrder.forEach((item) => {
      const orderDays = dayjs(item.endTime).diff(item.startTime, 'day') + 1
      const days = dayjs(item.endTime).diff(dayjs(), 'day') + 1

      if (orderDays > 0 && days > 0 && orderDays >= days) {
        const amountByDay = item.amount / orderDays

        orderList.push({
          orderNo: item.orderNo,
          refundAmount: item.amount,
          actualRefundAmount: Math.trunc(amountByDay * days)
        })
      }
    })

    // 还未使用过的订单
    const unUserOrder = await this.prisma.contract.findMany({
      where: {
        teamId,
        status: ContractStatus.Normal,
        startTime: {
          gt: new Date()
        }
      },
      include: {
        order: {
          select: {
            orderNo: true,
            payAmount: true
          }
        }
      }
    })

    unUserOrder.forEach((item) => {
      orderList.push({
        orderNo: item.orderNo,
        refundAmount: item.amount,
        actualRefundAmount: item.amount
      })
    })

    return orderList
  }

  /**
   * 订单退费
   */
  async refundOrder({ teamId, actualRefundAmount, remark }: PostRefundRequestDTO) {
    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })

    const refundOrderList = await this.orderListByRefund(teamId)

    if (refundOrderList && refundOrderList.length <= 0) {
      throw new ForbiddenException('当前团队没有订单可退')
    }

    const refundTotal = refundOrderList.reduce(
      (accumulator, current) => accumulator + current.actualRefundAmount,
      0
    )

    // 已经使用过的订单
    await this.prisma.contract.updateMany({
      where: {
        teamId,
        startTime: {
          lt: new Date()
        },
        endTime: {
          gt: new Date()
        }
      },
      data: {
        status: ContractStatus.isRefund
      }
    })

    // 还未使用过的订单
    await this.prisma.contract.updateMany({
      where: {
        teamId,
        startTime: {
          gt: new Date()
        }
      },
      data: {
        status: ContractStatus.isRefund
      }
    })

    const refundNo = this.nanoid()

    await Promise.all([
      this.prisma.refund.create({
        data: {
          refundNo,
          teamId,
          createTime: new Date(),
          refundAmount: refundTotal,
          actualRefundAmount,
          remark,
          orderInfo: refundOrderList
        }
      }),
      this.prisma.vip.update({
        where: {
          id: team.vip.id
        },
        data: {
          expirationTime: new Date(),
          messageLimit: 0,
          platformAccountNumberLimit: 0,
          teamMemberNumberLimit: 0
        }
      }),
      this.prisma.teamMember.updateMany({
        where: {
          teamId: team.id,
          NOT: {
            userId: team.ownerId
          }
        },
        data: {
          status: TeamMemberStatus.Disable
        }
      }),
      this.prisma.platformAccount.updateMany({
        where: {
          teamId
        },
        data: {
          status: AccountAccountsStatus.Disable,
          expiresIn: 0
        }
      }),
      this.prisma.order.updateMany({
        where: {
          teamId,
          orderStatus: OrderStatus.SUCCESS,
          toTime: {
            gte: new Date()
          }
        },
        data: {
          orderStatus: OrderStatus.REFUND
        }
      })
    ])
  }

  async handleCompletedOrder(orderNo: string) {
    try {
      const order = await this.prisma.order.findUnique({
        where: {
          orderNo
        },
        include: {
          team: true,
          vip: true,
          interest: true
        }
      })

      if (!order) {
        throw new NotFoundException('订单不存在')
      }

      if (!order.interest) {
        throw new NotFoundException('订单产品不存在')
      }

      if (!order.vip) {
        throw new NotFoundException('vip信息不存在')
      }

      if (order.orderStatus !== 'success') {
        throw new ForbiddenException('订单未完成')
      }

      let startTime = order.payTime
      let expirationTime = new Date()

      const messageLimit = order.interest.messageCount * order.interestCount
      const teamMemberNumberLimit = order.interest.memberCount * order.interestCount
      const platformAccountNumberLimit = order.interest.platformAccountCount * order.interestCount
      const douyinCommentNumberLimit = order.interestCount * order.interest.douyinCommentCount

      const contractList: {
        orderId: number
        orderNo: string
        startTime: Date
        endTime: Date
        amount: number
        teamId: number
        interestId: number
        interestCount: number
        createTime: Date
        status: number
        isFree: boolean
      }[] = []

      // 修改VIP相关信息
      if (order.usersCouponId) {
        // 消耗优惠券
        await this.prisma.usersCoupons.update({
          where: {
            id: order.usersCouponId
          },
          data: {
            status: UserCouponsStatus.used,
            orderNo: order?.orderNo,
            teamId: order?.teamId,
            castTime: new Date()
          }
        })
        const coupon = await this.prisma.usersCoupons.findUnique({
          where: {
            id: order.usersCouponId
          }
        })
        // 更新管理侧优惠券激活数量
        await this.prisma.coupons.update({
          where: {
            id: coupon.couponsId
          },
          data: {
            activeAmount: { increment: 1 }
          }
        })

        const user = await this.prisma.user.findUnique({
          where: {
            phone: order.phone
          }
        })

        if (!user.channelId && coupon.channelId) {
          await this.prisma.user.update({
            where: {
              id: user.id
            },
            data: {
              channelId: coupon.channelId
            }
          })
        }
      }

      if (order.isGiftOrder) {
        startTime = order.vip.expirationTime > startTime ? order.vip.expirationTime : startTime

        expirationTime =
          order.vipMonth > 0
            ? dayjs(startTime)
                .add(order.vipMonth + order.freeMonth, 'month')
                .toDate()
            : dayjs(startTime).add(order.giftDays, 'day').toDate()

        contractList.push({
          orderId: order.id,
          orderNo: order.orderNo,
          startTime,
          endTime: expirationTime,
          amount: order.payAmount ?? 0,
          teamId: order.teamId,
          interestId: order.interestId,
          interestCount: order.interestCount,
          createTime: new Date(),
          status: ContractStatus.Normal,
          isFree: false
        })
      } else {
        switch (order.orderType) {
          case OrderRecord.Create:
            expirationTime =
              order.vipMonth > 0
                ? dayjs(startTime)
                    .add(order.vipMonth + order.freeMonth, 'month')
                    .toDate()
                : dayjs(startTime).add(order.days, 'day').toDate()

            if (order.vipMonth > 0) {
              if (order.freeMonth > 0) {
                contractList.push({
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime,
                  endTime: dayjs(startTime).add(order.freeMonth, 'month').toDate(),
                  amount: 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: new Date(),
                  status: ContractStatus.Normal,
                  isFree: true
                })

                contractList.push({
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(startTime).add(order.freeMonth, 'month').toDate(),
                  endTime: dayjs(startTime)
                    .add(order.vipMonth + order.freeMonth, 'month')
                    .toDate(),
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: new Date(),
                  status: ContractStatus.Normal,
                  isFree: false
                })
              } else {
                contractList.push({
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime,
                  endTime: dayjs(startTime).add(order.vipMonth, 'month').toDate(),
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: new Date(),
                  status: ContractStatus.Normal,
                  isFree: false
                })
              }
            } else if (order.days > 0) {
              contractList.push({
                orderId: order.id,
                orderNo: order.orderNo,
                startTime,
                endTime: dayjs(startTime).add(order.days, 'day').toDate(),
                amount: order.payAmount ?? 0,
                teamId: order.teamId,
                interestId: order.interestId,
                interestCount: order.interestCount,
                createTime: new Date(),
                status: ContractStatus.Normal,
                isFree: false
              })
            }
            break
          case OrderRecord.Upgrade:
            ;({ expirationTime } = order.vip)

            contractList.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime,
              endTime: expirationTime,
              amount: order.payAmount ?? 0,
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount,
              createTime: new Date(),
              status: ContractStatus.Normal,
              isFree: false
            })

            break
          case OrderRecord.Renew:
            startTime = order.vip.expirationTime
            expirationTime =
              order.vipMonth > 0
                ? dayjs(startTime)
                    .add(order.vipMonth + order.freeMonth, 'month')
                    .toDate()
                : dayjs(startTime).add(order.days, 'day').toDate()

            if (order.vipMonth > 0) {
              if (order.freeMonth > 0) {
                contractList.push({
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime,
                  endTime: dayjs(startTime).add(order.freeMonth, 'month').toDate(),
                  amount: 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: new Date(),
                  status: ContractStatus.Normal,
                  isFree: true
                })

                contractList.push({
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(startTime).add(order.freeMonth, 'month').toDate(),
                  endTime: dayjs(startTime)
                    .add(order.vipMonth + order.freeMonth, 'month')
                    .toDate(),
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: new Date(),
                  status: ContractStatus.Normal,
                  isFree: false
                })
              } else {
                contractList.push({
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime,
                  endTime: dayjs(startTime)
                    .add(order.vipMonth + order.freeMonth, 'month')
                    .toDate(),
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: new Date(),
                  status: ContractStatus.Normal,
                  isFree: false
                })
              }
            } else if (order.days > 0) {
              contractList.push({
                orderId: order.id,
                orderNo: order.orderNo,
                startTime,
                endTime: dayjs(startTime).add(order.days, 'day').toDate(),
                amount: order.payAmount ?? 0,
                teamId: order.teamId,
                interestId: order.interestId,
                interestCount: order.interestCount,
                createTime: new Date(),
                status: ContractStatus.Normal,
                isFree: false
              })
            }
            break
          case OrderRecord.Gift:
            startTime = order.vip.expirationTime
            expirationTime = dayjs(startTime).add(order.giftDays, 'day').toDate()

            contractList.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime,
              endTime: dayjs(startTime).add(order.giftDays, 'day').toDate(),
              amount: order.payAmount ?? 0,
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount,
              createTime: new Date(),
              status: ContractStatus.Normal,
              isFree: false
            })
            break
        }
      }

      if (order.orderType === OrderRecord.Upgrade) {
        const douyinComment = (await this.cacheManager.get(`douyinComment:${order.teamId}`)) as {
          residueCount: number
        }

        if (douyinComment) {
          try {
            const douyinCommentNumber =
              (order.interestCount - order.vip.interestCount) * order.interest.douyinCommentCount

            const now = dayjs().tz('Asia/Shanghai')

            let today8AM = now.startOf('day').hour(8)

            if (now.hour() >= 8) {
              today8AM = today8AM.add(1, 'day')
            }

            const ttlMilliseconds = today8AM.diff(now, 'millisecond')

            await this.cacheManager.set(
              `douyinComment:${order.teamId}`,
              {
                residueCount: Number(douyinCommentNumber + douyinComment.residueCount)
              },
              ttlMilliseconds
            )
          } catch (error) {
            // eslint-disable-next-line no-console
            console.log(error.message)
          }
        }
      }

      await this.prisma.$transaction(async () => {
        await this.prisma.vip.update({
          where: {
            id: order.vip.id
          },
          data: {
            messageLimit,
            teamMemberNumberLimit,
            platformAccountNumberLimit,
            douyinCommentNumberLimit,
            expirationTime,
            interestCount: order.interestCount,
            lastOrderNo: order.orderNo
          }
        })

        if (order.payType === PayType.CORPORATETRANSFER) {
          await this.prisma.order.update({
            where: {
              id: order.id
            },
            data: {
              toTime: expirationTime
            }
          })
        }

        // 合同模型增加相关数据
        await this.prisma.contract.createMany({
          data: contractList
        })
      })

      if (order.salesType !== SalesType.NotBuy) {
        // 当订单销售类型不是"未购买"
        if (order.team.salesType !== SalesType.ReBuy) {
          // 当团队不是重购团队
          await this.prisma.team.update({
            where: {
              id: order.teamId
            },
            data: {
              salesType: order.salesType
            }
          })
        }
      }
    } catch (error) {
      throw new ForbiddenException('订单处理错误', error.msg)
    }
  }

  async alipayTradePagePay(
    orderNo: string,
    subject: string,
    timeExpire: Date,
    totalAmount: string
  ) {
    const { alipay } = this.configService.get<RootConfigMap['app']>('app')

    const alipaySdk = new AlipaySdk({
      // 设置应用 ID
      appId: alipay.appId,
      // 设置应用私钥
      privateKey: alipay.privateKey,
      // 设置支付宝公钥
      alipayPublicKey: alipay.alipayPublicKey,
      // 密钥类型，请与生成的密钥格式保持一致，参考平台配置一节
      keyType: 'PKCS8',

      gateway: 'https://openapi-sandbox.dl.alipaydev.com/gateway.do'
      // 设置网关地址，默认是 https://openapi.alipay.com
      // endpoint: 'https://openapi.alipay.com',
    })

    const result = await alipaySdk.curl('POST', '/v3/alipay/trade/precreate', {
      body: {
        out_trade_no: orderNo,
        subject,
        total_amount: totalAmount,
        notify_url: `${process.env.API_BASE_URL}/${process.env.ALIPAY_WEBHOOK_URL}`
      }
    })

    if (result.responseHttpStatus !== 200) {
      throw new ForbiddenException('支付宝生成二维码失败')
    }

    return result.data.qr_code
  }

  async wechatNativeOrder(orderNo: string, subject: string, timeExpire: Date, totalAmount: number) {
    const { wechatPay } = this.configService.get<RootConfigMap['app']>('app')

    const pay = new WxPay({
      appid: wechatPay.appId,
      mchid: wechatPay.mchId,
      serial_no: wechatPay.serialNo,
      key: wechatPay.apiV3Key,
      publicKey: fs.readFileSync(join(__dirname, '../cert', 'apiclient_cert.pem')), // 公钥
      privateKey: fs.readFileSync(join(__dirname, '../cert', 'apiclient_key.pem')) // 秘钥
    })

    const url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/native'

    const body = {
      appid: wechatPay.appId,
      mchid: wechatPay.mchId,
      description: subject,
      out_trade_no: orderNo,
      time_expire: timeExpire,
      notify_url: `${process.env.API_BASE_URL}/${process.env.WECHATPAY_WEBHOOK_URL}`,
      amount: {
        total: Math.round(totalAmount * 100),
        currency: 'CNY'
      }
    }

    const method = 'POST'
    const canonicalUrl = '/v3/pay/transactions/native'

    const noncestr = Math.random().toString(36).substring(2, 15)
    const timestamp = Math.floor(Date.now() / 1000).toString()

    const signature = pay.getSignature(method, noncestr, timestamp, canonicalUrl, body)
    const authorization = pay.getAuthorization(noncestr, timestamp, signature)

    const headers = {
      'Content-Type': 'application/json',
      Authorization: authorization
    }

    try {
      const response = await axios.post(url, body, { headers })

      if (response.status === 200) {
        return response.data.code_url
      }

      throw new ForbiddenException('生成微信二维码失败')
    } catch (error) {
      throw new ForbiddenException('生成微信二维码失败')
    }
  }

  async getTeamOrderSalesTypeCheck(teamId: number, isPay: boolean): Promise<SalesType> {
    const teamInfo = await this.prisma.team.findUnique({
      where: {
        id: teamId
      }
    })

    if (isPay) {
      if (teamInfo.salesType === SalesType.NotBuy || teamInfo.salesType === '') {
        return SalesType.FirstBuy
      }
      if (teamInfo.salesType === SalesType.FirstBuy) {
        return SalesType.ReBuy
      }
      if (teamInfo.salesType === SalesType.ReBuy) {
        return SalesType.ReBuy
      }
    }
    return SalesType.NotBuy
  }
}
