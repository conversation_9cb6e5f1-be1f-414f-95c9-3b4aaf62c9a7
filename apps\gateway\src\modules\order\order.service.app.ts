import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import {
  AppStoreServerAPIClient,
  Environment,
  JWSTransactionDecodedPayload
} from '@apple/app-store-server-library'
import { readFileSync } from 'fs'
import { join } from 'path'
import { AppPurchasesRequestDTO, OrderStatus } from './order.dto'
import { OrderManageService, TlsManageService } from '@qdy/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { PrismaService } from '@qdy/mysql'
import * as jwt from 'jsonwebtoken'
import axios from 'axios'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'

const filePath = join(process.cwd(), 'apple', 'SubscriptionKey_BWTY9ZSN4G.p8')

@Injectable()
export class OrderAppService {
  logger = new Logger('OrderAppService')

  client: AppStoreServerAPIClient

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly prisma: PrismaService,
    private readonly orderManageService: OrderManageService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly tlsManageService: TlsManageService
  ) {
    this.client = new AppStoreServerAPIClient(
      readFileSync(filePath).toString(),
      'BWTY9ZSN4G',
      '14759ce8-3761-4004-aed1-ce8a57ce4627',
      'com.qdy.app',
      process.env.APPLE_ENV as Environment
    )
  }

  async inAppPurchases(body: AppPurchasesRequestDTO) {
    this.tlsManageService.putLogs({
      logData: `ios支付(${body.transactionId})`,
      logLevel: 'info',
      requestUri: body.transactionId,
      jobStatus: 'inAppPurchases'
    })
    const { user } = this.request

    const privateKey = readFileSync(filePath).toString()
    const token = jwt.sign(
      {
        iss: '14759ce8-3761-4004-aed1-ce8a57ce4627',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 60 * 60,
        aud: 'appstoreconnect-v1',
        bid: 'com.qdy.app'
      },
      privateKey,
      {
        algorithm: 'ES256',
        header: { kid: 'BWTY9ZSN4G' }
      }
    )

    // 判断如果不是沙盒的时候，返回404的情况下，继续用沙盒模式去查询并处理后面的逻辑
    let signedTransactionInfo
    try {
      const response = await axios.get(
        `https://api.storekit.itunes.apple.com/inApps/v1/transactions/${body.transactionId}`,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      )

      // eslint-disable-next-line prefer-destructuring
      signedTransactionInfo = response.data.signedTransactionInfo
    } catch (err) {
      if (err.response?.status === 404) {
        try {
          const sandboxResponse = await axios.get(
            `https://api.storekit-sandbox.itunes.apple.com/inApps/v1/transactions/${body.transactionId}`,
            {
              headers: { Authorization: `Bearer ${token}` }
            }
          )

          // eslint-disable-next-line prefer-destructuring
          signedTransactionInfo = sandboxResponse.data.signedTransactionInfo
        } catch (sandboxErr) {
          this.tlsManageService.putLogs({
            logData: `ios支付(${body.transactionId})`,
            logLevel: 'error',
            requestUri: JSON.stringify(sandboxErr),
            jobStatus: 'inAppPurchases'
          })
          this.logger.error(sandboxErr)
          throw new ForbiddenException('获取苹果支付信息失败')
        }
      } else {
        this.tlsManageService.putLogs({
          logData: `ios支付(${body.transactionId})`,
          logLevel: 'error',
          requestUri: JSON.stringify(err),
          jobStatus: 'inAppPurchases'
        })
        this.logger.error(err)
        throw new ForbiddenException('获取苹果支付信息失败')
      }
    }

    // const res = await this.client.getTransactionInfo(body.transactionId)

    if (signedTransactionInfo === '') {
      this.tlsManageService.putLogs({
        logData: `ios支付(${body.transactionId})`,
        logLevel: 'error',
        requestUri: JSON.stringify('获取苹果支付信息失败'),
        jobStatus: 'inAppPurchases'
      })
      throw new ForbiddenException('获取苹果支付信息失败')
    }

    const transactionInfo: JWSTransactionDecodedPayload = jwt.decode(signedTransactionInfo)

    this.tlsManageService.putLogs({
      logData: 'transactionInfo',
      logLevel: 'info',
      requestUri: JSON.stringify(transactionInfo),
      jobStatus: 'inAppPurchases'
    })

    const orderInfo = await this.prisma.order.findFirst({
      where: {
        transactionId: body.transactionId,
        orderStatus: OrderStatus.SUCCESS
      }
    })

    if (orderInfo) {
      throw new ForbiddenException('此订单已有相关订单')
    }

    if (
      transactionInfo.bundleId === 'com.qdy.app' &&
      transactionInfo.inAppOwnershipType === 'PURCHASED'
    ) {
      const interest = await this.prisma.interest.findFirst()

      const orderNo = await this.orderManageService.iosOrder({
        teamId: user.currentTeamId,
        userId: user.id,
        interestCount: 1,
        days: 0,
        interestId: interest.id,
        month: body.month,
        isPay: false,
        isCorporateTransfer: false,
        transactionId: body.transactionId
      })

      const gmtPayment = new Date()

      await this.prisma.order.update({
        where: {
          orderNo
        },
        data: {
          payTime: gmtPayment,
          orderStatus: OrderStatus.SUCCESS,
          payAmount:
            transactionInfo.environment === 'Sandbox'
              ? null
              : transactionInfo.productId === 'qdy.com.2988'
                ? 3988
                : 498,
          remark: `ios支付(${transactionInfo.environment}-${body.transactionId})`,
          transactionId: body.transactionId,
          payType: 'iosPay'
        }
      })

      await this.orderManageService.handleCompletedOrder(orderNo)

      await this.cacheManager.del(`overview:${user.currentTeamId}`)
    }
  }
}
