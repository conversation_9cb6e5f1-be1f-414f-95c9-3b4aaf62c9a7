import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class InvitationEntity {
  /**
   * 邀请人
   */
  @Prop({
    type: Number,
    required: true
  })
  inviterUserId: number

  /**
   * 被邀请人
   */
  @Prop({
    type: Number,
    required: true
  })
  inviteeUserId: number

  /**
   * 邀请进入的团队id
   */
  @Prop({
    type: Number,
    required: true
  })
  teamId: number

  /**
   * 邀请状态
   */
  @Prop({
    type: String,
    enum: ['pending', 'accepted', 'rejected']
  })
  status: string


  @Prop({
    type: Number,
    default: () => Date.now()
  })
  createTime: number
}

export const InvitationSchema: ModelDefinition = {
  name: InvitationEntity.name,
  schema: SchemaFactory.createForClass(InvitationEntity)
}

export const InvitationMongoose = MongooseModule.forFeature([InvitationSchema])