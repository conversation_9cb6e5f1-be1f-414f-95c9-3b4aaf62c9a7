import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'

const logger = new Logger('account external')

const authorizeAccountApi = 'https://open.douyin.com/oauth/access_token/'
const authorizeAccountInfoApi = 'https://open.douyin.com/oauth/userinfo/'
const authorizeAccountRefreshApi = 'https://open.douyin.com/oauth/renew_refresh_token/'
const authorizeAccountRefreshAccApi = 'https://open.douyin.com/oauth/refresh_token/'
const authorizeRoleApi = 'https://open.douyin.com/api/douyin/v1/role/check/'
const postRetainConsultCardApi = 'https://open.douyin.com/im/save/retain_consult_card/'
const getRetainConsultCardApi = 'https://open.douyin.com/im/get/retain_consult_card/'
const delRetainConsultCardApi = 'https://open.douyin.com/im/del/retain_consult_card/'

export async function postAuthorizeRole(data: { accessToken: string; openId: string }) {
  const res = (await axios.post(
    authorizeRoleApi,
    {
      open_id: data.openId,
      role_labels: ['COMPANY_BAND', 'AUTH_COMPANY', 'STAFF']
    },
    {
      headers: {
        'access-token': data.accessToken
      }
    }
  )) as Record<string, any>

  if (res.data.err_no) {
    throw new BadRequestException(`[抖音官方]:获取账号角色失败`)
  }

  if (res.data.data.match_result) {
    if (res.data.data.filter_role) {
      return ['COMPANY_BAND', 'AUTH_COMPANY', 'STAFF'].filter(
        (item) => !res.data.data.filter_role[item]
      )[0]
    }
  }

  return null
}

export async function postAuthorizeAccount(data: {
  clientSecret: string
  clientKey: string
  code: string
}) {
  const res = (await axios.post(authorizeAccountApi, {
    client_key: data.clientKey,
    client_secret: data.clientSecret,
    code: data.code,
    grant_type: 'authorization_code'
  })) as {
    data: {
      access_token: string
      captcha: string
      desc_url: string
      description: string
      error_code: number
      expires_in: number
      log_id: string
      open_id: string
      refresh_expires_in: number
      refresh_token: string
      scope: string
    }
    message: string
  } as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return {
    openId: res.data.data.open_id,
    accessToken: res.data.data.access_token,
    refreshToken: res.data.data.refresh_token,
    expiresIn: res.data.data.expires_in,
    refreshExpiresIn: res.data.data.refresh_expires_in
  }
}

export async function postAuthorizeAccountInfoApi({
  accessToken,
  openId
}: {
  accessToken: string
  openId: string
}) {
  try {
    const res = (await axios.post(authorizeAccountInfoApi, {
      access_token: accessToken,
      open_id: openId
    })) as {
      data: {
        avatar: string
        avatar_larger: string
        client_key: string
        e_account_role: string
        error_code: number
        log_id: string
        nickname: string
        open_id: string
        union_id: string
        description: string
      }
    } as { data: Record<string, any> }

    if (res.data.data.error_code !== 0) {
      throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
    }

    return {
      avatar: res.data.data.avatar_larger,
      name: res.data.data.nickname,
      accountRole: res.data.data.e_account_role
    } as {
      avatar: string
      name: string
      accountRole: string
    }
  } catch (error) {
    logger.error(error)
    throw new BadRequestException('获取账号信息失败')
  }
}

export async function postAuthorizeAccountRefresh({
  clientKey,
  refreshToken
}: {
  clientKey: string
  refreshToken: string
}) {
  const res = (await axios.post(
    authorizeAccountRefreshApi,
    {
      client_key: clientKey,
      refresh_token: refreshToken
    },
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }
  )) as {
    data: {
      description: string
      error_code: number
      expires_in: number
      refresh_token: string
    }
    message: string
  } as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return {
    refreshToken: res.data.data.refresh_token,
    refreshExpiresIn: res.data.data.expires_in
  }
}

export async function postAuthorizeAccountRefreshAcc({
  clientKey,
  refreshToken
}: {
  clientKey: string
  refreshToken: string
}) {
  const res = (await axios.post(authorizeAccountRefreshAccApi, {
    client_key: clientKey,
    refresh_token: refreshToken,
    grant_type: 'refresh_token'
  })) as {
    data: {
      description: string
      error_code: number
      access_token: number
      expires_in: number
      refresh_token: string
      refresh_expires_in: number
    }
    message: string
  } as { data: Record<string, any> }

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return {
    refreshToken: res.data.data.refresh_token,
    expiresIn: res.data.data.expires_in,
    refreshExpiresIn: res.data.data.refresh_expires_in,
    accessToken: res.data.data.access_token
  }
}

/**
 * 创建编辑留资卡片
 */
export async function postRetainConsultCard(data: {
  accessToken: string
  openId: string
  cardId: string
  mediaId: string
  components: number[]
  title: string
}) {
  const res = await axios.post(
    postRetainConsultCardApi,
    {
      card_id: data.cardId,
      components: data.components,
      media_id: data.mediaId,
      title: data.title
    },
    {
      headers: {
        'access-token': data.accessToken
      },
      params: {
        open_id: data.openId
      }
    }
  )
  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.card_id
}

/**
 * 查询留资卡片
 */
export async function getRetainConsultCard(data: { accessToken: string; openId: string }) {
  const res = await axios.get(getRetainConsultCardApi, {
    params: {
      open_id: data.openId
    },
    headers: {
      'access-token': data.accessToken
    }
  })

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data.cards || []
}

/**
 * 删除留资卡片
 */
export async function delRetainConsultCard(data: {
  accessToken: string
  openId: string
  cardId: string
}) {
  const res = await axios.get(delRetainConsultCardApi, {
    params: {
      open_id: data.openId,
      card_id: data.cardId
    },
    headers: {
      'access-token': data.accessToken
    }
  })

  if (res.data.data.error_code !== 0) {
    throw new BadRequestException(`[抖音官方]:${res.data.data.description}`)
  }

  return res.data
}
