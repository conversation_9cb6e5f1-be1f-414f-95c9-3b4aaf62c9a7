import { NestFactory } from '@nestjs/core'
import { GatewayModule } from './gateway.module'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'
import compression from '@fastify/compress'
// import helmet from '@fastify/helmet'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { HttpStatus, Logger, ValidationPipe } from '@nestjs/common'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import multipart from '@fastify/multipart'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import dayjs from 'dayjs'

dayjs.extend(utc)
dayjs.extend(timezone)

export async function bootstrap() {
  const fastify = new FastifyAdapter()

  fastify.register(compression, { encodings: ['gzip', 'deflate'] })
  // fastify.register(helmet, {})
  fastify.register(multipart, {
    limits: {
      fileSize: 1024 * 1024 * 10
    }
  })

  const app = await NestFactory.create<NestFastifyApplication>(GatewayModule, fastify, {
    rawBody: true
  })
  const configService = app.get(ConfigService) as ConfigService<RootConfigMap, true>
  const appConfig = configService.get<RootConfigMap['app']>('app')

  app.enableCors({
    origin: appConfig.cors.allowOrigin,
    methods: appConfig.cors.allowMethod,
    allowedHeaders: appConfig.cors.allowHeader,

    preflightContinue: false,
    credentials: true,
    optionsSuccessStatus: HttpStatus.NO_CONTENT
  })

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true
    })
  )

  if (process.env.NODE_ENV === 'dev') {
    const config = new DocumentBuilder().setTitle('GPT API 文档').setVersion('1.0').build()
    const ClientDocument = SwaggerModule.createDocument(app, config)

    SwaggerModule.setup('gateway-api', app, ClientDocument)
  }

  await app.listen(appConfig.http.port, appConfig.http.host)

  Logger.log(`Server running on ${await app.getUrl()}`, 'NestApplication')
}

bootstrap()
