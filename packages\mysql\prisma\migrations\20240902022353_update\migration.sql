/*
  Warnings:

  - Added the required column `freeMonth` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `vipMonth` to the `Order` table without a default value. This is not possible if the table is not empty.
  - Added the required column `vipNumber` to the `Order` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `order` ADD COLUMN `freeMonth` INTEGER NOT NULL,
    ADD COLUMN `vipMonth` INTEGER NOT NULL,
    ADD COLUMN `vipNumber` INTEGER NOT NULL,
    MODIFY `payTime` DATETIME(3) NULL;

-- CreateTable
CREATE TABLE `Interest` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `platformAccountCount` INTEGER NOT NULL,
    `messageCount` INTEGER NOT NULL,
    `memberCount` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `Order_orderNo_idx` ON `Order`(`orderNo`);
