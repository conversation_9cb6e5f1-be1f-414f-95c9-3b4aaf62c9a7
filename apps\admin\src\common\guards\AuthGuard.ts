import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Inject,
  UnauthorizedException
} from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import type { FastifyRequest } from 'fastify'
import { Cache } from 'cache-manager'
import { User } from '@qdy/mysql'

@Injectable()
export class TokenGuard implements CanActivate {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  private readonly excludedRoutes = [
    { method: 'post', url: '/users/auth' },
    { method: 'post', url: '/users/two/factor/auth' }
  ]

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>()
    const { authorization } = request.headers

    const { routeOptions } = request

    // 允许访问排除的路由
    for (let i = 0; i < this.excludedRoutes.length; i++) {
      if (
        routeOptions.url.indexOf(this.excludedRoutes[i].url) >= 0 &&
        routeOptions.method.toLocaleLowerCase() === this.excludedRoutes[i].method
      ) {
        return true
      }
    }

    try {
      const user = await this.cacheManager.get<User>(authorization)

      if (user) {
        request.user = user
        request.authorization = authorization
        return true
      }
    } catch {
      // ignore
    }

    throw new UnauthorizedException('登录失效, 请重新登录')
  }
}
