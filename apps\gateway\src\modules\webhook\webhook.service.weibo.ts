/* eslint-disable no-continue */
import { BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import { PersonalChatMessageEntity, MessagesByAutoresponderEntity } from '@qdy/mongo'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { InjectModel } from '@nestjs/mongoose'
import { AnyObject, Model } from 'mongoose'
import { Cache } from 'cache-manager'
import { PrismaService } from '@qdy/mysql'
import { WebhookEvents } from './constant'
import { genSocketRedisKey } from '@qdy/utils'
import { WebWeiboBody } from './types'
import {
  AutoresponderKeywordKey,
  AutoresponderVariableKey,
  PlatformAccountKeywordKey
} from '../autoresponder/constant'
import {
  AutoresponderContentChildType,
  AutoresponderContentTextType,
  AutoresponderContentType,
  AutoresponderKeywordContent,
  AutoresponderKeywordRedisValue,
  AutoresponderKeywordRule,
  AutoresponderTriggerType
} from '../autoresponder/autoresponder.dto'
import { generateRandom, wait } from '../../common/utils'
import { Queue, Worker } from 'bullmq'
import { WebHookServiceGrpc } from './webhook.rpc'
import { AccountAccountsStatus } from '../account/account.dto'
import { customAlphabet } from 'nanoid'
import { weiboSendMessages } from '../interact/external.weibo'
import { followUserInfo } from './external.weibo'
import { sendJpushMessageEventKey, sendJpushMessageEventEmitter } from './webhook.jpush'
import { TosManageService } from '@qdy/common'

@Injectable()
export class WebhookServiceWeibo {
  logger = new Logger('WebhookWeiboService')

  taskQueue: Queue

  taskWorker: Worker

  callbackTaskQueue: Queue

  callbackTaskWorker: Worker

  constructor(
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(MessagesByAutoresponderEntity.name)
    private messagesByAutoresponderModel: Model<MessagesByAutoresponderEntity>,
    private readonly prisma: PrismaService,
    private readonly tosManageService: TosManageService
  ) {}

  nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 8)

  onModuleInit() {
    this.logger.log('autoresponderWeibo init')

    this.taskQueue = new Queue('autoresponderWeibo', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'autoresponderWeibo',
      async (job) => {
        const { contents, ...others } = job.data

        setTimeout(() => {
          this.autoresponderTask(contents, others).catch((err) => {
            throw new BadRequestException(`task${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.callbackTaskQueue = new Queue('callbackEventWeibo', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.callbackTaskWorker = new Worker(
      'callbackEventWeibo',
      async (job) => {
        const { body, createTime } = job.data

        setTimeout(() => {
          this.callbackEventTask(body, createTime).catch((err) => {
            this.logger.log(err.message)
            throw new BadRequestException(`callbackeventWeiboTask${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async webhook(body: WebWeiboBody, echostr: string) {
    const createAt = new Date(body.created_at)
    const createTime = createAt.getTime()
    const { sender_id: fromUserId } = body

    const job = await this.callbackTaskQueue.getJob(`${fromUserId}-${createTime}`)

    if (!job) {
      await this.callbackTaskQueue.add(
        'callbackEventWeibo',
        {
          body,
          createTime
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${fromUserId}-${createTime}`
        }
      )
    }

    return echostr
  }

  async callbackEventTask(body: WebWeiboBody, createTime: number) {
    let sessionId = ''
    let openId = ''
    let fromName = ''
    let fromAvatar = ''

    const fromUserId = body.sender_id.toString()
    let toUserId = body.receiver_id.toString()

    if (!toUserId) {
      toUserId = fromUserId
    }

    openId = toUserId
    sessionId = fromUserId

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId
      },
      include: {
        Team: true
      }
    })

    if (!platformAccount) {
      // 账号未添加直接过滤
      return
    }

    try {
      const fromUser = await followUserInfo({
        accessToken: platformAccount.accessToken,
        uid: fromUserId
      })

      if (fromUser.headimgurl) {
        const fromUserAvatar = await this.uploadFromUrl({
          url: fromUser.headimgurl,
          teamCode: platformAccount.Team.invitationCode
        })
        fromAvatar = fromUserAvatar
        fromName = fromUser.nickname
      }
    } catch (error) {
      this.logger.log(error.message)
    }

    if (body.type === 'event') {
      if (body.data.subtype === 'follow') {
        try {
          // 关注事件
          this.autoresponderFollow(toUserId, fromUserId, fromName, fromAvatar).catch((err) => {
            throw new BadRequestException(`autoresponderFollow error${err.message}`)
          })
        } catch (err) {
          throw new BadRequestException(`cacheManager error${err.message}`)
        }
      }

      return
    }

    const messageId = `${createTime}-${this.nanoid()}`

    const data = {
      platformType: 'weibo',
      uniqueId: messageId,
      event: WebhookEvents.IMReceiveMessage,
      openId,
      fromUserId,
      toUserId,
      fromAvatar,
      fromName,
      toAvatar: platformAccount.avatar,
      toName: platformAccount.name,
      sessionId,
      content: {},
      createTime,
      isAuto: 0,
      messageId
    }

    if (body.type === 'text') {
      data.content = {
        messageType: 'text',
        type: body.type,
        text: body.text
      }
    } else if (body.type === 'image') {
      data.content = {
        messageType: 'image',
        type: body.type,
        text: body.text,
        vfid: body.data.vfid,
        tovfid: body.data.tovfid
      }
    } else if (body.type === 'voice') {
      if (body.text.includes('语音')) {
        data.content = {
          messageType: 'other',
          type: body.type,
          text: '你收到一条新消息，请打开微博app查看'
        }
      } else {
        data.content = {
          messageType: 'video',
          type: body.type,
          text: body.text,
          vfid: body.data.vfid,
          tovfid: body.data.tovfid
        }
      }
    } else {
      data.content = {
        messageType: 'other',
        type: body.type,
        text: '你收到一条新消息，请打开微博app查看'
      }
    }

    if (data.openId !== data.fromUserId) {
      sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
        openId: data.openId,
        messageId: data.messageId,
        event: data.event,
        content: '收到一条微博私信消息'
      })
    }

    await this.personalChatMessageModel.create(data)

    if (body.type === 'text') {
      // 接收私信并且是文字私信
      this.autoresponderKeywordChat({
        fromUserId,
        toUserId,
        fromAvatar: data.fromAvatar,
        fromName: data.fromName,
        toAvatar: data.toAvatar,
        toName: data.toName,
        text: body.text
      }).catch((err) => {
        throw new BadRequestException(`autoresponderKeywordChat${err.message}`)
      })
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    const userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(toUserId))

    Object.keys(userIdMaps).forEach((socketId) => {
      dataList.push({
        socketId,
        data: { ...data, platformAccountId: parseInt(userIdMaps[socketId], 10) }
      })
    })

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`发送失败 error${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }

  async autoresponderFollow(
    toUserId: string,
    fromUserId: string,
    toName: string,
    toAvatar: string
  ) {
    if (fromUserId === toUserId) {
      return
    }

    try {
      const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

      if (res) {
        await wait(1000)
        const arr = JSON.parse(res || '[]') as AutoresponderKeywordRedisValue[]
        const items = []

        const account = await this.prisma.platformAccount.findUnique({
          where: {
            openId: toUserId
          }
        })

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          toUserId
        )

        const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const { contents, contentType, trigger, state, stopReply, scene, autoresponderId } = arr[
            i
          ] as AutoresponderKeywordRedisValue

          if (scene || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          const send = trigger === AutoresponderTriggerType.Follow

          if (send && !job) {
            items.push(arr[i])
          }
        }

        const matchItem = items[generateRandom(items.length - 1)]

        this.logger.debug(
          'autoresponderKeywordChat follow',
          JSON.stringify(matchItem),
          JSON.stringify(items)
        )

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId,
              toUserId,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'weibo'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponderWeibo',
            {
              token,
              fromUserId: toUserId,
              toUserId: fromUserId,
              platformAccountId,
              teamId,
              autoresponderId,
              contents,
              fromName: account.name,
              fromAvatar: account.avatar,
              toName,
              toAvatar
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`AutoresponderKeywordKey Follow error${error.message}`)
    }
  }

  async autoresponderKeywordChat({
    fromUserId,
    toUserId,
    text,
    fromName,
    fromAvatar,
    toName,
    toAvatar
  }: {
    fromUserId: string
    toUserId: string
    text: string
    fromName?: string
    fromAvatar?: string
    toName?: string
    toAvatar?: string
  }) {
    if (fromUserId === toUserId) {
      return
    }
    try {
      const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

      if (res) {
        await wait(1000)
        const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]
        const items = []

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          toUserId
        )

        const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const {
            contents,
            contentType,
            keyword,
            rule,
            state,
            stopReply,
            scene,
            autoresponderId,
            trigger
          } = arr[i] as AutoresponderKeywordRedisValue

          if (scene || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          if (
            rule !== AutoresponderKeywordRule.Match &&
            rule !== AutoresponderKeywordRule.Instantly
          ) {
            // 收到私信回复只有两种规则（关键字和即刻回复）
            continue
          }

          let send = rule !== AutoresponderKeywordRule.Match

          if (!send) {
            send = !!(keyword as string[]).find((item) => {
              return RegExp(item, 'g').test(text)
            })
          }

          const chatMessage = trigger === AutoresponderTriggerType.Chat

          if (send && !job && chatMessage) {
            items.push(arr[i])
          }
        }

        const matchItem =
          items.find((item) => item.rule === AutoresponderKeywordRule.Match) ||
          items[generateRandom(items.length - 1)]

        this.logger.debug(
          'autoresponderKeywordChat matchItem',
          JSON.stringify(matchItem),
          JSON.stringify(items)
        )

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId,
              toUserId,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'weibo'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponderWeibo',
            {
              token,
              fromUserId: toUserId,
              toUserId: fromUserId,
              platformAccountId,
              teamId,
              autoresponderId,
              contents,
              fromName: toName,
              fromAvatar: toAvatar,
              toName: fromName,
              toAvatar: fromAvatar
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`AutoresponderKeywordKey error${error.message}`)
    }
  }

  async autoresponderTask(
    contents: AnyObject[],
    {
      token,
      fromUserId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId,
      scene,
      fromName,
      fromAvatar,
      toName,
      toAvatar
    }: {
      token: string
      fromUserId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
      scene?: string
      fromName?: string
      fromAvatar?: string
      toName?: string
      toAvatar?: string
    }
  ) {
    const [content, ...otherContents] = contents as AutoresponderKeywordContent[]

    if (!content) {
      return
    }

    try {
      await this.autoresponderSend(content, {
        token,
        fromUserId,
        toUserId,
        platformAccountId,
        teamId,
        autoresponderId,
        fromName,
        fromAvatar,
        toName,
        toAvatar
      })

      if (otherContents.length) {
        const [start] = otherContents

        await this.taskQueue.add(
          'autoresponder',
          {
            token,
            fromUserId,
            toUserId,
            platformAccountId,
            teamId,
            fromName,
            fromAvatar,
            toName,
            toAvatar,
            autoresponderId,
            contents: otherContents,
            scene
          },
          {
            delay: start.delay * 1000 || 100,
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `${AutoresponderKeywordKey}-${toUserId}-${fromUserId}-${autoresponderId}`
          }
        )
      }
    } catch (error) {
      throw new BadRequestException(`autoresponderTask error${error.message}`)
    }
  }

  async autoresponderSend(
    content: AutoresponderKeywordContent,
    {
      token,
      fromUserId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId,
      fromName,
      fromAvatar,
      toName,
      toAvatar
    }: {
      token: string
      fromUserId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
      scene?: string
      fromName?: string
      fromAvatar?: string
      toName?: string
      toAvatar?: string
    }
  ) {
    let contentText = ''

    if (content.messageType === AutoresponderContentChildType.Text) {
      for (let j = 0; j < content.texts.length; j++) {
        const text = content.texts[j]
        if (text.type === AutoresponderContentTextType.Text) {
          contentText += text.text
        } else if (text.type === AutoresponderContentTextType.Variable) {
          const value = await this.cacheManager.store.client.hget(
            AutoresponderVariableKey,
            `${text.variableId}`
          )
          try {
            const values = JSON.parse(value).value
            contentText += values[generateRandom(values.length - 1)]
          } catch (err) {
            throw new BadRequestException(`autoresponderSend text error${err.message}`)
          }
        }
      }
    }

    const data = await weiboSendMessages({
      type: 'text',
      content: contentText,
      accessToken: token,
      fromUserId,
      toUserId,
      platformAccountId,
      teamId,
      auto: true,
      autoresponderId,
      redisClient: this.cacheManager,
      fromName,
      fromAvatar,
      toName,
      toAvatar
    })

    await this.cacheManager.set(data.messageId, 1, 1000 * 60 * 10)
  }

  async uploadFromUrl(data: { url: string; teamCode: string }) {
    try {
      return await this.tosManageService.putObjectByStream({
        url: data.url,
        referer: 'https://weibo.com',
        teamCode: data.teamCode
      })
    } catch (error) {
      this.logger.log(error.message)
    }
  }
}
