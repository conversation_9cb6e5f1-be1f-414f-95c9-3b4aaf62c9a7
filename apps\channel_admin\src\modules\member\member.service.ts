import { Inject } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'

export class MemberService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getMembers({
    page,
    size,
    phone,
    startTime,
    endTime
  }: {
    page: number
    size: number
    phone: string
    startTime: number
    endTime: number
  }) {
    const { user } = this.request

    const where: Parameters<typeof this.prisma.user.findMany>[0]['where'] = {
      phone: {
        contains: phone
      },
      createTime: {
        gte: new Date(startTime),
        lte: new Date(endTime)
      },
      channelId: user.channelId
    }

    if (!phone) {
      delete where.phone
    }

    if (!startTime || !endTime) {
      delete where.createTime
    }

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        include: {
          channel: true
        },
        orderBy: { createTime: 'desc' },
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.user.count({ where })
    ])

    return {
      page,
      size,
      total,
      data: users.map((item) => ({
        id: item.id,
        name: item.name,
        avatar: item.avatar,
        phone: this.maskPhoneNumber(item.phone),
        channel: item.channel,
        createTime: item.createTime.getTime()
      }))
    }
  }

  maskPhoneNumber(phoneNumber: string) {
    if (!phoneNumber) return ''
    const start = phoneNumber.slice(0, 3)
    const end = phoneNumber.slice(-4)
    return `${start}****${end}`
  }
}
