import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class UserRegisterStatisticEntity {
  // 日期
  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  // 用户注册数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  registerCount: number
}

export const UserRegisterStatisticsSchema: ModelDefinition = {
  name: UserRegisterStatisticEntity.name,
  schema: SchemaFactory.createForClass(UserRegisterStatisticEntity)
}

export const UserRegisterStatisticsMongoose = MongooseModule.forFeature([
  UserRegisterStatisticsSchema
])
