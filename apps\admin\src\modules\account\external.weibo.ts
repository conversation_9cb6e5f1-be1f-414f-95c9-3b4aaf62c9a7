import { BadRequestException } from '@nestjs/common'
import axios from 'axios'

const revokeoauthApi = 'https://api.weibo.com/oauth2/revokeoauth2'

export async function postRevokeoAuthWeiboAccount(accessToken: string) {
  const querys = {
    access_token: accessToken
  }

  const response = await axios
    .post(revokeoauth<PERSON><PERSON>, null, { params: querys })
    .catch(function (error) {
      if (error.response) {
        throw new BadRequestException(`[微博官方]:${error.response.data.error}`)
      } else {
        throw new BadRequestException(`[微博官方]:请求报错`)
      }
    })

  return {
    result: response.data.result
  }
}
