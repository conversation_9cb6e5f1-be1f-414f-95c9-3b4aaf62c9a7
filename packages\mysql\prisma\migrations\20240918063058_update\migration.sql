/*
  Warnings:

  - A unique constraint covering the columns `[code]` on the table `Channel` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,channelId]` on the table `UsersCoupons` will be added. If there are existing duplicate values, this will fail.
  - Made the column `phone` on table `userscoupons` required. This step will fail if there are existing NULL values in that column.
  - Made the column `creatorId` on table `userscoupons` required. This step will fail if there are existing NULL values in that column.

*/
-- DropIndex
DROP INDEX `UsersCoupons_channelId_idx` ON `userscoupons`;

-- AlterTable
ALTER TABLE `userscoupons` MODIFY `phone` VARCHAR(191) NOT NULL,
    MODIFY `creatorId` INTEGER NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `Channel_code_key` ON `Channel`(`code`);

-- CreateIndex
CREATE UNIQUE INDEX `UsersCoupons_userId_channelId_key` ON `UsersCoupons`(`userId`, `channelId`);
