import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class UserMessageReadRecordEntity {
  @Prop({
    type: String,
    required: true,
    index: true
  })
  uniqueId: string

  /**
   * @description 账号openId
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  openId: string

   /**
   * @description 会话id
   */
   @Prop({
    type: String,
    required: true,
    index: true
  })
  sessionId: string

  /**
   * @description 消息已读方
   */
  @Prop({
    type: Number,
    required: true,
    index: true
  })
  userId: number

  /**
   * @description 已读来源（web,app）
   */
  @Prop({
    type: String,
    required: true
  })
  source: string

  /**
   * @description 时间戳 消息时间戳
   */
  @Prop({
    type: Number,
    index: true
  })
  messageTime: number

  /**
   * @description 时间戳 
   */
  @Prop({
    type: Number,
    default: () => Date.now(),
    index: true
  })
  createTime: number

 
}

export const UserMessageReadRecordSchema: ModelDefinition = <const>{
  name: UserMessageReadRecordEntity.name,
  schema: SchemaFactory.createForClass(UserMessageReadRecordEntity)
    .index({
      openId: -1,
      sessionId: -1,
      userId: -1
    })
}

export const UserMessageReadRecordMongoose = MongooseModule.forFeature([UserMessageReadRecordSchema])
