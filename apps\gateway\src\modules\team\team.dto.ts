import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  MinLength
} from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class TeamRequestPatchDTO {
  @ApiProperty({
    description: '团队名称',
    example: '草花团队',
    required: false
  })
  @IsString()
  @Length(2, 18)
  @IsOptional()
  name?: string

  @ApiProperty({
    description: '团队头像',
    example: 'https://example.com/avatar.png',
    required: false
  })
  @IsString()
  @IsOptional()
  avatar?: string
}

export class TeamInfoByInvitationUseResponseDTO extends BaseResponseDTO {}

export class TeamRequestCreateBodyDTO {
  @ApiProperty({
    description: '团队名称',
    example: '草花团队',
    required: true
  })
  @IsNotEmpty({ message: '团队名称不能为空' })
  @IsString({ message: '团队名称必须为字符串' })
  @Length(2, 18, { message: '团队名称长度必须在2-18之间' })
  name: string
}

export class TeamRequestJoinParamDTO {
  @ApiProperty({
    description: '团队id',
    example: '3KIO12',
    required: true
  })
  @IsNotEmpty({ message: '团队ID不能为空' })
  @IsString()
  @MinLength(6, { message: '邀请码不能小于6位' })
  invitationCode: string
}

export class TeamResponseCreateDTO extends BaseResponseDTO {}

export class TeamResponseJoinDTO extends BaseResponseDTO {}

class TeamResponseList {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'https://example.com/avatar.png'
  })
  avatar: string

  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  currentTeam: boolean
}

export class TeamResponseListDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamResponseList],
    example: [
      {
        id: 99,
        name: '草花团队',
        avatar: 'https://example.com/avatar.png',
        currentTeam: true
      }
    ]
  })
  data: TeamResponseList[]
}

export class TeamResponseInfoByInvitationCodeDTO {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  teamId: string

  @ApiResponseProperty({
    type: String,
    example: '草花团队'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'https://example.com/avatar.png'
  })
  avatar: string

  @ApiResponseProperty({
    type: String,
    example: '***********'
  })
  invitationCode: string

  @ApiResponseProperty({
    type: Number,
    example: '91 <userId>'
  })
  ownerId: number

  @ApiResponseProperty({
    type: Boolean,
    example: 'true 表示有团队, false 表示没有团队'
  })
  checkTeam: boolean
}

export enum TeamMemberRole {
  Member = 0,
  Manager = 1,
  Owner = 2
}

export enum TeamMemberStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * vip过期冻结
   */
  Disable = 1
}

class TeamMember {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  teamId: string

  @ApiResponseProperty({
    type: String,
    example: '草花团队'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'https://example.com/avatar.png'
  })
  avatar: string

  @ApiResponseProperty({
    type: String,
    example: '***********'
  })
  phone: string

  @ApiResponseProperty({
    type: String,
    example: ['**********', 'DE89D1DE89D']
  })
  platformAccountIds: string[]

  @ApiResponseProperty({
    type: Number,
    enum: Object.values(TeamMemberRole).filter((v) => typeof v === 'number'),
    example: 1
  })
  role: TeamMemberRole

  @ApiResponseProperty({
    type: Date,
    example: '2022-01-01T00:00:00.000Z'
  })
  joinTime: Date

  /**
   * 团队成员账号可用状态，正常,vip过期冻结
   */
  @ApiResponseProperty({
    type: Number,
    enum: Object.values(TeamMemberStatus).filter((v) => typeof v === 'number'),
    example: 0
  })
  status: TeamMemberStatus
}

export class TeamResponseMembersDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamMember]
  })
  data: TeamMember[]
}

export class TeamResponseMemberPagedDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamMember]
  })
  data: TeamMember[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class TeamUpdateMemberRoleDTO {
  @ApiProperty({
    type: Number,
    enum: Object.values(TeamMemberRole).filter(
      (v) => typeof v === 'number' && v !== TeamMemberRole.Owner
    ),
    example: '0: 成员, 1: 管理员 <enum>'
  })
  @IsNotEmpty({ message: '角色不能为空' })
  @IsEnum([TeamMemberRole.Manager, TeamMemberRole.Member], { message: '角色不合法' })
  role: number
}

export class TeamRequestMembersPlatformAccountDTO {
  @ApiProperty({
    type: Number,
    example: **********
  })
  @IsNotEmpty({ message: '成员ID不能为空' })
  @IsNumber()
  memberId: number

  @ApiProperty({
    type: [Number],
    example: '**********DE89D1DE89D '
  })
  @IsNotEmpty({ message: '平台账号ID列表不能为空' })
  @IsArray()
  platformAccountIds: number[]
}

export class TeamRequestPlatformAccountMembersDTO {
  @ApiProperty({
    type: Number,
    example: '**********DE89D1DE89D '
  })
  @IsNotEmpty({ message: '平台账号ID不能为空' })
  @IsNumber()
  platformAccountId: number

  @ApiProperty({
    type: [Number],
    example: ['**********', 'DE89D1DE89D']
  })
  @IsNotEmpty({ message: '成员ID不能为空' })
  @IsArray()
  memberIds: number[]
}

export class TeamRequestMembersNameDTO {
  @ApiProperty({
    type: String,
    example: '小明'
  })
  @IsString()
  @Length(2, 18)
  @IsNotEmpty({ message: '名字不能为空' })
  name: string
}

export class TeamWechatMessageLimitRequestDTO {
  @ApiProperty({
    type: Number,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty({ message: '限制数量不能为空' })
  wechatMessageLimit: number
}
