import { ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

class OrderInterestResponse {
  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  id: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  platformAccountCount: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  messageCount: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  memberCount: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  price: number
}

class OrderInterestVipOftenDTO {
  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  mount: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  present: number
}

class OrderInterestData {
  @ApiResponseProperty({
    type: [OrderInterestResponse]
  })
  interests: OrderInterestResponse[]

  @ApiResponseProperty({
    type: [OrderInterestVipOftenDTO]
  })
  vipOften: OrderInterestVipOftenDTO[]
}

export class OrderInterestResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderInterestData
  })
  data: OrderInterestData
}
