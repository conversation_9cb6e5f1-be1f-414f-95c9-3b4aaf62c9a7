import { ModelDefinition, MongooseModule, Prop, SchemaFactory, Schema } from '@nestjs/mongoose'
import { SchemaTypes } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class WorkCommentEntity {
  @Prop({
    type: String,
    required: true,
    unique: true
  })
  uniqueId: string

  /**
   * @description 平台类型
   */
  @Prop({
    type: String,
    required: true
  })
  platformType: string

  @Prop({
    type: String,
    required: true
  })
  event: string

  /**
   * @description 账号唯一id（评论归属者）
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  openId: string

  /**
   * @description 消息id
   */
  @Prop({
    type: String,
    default: ''
  })
  messageId: string

  /**
   * @description 发送方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  fromUserId: string

  /**
   * @description 目标方id
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  toUserId: string

  /**
   * @description 发送方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  fromAvatar: string

  /**
   * @description 发送方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  fromName: string

  /**
   * @description 目标方头像
   */
  @Prop({
    type: String,
    default: ''
  })
  toAvatar: string

  /**
   * @description 目标方名称
   */
  @Prop({
    type: String,
    default: ''
  })
  toName: string

  /**
   * @description 单个邀请码使用上限
   */
  @Prop({
    type: Number,
    default: () => Date.now()
  })
  createTime: number

  /**
   * @description 会话id(作品id)
   */
  @Prop({
    type: String,
    required: true,
    index: true
  })
  sessionId: string

  /**
   * @description 是否自动发送(默认手动发送)
   */
  @Prop({
    type: Number,
    default: 0
  })
  isAuto: number

  /**
   * @description 消息内容
   */
  @Prop({
    type: SchemaTypes.Map,
    required: true
  })
  content: Map<string, string>

  /**
   * @description 微信评论回复消息
   */
  @Prop([
    {
      content: String,
      wxid: String,
      createTime: Number
    }
  ])
  commentReply: {
    content: string
    wxid: string
    createTime: number
  }[]
}

export const WorkCommentSchema: ModelDefinition = {
  name: WorkCommentEntity.name,
  schema: SchemaFactory.createForClass(WorkCommentEntity)
    .index({
      toUserId: 1,
      createTime: -1
    })
    .index({
      createTime: -1
    })
    .index({
      openId: 1,
      sessionId: 1,
      createTime: -1
    })
}

export const WorkCommentMongoose = MongooseModule.forFeature([WorkCommentSchema])
