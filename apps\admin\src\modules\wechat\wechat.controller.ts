import { Controller, Get, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { WechatService } from './wechat.service'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import {
  WechatLoginRecordResponseDTO,
  WechatOfflineResponseDTO,
  WechatOnlineResponseDTO,
  WechatsResponseDTO
} from './wechat.dto'

@Controller('wechat')
@ApiTags('视频号管理')
export class WechatController {
  constructor(private readonly wechatService: WechatService) {}

  @Get()
  @ApiOperation({ summary: '获取账号列表' })
  @ApiOkResponse({ description: '操作成功', type: WechatsResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'wxid',
    required: false,
    type: String,
    description: '按帐户名称搜索'
  })
  @ApiQuery({
    name: 'startTime',
    required: false,
    type: String,
    description: '开始时间（2025-05-01）'
  })
  @ApiQuery({
    name: 'endTime',
    required: false,
    type: String,
    description: '到期时间（2025-05-01）'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getAccounts(
    @Query('wxid') wxid: string,
    @Query('size') size: number,
    @Query('page') page: number,
    @Query('startTime') startTime: string,
    @Query('endTime') endTime: string
  ) {
    const querys = {
      wxid,
      startTime,
      endTime,
      page,
      size
    }

    if (!querys.page) {
      querys.page = 1
    }

    if (!querys.size) {
      querys.size = 10
    }

    return this.wechatService.getWechatList(querys)
  }

  @Get('onlineCount')
  @ApiOperation({ summary: '获取在线账号数量' })
  @ApiOkResponse({ description: '操作成功', type: WechatOnlineResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: '开始时间（********）'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getOnlineCount(@Query('startDate') startDate: string) {
    return this.wechatService.getWechatOnlineCount(startDate)
  }

  @Get('offlineCount')
  @ApiOperation({ summary: '获取掉线账号数量' })
  @ApiOkResponse({ description: '操作成功', type: WechatOfflineResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: '开始时间（********）'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getOfflineCount(@Query('startDate') startDate: string) {
    return this.wechatService.getWechatOfflineCount(startDate)
  }

  @Get('loginRecord')
  @ApiOperation({ summary: '获取登录记录' })
  @ApiOkResponse({ description: '操作成功', type: WechatLoginRecordResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'wxid',
    required: false,
    type: String,
    description: '按帐户名称搜索'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getLoginRecord(
    @Query('wxid') wxid: string,
    @Query('size') size: number,
    @Query('page') page: number
  ) {
    const querys = {
      wxid,
      page,
      size
    }
    if (!querys.page) {
      querys.page = 1
    }
    if (!querys.size) {
      querys.size = 10
    }
    return this.wechatService.getWechatLoginRecord(querys)
  }
}
