import {
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit
} from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import {
  GiftVipDTO,
  orderPriceRequestDTO,
  OrderStatus,
  OrderStatusRequestDTO,
  PayType,
  UpgradeOrderRequest,
  VipCreateDTO,
  VipRenewDTO
} from './vip.dto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { VIPOften } from './constant'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { eventKey, orderEventEmitter } from './order.event'
import { OrderManageService } from 'apps/common/modules'
import { SalesType } from '../overview/overview.dto'

@Injectable()
export class VipService implements OnModuleInit {
  logger = new Logger('VipService')

  constructor(
    private readonly prisma: PrismaService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly orderManageService: OrderManageService
  ) {}

  onModuleInit() {}

  async calculateOrderPrice(body: orderPriceRequestDTO) {
    const team = await this.prisma.team.findUnique({
      where: {
        id: body.teamId
      }
    })

    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    return this.orderManageService.calculateOrderPrice({
      userId: team.ownerId,
      teamId: team.id,
      interestCount: body.interestCount,
      interestId: body.interestId,
      couponId: body.couponId,
      month: body.month,
      days: body.days,
      orderType: body.orderType
    })
  }

  async createUpgradeOrder(data: UpgradeOrderRequest) {
    const { user } = this.request

    const team = await this.prisma.team.findUnique({
      where: {
        id: data.teamId
      },
      include: {
        vip: true
      }
    })

    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    const unexpiredOrderCount = await this.prisma.order.count({
      where: {
        teamId: data.teamId,
        orderStatus: OrderStatus.PENDING,
        expireTime: {
          gt: new Date()
        }
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    const { interestId, interestCount, payAmount, isPay } = data

    if (isPay && payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }

    const orderNo = await this.orderManageService.upgradeOrder({
      teamId: data.teamId,
      interestCount,
      interestId,
      isPay,
      userId: team.ownerId,
      remark: data.remark,
      creatorId: user.id,
      creatorName: user.username,
      payAmount: data.payAmount
    })

    orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })

    if (!data.isPay) {
      await this.prisma.order.update({
        where: {
          orderNo
        },
        data: {
          orderStatus: OrderStatus.SUCCESS,
          payAmount: 0,
          isGiftOrder: true,
          payTime: new Date(),
          payType: PayType.CORPORATETRANSFER
        }
      })

      await this.orderManageService.handleCompletedOrder(orderNo)
      this.cacheManager.del(`overview:${data.teamId}`)
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
    }

    return {
      orderNo
    }
  }

  async createVip(data: VipCreateDTO) {
    const { user } = this.request

    const team = await this.prisma.team.findUnique({
      where: {
        id: data.teamId
      }
    })

    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    const unexpiredOrderCount = await this.prisma.order.count({
      where: {
        teamId: data.teamId,
        orderStatus: OrderStatus.PENDING,
        expireTime: {
          gt: new Date()
        }
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    if (data.isPay && data.payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }

    const orderNo = await this.orderManageService.createOrder({
      teamId: data.teamId,
      interestCount: data.interestCount,
      interestId: data.interestId,
      isPay: data.isPay,
      month: data.month,
      days: data.days,
      userId: team.ownerId,
      remark: data.remark,
      creatorId: user.id,
      creatorName: user.username,
      payAmount: data.payAmount
    })

    orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })

    if (!data.isPay) {
      await this.prisma.order.update({
        where: {
          orderNo
        },
        data: {
          orderStatus: OrderStatus.SUCCESS,
          payAmount: 0,
          payTime: new Date(),
          isGiftOrder: true,
          payType: PayType.CORPORATETRANSFER
        }
      })

      await this.orderManageService.handleCompletedOrder(orderNo)
      this.cacheManager.del(`overview:${data.teamId}`)
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
    }

    return { orderNo }
  }

  async renewOrder(data: VipRenewDTO) {
    const { user } = this.request

    const team = await this.prisma.team.findUnique({
      where: {
        id: data.teamId
      }
    })

    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    const unexpiredOrderCount = await this.prisma.order.count({
      where: {
        teamId: data.teamId,
        orderStatus: OrderStatus.PENDING,
        expireTime: {
          gt: new Date()
        }
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    if (data.isPay && data.payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }

    const orderNo = await this.orderManageService.renewOrder({
      teamId: data.teamId,
      interestId: data.interestId,
      isPay: data.isPay,
      days: data.days,
      month: data.month,
      userId: team.ownerId,
      remark: data.remark,
      creatorId: user.id,
      creatorName: user.username,
      payAmount: data.payAmount
    })

    orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })

    if (!data.isPay) {
      await this.prisma.order.update({
        where: {
          orderNo
        },
        data: {
          orderStatus: OrderStatus.SUCCESS,
          payAmount: 0,
          payTime: new Date(),
          isGiftOrder: true,
          payType: PayType.CORPORATETRANSFER
        }
      })

      await this.orderManageService.handleCompletedOrder(orderNo)
      this.cacheManager.del(`overview:${data.teamId}`)
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
    }

    return { orderNo }
  }

  async giftOrder(data: GiftVipDTO) {
    const { user } = this.request

    const team = await this.prisma.team.findUnique({
      where: {
        id: data.teamId
      }
    })

    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    const orderNo = await this.orderManageService.giftOrder({
      teamId: data.teamId,
      interestId: data.interestId,
      isPay: false,
      userId: team.ownerId,
      remark: data.remark,
      creatorId: user.id,
      creatorName: user.username,
      giftDays: data.giftDays,
      isRegisterOrder: false
    })

    await this.orderManageService.handleCompletedOrder(orderNo)

    return { orderNo }
  }

  async getOrders({
    page,
    size,
    orderNo,
    payAmount,
    phone,
    orderStartTime,
    orderEndTime,
    payStartTime,
    payEndTime,
    orderStatus,
    orderType,
    salesType,
    channelId,
    invitationCode
  }: {
    page: number
    size: number
    phone: string
    orderNo: string
    payAmount: number
    orderStartTime: number
    orderEndTime: number
    payStartTime: number
    payEndTime: number
    orderStatus: string
    orderType: string
    salesType: string
    channelId: number
    invitationCode: string
  }) {
    const where: Parameters<typeof this.prisma.order.findMany>[0]['where'] = {
      orderNo,
      phone,
      channelId,
      orderType,
      payAmount: {
        gte: payAmount
      },
      fromTime: {
        gte: new Date(orderStartTime),
        lte: new Date(orderEndTime)
      },
      payTime: {
        gte: new Date(payStartTime),
        lte: new Date(payEndTime)
      }
    }

    if (!orderNo) {
      delete where.orderNo
    }

    if (!phone) {
      delete where.phone
    }

    if (!orderStartTime || !orderEndTime) {
      delete where.fromTime
    }

    if (!payStartTime || !payEndTime) {
      delete where.payTime
    }

    if (!channelId) {
      delete where.channelId
    }

    if (!payAmount) {
      delete where.payAmount
    }

    if (!orderType) {
      delete where.orderType
    }

    if (invitationCode) {
      const team = await this.prisma.team.findUnique({
        where: {
          invitationCode
        }
      })

      if (team) {
        where.teamId = team.id
      }
    }

    if (salesType) {
      if (salesType === 'Buy') {
        where.salesType = {
          in: [SalesType.FirstBuy, SalesType.ReBuy]
        }
      } else {
        where.salesType = salesType
      }
    }

    if (orderStatus) {
      if (!Object.values(OrderStatus).includes(orderStatus as OrderStatus)) {
        throw new ForbiddenException('参数错误')
      }

      if (orderStatus === OrderStatus.CANCELED) {
        where.OR = [
          {
            orderStatus: OrderStatus.CANCELED
          },
          {
            orderStatus: OrderStatus.PENDING,
            expireTime: {
              lte: new Date()
            }
          }
        ]
      } else {
        where.orderStatus = orderStatus
      }
    }

    const [orders, total, totalAmount] = await Promise.all([
      this.prisma.order.findMany({
        where,
        include: {
          team: true,
          channel: true,
          Contract: true,
          vip: true
        },
        orderBy: { fromTime: 'desc' },
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.order.count({ where }),
      this.prisma.order.aggregate({
        _sum: {
          payAmount: true
        },
        where
      })
    ])
    return {
      total,
      page,
      size,
      totalAmount: totalAmount._sum.payAmount || 0,
      data: orders.map((item) => ({
        ...item,
        team: item.team,
        vip: item.vip,
        channel: item.channel,
        orderStatus: item.orderStatus,
        payTime: item.payTime ? item.payTime.getTime() : 0,
        expireTime: item.expireTime ? item.expireTime.getTime() : 0,
        fromTime: item.fromTime.getTime(),
        isRefund: item.Contract.filter((item) => item.status === 1).length > 0
      }))
    }
  }

  async putOrderStatus(orderNo: string, body: OrderStatusRequestDTO) {
    const order = await this.prisma.order.findUnique({
      where: {
        orderNo
      },
      include: {
        interest: true,
        vip: true
      }
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.orderStatus !== OrderStatus.PENDING) {
      throw new ForbiddenException('订单不是待支付状态，无法开通')
    }

    try {
      const gmtPayment = new Date()

      // 关闭订单事件
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })

      await this.prisma.order.update({
        where: {
          id: order.id
        },
        data: {
          payTime: gmtPayment,
          orderStatus: OrderStatus.SUCCESS,
          payAmount: order.dueAmount,
          remark: body.remark,
          payType: PayType.CORPORATETRANSFER
        }
      })

      // 订单处理
      await this.orderManageService.handleCompletedOrder(order.orderNo)

      await this.cacheManager.del(`overview:${order.teamId}`)
    } catch (error) {
      throw new ForbiddenException(error)
    }
  }

  async getOrderInfo(orderNo: string) {
    const order = await this.prisma.order.findUnique({
      where: {
        orderNo
      },
      include: {
        orderInfos: true,
        interest: true,
        vip: true,
        team: true,
        UsersCoupons: true
      }
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    let discountAmount

    if (order.usersCouponId) {
      const usersCoupon = await this.prisma.usersCoupons.findUnique({
        where: {
          id: order.usersCouponId
        }
      })
      ;({ discountAmount } = usersCoupon)
    }

    return {
      vipInfo: {
        interestCount: order.interestCount,
        platformAccountCount: order.interestCount * order.interest.platformAccountCount,
        teamMemberCount: order.interestCount * order.interest.memberCount,
        messageCount: order.interestCount * order.interest.messageCount,
        month: order.vipMonth,
        freeMonth: order.freeMonth,
        giftDays: order.giftDays,
        expirationTime:
          order.vip.expirationTime && order.vip.expirationTime.getTime() > Date.now()
            ? order.vip.expirationTime.getTime()
            : 0
      },
      orderInfo: {
        orderNo: order.orderNo,
        invitationCode: order.team.invitationCode,
        teamName: order.team.name,
        fromTime: order.fromTime.getTime(),
        orderStatus:
          order.orderStatus === OrderStatus.PENDING && order.expireTime.getTime() < Date.now()
            ? OrderStatus.CANCELED
            : order.orderStatus,
        type: order.type,
        price: order.price || 0,
        dueAmount: order.dueAmount || 0,
        payAmount: order.payAmount || 0,
        creatorName: order.creatorName || '-',
        payTime: order.payTime ? order.payTime.getTime() : 0,
        toTime: order.toTime ? order.toTime.getTime() : 0,
        payType: order.payType,
        remark: order.remark,
        orderType: order.orderType,
        isGiftOrder: order.isGiftOrder,
        discountAmount,
        giftDays: order.giftDays,
        days: order.days,
        remainingDays: order.remainingDays
      }
    }
  }

  async getInterest() {
    const res = await this.prisma.interest.findMany()

    return {
      interests: res,
      vipOften: VIPOften
    }
  }
}
