import { Inject, Injectable, Logger } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Queue, Worker } from 'bullmq'
import { OrderStatus } from '../vip/vip.dto'
import dayjs from 'dayjs'

@Injectable()
export class ContractService {
  dataQueue: Queue

  dataWorker: Worker

  serverNumber: number

  LOCK_TIMEOUT = 10 * 60 * 1000

  logger = new Logger('ContractService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService
  ) {}

  async onModuleInit() {
    this.dataQueue = new Queue('contract-data-transfer', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.dataWorker = new Worker(
      'contract-data-transfer',
      async (job) => {
        const { teamId, expirationTime } = job.data

        await this.migrateUserTableData(teamId, expirationTime)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // this.onDataTransferByTeam()
    // this.serverNumber = 8
    // this.cacheManager.set('contract-data-transfer', 1, 1000 * 60 * 60 * 24)
  }

  async onDataTransferByTeam() {
    const teamList = await this.prisma.vip.findMany({
      where: {
        expirationTime: {
          gt: new Date()
        }
      },
      orderBy: {
        createTime: 'desc'
      }
    })

    for (let i = 0; i < teamList.length; i++) {
      const team = teamList[i]
      await this.onContractTransferTeamData(
        team.teamId,
        team.expirationTime ? team.expirationTime.getTime() : 0
      )
    }

    // const teamIdList = teamList.map((vip) => {
    //   const newData = {
    //     teamId: vip.teamId,
    //     expirationTime: vip.expirationTime.getTime()
    //   }
    //   return newData
    // })

    // const activeArray = teamIdList.slice(0, this.serverNumber)

    // if (activeArray.length > 0) {
    //   for (let j = 0; j < activeArray.length; j++) {
    //     const vip = activeArray[j]
    //     await this.dataQueue.add(
    //       'contract-data-transfer',
    //       {
    //         teamId: vip.teamId,
    //         expirationTime: vip.expirationTime
    //       },
    //       {
    //         removeOnComplete: true,
    //         removeOnFail: true,
    //         jobId: `contract-data-transfer-${vip.teamId}`
    //       }
    //     )
    //   }
    // }

    // const otherContents = teamIdList.slice(this.serverNumber)

    // if (otherContents.length) {
    //   await this.cacheManager.store.client.hset(
    //     'contract:transfer',
    //     'teamIds',
    //     JSON.stringify(otherContents)
    //   )
    // }
  }

  async migrateUserTableData(teamId: number, expirationTime: number) {
    const isLock = await this.tryAcquireLock(`contract-transfer-${teamId}`)

    if (!isLock) {
      // 数据迁移逻辑
      await this.onContractTransferTeamData(teamId, expirationTime)
    }

    const teamIds = await this.cacheManager.store.client.hget('contract:transfer', 'teamIds')

    if (teamIds) {
      const teamListArray = JSON.parse(teamIds)

      const activeArray = teamListArray.slice(0, this.serverNumber)

      if (activeArray.length > 0) {
        for (let j = 0; j < activeArray.length; j++) {
          const vip = activeArray[j]
          await this.dataQueue.add(
            'contract-data-transfer',
            {
              teamId: vip.teamId,
              expirationTime: vip.expirationTime
            },
            {
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `team-data-transfer-${vip.teamId}`
            }
          )
        }
      }

      const otherContents = teamListArray.slice(this.serverNumber)

      if (otherContents.length) {
        await this.cacheManager.store.client.hset(
          'contract:transfer',
          'teamIds',
          JSON.stringify(otherContents)
        )
      } else {
        await this.cacheManager.store.client.hdel('contract:transfer', 'teamIds')
      }
    }
  }

  async onContractTransferTeamData(teamId: number, expirationTime: number) {
    // 先查询未过期的订单

    this.logger.log(teamId)

    const orderLists = await this.prisma.order.findMany({
      where: {
        teamId,
        orderStatus: OrderStatus.SUCCESS
      },
      include: {
        Contract: true
      },
      orderBy: {
        fromTime: 'desc'
      }
    })

    let teamExpirationDate = new Date(expirationTime)

    for (let z = 0; z < orderLists.length; z++) {
      const order = orderLists[z]

      if (order.toTime > new Date()) {
        if (order.vipMonth >= 10) {
          if (order.Contract.length === 0) {
            // 已经没有合同了
            const tenMonthDate = dayjs(order.toTime)
              .subtract(
                order.vipMonth === 10 ? order.vipMonth : order.vipMonth - order.freeMonth,
                'month'
              )
              .toDate()

            try {
              await this.prisma.contract.create({
                data: {
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(tenMonthDate).subtract(order.freeMonth, 'month').toDate(),
                  endTime: tenMonthDate,
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: order.payTime,
                  isFree: true,
                  status: 0
                }
              })

              await this.prisma.contract.create({
                data: {
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: tenMonthDate,
                  endTime: order.toTime,
                  amount: 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: order.payTime,
                  status: 0
                }
              })
            } catch (error) {
              // eslint-disable-next-line no-console
              this.logger.log(error)
            }
          }

          if (order.Contract.length === 1) {
            const contract = order.Contract[0]
            const tenMonthDate = dayjs(contract.endTime)
              .subtract(
                order.vipMonth === 10 ? order.vipMonth : order.vipMonth - order.freeMonth,
                'month'
              )
              .toDate()
            await this.prisma.contract.update({
              where: {
                id: contract.id
              },
              data: {
                startTime: tenMonthDate
              }
            })

            await this.prisma.contract.create({
              data: {
                orderId: order.id,
                orderNo: order.orderNo,
                startTime: dayjs(tenMonthDate).subtract(order.freeMonth, 'month').toDate(),
                endTime: tenMonthDate,
                amount: 0,
                teamId: order.teamId,
                interestId: order.interestId,
                interestCount: order.interestCount,
                createTime: order.payTime,
                isFree: true,
                status: 0
              }
            })

            if (!order.isUpgrade || order.orderType !== 'upgrade') {
              teamExpirationDate = dayjs(teamExpirationDate).subtract(12, 'month').toDate()
            }
          }
        } else if (order.vipMonth > 0) {
          if (order.Contract.length === 0) {
            // 如果没有合同
            await this.prisma.contract.create({
              data: {
                orderId: order.id,
                orderNo: order.orderNo,
                startTime: dayjs(order.toTime).subtract(order.vipMonth, 'month').toDate(),
                endTime: order.toTime,
                amount: order.payAmount ?? 0,
                teamId: order.teamId,
                interestId: order.interestId,
                interestCount: order.interestCount,
                createTime: order.payTime,
                status: 0
              }
            })
          }
        } else {
          // 要么是赠送，要么是注册
          // eslint-disable-next-line no-lonely-if
          if (order.Contract.length === 0) {
            if (order.orderType === 'upgrade') {
              await this.prisma.contract.create({
                data: {
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(order.toTime).subtract(order.remainingDays, 'day').toDate(),
                  endTime: order.toTime,
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: order.payTime,
                  status: 0
                }
              })
            } else if (order.orderType === 'gift') {
              // 后台赠送
              await this.prisma.contract.create({
                data: {
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(order.toTime).subtract(order.giftDays, 'day').toDate(),
                  endTime: order.toTime,
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: order.payTime,
                  status: 0
                }
              })
            } else if (order.orderType === 'create' || order.isGiftOrder) {
              // 注册即赠送
              await this.prisma.contract.create({
                data: {
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(order.toTime).subtract(order.giftDays, 'day').toDate(),
                  endTime: order.toTime,
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: order.payTime,
                  status: 0
                }
              })
            } else if (order.isUpgrade && !order.isGiftOrder) {
              // 升级
              await this.prisma.contract.create({
                data: {
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(teamExpirationDate).subtract(order.giftDays, 'day').toDate(),
                  endTime: teamExpirationDate,
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: order.payTime,
                  status: 0
                }
              })
            } else {
              // 注册即赠送
              await this.prisma.contract.create({
                data: {
                  orderId: order.id,
                  orderNo: order.orderNo,
                  startTime: dayjs(teamExpirationDate).subtract(order.giftDays, 'day').toDate(),
                  endTime: teamExpirationDate,
                  amount: order.payAmount ?? 0,
                  teamId: order.teamId,
                  interestId: order.interestId,
                  interestCount: order.interestCount,
                  createTime: order.payTime,
                  status: 0
                }
              })

              teamExpirationDate = dayjs(teamExpirationDate)
                .subtract(order.giftDays, 'day')
                .toDate()
            }
          }
        }
      }
    }
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(`contract-transfer-${key}`, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
