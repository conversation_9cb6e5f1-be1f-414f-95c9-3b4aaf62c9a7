import { Logger } from '@nestjs/common'
import axios from 'axios'

const logger = new Logger('user external')

const ocpcapi = 'https://ocpc.baidu.com/ocpcapi/api/uploadConvertData'

export async function postOcpcapi(token: string, logidUrl: string) {
  const res = await axios.post(ocpcapi, {
    token,
    conversionTypes: [
      {
        logidUrl,
        newType: 25
      }
    ]
  })

  logger.log('百度api上报', JSON.stringify(res.data))

  return res.data
}
