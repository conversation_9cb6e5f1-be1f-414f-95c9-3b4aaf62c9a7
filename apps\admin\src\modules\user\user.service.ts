import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import {
  UserLoginRegisterRequestBodyDTO,
  UserRegisterRequestBodyDTO,
  BindMfaRequestDTO
} from './user.dto'
import { AdminUser, PrismaService } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { nanoid } from 'nanoid'
import { ConfigService } from '@nestjs/config'
import { RedisStore } from 'cache-manager-ioredis-yet'

import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { RootConfigMap } from '@qdy/config'
import crypto from 'crypto'
import { UserInitService } from './user.init'
import QRCode from 'qrcode'
import speakeasy from 'speakeasy'

@Injectable()
export class UserService {
  logger = new Logger('UserService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly userInitService: UserInitService
  ) {}

  verifyPassword(password: string, salt: string, hash: string) {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return hash === hashToVerify
  }

  /**
   * 用户登录
   * @param param
   * @param ip
   * @returns
   */
  async putLoginUser({ username, password }: UserLoginRegisterRequestBodyDTO) {
    const userInfo = await this.prisma.adminUser.findUnique({
      where: {
        username
      }
    })

    if (!userInfo || !this.verifyPassword(password, userInfo.salt, userInfo.password)) {
      throw new NotFoundException('账号或密码错误')
    }

    if (process.env.NODE_ENV === 'prod') {
      if (!userInfo.mfaSecret) {
        const secret = speakeasy.generateSecret({ name: '青豆云' })

        const encryptSecret = this.encrypt(secret.base32, userInfo.salt)

        const url = await QRCode.toDataURL(secret.otpauth_url)

        return {
          verifyMfa: true,
          initMfa: true,
          qrcode: url,
          secret: encryptSecret,
          authorization: ''
        }
      }

      return {
        verifyMfa: true,
        initMfa: false,
        qrcode: '',
        secret: '',
        authorization: ''
      }
    }

    const authorization = await this.generateAuthorization(userInfo)

    return {
      verifyMfa: false,
      initMfa: false,
      qrcode: '',
      secret: '',
      authorization
    }
  }

  async verifyMfa({ username, password, token, secret }: BindMfaRequestDTO) {
    const userInfo = await this.prisma.adminUser.findUnique({
      where: {
        username
      }
    })

    if (!userInfo || !this.verifyPassword(password, userInfo.salt, userInfo.password)) {
      throw new NotFoundException('账号或密码错误')
    }

    if (userInfo.mfaSecret) {
      const decryptSecret = this.decrypt(userInfo.mfaSecret, userInfo.secretIv)

      const verified = speakeasy.totp.verify({
        secret: decryptSecret,
        encoding: 'base32',
        token
      })

      if (verified) {
        const authorization = await this.generateAuthorization(userInfo)

        return {
          authorization
        }
      }

      throw new ForbiddenException('无效的验证码')
    } else {
      const decryptSecret = this.decrypt(secret, userInfo.salt)

      const verified = speakeasy.totp.verify({
        secret: decryptSecret,
        encoding: 'base32',
        token
      })

      if (verified) {
        await this.prisma.adminUser.update({
          where: {
            id: userInfo.id
          },
          data: {
            mfaSecret: secret,
            secretIv: userInfo.salt
          }
        })

        const authorization = await this.generateAuthorization(userInfo)

        return {
          authorization
        }
      }

      throw new ForbiddenException('无效的验证码')
    }
  }

  /**
   *  生成 token 并缓存
   * @param phone
   * @param ip
   */
  async generateAuthorization(userInfo: AdminUser) {
    const authorization = nanoid()
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')

    const oldAuthorization = await this.cacheManager.get<string>(userInfo.username)
    if (oldAuthorization) {
      await Promise.all([
        this.cacheManager.del(userInfo.username),
        this.cacheManager.del(oldAuthorization)
      ])
    }

    await Promise.all([
      this.cacheManager.set(userInfo.username, authorization, overdueToken),
      this.cacheManager.set(authorization, userInfo, overdueToken)
    ])

    return authorization
  }

  /**
   * 删除 Userorization
   * @param Userorization
   * @returns
   */
  async deleteAuthorization() {
    const { user, authorization } = this.request

    if (!user) {
      throw new ForbiddenException('登录失效, 请重新登录')
    }

    if (user) {
      await Promise.all([
        this.cacheManager.del(user.username),
        this.cacheManager.del(authorization)
      ])
    }

    this.logger.log(`delete Authorization: ${authorization} and phone: ${user.phone}`)
  }

  async getUserInfo() {
    const { user } = this.request
    return {
      id: user.id,
      username: user.username,
      role: user.role
    }
  }

  async getAdminUsers(page: number = 1, size: number = 10) {
    if (!page || page < 1) {
      page = 1
    }
    if (!size || size < 1) {
      size = 10
    }

    const [adminUsers, total] = await Promise.all([
      this.prisma.adminUser.findMany({
        orderBy: { id: 'desc' },
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.adminUser.count()
    ])

    return {
      total,
      page,
      size,
      data: adminUsers.map((item) => ({
        id: item.id,
        role: item.role,
        nickname: item.nickname,
        username: item.username,
        createTime: item.createTime.getTime()
      }))
    }
  }

  hasAlphabet(password: string): boolean {
    return /[a-zA-Z]/.test(password)
  }

  hasDigit(password: string): boolean {
    return /\d/.test(password)
  }

  validatePassword(password: string) {
    return this.hasAlphabet(password) && this.hasDigit(password)
  }

  async postAdminUsers(data: UserRegisterRequestBodyDTO) {
    if (!data.password) {
      throw new ForbiddenException('密码不存在, 请先设置密码')
    }

    const adminUser = await this.prisma.adminUser.findFirst({
      where: {
        username: data.username
      }
    })
    if (adminUser) {
      throw new ForbiddenException('该账号已存在')
    }
    const result = this.validatePassword(data.password)
    if (!result) {
      throw new ForbiddenException('密码至少包含一个大写字母、一个小写字母、一位数字和一个特殊字符')
    }

    const { salt, hash } = this.userInitService.hashPassword(data.password)

    await this.prisma.adminUser.create({
      data: {
        username: data.username,
        nickname: data.nickname,
        password: hash,
        salt,
        role: 1,
        mfaSecret: ''
      }
    })
  }

  async patchAdminUsers(usersId: number, data: UserRegisterRequestBodyDTO) {
    const { user } = this.request

    if (user.role !== 0) {
      throw new ForbiddenException('你没编辑权限')
    }

    const adminUser = await this.prisma.adminUser.findUnique({
      where: {
        id: usersId
      }
    })
    if (!adminUser) {
      throw new BadRequestException('该用户不存在')
    }

    const updateData: any = {}
    updateData.nickname = data.nickname

    if (data.password) {
      const result = this.validatePassword(data.password)
      if (!result) {
        throw new ForbiddenException(
          '密码至少包含一个大写字母、一个小写字母、一位数字和一个特殊字符'
        )
      }
      const { salt, hash } = this.userInitService.hashPassword(data.password)
      updateData.salt = salt
      updateData.password = hash
    }

    await this.prisma.adminUser.update({
      where: {
        id: usersId
      },
      data: updateData
    })
  }

  async deleteAdminUsers(usersId: number) {
    const { user } = this.request

    if (user.role !== 0) {
      throw new ForbiddenException('你没有删除权限')
    }
    const adminUser = await this.prisma.adminUser.findUnique({
      where: {
        id: usersId
      }
    })
    if (!adminUser) {
      throw new BadRequestException('该用户不存在')
    }

    await this.prisma.adminUser.delete({
      where: {
        id: usersId
      }
    })
  }

  decrypt(encryptedDataStr: string, secret: string) {
    try {
      const encryptDatatoArray = encryptedDataStr.split(':')

      // 将 Base64 编码的加密数据转为 Buffer
      const encryptedData = encryptDatatoArray[1]

      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        secret,
        Buffer.from(encryptDatatoArray[0], 'base64')
      )

      // 解密数据
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      // 返回解密后的字符串
      return decrypted
    } catch (err) {
      throw new BadRequestException(`[解密数据失败]:${err.message}`)
    }
  }

  encrypt(plaintext, key) {
    try {
      const iv = crypto.randomBytes(16)

      // 创建 AES 加密器（使用 AES-128-CBC）
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
      let encrypted = cipher.update(plaintext, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      // 返回加密后的数据和 IV（Base64 编码）
      return iv.toString('base64') + ':' + encrypted
    } catch (err) {
      throw new BadRequestException(`[加密数据失败]:${err.message}`)
    }
  }
}
