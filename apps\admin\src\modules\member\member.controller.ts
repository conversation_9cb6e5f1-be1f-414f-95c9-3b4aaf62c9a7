import { Body, Controller, Delete, Get, Param, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { MemberService } from './member.service'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { MemberResponseDTO, MemberUpdateChannelDTO } from './member.dto'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('members')
@ApiTags('成员管理')
export class MemberController {
  constructor(private readonly memberService: MemberService) {}

  @Get()
  @ApiOperation({ summary: '获取成员列表' })
  @ApiOkResponse({ description: '操作成功', type: MemberResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码 <默认 1>'
  })
  @ApiQuery({
    name: 'size',
    required: false,
    type: Number,
    description: '每页数量 <默认 10>'
  })
  @ApiQuery({
    name: 'channelId',
    required: false,
    type: Number,
    description: '渠道id'
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '成员名称'
  })
  @ApiQuery({
    name: 'phone',
    required: false,
    type: String,
    description: '手机号码'
  })
  @ApiQuery({
    name: 'startTime',
    required: false,
    type: Number,
    description: '开始时间'
  })
  @ApiQuery({
    name: 'endTime',
    required: false,
    type: Number,
    description: '到期时间'
  })
  async getMembers(
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number,
    @Query('channelId', {
      transform: (value) => value || 0
    })
    channelId: number,
    @Query('name', {
      transform: (value) => value || ''
    })
    name: string,
    @Query('phone', {
      transform: (value) => value || ''
    })
    phone: string,
    @Query('startTime', {
      transform: (value) => value || 0
    })
    startTime: number,
    @Query('endTime', {
      transform: (value) => value || 0
    })
    endTime: number
  ) {
    const querys = {
      page,
      size,
      channelId,
      name,
      phone,
      startTime,
      endTime
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    const response = this.memberService.getMembers(querys)

    return response
  }

  @Put(`:id/channels`)
  @ApiOperation({ summary: '设置渠道' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async setChannel(@Param('id') id: number, @Body() body: MemberUpdateChannelDTO) {
    const response = this.memberService.setChannel(id, body)

    return response
  }

  @Delete(`:id`)
  @ApiOperation({ summary: '删除用户' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'phone',
    required: true,
    type: String,
    description: '手机号码'
  })
  async deleteMember(@Param('id') id: number, @Query('phone') phone: string) {
    const response = this.memberService.deleteMember(id, phone)

    return response
  }
}
