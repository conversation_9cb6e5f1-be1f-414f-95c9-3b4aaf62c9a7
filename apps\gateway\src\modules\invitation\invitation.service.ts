import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { InvitationEntity } from '@qdy/mongo'
import { type FastifyRequest } from 'fastify'
import { PrismaService } from '@qdy/mysql'
import { InvitationActionRequestBodyDTO, InvitationQueryDTO, StatusEnum } from './invitation.dto'
import { TeamMemberRole } from '../team/team.dto'

@Injectable()
export class InvitationService {
  constructor(
    @InjectModel(InvitationEntity.name) private invitationModel: Model<InvitationEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly prisma: PrismaService
  ) {}

  async getInvitation({ createTime = 0, page = 1, size = 10 }: InvitationQueryDTO) {
    const { user } = this.request

    const skip = (page - 1) * size

    const total = await this.invitationModel.countDocuments({
      inviteeUserId: user.id,
      createTime: {
        $gt: createTime
      }
    })

    const result = await this.invitationModel
      .find({
        inviteeUserId: user.id,
        createTime: {
          $gt: createTime
        }
      })
      .skip(skip)
      .limit(size)
      .sort({ createTime: 'desc' })

    const inviterUserIds = result.map((item) => item.inviterUserId)
    const teamIds = result.map((item) => item.teamId)

    const inviterUserList = await this.prisma.user.findMany({
      where: {
        id: {
          in: inviterUserIds
        }
      }
    })

    const teamList = await this.prisma.team.findMany({
      where: {
        id: {
          in: teamIds
        }
      }
    })

    const data =
      result.length > 0
        ? result.map((item) => ({
            id: item._id,
            inviterUserName: inviterUserList.find((user) => user.id === item.inviterUserId)?.name,
            inviterUserAvatar: inviterUserList.find((user) => user.id === item.inviterUserId)
              ?.avatar,
            teamName: teamList.find((team) => team.id === item.teamId)?.name,
            status: item.status,
            createTime: item.createTime
          }))
        : []

    return {
      total,
      page,
      size,
      data
    }
  }

  async invitationAction({ status, id }: InvitationActionRequestBodyDTO) {
    const invitation = await this.invitationModel.findById(id)
    const { user } = this.request

    if (!invitation) {
      throw new NotFoundException('邀请不存在')
    }

    if (invitation.inviteeUserId !== user.id) {
      throw new ForbiddenException('被邀请人与当前用户不匹配')
    }

    if (invitation.status !== 'pending') {
      throw new ForbiddenException('邀请已处理')
    }

    if (status === StatusEnum.Rejected) {
      invitation.status = status
      await invitation.save()
    } else {
      const team = await this.prisma.team.findUnique({
        where: {
          id: invitation.teamId
        },
        include: {
          vip: true
        }
      })

      if (!team) {
        throw new ForbiddenException('团队已不存在')
      }

      const teamCount = await this.prisma.teamMember.count({ where: { userId: user.id } })

      if (teamCount >= 10) {
        throw new ForbiddenException('当前账号已有10个团队,请先退出/解散团队后再加入')
      }

      const teamMember = await this.prisma.teamMember.findUnique({
        where: {
          userId_teamId: {
            teamId: invitation.teamId,
            userId: invitation.inviteeUserId
          }
        }
      })

      if (teamMember) {
        throw new ForbiddenException('您已加入该团队')
      }

      const [teamMemberCount, systemDosage] = await Promise.all([
        this.prisma.teamMember.count({
          where: {
            teamId: team.id
          }
        }),
        this.prisma.systemDosage.findFirst()
      ])

      if (team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
        if (team.vip.teamMemberNumberLimit <= teamMemberCount) {
          throw new ForbiddenException('团队成员数量已达上限')
        }
      } else if (systemDosage.standardTeamMemberNumberLimit <= teamMemberCount) {
        throw new ForbiddenException('团队成员数量已达上限')
      }

      await this.prisma.teamMember.create({
        data: {
          userId: invitation.inviteeUserId,
          teamId: invitation.teamId,
          role: TeamMemberRole.Member
        }
      })

      invitation.status = status
      await invitation.save()
    }
  }

  async allowInvitation(phone: string) {
    const { user } = this.request
    let isJoin = false
    let isInvitation = false

    const userInfo = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    if (!userInfo) {
      return {}
    }

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: userInfo.id
        }
      }
    })

    if (teamMember) {
      isJoin = true
    }

    const invitation = await this.invitationModel.find({
      inviteeUserId: userInfo.id,
      teamId: user.currentTeamId,
      inviterUserId: user.id,
      status: StatusEnum.Pending
    })

    if (invitation.length > 0) {
      isInvitation = true
    }

    return {
      user: {
        id: userInfo.id,
        phone: userInfo.phone,
        name: userInfo.name,
        avatar: userInfo.avatar
      },
      isJoin,
      isInvitation
    }
  }
}
