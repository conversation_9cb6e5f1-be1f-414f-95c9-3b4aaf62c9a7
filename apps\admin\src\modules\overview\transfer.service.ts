import { Inject, Injectable, Logger } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Queue, Worker } from 'bullmq'
import { OrderStatus } from '../vip/vip.dto'
import { SalesType } from './overview.dto'

@Injectable()
export class TransferService {
  dataQueue: Queue

  dataWorker: Worker

  serverNumber: number

  LOCK_TIMEOUT = 10 * 60 * 1000

  logger = new Logger('TransferService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService
  ) {}

  async onModuleInit() {
    this.dataQueue = new Queue('team-data-transfer', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.dataWorker = new Worker(
      'team-data-transfer',
      async (job) => {
        const { teamId } = job.data

        await this.migrateUserTableData(teamId)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // this.onDataTransferByTeam()
  }

  async onDataTransferByTeam() {
    const teamList = await this.prisma.team.findMany({
      orderBy: {
        createTime: 'desc'
      }
    })

    for (let i = 0; i < teamList.length; i++) {
      const team = teamList[i]
      await this.onTransferTeamData(team.id)
    }

    // const teamIdList = teamList.map((team) => team.id)

    // const activeArray = teamIdList.slice(0, this.serverNumber)

    // if (activeArray.length > 0) {
    //   for (let j = 0; j < activeArray.length; j++) {
    //     const teamId = activeArray[j]
    //     await this.dataQueue.add(
    //       'team-data-transfer',
    //       {
    //         teamId
    //       },
    //       {
    //         removeOnComplete: true,
    //         removeOnFail: true,
    //         jobId: `team-data-transfer-${teamId}`
    //       }
    //     )
    //   }
    // }

    // const otherContents = teamIdList.slice(this.serverNumber)

    // if (otherContents.length) {
    //   await this.cacheManager.store.client.hset(
    //     'teamIds:transfer',
    //     'teamIds',
    //     JSON.stringify(otherContents)
    //   )
    // }
  }

  async migrateUserTableData(teamId: number) {
    const isLock = await this.tryAcquireLock(`team-transfer-${teamId}`)

    if (!isLock) {
      // 数据迁移逻辑
      await this.onTransferTeamData(teamId)
    }

    const teamIds = await this.cacheManager.store.client.hget('teamIds:transfer', 'teamIds')

    if (teamIds) {
      const teamListArray = JSON.parse(teamIds)

      const activeArray = teamListArray.slice(0, this.serverNumber)

      if (activeArray.length > 0) {
        for (let j = 0; j < activeArray.length; j++) {
          const teamId = activeArray[j]
          await this.dataQueue.add(
            'team-data-transfer',
            {
              teamId
            },
            {
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `team-data-transfer-${teamId}`
            }
          )
        }
      }

      const otherContents = teamListArray.slice(this.serverNumber)

      if (otherContents.length) {
        await this.cacheManager.store.client.hset(
          'teamIds:transfer',
          'teamIds',
          JSON.stringify(otherContents)
        )
      } else {
        await this.cacheManager.store.client.hdel('teamIds:transfer', 'teamIds')
      }
    }
  }

  async onTransferTeamData(teamId: number) {
    this.logger.log(teamId)

    let salesType: SalesType = SalesType.NotBuy
    const orderList = await this.prisma.order.findMany({
      where: {
        teamId
      },
      orderBy: {
        fromTime: 'asc'
      }
    })

    for (let i = 0; i < orderList.length; i++) {
      const order = orderList[i]
      const data: { salesType: SalesType; payAmount: number } = {
        salesType: SalesType.NotBuy,
        payAmount: null
      }
      if (order.isGiftOrder) {
        data.salesType = SalesType.NotBuy
        data.payAmount = 0

        if (salesType === SalesType.NotBuy) {
          salesType = SalesType.NotBuy
        }
      } else {
        // eslint-disable-next-line no-lonely-if
        switch (order.salesType) {
          case SalesType.NotBuy:
            data.salesType = SalesType.FirstBuy
            if (
              order.orderStatus === OrderStatus.SUCCESS ||
              order.orderStatus === OrderStatus.REFUND
            ) {
              data.payAmount = order.payAmount ? order.payAmount : order.dueAmount
              salesType = SalesType.FirstBuy
            }
            break
          case SalesType.FirstBuy:
            data.salesType = SalesType.ReBuy
            if (
              order.orderStatus === OrderStatus.SUCCESS ||
              order.orderStatus === OrderStatus.REFUND
            ) {
              data.payAmount = order.payAmount ? order.payAmount : order.dueAmount
              salesType = SalesType.ReBuy
            }
            break
          case SalesType.ReBuy:
            data.salesType = SalesType.ReBuy

            if (
              order.orderStatus === OrderStatus.SUCCESS ||
              order.orderStatus === OrderStatus.REFUND
            ) {
              data.payAmount = order.payAmount ? order.payAmount : order.dueAmount
              salesType = SalesType.ReBuy
            }
            break
          default:
            data.salesType = SalesType.ReBuy

            if (
              order.orderStatus === OrderStatus.SUCCESS ||
              order.orderStatus === OrderStatus.REFUND
            ) {
              data.payAmount = order.payAmount ? order.payAmount : order.dueAmount
              salesType = SalesType.ReBuy
            }
            break
        }
        // if (salesType === SalesType.NotBuy) {
        //   data.salesType = SalesType.FirstBuy
        //   if (
        //     order.orderStatus === OrderStatus.SUCCESS ||
        //     order.orderStatus === OrderStatus.REFUND
        //   ) {
        //     data.payAmount = order.payAmount ? order.payAmount : order.dueAmount
        //     salesType = SalesType.FirstBuy
        //   }
        // } else if (salesType === SalesType.FirstBuy) {
        //   data.salesType = SalesType.ReBuy
        //   if (
        //     order.orderStatus === OrderStatus.SUCCESS ||
        //     order.orderStatus === OrderStatus.REFUND
        //   ) {
        //     data.payAmount = order.payAmount ? order.payAmount : order.dueAmount
        //     salesType = SalesType.ReBuy
        //   }
        // } else {
        //   data.salesType = SalesType.ReBuy

        //   if (
        //     order.orderStatus === OrderStatus.SUCCESS ||
        //     order.orderStatus === OrderStatus.REFUND
        //   ) {
        //     data.payAmount = order.payAmount ? order.payAmount : order.dueAmount
        //     salesType = SalesType.ReBuy
        //   }
        // }
      }

      await this.prisma.order.update({
        where: {
          id: order.id
        },
        data
      })
    }

    if (orderList.length > 0) {
      await this.prisma.team.update({
        where: {
          id: teamId
        },
        data: {
          salesType
        }
      })
    }
  }

  private async tryAcquireLock(key: string): Promise<boolean> {
    const lock = ((await this.cacheManager.get(key)) || 0) as number

    if (!lock) {
      await this.cacheManager.set(`team-transfer-${key}`, 1, this.LOCK_TIMEOUT)
    }
    return !!lock
  }
}
