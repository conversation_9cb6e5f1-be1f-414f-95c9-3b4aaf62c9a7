import { ForbiddenException, HttpException, Inject, Injectable, Logger } from '@nestjs/common'
import {
  AutoresponderContentChildType,
  AutoresponderContentType,
  AutoresponderKeywordContentRs,
  AutoresponderKeywordCreateDTO,
  AutoresponderKeywordRedisValue,
  AutoresponderOpuser,
  AutoresponderPlatformAccountType,
  AutoresponderTriggerType,
  AutoresponderType
} from './autoresponder.dto'
import { FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import { Autoresponder, PlatformAccount, PrismaService } from '@qdy/mysql'
import { TeamMemberRole } from '../team/team.dto'
import { AnyObject } from 'mongoose'
import { AutoresponderKeywordKey } from './constant'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'

@Injectable()
export class AutoresponderKeywordService {
  logger = new Logger('InteractService.keyword')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly prisma: PrismaService
  ) {}

  async createAutoresponder(data: AutoresponderKeywordCreateDTO) {
    const { user } = this.request

    const { teamMember, platformAccountIds, platformAccounts } = await this.checkRole({
      userId: user.id,
      propPlatformAccountIds: data.platformAccountIds
    })

    const newPlatformAccountIds =
      data.platformAccountType === AutoresponderPlatformAccountType.All
        ? [...platformAccountIds]
        : data.platformAccountIds

    const platfromAccountIdsByOpuser = data.opusers.map((item) => item.platformAccountId)

    const hasOtherPlatformAccountIds = await this.checkPlatformAccount({
      // 需要对账号id进行去重
      platformAccountIds: [...new Set([...newPlatformAccountIds, ...platfromAccountIdsByOpuser])],
      platform: data.platform
    })

    if (!hasOtherPlatformAccountIds) {
      throw new ForbiddenException('包含其他平台账号')
    }

    if (data.contentType === AutoresponderContentType.Greeting) {
      await this.checkLock({
        teamId: teamMember.teamId,
        platformAccountIds: newPlatformAccountIds
      })
    }

    const newKeyword = await this.prisma.autoresponder.create({
      data: {
        ...data,
        isNew: true,
        contents: data.contents as AnyObject[],
        opusers: data.opusers as AnyObject[],
        platformAccountIds: newPlatformAccountIds,
        teamId: teamMember.teamId,
        ownerTeamMemberId: user.id
      }
    })

    const platformAccountMap: Record<string, PlatformAccount> = {}
    platformAccounts.forEach((platformAccount) => {
      platformAccountMap[platformAccount.id] = platformAccount
    })

    if (data.state && data.platformAccountIds && data.platformAccountIds.length) {
      data.platformAccountIds.forEach((id) => {
        const platformAccount = platformAccountMap[id]

        if (platformAccount) {
          this.cacheManager.store.client
            .hget(AutoresponderKeywordKey, platformAccount.openId)
            .then((value) => {
              const arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
              // autoresponder-update-value 全局搜索需要修改的地方

              const cardContent = data.contents.filter(
                (item) => item.messageType === AutoresponderContentChildType.Card
              )

              if (cardContent.length > 0) {
                cardContent.forEach((content) => {
                  const { cardInfo } = content
                  const cardIdArray = cardInfo.map((card) => card.accountId)

                  const accountIds1 = new Set(data.platformAccountIds)
                  const accountIds2 = new Set(cardIdArray)

                  if (accountIds1.size !== accountIds2.size) {
                    throw new ForbiddenException('留资卡片账号与选择账号不相符')
                  }

                  const newAccounts = Array.from(accountIds1).filter(
                    (element) => !accountIds2.has(element)
                  )

                  if (newAccounts.length > 0) {
                    throw new ForbiddenException('留资卡片账号与选择账号不相符')
                  }
                })
              }

              arr.push({
                scene: data.scene,
                trigger: data.trigger,
                keyword: data.keywords,
                rule: data.rule,
                contents: data.contents,
                contentType: data.contentType,
                stopReply: data.stopReply,
                stopInterval: data.stopInterval,
                stopTime: data.stopTime,
                isDelay: data.isDelay,
                delayTime: data.delayTime,
                state: data.state,
                executionCount: data.executionCount ?? 0,
                isNew: true,
                autoresponderId: newKeyword.id,
                platform: platformAccount.platform
              })
              this.cacheManager.store.client.hset(
                AutoresponderKeywordKey,
                platformAccount.openId,
                JSON.stringify(arr)
              )
            })
        }
      })
    }

    if (data.state && data.opusers && data.opusers.length) {
      data.opusers.forEach((item) => {
        const platformAccount = platformAccountMap[item.platformAccountId]
        if (platformAccount) {
          const opusersKey = `${platformAccount.openId}:${item.id}`

          this.cacheManager.store.client.hget(AutoresponderKeywordKey, opusersKey).then((value) => {
            const arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
            // autoresponder-update-value 全局搜索需要修改的地方
            arr.push({
              scene: data.scene,
              trigger: data.trigger,
              keyword: data.keywords,
              rule: data.rule,
              contents: data.contents,
              contentType: data.contentType,
              state: data.state,
              executionCount: data.executionCount ?? 0,
              stopReply: data.stopReply,
              stopInterval: data.stopInterval,
              isDelay: data.isDelay,
              delayTime: data.delayTime,
              stopTime: data.stopTime,
              isNew: true,
              autoresponderId: newKeyword.id,
              platform: platformAccount.platform
            })
            this.cacheManager.store.client.hset(
              AutoresponderKeywordKey,
              opusersKey,
              JSON.stringify(arr)
            )
          })
        }
      })
    }
  }

  async getAutoresponderById(id: number) {
    const { user } = this.request

    const { teamMember } = await this.checkRole({
      userId: user.id,
      updateId: id
    })

    const [autoresponder, platformAccounts, variables] = await Promise.all([
      this.prisma.autoresponder.findUnique({
        where: {
          id
        }
      }),
      this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      }),
      this.prisma.variable.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    ])

    if (!autoresponder) {
      throw new ForbiddenException('自动回复不存在')
    }

    const variablesMap = variables.reduce((memo, current) => {
      memo[current.id] = current
      return memo
    }, {})

    const platformAccountIds = []
    const itemPlatformAccountIds = new Set<number>(autoresponder.platformAccountIds as number[])
    platformAccounts.forEach((platformAccount) => {
      if (itemPlatformAccountIds.has(platformAccount.id)) {
        platformAccountIds.push({
          name: platformAccount.name,
          avatar: platformAccount.avatar,
          id: platformAccount.id,
          accountRole: platformAccount.accountRole,
          platform: platformAccount.platform,
          status: platformAccount.status,
          scopes: platformAccount.unauthorize,
          expiresIn: platformAccount.expiresIn,
          isBind: platformAccount.isBind,
          remark: platformAccount.remark,
          expiresTime: !platformAccount.expiresIn
            ? 0
            : platformAccount.expiresIn * 1000 * 5 * 2 + platformAccount.tokenTime.getTime()
        })
      }
    })

    if (autoresponder.contents instanceof Array) {
      for (let i = 0; i < autoresponder.contents.length; i++) {
        const _content = autoresponder.contents[i] as unknown as AutoresponderKeywordContentRs
        for (let j = 0; j < _content.texts.length; j++) {
          if (_content.texts[j].variableId) {
            ;(autoresponder.contents[i] as any).texts[j].variable =
              variablesMap[_content.texts[j].variableId]
          }
        }
      }
    }

    return {
      ...autoresponder,
      platformAccountIds
    }
  }

  async getAutoresponder({
    name,
    page,
    size,
    type,
    rule,
    platform,
    trigger
  }: {
    name: string
    page: number
    size: number
    type: number
    rule: string
    platform: number
    trigger: number
  }) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const where: Parameters<typeof this.prisma.autoresponder.findMany>[0]['where'] = {
      teamId: teamMember.teamId,
      rule,
      name: {
        contains: name
      },
      isNew: true,
      platform,
      trigger
    }

    if (!name) {
      delete where.name
    }

    if (type) {
      switch (type) {
        case AutoresponderType.CommentAutoresponder:
          where.contentType = 0
          where.scene = 1
          break
        case AutoresponderType.ChatAutoresponder:
          where.contentType = 0
          where.scene = 0
          break
        case AutoresponderType.Greeting:
          where.contentType = 1
          break
        default:
          break
      }
    }

    if (!rule) {
      delete where.rule
    }

    const [autoresponders, total, platformAccounts] = await Promise.all([
      this.prisma.autoresponder.findMany({
        orderBy: {
          id: 'desc'
        },
        where,
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.autoresponder.count({
        where
      }),
      this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    ])

    return {
      data: autoresponders.map((item) => {
        const platformAccountIds = []

        const itemPlatformAccountIds = new Set<number>(item.platformAccountIds as number[])
        platformAccounts.forEach((platformAccount) => {
          if (itemPlatformAccountIds.has(platformAccount.id)) {
            platformAccountIds.push({
              name: platformAccount.name,
              avatar: platformAccount.avatar,
              id: platformAccount.id
            })
          }
        })

        let type = AutoresponderType.CommentAutoresponder

        if (!item.contentType && !item.scene) {
          type = AutoresponderType.ChatAutoresponder
        } else if (item.contentType) {
          type = AutoresponderType.Greeting
        }

        return {
          ...item,
          type,
          degree: item.singleDegree + item.commentDegree,
          platformAccountsLength: platformAccountIds.length,
          opusersLength: (item.opusers as AnyObject[]).length,
          platformAccountIds
        }
      }),
      total,
      page,
      size
    }
  }

  async deleteAutoresponder(id: number) {
    const { user } = this.request

    const { teamMember, platformAccounts } = await this.checkRole({
      userId: user.id,
      updateId: id
    })

    try {
      const res = await this.prisma.autoresponder.delete({
        where: {
          id,
          teamId: teamMember.teamId
        }
      })

      const platformAccountMap = {}
      platformAccounts.forEach((platformAccount) => {
        platformAccountMap[platformAccount.id] = platformAccount
      })

      if (res.platformAccountIds && (res.platformAccountIds as number[]).length) {
        ;(res.platformAccountIds as number[]).forEach((id) => {
          const platformAccount = platformAccountMap[id]
          if (platformAccount) {
            this.cacheManager.store.client
              .hget(AutoresponderKeywordKey, platformAccount.openId)
              .then((value) => {
                const arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
                const newArr = arr.filter((item) => item.autoresponderId !== res.id)
                this.cacheManager.store.client.hset(
                  AutoresponderKeywordKey,
                  platformAccount.openId,
                  JSON.stringify(newArr)
                )
              })
          }
        })
      }

      if (res.opusers && (res.opusers as unknown as AutoresponderOpuser[]).length) {
        ;(res.opusers as unknown as AutoresponderOpuser[]).forEach((item) => {
          const platformAccount = platformAccountMap[item.platformAccountId]
          if (platformAccount) {
            this.cacheManager.store.client
              .hget(AutoresponderKeywordKey, platformAccount.openId)
              .then((value) => {
                const arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
                const newArr = arr.filter((item) => item.autoresponderId !== res.id)
                this.cacheManager.store.client.hset(
                  AutoresponderKeywordKey,
                  platformAccount.openId,
                  JSON.stringify(newArr)
                )
              })
          }
        })
      }
    } catch (error) {
      this.logger.error(error)
      throw new ForbiddenException('删除失败')
    }
  }

  async updateAutoresponder(data: AutoresponderKeywordCreateDTO & { id: number }) {
    const { user } = this.request

    const { teamMember, platformAccountIds, platformAccounts } = await this.checkRole({
      userId: user.id,
      updateId: data.id,
      propPlatformAccountIds: data.platformAccountIds
    })

    const newPlatformAccountIds =
      data.platformAccountType === AutoresponderPlatformAccountType.All
        ? [...platformAccountIds]
        : data.platformAccountIds

    if (data.contentType === AutoresponderContentType.Greeting) {
      await this.checkLock({
        teamId: teamMember.teamId,
        platformAccountIds: newPlatformAccountIds,
        oldId: data.id
      })
    }

    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { contentType: __, scene: _, ...rest } = data
      const updateData = {
        ...rest,
        contents: data.contents as AnyObject[],
        opusers: data.opusers as AnyObject[],
        platformAccountIds: newPlatformAccountIds
      }
      const oldRes = await this.prisma.autoresponder.findUnique({
        where: {
          id: data.id
        }
      })
      const res = await this.prisma.autoresponder.update({
        data: updateData,
        where: {
          id: data.id,
          teamId: teamMember.teamId
        }
      })

      this.updateRedis({
        res: { ...res, ...updateData },
        oldRes,
        platformAccounts
      })
    } catch (error) {
      this.logger.error(error)
      throw new ForbiddenException('更新失败')
    }
  }

  async updateAutoresponderState(id: number, state: boolean) {
    const { user } = this.request

    const { teamMember, platformAccounts } = await this.checkRole({
      userId: user.id,
      updateId: id
    })

    try {
      const oldRes = await this.prisma.autoresponder.findUnique({
        where: {
          id
        }
      })
      const res = await this.prisma.autoresponder.update({
        data: {
          state
        },
        where: {
          id,
          teamId: teamMember.teamId
        }
      })

      this.updateRedis({
        res: { ...res, state },
        oldRes,
        platformAccounts
      })
    } catch (error) {
      this.logger.error(error)
      throw new ForbiddenException('更新失败')
    }
  }

  async checkRole({
    userId,
    updateId,
    propPlatformAccountIds
  }: {
    propPlatformAccountIds?: number[]
    updateId?: number
    userId: number
  }) {
    const { user } = this.request
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    if (teamMember.role === TeamMemberRole.Member && updateId) {
      const autoresponder = await this.prisma.autoresponder.findUnique({
        where: {
          id: updateId
        }
      })

      if (!autoresponder) {
        throw new ForbiddenException('自动回复不存在')
      }

      const memberPlatformAccoutIds = new Set(teamMember.platformAccounts.map((item) => item.id))
      ;(autoresponder.platformAccountIds as number[]).forEach((id) => {
        if (!memberPlatformAccoutIds.has(id)) {
          throw new ForbiddenException('权限不足')
        }
      })
    }

    if (propPlatformAccountIds) {
      if (updateId) {
        const platformAccounts = await this.prisma.platformAccount.findMany({
          where: {
            teamId: teamMember.teamId
          }
        })

        const platformAccountIds = new Set(platformAccounts.map((item) => item.id))

        propPlatformAccountIds.forEach((id) => {
          if (!platformAccountIds.has(id)) {
            throw new ForbiddenException('平台账号不存在')
          }
        })

        return { teamMember, platformAccountIds, platformAccounts }
      }

      if (teamMember.role === TeamMemberRole.Member) {
        const platformAccountIds = new Set(teamMember.platformAccounts.map((item) => item.id))

        propPlatformAccountIds.forEach((id) => {
          if (!platformAccountIds.has(id)) {
            throw new ForbiddenException('平台账号不存在')
          }
        })

        return { teamMember, platformAccountIds, platformAccounts: teamMember.platformAccounts }
      }

      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })

      const platformAccountIds = new Set(platformAccounts.map((item) => item.id))

      propPlatformAccountIds.forEach((id) => {
        if (!platformAccountIds.has(id)) {
          throw new ForbiddenException('平台账号不存在')
        }
      })

      return { teamMember, platformAccountIds, platformAccounts }
    }

    if (teamMember.role === TeamMemberRole.Member) {
      return { teamMember, platformAccounts: teamMember.platformAccounts }
    }

    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        teamId: teamMember.teamId
      }
    })

    return { teamMember, platformAccounts }
  }

  async checkPlatformAccount({
    platformAccountIds,
    platform
  }: {
    platformAccountIds: number[]
    platform: number
  }) {
    const result = await this.prisma.platformAccount.findMany({
      where: {
        id: {
          in: platformAccountIds
        },
        platform
      }
    })

    return result.length === platformAccountIds.length
  }

  async updateRedis({
    res,
    oldRes,
    platformAccounts
  }: {
    res: Autoresponder
    oldRes: Autoresponder
    platformAccounts: PlatformAccount[]
  }) {
    const platformAccountMap = {}
    if (!platformAccounts) {
      this.logger.error('没有获取到平台账号, ', res)
      return
    }
    platformAccounts.forEach((platformAccount) => {
      platformAccountMap[platformAccount.id] = platformAccount
    })

    const genValue = (platformAccount: PlatformAccount) => {
      return {
        // autoresponder-update-value 全局搜索需要修改的地方,
        scene: res.scene,
        trigger: res.trigger,
        keyword: res.keywords,
        stopReply: res.stopReply,
        stopInterval: res.stopInterval,
        stopTime: res.stopTime,
        isDelay: res.isDelay,
        delayTime: res.delayTime,
        rule: res.rule,
        contents: res.contents as AnyObject[],
        contentType: res.contentType,
        state: res.state,
        executionCount: res.executionCount ?? 0,
        isNew: res.isNew,
        platform: platformAccount.platform,
        autoresponderId: res.id
      }
    }

    const oldTask = []

    for (let i = 0; i < (oldRes.platformAccountIds as number[]).length; i++) {
      const id = oldRes.platformAccountIds[i]

      const value = await this.cacheManager.store.client.hget(
        AutoresponderKeywordKey,
        platformAccountMap[id].openId
      )

      if (value) {
        try {
          let arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
          if (arr.length) {
            arr = arr.filter((item) => item.autoresponderId !== res.id)
            oldTask.push(
              this.cacheManager.store.client.hset(
                AutoresponderKeywordKey,
                platformAccountMap[id].openId,
                JSON.stringify(arr)
              )
            )
          }
        } catch (e) {
          this.logger.error('update redis', e)
        }
      }
    }

    for (let i = 0; i < (oldRes.opusers as unknown as AutoresponderOpuser[]).length; i++) {
      const item = oldRes.opusers[i] as AutoresponderOpuser
      const opusersKey = `${platformAccountMap[item.platformAccountId].openId}:${item.id}`

      const value = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, opusersKey)

      // const value = allKeyword[opusersKey]

      if (value) {
        try {
          let arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
          if (arr.length) {
            arr = arr.filter((item) => item.autoresponderId !== res.id)
            oldTask.push(
              this.cacheManager.store.client.hset(
                AutoresponderKeywordKey,
                opusersKey,
                JSON.stringify(arr)
              )
            )
          }
        } catch (e) {
          this.logger.error('update redis', e)
        }
      }
    }

    await Promise.all(oldTask)
    ;(res.opusers as unknown as AutoresponderOpuser[]).forEach((item) => {
      const opusersKey = `${platformAccountMap[item.platformAccountId].openId}:${item.id}`
      this.cacheManager.store.client.hget(AutoresponderKeywordKey, opusersKey).then((value) => {
        const arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
        if (arr.length) {
          const index = arr.findIndex((item) => item.autoresponderId === res.id)
          const value = genValue(platformAccountMap[item.platformAccountId])
          if (index >= 0) {
            arr[index] = value
            this.cacheManager.store.client.hset(
              AutoresponderKeywordKey,
              opusersKey,
              JSON.stringify(arr)
            )
          } else {
            arr.push(value)
            this.cacheManager.store.client.hset(
              AutoresponderKeywordKey,
              opusersKey,
              JSON.stringify(arr)
            )
          }
        } else {
          arr.push(genValue(platformAccountMap[item.platformAccountId]))
          this.cacheManager.store.client.hset(
            AutoresponderKeywordKey,
            opusersKey,
            JSON.stringify(arr)
          )
        }
      })
    })
    ;(res.platformAccountIds as number[]).forEach((id) => {
      this.cacheManager.store.client
        .hget(AutoresponderKeywordKey, platformAccountMap[id].openId)
        .then((value) => {
          try {
            const arr = (JSON.parse(value) ?? []) as AutoresponderKeywordRedisValue[]
            if (arr.length) {
              const index = arr.findIndex((item) => item.autoresponderId === res.id)
              const value = genValue(platformAccountMap[id])
              if (index >= 0) {
                arr[index] = value
                this.cacheManager.store.client.hset(
                  AutoresponderKeywordKey,
                  platformAccountMap[id].openId,
                  JSON.stringify(arr)
                )
              } else {
                arr.push(value)
                this.cacheManager.store.client.hset(
                  AutoresponderKeywordKey,
                  platformAccountMap[id].openId,
                  JSON.stringify(arr)
                )
              }
            } else {
              arr.push(genValue(platformAccountMap[id]))
              this.cacheManager.store.client.hset(
                AutoresponderKeywordKey,
                platformAccountMap[id].openId,
                JSON.stringify(arr)
              )
            }
          } catch (e) {
            this.logger.error('update redis', e)
          }
        })
    })
  }

  /**
   * 检查欢迎语是否已经存在 by 账号
   * @param param0
   */
  async checkLock({
    platformAccountIds,
    oldId,
    teamId
  }: {
    teamId: number
    platformAccountIds: number[]
    oldId?: number
  }) {
    if (!platformAccountIds.length) {
      throw new ForbiddenException('请选择账号')
    }

    const isLock = await this.prisma.autoresponder.findFirst({
      where: {
        contentType: AutoresponderContentType.Greeting,
        teamId,
        isNew: true,
        NOT: {
          id: oldId
        },
        OR: [
          ...platformAccountIds.map((id) => ({
            platformAccountIds: {
              array_contains: id
            }
          }))
        ]
      }
    })

    if (isLock) {
      throw new ForbiddenException('账号已经创建了欢迎语')
    }
  }

  async getAutoresponderCountByPlatform(platform: number) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const counts = await this.prisma.autoresponder.groupBy({
      by: ['contentType', 'scene', 'trigger'],
      where: {
        teamId: teamMember.teamId,
        platform,
        isNew: true
      },
      _count: {
        rule: true
      }
    })

    const welcomeStrategyCount =
      counts.find(
        (item) =>
          item.trigger === AutoresponderTriggerType.welcome &&
          item.contentType === 1 &&
          item.scene === 0
      )?._count.rule || 0

    const commentStrategyCount =
      counts.find((item) => item.contentType === 0 && item.scene === 1)?._count.rule || 0

    const chatStrategyCount =
      counts.find(
        (item) =>
          item.trigger === AutoresponderTriggerType.Chat &&
          item.contentType === 0 &&
          item.scene === 0
      )?._count.rule || 0

    const likeChatStrategyCount =
      counts.find(
        (item) =>
          item.trigger === AutoresponderTriggerType.Like &&
          item.contentType === 0 &&
          item.scene === 0
      )?._count.rule || 0

    const followChatStrategyCount =
      counts.find(
        (item) =>
          item.trigger === AutoresponderTriggerType.Follow &&
          item.contentType === 0 &&
          item.scene === 0
      )?._count.rule || 0

    const commentChatStrategyCount =
      counts.find(
        (item) =>
          item.trigger === AutoresponderTriggerType.Comment &&
          item.contentType === 0 &&
          item.scene === 0
      )?._count.rule || 0

    return {
      welcomeStrategyCount,
      chatStrategyCount,
      commentStrategyCount,
      likeChatStrategyCount,
      followChatStrategyCount,
      commentChatStrategyCount
    }
  }
}
