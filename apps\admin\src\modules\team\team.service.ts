import { ForbiddenException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { type Cache } from 'cache-manager'
import { type RedisStore } from 'cache-manager-ioredis-yet'
import { PrismaService } from '@qdy/mysql'
import { AdminTeamQueryDTO, RefundOrderRequestBodyDTO } from './team.dto'
import { customAlphabet } from 'nanoid'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { DailyOverviewEntity, OverviewEntity } from '@qdy/mongo'
import dayjs from 'dayjs'
import { AutoresponderKeywordKey, OrderStatus, TeamMemberStatus } from '../vip/vip.dto'
import { OrderManageService } from 'apps/common/modules'
import { wechatLogout } from '../vip/external.wechat'
import { Platform } from '@qdy/utils'
import { SalesType } from '../overview/overview.dto'

@Injectable()
export class TeamService {
  logger = new Logger('AccountService')

  nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 18)

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @InjectModel(OverviewEntity.name) private overviewModel: Model<OverviewEntity>,
    @InjectModel(DailyOverviewEntity.name) private dailyOverviewModel: Model<DailyOverviewEntity>,
    private readonly orderManageService: OrderManageService
  ) {}

  /**
   * 获取团队列表
   * @param param0
   * @returns
   */
  async getTeams({
    name,
    createEndTime,
    createStartTime,
    vipExpirationEndTime,
    vipExpirationStartTime,
    phone,
    invitationCode,
    isVip,
    salesType,
    page = 1,
    size = 10,
    sort
  }: AdminTeamQueryDTO) {
    const where: Parameters<typeof this.prisma.team.findMany>[0]['where'] = {
      name: {
        contains: name?.trim()
      },
      isDelete: false,
      invitationCode,
      createTime: {
        gte: new Date(createStartTime),
        lte: new Date(createEndTime)
      },
      vip: {}
    }

    if (salesType) {
      if (salesType === 'Buy') {
        where.salesType = {
          in: [SalesType.FirstBuy, SalesType.ReBuy]
        }
      } else {
        where.salesType = salesType
      }
    }

    const orderby: Parameters<typeof this.prisma.team.findMany>[0]['orderBy'] = []

    if (sort) {
      const allowSortFields = ['platformAccounts', 'members']
      const allowSortOrders = ['asc', 'desc']

      // 添加格式校验：确保 sort 参数包含分隔符
      if (!sort.includes('.')) {
        throw new ForbiddenException('排序参数格式错误，应为 field.order')
      }

      // 解构时设置默认值防止 undefined
      const [field, order = ''] = sort.split('.')

      // 检查 order 是否合法（包括空值）
      if (!order || !allowSortOrders.includes(order.toLowerCase())) {
        throw new ForbiddenException('排序方式不正确')
      }

      if (allowSortFields.includes(field)) {
        orderby.push({
          [field]: {
            _count: order.toLowerCase() // 统一转为小写
          }
        })
      }
    }

    orderby.push({
      createTime: 'desc'
    })

    if (isVip !== undefined) {
      if (isVip) {
        where.vip.expirationTime = {
          gte: new Date()
        }
      } else {
        where.vip.expirationTime = {
          lt: new Date()
        }
      }
    }

    if (vipExpirationStartTime && vipExpirationEndTime) {
      where.vip.expirationTime = {
        gte: new Date(vipExpirationStartTime!),
        lte: new Date(vipExpirationEndTime!)
      }
    }

    if (!createEndTime || !createStartTime) {
      delete where.createTime
    }

    if (!name) {
      delete where.name
    }

    if (!invitationCode) {
      delete where.invitationCode
    }

    let phoneWhere = {}

    if (phone) {
      const userList = await this.prisma.user.findMany({
        where: {
          phone: {
            contains: phone
          }
        }
      })

      const ids = []
      if (userList) {
        userList.forEach((element) => {
          ids.push(element.id)
        })

        phoneWhere = {
          ownerId: {
            in: ids
          }
        }
      }
    }

    const [data, total] = await Promise.all([
      this.prisma.team.findMany({
        where: {
          ...phoneWhere,
          ...where
        },
        skip: (page - 1) * size,
        take: size,
        orderBy: orderby,
        include: {
          vip: true,
          platformAccounts: true,
          members: true,
          Refund: true,
          Order: {
            where: {
              orderStatus: OrderStatus.SUCCESS,
              salesType: {
                in: [SalesType.FirstBuy, SalesType.ReBuy]
              }
            }
          }
        }
      }),
      this.prisma.team.count({
        where: {
          ...phoneWhere,
          ...where
        }
      })
    ])

    const users = await this.prisma.user.findMany({
      where: {
        id: {
          in: data.map((item) => item.ownerId)
        }
      }
    })

    const usersMap = new Map(users.map((item) => [item.id, item]))

    return {
      total,
      page,
      size,
      data: data.map((item) => {
        const platformAccountDouyin = []
        const platformAccountWechat = []

        let platformAccountDouyinCount = 0
        let platformAccountWechatCount = 0

        item.platformAccounts.forEach((item) => {
          if (item.platform === 0) {
            platformAccountDouyin.push(item)
            platformAccountDouyinCount += 1
          } else if (item.platform === 1) {
            platformAccountWechat.push(item)
            platformAccountWechatCount += 1
          }
        })

        // interestCount: order.interestCount,
        // platformAccountCount: order.interest.platformAccountCount * order.interestCount,
        // teamMemberCount: order.interest.memberCount * order.interestCount,
        // messageCount: order.interest.messageCount * order.interestCount,

        return {
          ...item,
          owner: usersMap.get(item.ownerId) || {},
          platformAccountDouyin,
          platformAccountWechat,
          platformAccountDouyinCount,
          platformAccountWechatCount,
          platformAccountCount: item.platformAccounts.length,
          members: item.members.length,
          orders: item.Order.length,
          vip: item.vip && {
            ...item.vip,
            createTime: item.vip.createTime.getTime(),
            expirationTime: item.vip.expirationTime ? item.vip.expirationTime.getTime() : 0
          },
          hasRefund: item.Refund.length > 0,
          createTime: item.createTime.getTime()
        }
      })
    }
  }

  async getTeamByPhone(phone: string) {
    const user = await this.prisma.user.findUnique({
      where: {
        phone
      }
    })

    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new NotFoundException('用户没有加入任何团队')
    }

    return [{ ...teamMember.team, createTime: teamMember.team.createTime.getTime() }]
  }

  async getTeamMessages(teamId: number) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)
    startDate.setHours(0, 0, 0, 0)

    const result = await this.dailyOverviewModel.find({
      teamId,
      createTime: {
        $gte: dayjs(startDate).format('YYYY-MM-DD'),
        $lte: dayjs().format('YYYY-MM-DD')
      }
    })

    return result
  }

  async getTeamOrder(invitationCode: string) {
    const team = await this.prisma.team.findUnique({
      where: {
        invitationCode
      },
      include: {
        vip: true
      }
    })

    if (team) {
      const result = await this.prisma.order.findFirst({
        where: {
          teamId: team.id,
          orderStatus: OrderStatus.PENDING,
          expireTime: {
            gte: new Date()
          }
        }
      })

      if (team.vip.expirationTime < new Date()) {
        return {
          id: team.id,
          hasPendingOrder: !!result,
          avatar: team.avatar,
          name: team.name,
          price: team.vip.price,
          createTime: team.vip.createTime,
          expirationTime: team.vip.expirationTime,
          remainingDay: 0
        }
      }

      const remainingDay = dayjs(team.vip.expirationTime).diff(dayjs(), 'day')

      const oldPrice = (team.vip.price / team.vip.day) * remainingDay

      const oldVipPrice = await this.prisma.orderRecord.aggregate({
        _sum: {
          price: true
        },
        where: {
          teamId: team.id
        }
      })

      const availableRefundPrice = (oldVipPrice._sum.price / team.vip.day) * remainingDay

      return {
        id: team.id,
        hasPendingOrder: !!result,
        avatar: team.avatar,
        name: team.name,
        month: team.vip.month,
        freeMonth: team.vip.freeMonth,
        interestCount: team.vip.interestCount,
        price: team.vip.price,
        payTime: team.vip.createTime,
        expirationTime: team.vip.expirationTime,
        day: team.vip.day,
        remainingDay,
        refundPrice: oldVipPrice._sum.price || 0,
        availableRefundPrice,
        balance: parseFloat(oldPrice.toFixed(2)),
        hasVip:
          team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()
      }
    }

    return null
  }

  async getDau() {
    const startDate = dayjs().subtract(30, 'day')

    const res = await this.dailyOverviewModel.aggregate([
      {
        $match: {
          createdAt: {
            $gte: startDate.toDate(),
            $lt: new Date() // 当前日期
          }
        }
      },
      {
        $addFields: {
          totalCount: {
            $sum: ['$singleCount', '$autoSingleCount', '$autoCommentCount', '$commentCount']
          }
        }
      },
      {
        $match: {
          totalCount: { $gte: 10 }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ])

    return {
      list: res.map((item) => ({
        date: item._id,
        count: item.count
      }))
    }
  }

  async getTeamOrderRecord(teamId: number) {
    const result = await this.prisma.orderRecord.findMany({
      where: {
        teamId
      }
    })

    return result
  }

  async getTeamRefund(teamId: number) {
    const result = await this.prisma.refund.findMany({
      where: {
        teamId
      }
    })

    return result
  }

  async getOrderListByRefund(teamId: number) {
    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })

    if (!team) {
      throw new ForbiddenException('团队不存在')
    }

    if (!team.vip.expirationTime || team.vip.expirationTime.getTime() < Date.now()) {
      throw new ForbiddenException('团队VIP已过期')
    }

    return this.orderManageService.orderListByRefund(teamId)
  }

  async refund(teamId: number, { refundAmount, realityPrice, remark }: RefundOrderRequestBodyDTO) {
    const team = await this.prisma.team.findUnique({
      where: {
        id: teamId
      },
      include: {
        vip: true
      }
    })

    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    if (!team.vip.expirationTime || team.vip.expirationTime.getTime() < Date.now()) {
      throw new ForbiddenException('VIP已过期')
    }

    await this.orderManageService.refundOrder({
      teamId,
      refundAmount,
      actualRefundAmount: realityPrice,
      remark
    })

    const platformAccounts = await this.prisma.platformAccount.findMany({
      where: {
        teamId: team.id
      }
    })

    const ids = []
    const wechatIds = []
    const cacheTask = []

    for (let j = 0; j < platformAccounts.length; j++) {
      const platformAccount = platformAccounts[j]
      const platformAccountCache = await this.cacheManager.store.client.hget(
        AutoresponderKeywordKey,
        platformAccount.openId
      )

      if (platformAccountCache) {
        const value = JSON.parse(platformAccountCache)
        value.status = TeamMemberStatus.Disable

        cacheTask.push(
          this.cacheManager.store.client.hset(
            AutoresponderKeywordKey,
            platformAccount.openId,
            JSON.stringify(value)
          )
        )
      }
      ids.push(platformAccount.id)

      if (platformAccount.platform === Platform.Wechat) {
        await wechatLogout({
          appId: platformAccount.appId,
          isNew: platformAccount.isNew
        })
        wechatIds.push(platformAccount.id)
      }
    }

    await Promise.all(cacheTask)
  }
}
