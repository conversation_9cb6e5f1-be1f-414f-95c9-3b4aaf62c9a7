import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { type Cache } from 'cache-manager'
import { type RedisStore } from 'cache-manager-ioredis-yet'
import { PrismaService } from '@qdy/mysql'
import dayjs from 'dayjs'
import { OrderStatus } from '../vip/vip.dto'
import { Cron } from '@nestjs/schedule'
import { SalesType } from '../overview/overview.dto'

@Injectable()
export class TeamStatisticCornService implements OnModuleInit {
  private readonly logger = new Logger(TeamStatisticCornService.name)

  private lockValue: string = 'handleTeamSalesStatistic'

  private readonly lockPrefix = 'lock:'

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService
  ) {}

  async onModuleInit() {
    // const currentDate = new Date()
    // for (let i = 60; i >= 1; i--) {
    //   const prevDate = new Date(currentDate)
    //   prevDate.setDate(currentDate.getDate() - i)
    //   await this.TeamStatisticTask(dayjs(prevDate).format('YYYY-MM-DD'))
    // }
    // this.logger.log('teamStatistic-corn-service init')
  }

  /**
   * 每日团队数据归档
   */
  @Cron('00 02 * * *', {
    name: 'teamStatistic',
    timeZone: 'Asia/Shanghai'
  })
  async UpdateTeamStatisticCronTask() {
    if (await this.acquireLock(this.lockValue, 60)) {
      try {
        const yesterday = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
        await this.TeamStatisticTask(yesterday)
      } catch (e) {
        this.logger.error('每日团队销售数据定时处理失败', e)
      } finally {
        await this.releaseLock(this.lockValue)
      }
    }
  }

  async TeamStatisticTask(yesterday: string) {
    const beijingStart = dayjs(yesterday).tz('Asia/Shanghai').startOf('day')
    const beijingEnd = dayjs(yesterday).tz('Asia/Shanghai').endOf('day')
    // 转换为 UTC 时间
    const startOfYesterday = beijingStart.utc().toDate()
    const endOfYesterday = beijingEnd.utc().toDate()

    // 注册有效团队数
    const registerTeamCount = await this.prisma.team.count({
      where: {
        createTime: {
          gt: startOfYesterday,
          lte: endOfYesterday
        }
      }
    })
    // 首次付费团队数
    const paidTeamCount = await this.prisma.order.count({
      where: {
        orderStatus: OrderStatus.SUCCESS,
        salesType: SalesType.FirstBuy,
        fromTime: {
          gt: startOfYesterday,
          lte: endOfYesterday
        }
      }
    })

    // 首次付费订单总金额
    const firstPayAmountTotal = await this.prisma.order.aggregate({
      _sum: {
        payAmount: true
      },
      where: {
        orderStatus: OrderStatus.SUCCESS,
        salesType: SalesType.FirstBuy,
        fromTime: {
          gt: startOfYesterday,
          lte: endOfYesterday
        }
      }
    })

    // 续费订单团队数

    const renewTeam = await this.prisma.order.groupBy({
      by: ['teamId'],
      where: {
        orderStatus: OrderStatus.SUCCESS,
        salesType: SalesType.ReBuy,
        fromTime: {
          gt: startOfYesterday,
          lte: endOfYesterday
        }
      },
      _count: {
        id: true
      }
    })

    // 续费订单总金额
    const renewPayAmountTotal = await this.prisma.order.aggregate({
      _sum: {
        payAmount: true
      },
      where: {
        orderStatus: OrderStatus.SUCCESS,
        salesType: SalesType.ReBuy,
        fromTime: {
          gt: startOfYesterday,
          lte: endOfYesterday
        }
      }
    })

    // 过期未续费团队数
    const unRenewTeam = await this.prisma.team.findMany({
      where: {
        salesType: {
          in: [SalesType.FirstBuy, SalesType.ReBuy]
        },
        vip: {
          expirationTime: {
            gt: startOfYesterday,
            lte: endOfYesterday
          }
        },
        isDelete: false
      }
    })

    const renewTeamCount = renewTeam.length

    const unRenewTeamCount = unRenewTeam.length

    const payAmountTotal =
      (firstPayAmountTotal._sum.payAmount || 0) + (renewPayAmountTotal._sum.payAmount || 0)
    const customerUnitPrice =
      paidTeamCount + renewTeamCount > 0
        ? parseFloat((payAmountTotal / (paidTeamCount + renewTeamCount)).toFixed(2))
        : 0

    const conversionRate =
      registerTeamCount > 0 && paidTeamCount > 0
        ? parseFloat(((paidTeamCount * 100) / registerTeamCount).toFixed(2))
        : 0
    const renewRate =
      unRenewTeamCount + renewTeamCount > 0
        ? parseFloat(((renewTeamCount * 100) / (unRenewTeamCount + renewTeamCount)).toFixed(2))
        : 0
    const teamStatistic = await this.prisma.teamStatistic.findFirst({
      where: {
        statisticDate: new Date(yesterday)
      }
    })

    if (teamStatistic) {
      await this.prisma.teamStatistic.update({
        where: {
          id: teamStatistic.id
        },
        data: {
          paidTeamCount,
          renewTeamCount,
          expiredTeamCount: unRenewTeamCount,
          registerTeamCount,
          renewRate,
          conversionRate,
          payAmountTotal,
          customerUnitPrice
        }
      })
    } else {
      await this.prisma.teamStatistic.create({
        data: {
          statisticDate: new Date(yesterday),
          paidTeamCount,
          renewTeamCount,
          expiredTeamCount: unRenewTeamCount,
          registerTeamCount,
          renewRate,
          conversionRate,
          payAmountTotal,
          customerUnitPrice
        }
      })
    }

    // 更新三天前有效团队归档数据
    const threeDayAgo = dayjs(yesterday).tz('Asia/Shanghai').subtract(3, 'day').format('YYYY-MM-DD')
    const threeDayRegister = await this.prisma.teamStatistic.findFirst({
      where: {
        statisticDate: new Date(threeDayAgo)
      }
    })
    const beijingThreeStart = dayjs(threeDayAgo).tz('Asia/Shanghai').startOf('day')
    const beijingThreeEnd = dayjs(threeDayAgo).tz('Asia/Shanghai').endOf('day')
    // 转换为 UTC 时间
    const startOfthreeAgo = beijingThreeStart.utc().toDate()
    const endOfthreeAgo = beijingThreeEnd.utc().toDate()
    if (threeDayRegister) {
      // 三天前的注册有效团队数
      const effectiveTeam = await this.prisma.team.findMany({
        where: {
          isDelete: false,
          vip: {
            expirationTime: {
              gt: startOfthreeAgo,
              lte: endOfthreeAgo
            }
          }
        },
        include: {
          platformAccounts: true,
          members: true
        }
      })

      const effectiveTeamCount = effectiveTeam.filter(
        (item) => item.platformAccounts.length > 0 || item.members.length > 1
      ).length

      const registerTeamCount = effectiveTeamCount
      const conversionRate =
        effectiveTeamCount > 0 && threeDayRegister.paidTeamCount > 0
          ? parseFloat(((threeDayRegister.paidTeamCount * 100) / effectiveTeamCount).toFixed(2))
          : 0

      await this.prisma.teamStatistic.update({
        where: {
          id: threeDayRegister.id
        },
        data: {
          registerTeamCount,
          conversionRate
        }
      })
    }
  }

  // 获取锁
  async acquireLock(key: string, ttl: number): Promise<boolean> {
    const result = await this.cacheManager.store.client.set(
      this.lockPrefix + key,
      'locked',
      'EX',
      ttl,
      'NX'
    )
    return result === 'OK'
  }

  // 释放锁
  async releaseLock(key: string) {
    await this.cacheManager.store.client.del(this.lockPrefix + key)
  }
}
