import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsString, Matches } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export enum PublicRequestUploadType {
  Avatar = 'avatar',
  Autoresponder = 'autoresponder',
  Image = 'image',
  App = 'app'
}

export class PublicCreateUploadUrlRequestBodyDTO {
  @ApiProperty({
    enum: Object.values(PublicRequestUploadType).filter((v) => typeof v === 'string'),
    description: '文件类型',
    example: ' avatar: 头像 image: 图片',
    required: true
  })
  @IsEnum(PublicRequestUploadType)
  type: PublicRequestUploadType

  @ApiProperty({
    type: String,
    example: 'objectName: "avatar/2023-10-01/1234567890.png"',
    required: true
  })
  @IsString()
  objectName: string
}

export class PublicCreateUploadUrlResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: String,
    example:
      'eyJleHBpcmF0aW9uIjoiMjAyNC0wNS0wNlQxMDo0ODo1MS41MTNaIiwiY29uZGl0aW9ucyI6W1siY29udGVudC1sZW5ndGgtcmFuZ2UiLDAsMTA0ODU3NjAwMF0seyJidWNrZXQiOiJxZHktaW1hZ2UifV19'
  })
  policy: string

  @ApiResponseProperty({
    type: String,
    example: 'Ao4nnTaft83zHUBwX5ZXw1i3GNA'
  })
  signature: string

  @ApiResponseProperty({
    type: String,
    example: 'LTAI5tD3QdxUQZbJAeHeX7Ma'
  })
  ossAccessKeyId: string

  @ApiResponseProperty({
    type: String,
    example: 'https://oss-cn-shanghai.aliyuncs.com'
  })
  host: string

  @ApiResponseProperty({
    type: String,
    example: 'avatar'
  })
  dir: string

  @ApiResponseProperty({
    type: String,
    example: 'abc123'
  })
  fileKey: string
}

class PublicResponseNotify {
  @ApiResponseProperty({
    type: String,
    example: 'Hello, World!'
  })
  content: string

  @ApiResponseProperty({
    type: String
  })
  title: string

  @ApiResponseProperty({
    type: Boolean
  })
  isToast: boolean

  @ApiResponseProperty({
    type: Date,
    example: '2021-06-01 12:00:00'
  })
  createTime: number
}

export class PublicResponseNotifyDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [PublicResponseNotify]
  })
  data: PublicResponseNotify[]
}

export class City {
  @ApiResponseProperty({
    type: Number
  })
  id: number

  @ApiResponseProperty({
    type: String
  })
  name: string

  @ApiResponseProperty({
    type: Number
  })
  parentId: number
}

export class CityResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [City]
  })
  data: City[]
}

export class LastAppVersionResponseDTO {
  @ApiResponseProperty({
    type: String
  })
  url: string

  @ApiResponseProperty({
    type: String
  })
  version: string

  @ApiResponseProperty({
    type: Boolean
  })
  force: boolean

  @ApiResponseProperty({
    type: String
  })
  desc: string
}

export class LastAppVersionWebSiteResponseDTO {
  @ApiResponseProperty({
    type: LastAppVersionResponseDTO
  })
  ios: LastAppVersionResponseDTO

  @ApiResponseProperty({
    type: LastAppVersionResponseDTO
  })
  android: LastAppVersionResponseDTO
}

export enum AppPlatformType {
  Android = 'android',
  IOS = 'ios'
}

export class LastAppVersionRequestDTO {
  @ApiProperty({
    description: '当前版本',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+\.\d+\.\d+$/, { message: '版本号格式错误，应为 x.x.x，例如 1.0.0' })
  version: string

  @ApiProperty({
    enum: Object.values(AppPlatformType).filter((v) => typeof v === 'string'),
    description: '类型',
    example: ' ios/android',
    required: true
  })
  @IsEnum(AppPlatformType)
  @IsNotEmpty()
  type: AppPlatformType
}
