import { registerAs } from '@nestjs/config'

export const BaseConfigRegister = registerAs('app', () => {
  return {
    /**
     * 客户端ID
     */
    clientSecret: process.env.CLIENT_SECRET,

    /**
     * 客户端密钥
     */
    clientKey: process.env.CLIENT_KEY,

    secondClientSecret: process.env.SECOND_CLIEND_SECRET,

    secondClientKey: process.env.SECOND_CLIENT_KEY,

    /**
     * 客户端ID
     */
    kuaishouClientSecret: process.env.KUAISHOU_CLIENT_SECRET,

    /**
     * 客户端密钥
     */
    kuaishouClientKey: process.env.KUAISHOU_CLIENT_KEY,

    /**
     * 客户端ID
     */
    xiaohongshuClientSecret: process.env.XIAOHONGSHU_CLIENT_SECRET,

    /**
     * 客户端密钥
     */
    xiaohongshuClientKey: process.env.XIAOHONGSHU_CLIENT_KEY,

    /**
     * 微博客户端ID
     */
    weiboClientKey: process.env.WEIBO_CLIENT_KEY,

    /**
     * 微博客户端密钥
     */
    weiboClientSecret: process.env.WEIBO_CLIENT_SECRET,

    /**
     * 回调uri
     */
    weiboRedirectUri: process.env.WEIBO_REDIRECT_URI,

    /**
     * 极光推送appid
     */
    jpushKey: process.env.JPUSH_KEY,

    /**
     * 极光推送密钥
     */
    jpushSecret: process.env.JPUSH_SECRET,

    /**
     * 应用名称
     */
    language: 'zh',
    /**
     * 应用端口, host
     */
    http: {
      host: '0.0.0.0',
      port: 3000
    },
    /**
     * 同步间隔
     */
    syncInterval: '',
    /**
     * 时区
     */
    timezone: 'Asia/Shanghai',
    /**
     * 短信accessKeyId
     */
    smsAccessKeyId: 'LTAI5tMJQUPHUGvsKrsPcYmk',

    /**
     * 短信accessKeySecret
     */
    smsAccessKeySecret: '******************************',

    /**
     * 短信endpoint
     */
    smsEndpoint: 'dysmsapi.aliyuncs.com',

    /**
     * 短信签名name
     */
    smsSignName: '青豆云',

    /**
     * 短信模板code
     */
    smsTemplateCode: 'SMS_222866265',

    /**
     * 令牌过期时间
     */
    overdueToken: 1000 * 60 * 60 * 24 * 7,

    /**
     * 短信验证码过期时间
     */
    smsCodeTime: 1000 * 60 * 5,
    /**
     * cors配置
     */
    cors: {
      allowMethod: '*',
      allowOrigin: '*',
      allowHeader: '*'
    },
    /**
     * rateLimit配置
     */
    rateLimit: {
      resetTime: '1 minute',
      maxRequestPerId: 30
    },

    wechatConfig: {
      apiBaseUrl: 'http://api.videosapi.com/finder/v2/api',
      Token: process.env.WECHAT_TOKEN,
      NewToken: process.env.WECHAT_NEW_TOKEN,
      pictureExpiredTime: 1000 * 60 * 60 * 24 * 6
    },

    alipay: {
      appId: '2021004171632869',
      privateKey:
        'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCKibvhdDxyBfP3JtuHdxDU9/Pa8omU32NIPfCgazGpZh29xtjuyhmlzVrVsuaXDmoiJSP0Df2/mK79BhQKeoESjmKcJPPncAxyBVwRot+FqcwRlKuGzjzZpPpyfUO6yfwDhshJ9xKFYqEATQSvjTFloaXXad7OxmvSajuEHO2Ph3okyjP8Lf1qJuoEmJTKjNaLPNxGjRZ/Bf2pBmGoXTuvQVXW0QGmfe8hzsjFwZZGJlegq7ib7OEhomKfgK+meG+k9dqgQUPgf33PWisOb/JYsa/vl2T9fRpOIZq87+ekvCzVeROFVxcTG4FNRS5u6RSUPEmJKqO/F3j+xGqTztAXAgMBAAECggEAfT3dAwmg53foQOP5DHMgXVe3NZWTuWlmCNYuJegiYkE12+3bCazdGN4YIx7zhVX7rtiisyy+3Cn88DNBhOOQsiTUktJ5SR4rshyUSkFTDzQcjzw3AMyLiDq0SzJhO4kEcm+zu+JH9Rvf7zSxar8bDATht2c3jPkmWne9TEcWoGyGA8HV/MCpbK0k7u+Bk8RrOmjr/IF6KviNUIXhWCXjWRtXrJXsltUoIwsUi0wD2L34RPmIDTO+nheD0DEXsK1jZ+QsUbErWMwEwgv7FswuK79omwhTHXU4FygA8aUuzTCQR32vku0F+g34av0Zy6umcPB3cj+H+gjtIJgj1/sxuQKBgQC+KaPknkkCNSbfztvqRjukximlPLkH15aMsg3+KXv/4Wh6lYfDf6+Xq7+DOuhUnqAUF5fucKxiJmlyVkmea2swlOcB5pV6uMdDeL+FaT94h7TVIZ6Lz4UqbGewXi7QcI8cLkmtDe6aiTeP2ssPcT2eoyYHLILMdj4bT8btfLCLWwKBgQC6gIlKM2VKGcw0LJDungbIySqScNPrnkjti17a2xqeBJ2inxKlxIYpR1HRzAXJdyg8njHCY19h7sPD2Mx/TnKpY9v2e93/qu4+zBPN6IeB5qv5hRieh1hBi4YkYkv+E4ItDddSqCUvRaaX0sh/qcpoDmDxt88uhV4xoB7vTdD29QKBgQCcv2wmNccOd8ucbkHeIZhmYSXQ23XVngKt5NMd7woUSLQ7X2hyYJzX061gOb1qivp+tVf6Jowht3Hx2miCmh7YA9VLPst2RjuSRUTxbmjwLESF7llm+17UMlliCrBiBnYgj5MRf8tvPK48+Oeqyfg1/FuLTEhgyxK0ppTeC4rmwwKBgCLRWlAi9jb2R/w5kbkqgWO8wpjiPX4QZ9cJHOVI4Fg9zlwMzINsBAvyx6CeMTpiZ32hnvKGcMkstRl8APmHYLqk7OzARTq045OPr+bSEXI76UFFCsqVw/FApgNm4n8Fj1jv65H/2ClSHH7dmBbVUPdKL+kQgrpRpbsoBH5yjZ99AoGAMbZfdrcVc3TK65O/Y6MGiAQN3GKeVrnYKt+RA654UOI78fw4Jw4paRbcPCKmmasw/3fizcXKF0+nvAtqeOkX5J5gxeALPX+boUVlGjbyKeInvvxNIzJL0sAxcT8TIn5rrthomBdcpFJFTUkx1FXCsf4MqAfv5thk1GCXpAzcV00=',
      alipayPublicKey:
        'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAorK2w42LGXk6cjoLde+iUg02GUZUfsuPqveft0ZBbJhvS6il4wJgOtJFKQq4XVes4kXGGTipDGsDSWlOD+6rGkiuyVWDdwC3rh2K/rQzH46FweGM0v9bkyg1+TMBAgbXZtxb47BOeYvVEbfBu6x98oFTFcFrzl9eT90jD/njvCV3M/z+HRJ/0aANR6I2exKHLulxf5JJovYd3qgrvr+3UVdg0Vb4wzCMTSzVJbbxFTX20luk//EXAKf80sdzriDJJdR4sHFst/H62UEh5+VvqhtakaVSqkYaj3QVYksilDd3GrUBO7Mpl1SP8//gXe+MJVJSDPVQ3sslPhsF3OBBaQIDAQAB'
    },

    wechatPay: {
      appId: 'wxf4f1893f2af528e0',
      mchId: '1684338064',
      serialNo: '1F0619EC809652AB475D5B12C95D2DFB72590A4B',
      apiV3Key: 'iT5QMwBfZFC3WHhG0xMR1icchKcbPTaQ',
      privateKey:
        'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDDgkV+arcc1Tejw0OYVSijuAEIYKXj1RhbvHhUW0rUjG3gqyH5EebvLz8hB4pWin4J11i8QjhhSzE3fPoRjTQhgnTnZrQB0KOWwBjXQdR6S0MWJxqHil0NM1cxyG+v1baLUSxJu0NjACTW6jd4NiYfT5htxRhR1iih1u7fVQ2Ko5o56hgpOSw1K+FVJq4opmhSkl1qGWJglYhz8dE608bVKQVgVNGprjlUdMOPcABwZqDW/QKxAZs1fU6IE/LuUzpAaqNTQ0/t5qLx1NPTrWMIB7iinVWgZSPWat99yNHOufaoRECuAFXx/65wl2EF7V2b5awHXMZKRnjxOShDEe5AgMBAAECggEAGWGEHBFGjFuxyXTEBbaoe/ibSbciUh2L9/zTsj/Xyxgf+5a75lXIoSyFCYitEOcRzb1DK818/ESCsy1qWXT3W5JQEupmXAX19tAhaXCGYQn8P5f7/bSct+X+X9whg7Y+6H81PK0u5l9SaQQnB1ggQthWdYmsypL+22PHxv6Hr4Orl80Yd7eB6RGo+W/Px44llhhBrCRvqcRNSBArsUr7ZNxyI9To+K23eZv7PFJZf4eb+ZayDj9vrB8OL7VBc707//00zH18wQHcfbzHWLEENc9b/W2lG6hfxAiEUj2e11Af+7KcKPydb0i+QqHdfL5xz5HrtzW9CBx6AGE/3y+KwQKBgQDnF05ef+eBFpHSa4esudZmGGQS3Yqhnjq56btjVntJ3vcoXNIgKolwfLIQaDUqrlBNX6lbuFL2NM0MxvZr/nqNZqHCvE4JOFi7bsln8wWyATAWiIdhLiqEUsjBaVTgW8MoRO9IXrlsWhjlD1fwKFZh2YCvans6NrW+dNaRy0aTPQKBgQDYlR2xuMgsGbuFRGIi2ekNfp0LA0qLSOs0VvfArs72MiO96NsbHGUC3MkEeZLwgA/TqbIBASfUDbUR49W5pZEUkKkt7wRGJnbl3YZ+CCT/96IbdvgMxs5a2Ipnh8HiAQr3b5ViOonDNhDnfte1QOrktAzGBPVC8F3et6Y40yReLQKBgHtTbchy2/qBL/sq1bIxTyEXPTjwME7GIbMb8Jw4B/3JVv+cYas26Dg249RruIv5kPNp7Pj86pFoZlG+UL4bhg22SvehBMhY4MnOev/VqyFvDtnHrPAI8jnB8E9/xgMiFYmFGGn3217eylBcGuMYs9jueYWj/vV3QxDMS8JsyFJFAoGAbfjBXCVkVqg2REPlxKZ7JKP+n8HrDvBWpXvnTwo0AmSRK3PaFVkEnUB4PXJxvvZ3ktUekk4A6DaRk2Tpd9tu4E9X5//uXqRXSP6DtOjzyJj30hRFfU9RJmW/WTRgqR44cE1TP85TdzCzwqfwZZNx8fDOb5nC41tjfpwnpcDoPX0CgYEApWA0jAcmAfKV+NGDS60mD+9pmhoXvR7nB5P6pXLFwt3/sM8EeqtLTRSPQ+TafMj6TOQS9KOJjTIiu7/LrhJ2zf5jE+******************************/******************************+9V4LqXireRPxJ9vGaMs='
    },

    serviceCodeUrl: 'https://qdy-image-oss.yixiaoer.cn/service-qr-code-DWjeU5gc.jpg',

    /**
     * 用户注册赠送会员天数，0为不赠送
     */
    membershipDurantionDays: 3,

    /**
     * 百度api上报Token
     */
    baiduApiToken: 'EJCwS7gofLGSTBmQnAsh12D6nzDTGPms@oRkllfvYbcDpEWyhNz8OPlxppaSOOe6Y',

    /**
     * 阿里云日志密钥
     */
    slsConfig: {
      accessKeyId: 'LTAI5tQFvvUg26CEW3xtXEES',
      accessKeySecret: '******************************',
      project: 'qingdouyun'
    },

    /**
     * 火山云日志密钥
     */
    tlsConfig: {
      accessKeyId: 'AKLTMzYwOTdiYjA3YWI4NGUxMGFjMzMzYjJiMDNjMjA4Nzk',
      accessKeySecret: 'WW1Vek1UTTRaVE0yWkRNNE5ETTBZbUUyTkRKak9UQmtObUUwTXpRNVlqUQ==',
      host: 'tls-cn-shanghai.ivolces.com',
      region: 'cn-shanghai',
      project: 'qingdouyun'
    }
  }
})

export type BaseConfig = ReturnType<typeof BaseConfigRegister>
