import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'

import {
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiQuery
} from '@nestjs/swagger'

import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { CouponsService } from './coupons.service'
import {
  CouponResponseByIdDTO,
  CouponsCreateDTO,
  CouponsRecordResponseDTO,
  CouponsResponseDTO,
  SendCouponsRequestDTO,
  UpdateCouponsRequestDTO
} from './coupons.dto'

@Controller('coupons')
@ApiTags('优惠券管理')
export class CouponsController {
  constructor(private readonly couponsService: CouponsService) {}

  @Post()
  @ApiOperation({ summary: '创建优惠券' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async createCoupons(@Body() data: CouponsCreateDTO) {
    return this.couponsService.createCoupons(data)
  }

  @Get()
  @ApiOperation({ summary: '获取优惠券列表' })
  @ApiOkResponse({ description: '操作成功', type: CouponsResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '页码 <默认 10>' })
  async getCoupons(@Query('page') page: number, @Query('size') size: number) {
    return this.couponsService.getCoupons(page, size)
  }

  @Post('send')
  @ApiOperation({ summary: '发放优惠券' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async sendCoupons(@Body() data: SendCouponsRequestDTO) {
    return this.couponsService.sendCoupons(data)
  }

  @Delete(':couponsId')
  @ApiOperation({ summary: '删除优惠券' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async deleteCoupons(@Param('couponsId') couponsId: number) {
    return this.couponsService.deleteCoupons(couponsId)
  }

  @Patch()
  @ApiOperation({ summary: '更新优惠券' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async updateCoupons(@Body() data: UpdateCouponsRequestDTO) {
    return this.couponsService.updateCoupons(data)
  }

  @Get(':couponsId')
  @ApiOperation({ summary: '获取优惠券详情' })
  @ApiOkResponse({ description: '操作成功', type: CouponResponseByIdDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getCoupon(@Param('couponsId') couponsId: number) {
    return this.couponsService.getCoupon(couponsId)
  }

  @Get('send-record')
  @ApiOperation({ summary: '获取优惠券发放记录' })
  @ApiOkResponse({ description: '操作成功', type: CouponsRecordResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({ name: 'couponId', required: false, type: Number, description: '优惠券id <默认 0>' })
  @ApiQuery({
    name: 'status',
    required: false,
    type: Number,
    description: '状态码，0:可用，1:已使用，2:已过期，-1：全部 <默认 -1>'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '单页数量 <默认 10>' })
  @ApiQuery({ name: 'phone', required: false, type: String, description: '手机号' })
  @ApiQuery({ name: 'teamId', required: false, type: Number, description: '团队id' })
  @ApiQuery({ name: 'channelId', required: false, type: Number, description: '渠道id' })
  @ApiQuery({
    name: 'createTimeStart',
    required: false,
    type: Number,
    description: '创建起始时间戳'
  })
  @ApiQuery({ name: 'createTimeEnd', required: false, type: Number, description: '创建终止时间戳' })
  @ApiQuery({ name: 'castTimeStart', required: false, type: Number, description: '使用起始时间戳' })
  @ApiQuery({ name: 'castTimeEnd', required: false, type: Number, description: '使用终止时间戳' })
  async getCouponSendRecord(
    @Query('couponId') couponId: number,
    @Query('status') status: number,
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('phone') phone: string,
    @Query('teamId') teamId: number,
    @Query('channelId') channelId: number,
    @Query('createTimeStart') createTimeStart: number,
    @Query('createTimeEnd') createTimeEnd: number,
    @Query('castTimeStart') castTimeStart: number,
    @Query('castTimeEnd') castTimeEnd: number
  ) {
    return this.couponsService.getUserCouponsRecord(
      couponId,
      status,
      page,
      size,
      phone,
      teamId,
      channelId,
      createTimeStart,
      createTimeEnd,
      castTimeStart,
      castTimeEnd
    )
  }
}
