import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Inject,
  UnauthorizedException
} from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import type { FastifyRequest } from 'fastify'
import { Cache } from 'cache-manager'
import { User } from '@qdy/mysql'

@Injectable()
export class TokenGuard implements CanActivate {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  private readonly excludedRoutes = [
    [null, '/users/auth'],
    [null, '/users/agree-auth'],
    [null, '/users/sign-url'],
    [null, '/users/auth/exchangeKey'],
    [null, '/users/auth/password'],
    [null, '/users/sms-code'],
    [null, '/teams/invite-codes'],
    [null, '/users/reset-password'],
    [null, '/users/ocpcapi'],
    [null, '/public/last-app-version'],
    [null, '/open/im/third/bind_account'],
    [null, '/open/im/third/unbind_account'],
    [null, '/open/im/auth/bind_user/event'],
    [null, '/open/im/send'],
    [null, '/open/im/push_lead'],
    [null, '/open/intent/comment'],
    [null, `${process.env.WEBHOOK_URL}`],
    [null, `${process.env.WECHAT_WEBHOOK_URL}`],
    [null, `${process.env.WECHAT_WEBHOOK_NEW_URL}`],
    [null, `${process.env.ALIPAY_WEBHOOK_URL}`],
    [null, `${process.env.WECHATPAY_WEBHOOK_URL}`],
    [null, `${process.env.KUAISHOU_WEBHOOK_URL}`],
    [null, `${process.env.WEIBO_WEBHOOK_URL}`]
  ]

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>()
    const { authorization } = request.headers

    const { routeOptions } = request

    // 允许访问排除的路由
    for (let i = 0; i < this.excludedRoutes.length; i++) {
      const [type, url] = this.excludedRoutes[i]

      if (routeOptions.url.indexOf(url) >= 0) {
        if (!type) {
          return true
        }

        if (type === routeOptions.method.toLocaleLowerCase()) {
          return true
        }
      }
    }

    try {
      const user = await this.cacheManager.get<User>(authorization)

      if (user) {
        // @ts-expect-error user is a User type
        request.user = user.v ? user.v : user
        request.authorization = authorization
        return true
      }
    } catch {
      // ignore
    }

    throw new UnauthorizedException('登录失效, 请重新登录')
  }
}
