import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { OrderStatus } from './constant'

export const eventKey = 'update-order'

export const orderEventEmitter = new EventEmitter()

@Injectable()
export class OrderEventService implements OnModuleInit {
  logger = new Logger('OrderEventService')

  constructor(private readonly prisma: PrismaService) {}

  taskQueue: Queue

  taskWorker: Worker

  onModuleInit() {
    orderEventEmitter.on(eventKey, this.createUpdateOrderTask.bind(this))

    this.taskQueue = new Queue('order-event', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'order-event',
      async (job) => {
        const { orderNo } = job.data
        await this.updateOrder(orderNo)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async createUpdateOrderTask({ orderNo, type }: { orderNo: string; type: 'create' | 'close' }) {
    switch (type) {
      case 'create':
        await this.taskQueue.add(
          'order-event',
          { orderNo },
          {
            delay: 2 * 60 * 60 * 1000,
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `${orderNo}-order-task`
          }
        )
        break
      case 'close':
        ;(await this.taskQueue.getJob(`${orderNo}-order-task`)).remove()
        break
      default:
        break
    }
  }

  async updateOrder(orderNo: string) {
    try {
      await this.prisma.order.update({
        where: {
          orderNo
        },
        data: {
          orderStatus: OrderStatus.CANCELED
        }
      })
    } catch (error) {
      this.logger.error(error)
    }
  }
}
