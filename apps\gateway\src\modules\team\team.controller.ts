import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req
} from '@nestjs/common'
import { TeamService } from './team.service'
import {
  ApiBadRequestResponse,
  ApiConflictResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  TeamRequestCreateBodyDTO,
  TeamRequestJoinParamDTO,
  TeamRequestMembersNameDTO,
  TeamRequestPlatformAccountMembersDTO,
  TeamRequestPatchDTO,
  TeamResponseCreateDTO,
  TeamResponseInfoByInvitationCodeDTO,
  TeamResponseJoinDTO,
  TeamResponseMembersDTO,
  TeamUpdateMemberRoleDTO,
  TeamRequestMembersPlatformAccountDTO,
  TeamResponseListDTO,
  TeamInfoByInvitationUseResponseDTO,
  TeamResponseMemberPagedDTO,
  TeamWechatMessageLimitRequestDTO
} from './team.dto'

import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO,
  BaseConflictResponseDTO
} from '../../common/dto/BaseRequestDTO'
import { validate } from 'class-validator'
import { FastifyRequest } from 'fastify'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('teams')
@ApiTags('团队管理')
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  @Patch('/our-team')
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiOperation({ summary: '修改团队信息' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  putTeams(@Body() body: TeamRequestPatchDTO) {
    return this.teamService.updateTeamInfo(body)
  }

  /**
   * 创建团队
   * @param param0
   * @returns
   */
  @Post()
  @ApiOperation({ summary: '创建团队' })
  @ApiOkResponse({ type: TeamResponseCreateDTO })
  @ApiConflictResponse({ description: '团队名已被使用' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({ name: 'authorization', required: true })
  createTeam(@Body() { name }: TeamRequestCreateBodyDTO) {
    return this.teamService.createTeam(name)
  }

  /**
   * 加入团队
   * @param teamId
   * @returns
   */
  @Post(':invitationCode/members')
  @ApiOperation({ summary: '加入团队' })
  @ApiOkResponse({ type: TeamResponseJoinDTO })
  @ApiNotFoundResponse({ description: '团队已解散', type: BaseConflictResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiForbiddenResponse({ description: '用户已加入团队', type: BaseForbiddenResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async joinTeam(@Param('invitationCode') invitationCode: string) {
    const teamRequest = new TeamRequestJoinParamDTO()
    teamRequest.invitationCode = invitationCode
    const validationErrors = await validate(teamRequest)
    if (validationErrors.length > 0) {
      throw new BadRequestException('邀请码错误')
    }

    return this.teamService.joinTeam(invitationCode)
  }

  @Get('/our-team')
  @ApiOperation({ summary: '获取我的团队信息' })
  @ApiOkResponse({ type: TeamResponseInfoByInvitationCodeDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async getOurTeam(@Req() req: FastifyRequest) {
    const userAgent = req.headers['user-agent'] as string
    const xffHeader = req.headers['x-forwarded-for']
    const clientIp = xffHeader
      ? Array.isArray(xffHeader)
        ? xffHeader[0]
        : xffHeader.split(',')[0]
      : req.ip
    return this.teamService.getOurTeam({ clientIp, userAgent })
  }

  /**
   * 获取团队信息
   */
  @Get('invite-codes/:inviteCode')
  @ApiOperation({ summary: '根据邀请码获取团队信息' })
  @ApiOkResponse({ type: TeamResponseInfoByInvitationCodeDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeamInfoByInvitationCode(@Param('inviteCode') invitationCode: string) {
    return this.teamService.getTeamInfoByInvitationCode(invitationCode)
  }

  /**
   * 通过手机号码发送邀请
   * @param phone 手机号码
   * @returns
   */
  @Post(`invite-users/:phone`)
  @ApiOperation({ summary: '根据手机号码邀请用户加入团队' })
  @ApiOkResponse({ type: TeamInfoByInvitationUseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async invitationUserByPhone(@Param('phone') phone: string) {
    return this.teamService.invitationUserByPhone(phone)
  }

  /**
   * 获取团队成员列表
   */
  @Get('our-team/members')
  @ApiOperation({ summary: '获取团队成员列表' })
  @ApiOkResponse({ type: TeamResponseMembersDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeamMembers() {
    return this.teamService.getTeamMembers()
  }

  @Get('our-team/members/paginated')
  @ApiOperation({ summary: '获取团队成员列表(分页)' })
  @ApiOkResponse({ type: TeamResponseMemberPagedDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeamMembersPaged(@Query('size') size: number, @Query('page') page: number) {
    const querys = {
      page,
      size
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    return this.teamService.getMembersByPaged(querys)
  }

  /**
   * 设置团队成员角色
   */
  @Patch('our-team/members/:memberId')
  @ApiOperation({ summary: '设置团队成员角色' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async setMemberRole(@Param('memberId') memberId: number, @Body() body: TeamUpdateMemberRoleDTO) {
    return this.teamService.setMemberRole({ ...body, memberId })
  }

  /**
   * 设置团队成员名字
   * @param memberId
   * @param body
   * @returns
   */
  @Patch('our-team/members/:memberId/name')
  @ApiOperation({ summary: '管理员设置团队成员名字' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async setMemberName(
    @Param('memberId') memberId: number,
    @Body() body: TeamRequestMembersNameDTO
  ) {
    return this.teamService.setMemberName({ ...body, memberId })
  }

  /**
   * 移除团队成员
   */
  @Delete('our-team/members/:memberId')
  @ApiOperation({ summary: '移除团队成员' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async removeMember(@Param('memberId') memberId: number) {
    return this.teamService.removeMember(memberId)
  }

  /**
   * 移除团队成员
   */
  @Delete('our-team/members/quit')
  @ApiOperation({ summary: '退出团队' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async quitMember() {
    return this.teamService.quitMember()
  }

  /**
   * 设置团队成员平台账号
   */
  @Put('our-team/platform-accounts/members')
  @ApiOperation({ summary: '设置团队成员平台账号' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async setPlatformAccountMembers(@Body() data: TeamRequestPlatformAccountMembersDTO) {
    return this.teamService.setPlatformAccountMembers(data)
  }

  @Put('our-team/members/platform-accounts')
  @ApiOperation({ summary: '设置平台账号运营人' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization'
  })
  async setMemberPlatformAccounts(@Body() data: TeamRequestMembersPlatformAccountDTO) {
    return this.teamService.setMemberPlatformAccounts(data)
  }

  @Delete('our-team/dissolve')
  @ApiOperation({ summary: '解散团队' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async dissolveTeam(@Body('name') name: string) {
    return this.teamService.dissolveTeam(name)
  }

  @Get('our-team/list')
  @ApiOperation({ summary: '获取团队列表' })
  @ApiOkResponse({ type: TeamResponseListDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeamList() {
    return this.teamService.getTeamList()
  }

  @Put('our-team/list/:teamId')
  @ApiOperation({ summary: '设置当前团队' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async setCurrentTeam(
    @Param('teamId', {
      transform(value) {
        return Number(value)
      }
    })
    teamId: number
  ) {
    return this.teamService.setCurrentTeam(teamId)
  }

  /**
   * 解冻团队成员账号
   */
  @Put('our-team/members/:memberId/enable')
  @ApiOperation({ summary: '解除团队成员账号冻结状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async enableMember(@Param('memberId') memberId: number) {
    return this.teamService.enableMember(memberId)
  }

  /**
   * 设置微信频率数
   */
  @Put('wechat-message-limit')
  @ApiOperation({ summary: '设置微信频率数' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async wechatMessageLimit(@Body() body: TeamWechatMessageLimitRequestDTO) {
    return this.teamService.putWechatMessageLimit(body)
  }
}
