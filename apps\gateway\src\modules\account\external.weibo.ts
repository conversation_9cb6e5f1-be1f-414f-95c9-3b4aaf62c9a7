import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'

const logger = new Logger('account external')

const authorizeAccountApi = 'https://api.weibo.com/oauth2/access_token'
const authorizeAccountInfoApi = 'https://api.weibo.com/2/users/show.json'
const authorizeAccountTokenInfo = 'https://api.weibo.com/oauth2/get_token_info'
const revokeoauthApi = 'https://api.weibo.com/oauth2/revokeoauth2'

export async function postAuthorizeWeiboAccount(data: {
  clientSecret: string
  clientKey: string
  redirectUri: string
  code: string
}) {
  const querys = {
    client_id: data.clientKey,
    client_secret: data.clientSecret,
    code: data.code,
    grant_type: 'authorization_code',
    redirect_uri: data.redirectUri
  }

  logger.log(querys)

  const response = await axios
    .post(authorizeAccountApi, null, { params: querys })
    .catch(function (error) {
      if (error.response) {
        throw new BadRequestException(`[微博官方]:${error.response.data.error}`)
      } else {
        throw new BadRequestException(`[微博官方]:请求报错`)
      }
    })

  return {
    openId: response.data.uid,
    expiresIn: response.data.expires_in,
    accessToken: response.data.access_token
  }
}

export async function getAuthorizeWeiboAccountInfoApi({
  accessToken,
  uid
}: {
  accessToken: string
  uid: string
}) {
  try {
    const res = await axios
      .get(authorizeAccountInfoApi, {
        params: {
          uid,
          access_token: accessToken
        }
      })
      .catch(function (error) {
        if (error.response) {
          throw new BadRequestException(`[微博官方]:${error.response.data.error}`)
        } else {
          throw new BadRequestException(`[微博官方]:请求报错`)
        }
      })

    return res.data
  } catch (error) {
    logger.error(error)
    throw new BadRequestException('获取账号信息失败')
  }
}

export async function postAccountTokenInfo(data: { accessToken: string }) {
  const querys = {
    access_token: data.accessToken
  }

  logger.log(querys)

  const response = await axios
    .post(authorizeAccountTokenInfo, null, { params: querys })
    .catch(function (error) {
      if (error.response) {
        throw new BadRequestException(`[微博官方]:${error.response.data.error}`)
      } else {
        throw new BadRequestException(`[微博官方]:请求报错`)
      }
    })

  return {
    openId: response.data.uid,
    expiresIn: response.data.expires_in
  }
}

export async function postRevokeoAuthWeiboAccount(accessToken: string) {
  const querys = {
    access_token: accessToken
  }

  const response = await axios
    .post(revokeoauthApi, null, { params: querys })
    .catch(function (error) {
      if (error.response) {
        throw new BadRequestException(`[微博官方]:${error.response.data.error}`)
      } else {
        throw new BadRequestException(`[微博官方]:请求报错`)
      }
    })

  return {
    result: response.data.result
  }
}
