import { BadRequestException, Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { AppVersionEntity, NoticeEntity } from '@qdy/mongo'
import { AnyObject, Model } from 'mongoose'
import {
  AppPlatformType,
  AppVersionListRequestDTO,
  CreateAppVersionRequestBodyDTO,
  PublicCreateUploadUrlRequestBodyDTO,
  PublicRequestNotifyDTO
} from './public.dto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { type AdminUser } from '@qdy/mysql'
import { TosManageService } from '@qdy/common'

@Injectable()
export class PublicService implements OnModuleInit {
  async onModuleInit() {}

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(NoticeEntity.name) private noticeModel: Model<NoticeEntity>,
    @InjectModel(AppVersionEntity.name) private appVersionModel: Model<AppVersionEntity>,
    private readonly tosManageService: TosManageService
  ) {}

  async getSystemNotice(page: number, size: number) {
    const skip = (page - 1) * size

    const [total, data] = await Promise.all([
      this.noticeModel.countDocuments(),
      this.noticeModel.find().skip(skip).limit(size).sort({ createTime: -1 })
    ])

    return {
      data,
      total,
      page,
      size
    }
  }

  async updateSystemNotice(noticeId: string, { content, title, isToast }: PublicRequestNotifyDTO) {
    await this.noticeModel.updateOne(
      { _id: noticeId },
      {
        content,
        title,
        isToast,
        createTime: Date.now()
      }
    )
  }

  async deleteSystemNotice(noticeId: string) {
    await this.noticeModel.deleteOne({ _id: noticeId })
  }

  async createSystemNotice({ content, title, isToast }: PublicRequestNotifyDTO) {
    await this.noticeModel.create({
      content,
      title,
      isToast,
      createTime: Date.now()
    })
  }

  async createAppVersion(data: CreateAppVersionRequestBodyDTO, token: string) {
    const user = await this.cacheManager.get<AdminUser>(token)

    try {
      await this.appVersionModel.create({
        version: data.version,
        releaseTime: Date.now(),
        desc: data.desc,
        force: data.force,
        url: data.url,
        type: data.type,
        user: {
          id: user.id,
          name: user.nickname || user.username
        }
      })
    } catch (e) {
      if (e.code === 11000) {
        throw new BadRequestException(`创建失败: 已存在版本号为${data.version}的版本`)
      }

      throw new BadRequestException(`创建失败`)
    }
  }

  async getAppVersion(body: AppVersionListRequestDTO) {
    const { releaseTimeMin, releaseTimeMax, type, page, size } = body

    const filter: AnyObject = {}

    if (releaseTimeMin) {
      filter.releaseTime = { ...filter.releaseTime, $gte: releaseTimeMin }
    }
    if (releaseTimeMax) {
      filter.releaseTime = { ...filter.releaseTime, $lte: releaseTimeMax }
    }

    if (type) {
      filter.type = type
    }

    const skip = (page - 1) * size
    const total = await this.appVersionModel.countDocuments(filter)
    const data = await this.appVersionModel
      .find(filter)
      .sort({ releaseTime: -1 })
      .skip(skip)
      .limit(size)
      .exec()

    return {
      total,
      page,
      size,
      data
    }
  }

  async getLastAppVersions(type: AppPlatformType) {
    const currentVersion = await this.appVersionModel
      .findOne({
        type
      })
      .sort({ releaseTime: -1 })
      .lean()

    return currentVersion
  }

  async deleteAppVersion(id: string) {
    await this.appVersionModel.deleteOne({ _id: id })
  }

  async createUploadUrl({ objectName, type }: PublicCreateUploadUrlRequestBodyDTO) {
    const result = await this.tosManageService.getPreSignedPostSignature({
      objectName: `${type}/${objectName}`
    })

    return result
  }
}
