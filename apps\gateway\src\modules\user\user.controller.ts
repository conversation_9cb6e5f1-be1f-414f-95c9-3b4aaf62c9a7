import { Body, Controller, Delete, Post, Get, Patch, Param, Req, Query } from '@nestjs/common'
import { UserService } from './user.service'
import { FastifyRequest } from 'fastify'
// import { ThrottlerGuard } from '@nestjs/throttler'
import {
  ApiForbiddenResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiQuery
} from '@nestjs/swagger'
import {
  UserDeleteResponseDTO,
  UserLoginOkResponseDTO,
  UserLoginRegisterRequestBodyDTO,
  UserSendCodeRequestBodyDTO,
  UserSendCodeResponseDTO,
  UserOkUserInfoResponseDTO,
  UserRequestUpdateDTO,
  UserPasswordLoginRequestBodyDTO,
  UserPasswordRegisterBodyDto,
  UserPasswordUpdateBodyDto,
  UserPasswordResetBodyDto,
  UserInfoExchangeKeyRequestBodyDTO,
  BaiduOcpcRequestBodyDTO,
  AgreeAuthRequestBodyDTO,
  AgreeAuthOkResponseDTO,
  UserSendCodeRequestBodyDTOV2
} from './user.dto'

import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseRequestDTO'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('users')
@ApiTags('用户管理')
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * 用户注册
   * @param data
   */
  @Post('auth')
  @ApiOperation({ summary: '用户注册/登录' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '验证码无效', type: BaseBadRequestDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  async registerUser(@Body() data: UserLoginRegisterRequestBodyDTO, @Req() req: FastifyRequest) {
    const response = await this.userService.putLoginUser(
      data,
      req.headers['device-type'] as string,
      req.headers['user-agent'] as string,
      req.headers.registrationid as string
    )

    return response
  }

  /**
   * 聚光注册
   * @param data
   */
  @Post('agree-auth')
  @ApiOperation({ summary: '小红书聚光授权登录' })
  @ApiOkResponse({ type: AgreeAuthOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '验证码无效', type: BaseBadRequestDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  async juguangAuth(@Body() data: AgreeAuthRequestBodyDTO) {
    const response = await this.userService.agreeAuth(data)
    return response
  }

  /**
   * 聚光注册
   * @param data
   */
  @Get('sign-url')
  @ApiOperation({ summary: '小红书聚光授权登录' })
  @ApiOkResponse({ type: AgreeAuthOkResponseDTO, description: '操作成功' })
  async getPreSignedUrl() {
    const response = await this.userService.getPreSignedUrl()
    return response
  }

  /**
   * 聚光授权获取小红书授权账号
   */
  @Delete('third-account')
  @ApiOperation({ summary: '聚光授权获取小红书授权账号' })
  @ApiOkResponse({ description: '操作成功', type: UserDeleteResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getThirdAccountToJuguang() {
    const response = await this.userService.getThirdAccountToJuguang()
    return response
  }

  /**
   * 退出登录
   */
  @Delete('auth')
  @ApiOperation({ summary: '退出登录' })
  @ApiOkResponse({ description: '操作成功', type: UserDeleteResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async loginOut(@Req() req: FastifyRequest) {
    return this.userService.deleteAuthorization(req.headers['device-type'] as string)
  }

  @Post('auth/password')
  @ApiOperation({ summary: '密码登录' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  async passwordLogin(@Body() data: UserPasswordLoginRequestBodyDTO, @Req() req: FastifyRequest) {
    const response = await this.userService.putPasswordLoginUser(
      data,
      req.headers['device-type'] as string,
      req.headers['user-agent'] as string,
      req.headers.registrationid as string
    )

    return response
  }

  /**
   *  发送短信验证码
   * @param param0
   * @returns
   */
  @ApiOperation({ summary: '发送验证码' })
  @ApiOkResponse({ type: UserSendCodeResponseDTO, description: '操作成功' })
  @ApiBadRequestResponse({ description: '验证码发送失败', type: BaseBadRequestDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @Post('sms-code')
  sendVerificationCode(@Body() data: UserSendCodeRequestBodyDTO) {
    return this.userService.sendVerificationCode(data)
  }

  /**
   *  发送短信验证码v2
   * @param param0
   * @returns
   */
  @ApiOperation({ summary: '发送验证码v2' })
  @ApiOkResponse({ type: UserSendCodeResponseDTO, description: '操作成功' })
  @ApiBadRequestResponse({ description: '验证码发送失败', type: BaseBadRequestDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @Post('sms-code-v2')
  sendVerificationCodeV2(@Body() data: UserSendCodeRequestBodyDTOV2) {
    return this.userService.sendVerificationCodeV2(data)
  }

  @Get('info')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiOkResponse({ type: UserOkUserInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getUserInfo() {
    return this.userService.getUserInfo()
  }

  @Get(`:phone/info`)
  @ApiOperation({ summary: '根据手机号获取用户信息' })
  @ApiOkResponse({ type: UserOkUserInfoResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getUserInfoByPhone(@Param('phone') phone: string) {
    return this.userService.getUserInfoByPhone(phone)
  }

  @Patch('info')
  @ApiOperation({ summary: '更新用户信息' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async updateUserInfo(@Body() data: UserRequestUpdateDTO) {
    return this.userService.updateUserInfo(data)
  }

  @Post('password')
  @ApiOperation({ summary: '设置密码' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async setUserPassword(@Body() data: UserPasswordRegisterBodyDto) {
    return this.userService.setPassword(data)
  }

  @Patch('password')
  @ApiOperation({ summary: '更改密码' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async updateUserPassword(@Body() data: UserPasswordUpdateBodyDto) {
    return this.userService.updatePassword(data)
  }

  @Post('reset-password')
  @ApiOperation({ summary: '忘记密码' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async resetUserPassword(@Body() data: UserPasswordResetBodyDto) {
    return this.userService.resetPassword(data)
  }

  @Post(`auth/exchangeKey`)
  @ApiOperation({ summary: '根据兑换密钥获取用户信息' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async postUserInfoByExchangeKey(@Body() data: UserInfoExchangeKeyRequestBodyDTO) {
    return this.userService.getUserInfoByExchage(data)
  }

  @Post('ocpcapi')
  @ApiOperation({ summary: '百度api上报' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async postBaiduOcpcapi(@Body() data: BaiduOcpcRequestBodyDTO) {
    return this.userService.postBaiduOcpcApi(data)
  }

  @Delete(`:id`)
  @ApiOperation({ summary: '删除用户' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'phone',
    required: true,
    type: String,
    description: '手机号码'
  })
  async deleteMember(@Param('id') id: number, @Query('phone') phone: string) {
    const response = this.userService.deleteMember(id, phone)

    return response
  }
}
