/*
  Warnings:

  - You are about to drop the column `memberCount` on the `Vip` table. All the data in the column will be lost.
  - You are about to drop the column `platformAccountCount` on the `Vip` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `Vip` DROP COLUMN `memberCount`,
    DROP COLUMN `platformAccountCount`,
    ADD COLUMN `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);

-- CreateTable
CREATE TABLE `Order` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `orderNo` VARCHAR(191) NOT NULL,
    `teamId` INTEGER NOT NULL,
    `vipId` INTEGER NOT NULL,
    `type` ENUM('Normal') NOT NULL,
    `price` DOUBLE NOT NULL,
    `payAmount` DOUBLE NOT NULL,
    `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `payType` ENUM('Online', 'Manual') NOT NULL,
    `remark` VARCHAR(191) NULL,

    UNIQUE INDEX `Order_orderNo_key`(`orderNo`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Order` ADD CONSTRAINT `Order_teamId_fkey` FOREIGN KEY (`teamId`) REFERENCES `Team`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Order` ADD CONSTRAINT `Order_vipId_fkey` FOREIGN KEY (`vipId`) REFERENCES `Vip`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
