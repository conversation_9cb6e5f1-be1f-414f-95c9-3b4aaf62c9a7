/* eslint-disable no-continue */
import { BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import { PersonalChatMessageEntity, MessagesByAutoresponderEntity } from '@qdy/mongo'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { InjectModel } from '@nestjs/mongoose'
import { AnyObject, Model } from 'mongoose'
import { Cache } from 'cache-manager'
import { PrismaService } from '@qdy/mysql'
import { WebhookEvents, WebhookKuaishouEvents } from './constant'
import { genSocketRedisKey, Platform } from '@qdy/utils'
import { WebhookKuaishouBody } from './types'
import {
  AutoresponderKeywordKey,
  AutoresponderVariableKey,
  PlatformAccountKeywordKey
} from '../autoresponder/constant'
import {
  AutoresponderContentChildType,
  AutoresponderContentTextType,
  AutoresponderContentType,
  AutoresponderKeywordContent,
  AutoresponderKeywordRedisValue,
  AutoresponderKeywordRule,
  AutoresponderTriggerType
} from '../autoresponder/autoresponder.dto'
import { generateRandom, wait } from '../../common/utils'
import { Queue, Worker } from 'bullmq'
import { WebHookServiceGrpc } from './webhook.rpc'
import { AccountAccountsStatus } from '../account/account.dto'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { ContentType, decrypt, postSendMessage } from './external.kuaishou'
import { PlatformAccountManageService } from '@qdy/common'
import { sendJpushMessageEventKey, sendJpushMessageEventEmitter } from './webhook.jpush'

@Injectable()
export class WebhookServiceKuaishou {
  logger = new Logger('WebhookKuaishouService')

  taskQueue: Queue

  taskWorker: Worker

  callbackTaskQueue: Queue

  callbackTaskWorker: Worker

  constructor(
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(MessagesByAutoresponderEntity.name)
    private messagesByAutoresponderModel: Model<MessagesByAutoresponderEntity>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  onModuleInit() {
    this.logger.log('WebhookServiceKuaishou init')

    this.taskQueue = new Queue('autoresponderKuaishou', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'autoresponderKuaishou',
      async (job) => {
        const { contents, ...others } = job.data

        setTimeout(() => {
          this.autoresponderTask(contents, others).catch((err) => {
            throw new BadRequestException(`task${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.callbackTaskQueue = new Queue('callbackEventKuaishou', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.callbackTaskWorker = new Worker(
      'callbackEventKuaishou',
      async (job) => {
        const { body, messageId } = job.data

        setTimeout(() => {
          this.callbackEventTask(body, messageId).catch((err) => {
            this.logger.log(err.message)
            throw new BadRequestException(`callbackeventKuaishouTask${err.message}`)
          })
        }, 500)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async webhook(body: WebhookKuaishouBody) {
    const { message_id: messageId } = body

    // if (body.event === WebhookEvents.Authorize || body.event === WebhookEvents.ContractAuthorize) {
    //   return 'success'
    // }

    const job = await this.callbackTaskQueue.getJob(`${body.event}-${messageId}`)

    if (!job) {
      await this.callbackTaskQueue.add(
        'callbackEventKuaishou',
        {
          body,
          messageId
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${body.event}-${messageId}`
        }
      )
    }

    return {
      result: 1,
      message_id: body.message_id
    }
  }

  async callbackEventTask(body: WebhookKuaishouBody, messageId: string) {
    const { kuaishouClientSecret } = this.configService.get<RootConfigMap['app']>('app')

    let sessionId = ''
    let openId = ''
    let userIdMaps: Record<string, string>
    let textContet = ''

    if (body.event === WebhookKuaishouEvents.Unauthorize) {
      try {
        this.logger.log(body)
        userIdMaps = await this.cacheManager.store.client.hgetall(
          genSocketRedisKey(body.data.open_id)
        )

        this.logger.log(userIdMaps)

        const platformAccount = await this.prisma.platformAccount.update({
          where: {
            openId: body.data.open_id,
            platform: Platform.Kuaishou
          },
          data: {
            expiresIn: 0,
            refreshExpiresIn: 0
          }
        })

        await this.platformAccountManageService.deletePlatformAccountRedisInfo(platformAccount)

        const changePlatformAccounts = []
        Object.keys(userIdMaps).forEach((socketId) => {
          changePlatformAccounts.push({
            socketId,
            data: {
              type: 'changePlatformAccount',
              data: [
                {
                  action: 'timeout',
                  platform: platformAccount.platform,
                  name: platformAccount.name,
                  avatar: platformAccount.avatar,
                  openId: platformAccount.openId,
                  accountRole: platformAccount.accountRole,
                  id: platformAccount.id,
                  teamId: platformAccount.teamId,
                  expiresTime: 0
                }
              ]
            }
          })
        })

        this.logger.log(changePlatformAccounts)

        this.webhookGrpcService.socketService
          .send({ list: JSON.stringify(changePlatformAccounts) })
          .subscribe({
            next: () => {},
            error: (err) => {
              throw new BadRequestException(`发送失败 error${err.message}`)
            },
            complete: () => {}
          })

        return
      } catch (error) {
        throw new BadRequestException(`解除授权失败 error${error.message}`)
      }
    }

    const fromUserId = body.data.from_user.user_id
    const formUserRole = body.data.from_user.user_role

    let toUserId = body.data.to_user.user_id

    if (!toUserId) {
      toUserId = fromUserId
    }

    if (body.event === WebhookKuaishouEvents.Authorize) {
      return
    }

    if (body.event === WebhookKuaishouEvents.ImEnterSession) {
      try {
        const lookKey = `welcome-look:${fromUserId}-${toUserId}`
        if (await this.cacheManager.get(lookKey)) {
          return
        }
        await this.cacheManager.set(lookKey, true, 60 * 60 * 1000)

        this.autoresponderWelcome(toUserId, fromUserId)
          .then(async (value) => {
            if (!value) {
              await this.cacheManager.del(lookKey)
            }
          })
          .catch((err) => {
            throw new BadRequestException(`autoresponderWelcome error${err.message}`)
          })
      } catch (err) {
        throw new BadRequestException(`cacheManager error${err.message}`)
      }
      return
    }

    if (body.event === WebhookKuaishouEvents.PushPotentialCustomerMsg) {
      try {
        if (body.data.interact_type === 4) {
          // 关注事件
          this.autoresponderFollow(toUserId, fromUserId).catch((err) => {
            throw new BadRequestException(`autoresponderFollow error${err.message}`)
          })
        }
      } catch (err) {
        throw new BadRequestException(`cacheManager error${err.message}`)
      }
      return
    }

    let event = WebhookEvents.IMReceiveMessage

    if (formUserRole === 1) {
      event = WebhookEvents.IMSendMessage
      openId = fromUserId
      sessionId = toUserId
      userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(fromUserId))
    } else {
      openId = toUserId
      sessionId = fromUserId
      userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(toUserId))

      sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
        openId: fromUserId,
        messageId,
        event,
        content: '收到一条快手私信消息'
      })
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId
      }
    })

    const data = {
      platformType: 'kuaishou',
      uniqueId: messageId,
      event,
      openId,
      fromUserId,
      toUserId,
      fromAvatar: '',
      fromName: '',
      toAvatar: '',
      toName: '',
      sessionId,
      content: {},
      createTime: body.data.message_create_time,
      isAuto: 0,
      messageId: body.data.message_id
    }

    if (formUserRole === 1) {
      data.fromName = platformAccount.name
      data.fromAvatar = platformAccount.avatar
    } else {
      data.toName = platformAccount.name
      data.toAvatar = platformAccount.avatar
    }

    if (body.data.expand_content) {
      this.logger.log(kuaishouClientSecret)
      this.logger.log(body.data.expand_content)

      // 用户信息解密
      const userDetailInfos = decrypt(body.data.expand_content, kuaishouClientSecret)

      const userList = JSON.parse(userDetailInfos)

      userList.userDetailInfos.forEach((item) => {
        if (item.userId === fromUserId) {
          data.fromAvatar = item.avatarUrl
          data.fromName = item.nickName
        } else if (item.userId === toUserId) {
          data.toAvatar = item.avatarUrl
          data.toName = item.nickName
        }
      })
    }

    if (body.data.content.content_type === 1) {
      const content = JSON.parse(body.data.content.content)
      textContet = decrypt(content.text, kuaishouClientSecret)

      data.content = {
        messageType: 'text',
        text: textContet
      }
    } else if (body.data.content.content_type === 2) {
      const content = JSON.parse(body.data.content.content)

      const imageContent = decrypt(content.uri, kuaishouClientSecret)
      data.content = {
        messageType: 'image',
        text: imageContent
      }
    } else if (body.data.content.content_type === 10000) {
      const textContent = decrypt(body.data.content.content, kuaishouClientSecret)
      data.content = {
        messageType: 'retain_consult_card',
        text: textContent
      }

      const cardInfo = JSON.parse(textContent)

      if (cardInfo.card_status === '2') {
        // 留资卡片完成态
        const platformAccount = await this.prisma.platformAccount.findUnique({
          where: {
            openId: toUserId
          }
        })

        if (platformAccount) {
          await this.prisma.cardMessage.create({
            data: {
              cardId: data.toUserId,
              teamId: platformAccount.teamId,
              fromUserId: data.fromUserId,
              fromName: data.fromName,
              fromAvatar: data.fromAvatar,
              toUserId: data.toUserId,
              toAvatar: data.toAvatar,
              toName: data.toName,
              name: cardInfo.card_data.find((item) => item.label === '姓名')?.value,
              phone: cardInfo.card_data.find((item) => item.label === '手机号')?.value,
              address: cardInfo.card_data.find((item) => item.label === '意向城市')?.value,
              cardData: cardInfo,
              createTime: new Date(data.createTime)
            }
          })
        }
      }
    }

    await this.personalChatMessageModel.create(data)

    if (event === WebhookEvents.IMReceiveMessage && body.data.content.content_type === 1) {
      // 接收私信并且是文字私信
      this.autoresponderKeywordChat({
        fromUserId,
        toUserId,
        text: textContet
      }).catch((err) => {
        throw new BadRequestException(`autoresponderKeywordChat${err.message}`)
      })
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    Object.keys(userIdMaps).forEach((socketId) => {
      dataList.push({
        socketId,
        data: { ...data, platformAccountId: parseInt(userIdMaps[socketId], 10) }
      })
    })

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`发送失败 error${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }

  async autoresponderWelcome(toUserId: string, fromUserId: string) {
    const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)
    const accountRes = await this.cacheManager.store.client.hget(
      PlatformAccountKeywordKey,
      toUserId
    )

    let isSuc = false

    if (res) {
      const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]

      const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

      await wait(1000)

      const account = await this.prisma.platformAccount.findUnique({
        where: {
          openId: toUserId
        }
      })

      if (!arr.length) {
        return
      }

      for (let i = 0; i < arr.length; i++) {
        const { contents, contentType, state, autoresponderId, executionCount } = arr[i]

        if (
          contentType !== AutoresponderContentType.Greeting ||
          status === AccountAccountsStatus.Disable
        ) {
          continue
        }

        if (!state || accountExpired < Date.now()) {
          continue
        }

        if (!Array.isArray(contents)) {
          continue
        }

        if (!contents.length) {
          continue
        }

        if (executionCount) {
          // 查询是否有对这个账号有发送过消息
          const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
            where: {
              fromUserId,
              toUserId,
              autoresponderId
            }
          })

          if (messageByAutoresponder) {
            continue
          }

          await this.messagesByAutoresponderModel.create({
            fromUserId,
            toUserId,
            autoresponderId,
            platformType: 'kuaishou'
          })
        }

        for (let i = 0; i < contents.length; i++) {
          const content = contents[i] as AutoresponderKeywordContent

          await this.autoresponderSend(content, {
            token,
            openId: toUserId,
            toUserId: fromUserId,
            platformAccountId,
            teamId,
            autoresponderId,
            scene: 'im_enter_session_prologue_msg',
            fromName: account.name,
            fromAvatar: account.avatar
          })

          await wait(100)
          isSuc = true
        }
      }
    }

    return isSuc
  }

  async autoresponderFollow(toUserId: string, fromUserId: string) {
    try {
      if (fromUserId === toUserId) {
        return
      }

      const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

      if (res) {
        await wait(1000)
        const arr = JSON.parse(res || '[]') as AutoresponderKeywordRedisValue[]
        const items = []

        const account = await this.prisma.platformAccount.findUnique({
          where: {
            openId: toUserId
          }
        })

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          toUserId
        )

        const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const { contents, contentType, trigger, state, stopReply, scene, autoresponderId } = arr[
            i
          ] as AutoresponderKeywordRedisValue

          if (scene || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          const send = trigger === AutoresponderTriggerType.Follow

          if (send && !job) {
            items.push(arr[i])
          }
        }

        const matchItem = items[generateRandom(items.length - 1)]

        this.logger.debug(
          'autoresponderKeywordChat follow',
          JSON.stringify(matchItem),
          JSON.stringify(items)
        )

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId,
              toUserId,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'kuaishou'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponderKuaishou',
            {
              token,
              openId: toUserId,
              toUserId: fromUserId,
              platformAccountId,
              teamId,
              autoresponderId,
              contents,
              fromName: account.name,
              fromAvatar: account.avatar,
              scene: 'im_potential_customer_msg'
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`AutoresponderKeywordKey Follow error${error.message}`)
    }
  }

  async autoresponderKeywordChat({
    fromUserId,
    toUserId,
    text,
    fromName,
    fromAvatar,
    toName,
    toAvatar
  }: {
    fromUserId: string
    toUserId: string
    text: string
    fromName?: string
    fromAvatar?: string
    toName?: string
    toAvatar?: string
  }) {
    try {
      if (fromUserId === toUserId) {
        return
      }

      const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, toUserId)

      if (res) {
        await wait(1000)
        const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]
        const items = []

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          toUserId
        )

        const { token, accountExpired, platformAccountId, teamId, status } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const {
            contents,
            contentType,
            keyword,
            rule,
            state,
            stopReply,
            scene,
            autoresponderId,
            trigger
          } = arr[i] as AutoresponderKeywordRedisValue

          if (scene || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          if (
            rule !== AutoresponderKeywordRule.Match &&
            rule !== AutoresponderKeywordRule.Instantly
          ) {
            // 收到私信回复只有两种规则（关键字和即刻回复）
            continue
          }

          let send = rule !== AutoresponderKeywordRule.Match

          if (!send) {
            send = !!(keyword as string[]).find((item) => {
              return RegExp(item, 'g').test(text)
            })
          }

          const chatMessage = trigger === AutoresponderTriggerType.Chat

          if (send && !job && chatMessage) {
            items.push(arr[i])
          }
        }

        const matchItem =
          items.find((item) => item.rule === AutoresponderKeywordRule.Match) ||
          items[generateRandom(items.length - 1)]

        this.logger.debug(
          'autoresponderKeywordChat matchItem',
          JSON.stringify(matchItem),
          JSON.stringify(items)
        )

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              where: {
                fromUserId,
                toUserId,
                autoresponderId
              }
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'kuaishou'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponderKuaishou',
            {
              token,
              openId: toUserId,
              toUserId: fromUserId,
              platformAccountId,
              teamId,
              autoresponderId,
              contents,
              fromName,
              fromAvatar,
              toName,
              toAvatar
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`AutoresponderKeywordKey error${error.message}`)
    }
  }

  async autoresponderTask(
    contents: AnyObject[],
    {
      token,
      openId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId,
      scene,
      fromName,
      fromAvatar,
      toName,
      toAvatar
    }: {
      token: string
      openId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
      scene?: string
      fromName?: string
      fromAvatar?: string
      toName?: string
      toAvatar?: string
    }
  ) {
    const [content, ...otherContents] = contents as AutoresponderKeywordContent[]

    if (!content) {
      return
    }

    try {
      await this.autoresponderSend(content, {
        token,
        openId,
        toUserId,
        platformAccountId,
        teamId,
        autoresponderId,
        scene,
        fromName,
        fromAvatar,
        toName,
        toAvatar
      })

      if (otherContents.length) {
        const [start] = otherContents

        await this.taskQueue.add(
          'autoresponder',
          {
            token,
            openId,
            toUserId,
            platformAccountId,
            teamId,
            autoresponderId,
            contents: otherContents,
            scene
          },
          {
            delay: start.delay * 1000 || 100,
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `${AutoresponderKeywordKey}-${toUserId}-${openId}-${autoresponderId}`
          }
        )
      }
    } catch (error) {
      throw new BadRequestException(`autoresponderTask error${error.message}`)
    }
  }

  async autoresponderSend(
    content: AutoresponderKeywordContent,
    {
      token,
      openId,
      toUserId,
      platformAccountId,
      teamId,
      autoresponderId,
      scene,
      fromName,
      fromAvatar,
      toName,
      toAvatar
    }: {
      token: string
      openId: string
      toUserId: string
      platformAccountId: number
      teamId: number
      autoresponderId: number
      scene?: string
      fromName?: string
      fromAvatar?: string
      toName?: string
      toAvatar?: string
    }
  ) {
    let contentText = ''
    let messageType = ContentType.Text
    const { kuaishouClientKey, kuaishouClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')

    if (content.messageType === AutoresponderContentChildType.Text) {
      for (let j = 0; j < content.texts.length; j++) {
        const text = content.texts[j]
        if (text.type === AutoresponderContentTextType.Text) {
          contentText += text.text
        } else if (text.type === AutoresponderContentTextType.Variable) {
          const value = await this.cacheManager.store.client.hget(
            AutoresponderVariableKey,
            `${text.variableId}`
          )
          try {
            const values = JSON.parse(value).value
            contentText += values[generateRandom(values.length - 1)]
          } catch (err) {
            throw new BadRequestException(`autoresponderSend text error${err.message}`)
          }
        }
      }
    } else if (content.messageType === AutoresponderContentChildType.Image) {
      messageType = ContentType.Image
      contentText = content.imageUrl
    } else if (content.messageType === AutoresponderContentChildType.Card) {
      messageType = ContentType.Card
      contentText = content.cardInfo.find((item) => item.accountId === platformAccountId).cardId
    }

    const data = await postSendMessage({
      messageType,
      content: contentText,
      secret: kuaishouClientSecret,
      appId: kuaishouClientKey,
      openId,
      accessToken: token,
      toUserId,
      platformAccountId,
      teamId,
      auto: true,
      width: content.width ?? 0,
      height: content.height ?? 0,
      contentLenght: content.contentLenght ?? 0,
      autoresponderId,
      redisClient: this.cacheManager,
      scene,
      fromName,
      fromAvatar,
      toName,
      toAvatar
    })

    await this.cacheManager.set(data.messageId, 1, 1000 * 60 * 10)
  }
}
