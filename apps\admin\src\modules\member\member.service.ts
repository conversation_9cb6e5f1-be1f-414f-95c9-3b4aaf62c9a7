import { ForbiddenException, Inject, Logger, NotFoundException } from '@nestjs/common'
import { FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import { PrismaService } from '@qdy/mysql'
import { MemberUpdateChannelDTO } from './member.dto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { genSocketRedisKey, Platform } from '@qdy/utils'
import { AutoresponderKeywordKey } from '../vip/vip.dto'
import { wechatLogout } from '../vip/external.wechat'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'

export class MemberService {
  logger = new Logger('MemberService')

  constructor(
    private readonly prisma: PrismaService,
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async getMembers({
    page,
    size,
    channelId,
    name,
    phone,
    startTime,
    endTime
  }: {
    page: number
    size: number
    channelId: number
    name: string
    phone: string
    startTime: number
    endTime: number
  }) {
    const where: Parameters<typeof this.prisma.user.findMany>[0]['where'] = {
      name: {
        contains: name
      },
      phone: {
        contains: phone
      },
      createTime: {
        gte: new Date(startTime),
        lte: new Date(endTime)
      },
      channelId
    }

    if (!name) {
      delete where.name
    }

    if (!phone) {
      delete where.phone
    }

    if (!startTime || !endTime) {
      delete where.createTime
    }

    if (!channelId) {
      delete where.channelId
    }

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        include: {
          channel: true
        },
        orderBy: { createTime: 'desc' },
        skip: (page - 1) * size,
        take: size
      }),
      this.prisma.user.count({ where })
    ])

    return {
      page,
      size,
      total,
      data: users.map((item) => ({
        id: item.id,
        name: item.name,
        avatar: item.avatar,
        phone: item.phone,
        channel: item.channel,
        createTime: item.createTime.getTime()
      }))
    }
  }

  async setChannel(id: number, data: MemberUpdateChannelDTO) {
    const member = await this.prisma.user.findUnique({
      where: {
        id
      }
    })

    if (!member) {
      throw new NotFoundException('用户不存在')
    }

    if (member.channelId) {
      throw new ForbiddenException('该用户已有渠道,无法设置')
    }

    const channel = await this.prisma.channel.findUnique({
      where: {
        code: data.channelCode
      }
    })

    if (!channel) {
      throw new NotFoundException('渠道不存在')
    }

    await this.prisma.user.update({
      where: {
        id
      },
      data: {
        channelId: channel.id
      }
    })
  }

  async deleteMember(id: number, phone: string) {
    const member = await this.prisma.user.findUnique({
      where: {
        id
      },
      include: {
        teamMember: true
      }
    })

    if (!member) {
      throw new NotFoundException('该用户不存在')
    }

    if (member.phone !== phone) {
      throw new ForbiddenException('手机号参数错误')
    }

    // 一：加入的团队，退出团队
    const memberTeam = member.teamMember.filter((item) => item.role !== 2)

    for (let j = 0; j < memberTeam.length; j++) {
      const { platformAccounts, userId } = await this.prisma.teamMember.delete({
        where: {
          id: memberTeam[j].id,
          teamId: memberTeam[j].teamId
        },
        include: {
          platformAccounts: true
        }
      })

      const [socketId, socketIdApp] = await Promise.all([
        this.cacheManager.get<string>(genSocketRedisKey(userId)),
        this.cacheManager.get<string>(genSocketRedisKey(userId + 'app'))
      ])

      await Promise.allSettled([
        (async () => {
          if (socketIdApp) {
            this.cacheManager.del(genSocketRedisKey(userId + 'app'))

            const tasks: Promise<number>[] = []
            for (let i = 0; i < platformAccounts.length; i++) {
              tasks.push(
                this.cacheManager.store.client.hdel(
                  genSocketRedisKey(platformAccounts[i].openId),
                  socketIdApp
                )
              )
            }

            await Promise.all(tasks)
          }
        })(),
        (async () => {
          if (socketId) {
            this.cacheManager.del(genSocketRedisKey(userId))

            const tasks: Promise<number>[] = []
            for (let i = 0; i < platformAccounts.length; i++) {
              tasks.push(
                this.cacheManager.store.client.hdel(
                  genSocketRedisKey(platformAccounts[i].openId),
                  socketId
                )
              )
            }

            await Promise.all(tasks)
          }
        })()
      ])
    }

    // 二：自己的团队，解散团队
    const ownerTeam = member.teamMember.filter((item) => item.role === 2)

    for (let z = 0; z < ownerTeam.length; z++) {
      const platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: ownerTeam[z].teamId
        }
      })

      const platformAccountTasks = []

      platformAccounts.forEach((item) => {
        platformAccountTasks.push(this.deleteAccount(item.id))
      })

      await this.prisma.$transaction(async (prisma) => {
        await Promise.all(platformAccountTasks)

        await prisma.teamMember.deleteMany({
          where: {
            teamId: ownerTeam[z].teamId
          }
        })

        await this.prisma.autoresponder.deleteMany({
          where: {
            teamId: ownerTeam[z].teamId
          }
        })

        await Promise.all([
          prisma.platformAccount.deleteMany({
            where: {
              teamId: ownerTeam[z].teamId
            }
          }),

          prisma.speech.deleteMany({
            where: {
              teamId: ownerTeam[z].teamId
            }
          }),
          prisma.variable.deleteMany({
            where: {
              teamId: ownerTeam[z].teamId
            }
          })
        ])

        await prisma.team.update({
          where: {
            id: ownerTeam[z].teamId
          },
          data: {
            isDelete: true
          }
        })
      })
    }

    await this.prisma.user.delete({
      where: {
        id
      }
    })
  }

  async deleteAccount(platformAccountId: number) {
    try {
      const platformAccount = await this.prisma.platformAccount.delete({
        where: { id: platformAccountId }
      })

      const res = await this.cacheManager.store.client.hgetall(AutoresponderKeywordKey)
      if (res) {
        Object.keys(res).forEach((key) => {
          const [openId] = key.split(':')
          if (openId === platformAccount.openId) {
            this.cacheManager.store.client.hdel(AutoresponderKeywordKey, key)
          }
        })
      }

      const bindUsers = await this.cacheManager.store.client.hgetall(
        genSocketRedisKey(platformAccount.openId)
      )

      const socketIds = []
      const delTask = []
      if (bindUsers) {
        Object.keys(bindUsers).forEach((socketId) => {
          socketIds.push(socketId)
          delTask.push(
            this.cacheManager.store.client.hdel(genSocketRedisKey(platformAccount.openId), socketId)
          )
        })
      }

      await Promise.all(delTask)

      if (platformAccount.platform === Platform.Wechat) {
        await wechatLogout({
          appId: platformAccount.appId
        })
      }
    } catch (e) {
      this.logger.error('删除平台账号失败', e)
    }
  }
}
