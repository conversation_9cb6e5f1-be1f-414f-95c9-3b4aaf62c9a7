import { Module } from '@nestjs/common'
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core'
import { GlobalExceptionFilter } from './common/filters'

import { ResponseTransformInterceptor } from './common/interceptors'
import { TokenGuard } from './common/guards'

import { RedisModule } from '@qdy/redis'
import { MongoModule } from '@qdy/mongo'
import { PrismaModule } from '@qdy/mysql'
import { ConfigModule } from '@qdy/config'
import { UserModule } from './modules/user/user.module'
import { OrderModule } from './modules/order/order.module'
import { MemberModule } from './modules/member/member.module'

@Module({
  imports: [
    ConfigModule,
    RedisModule,
    MongoModule,
    PrismaModule,
    UserModule,
    OrderModule,
    MemberModule
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor
    },
    {
      provide: APP_GUARD,
      useClass: TokenGuard
    }
  ]
})
export class AdminModule {}
