import { Modu<PERSON> } from '@nestjs/common'
import { TeamController } from './team.controller'
import { TeamService } from './team.service'
import { TeamSocketService } from './team.socket'
import { InvitationMongoose, LogMongoose, MessagesMongoose } from '@qdy/mongo'
import { AutoresponderModule } from '../autoresponder/autoresponder.module'
import { AccountModule } from '../account/account.module'

@Module({
  imports: [MessagesMongoose, AutoresponderModule, AccountModule, InvitationMongoose, LogMongoose],
  providers: [TeamService, TeamSocketService],
  controllers: [TeamController],
  exports: [TeamService]
})
export class TeamModule {}
