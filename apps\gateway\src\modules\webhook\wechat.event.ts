import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { EventEmitter } from 'events'
import { WebHookServiceGrpc } from './webhook.rpc'
import { PrismaService } from '@qdy/mysql'
import { AutoresponsederManageService } from '@qdy/common'

export const wechatEventKey = 'wechat-account-loginout'

export const wechatEventEmitter = new EventEmitter()

@Injectable()
export class WechatEventService implements OnModuleInit {
  logger = new Logger('WechatEventService')

  constructor(
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly autoresponsederManageService: AutoresponsederManageService
  ) {}

  onModuleInit() {
    wechatEventEmitter.on(wechatEventKey, this.wechatAccountLoginOutTask.bind(this))
  }

  async wechatAccountLoginOutTask({ appId }: { appId: string }) {
    this.logger.log('wechatAccountLoginOutTask', appId)
  }
}
