import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'

const logger = new Logger('webhook external weibo')

const followUserInfoApi = 'https://api.weibo.com/2/eps/user/info.json'

export async function followUserInfo({ accessToken, uid }: { accessToken: string; uid: string }) {
  const res = (await axios.get(followUserInfoApi, {
    params: {
      uid,
      access_token: accessToken
    }
  })) as {
    data: {
      subscribe: number
      id: string
      nickname: string
      sex: number
      language: string
      city: string
      province: string
      country: string
      headimgurl: string
      headimgurl_large: string
      headimgurl_hd: string
      follow: number
      subscribe_time: number
      error: string
      error_code: number
    }
  }

  if (res.data.error_code) {
    logger.debug({
      code: res.data.error_code,
      description: res.data.error
    })

    throw new BadRequestException(`[微博官方]:${res.data.error}`)
  }

  return res.data
}
