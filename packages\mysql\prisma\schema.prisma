generator client {
  provider = "prisma-client-js"
  output   = "../client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL_MYSQL")
}

//

// 用户
model User {
  id                    Int                     @id @default(autoincrement())
  phone                 String                  @unique
  name                  String
  avatar                String?
  state                 Int
  password              String?
  salt                  String?
  currentTeamId         Int?
  sessionConfig         SessionConfig[]
  teamMember            TeamMember[]
  PlatformAccountConfig PlatformAccountConfig[]
  createTime            DateTime                @default(now())
  channelId             Int?
  platform              String?                 @default("")
  registrationId        String?                 @default("")
  channel               Channel?                @relation(fields: [channelId], references: [id])
}

// 团队
model Team {
  id               Int               @id @default(autoincrement())
  name             String
  avatar           String?
  invitationCode   String            @unique
  ownerId          Int
  members          TeamMember[]
  speeches         Speech[]
  platformAccounts PlatformAccount[]
  isDelete         Boolean           @default(false)
  vip              Vip?
  deviceInfo       Json?
  douyinClientKey  String?
  // 订单销售类型：新购，复购，赠送
  salesType        String            @default("NotBuy")

  createTime   DateTime       @default(now())
  Variable     Variable[]
  Order        Order[]
  OrderRecord  OrderRecord[]
  UsersCoupons UsersCoupons[]
  Contract     Contract[]
  Refund       Refund[]
}

model TeamStatistic {
  id                Int @id @default(autoincrement())
  // 注册团队数
  registerTeamCount Int @default(0)

  // 付费团队数
  paidTeamCount Int @default(0)

  // 过期团队数
  expiredTeamCount Int @default(0)

  // 续费团队数
  renewTeamCount Int @default(0)

  // 转化率
  conversionRate Float @default(0)

  // 续费率
  renewRate     Float    @default(0)

  // 实付订单总金额
  payAmountTotal Float @default(0)

  // 客单价
  customerUnitPrice Float @default(0)

  statisticDate DateTime @default(now())
  createTime    DateTime @default(now())
}

// 团队成员
model TeamMember {
  id               Int               @id @default(autoincrement())
  name             String            @default("")
  role             Int
  user             User              @relation(fields: [userId], references: [id])
  userId           Int
  team             Team              @relation(fields: [teamId], references: [id])
  teamId           Int
  platformAccounts PlatformAccount[]
  joinTime         DateTime          @default(now())
  status           Int               @default(0)

  replyMessage Int @default(0)

  @@unique([userId, teamId])
}

// 第三方平台账号
model PlatformAccount {
  id               Int          @id @default(autoincrement())
  teamId           Int
  openId           String       @unique
  avatar           String
  name             String
  accountRole      String       @default("")
  expiresIn        Int
  refreshExpiresIn Int
  accessToken      String       @db.VarChar(240)
  refreshToken     String       @db.VarChar(1000)
  affiliates       TeamMember[]
  Team             Team         @relation(fields: [teamId], references: [id])
  createTime       DateTime     @default(now())
  tokenTime        DateTime     @default(now())
  refreshTime      DateTime     @default(now())
  // platform         Int          @default(0)
  platform         Int          @default(0)
  // wechat
  appId            String       @default("")
  username         String       @default("")
  wechatInfo       Json         @default("{}")
  regionId         String       @default("")
  status           Int          @default(0)
  receiveMessage   Int          @default(0)
  replyMessage     Int          @default(0)
  autoMessage      Int          @default(0)
  remark           String       @default("")
  unauthorize      String?      @db.Text
  isBind           Boolean      @default(false)
  parentOpenId     String?      @default("")
  groups           Group[]
  isNew            Boolean      @default(false)

  @@unique([teamId, openId])
}

model JuGuang {
  id       Int    @id @default(autoincrement())
  userId   String @unique
  appId    String
  nickName String
  token    String
}

model Group {
  id               Int               @id @default(autoincrement())
  name             String
  teamId           Int
  createTime       DateTime          @default(now())
  platformAccounts PlatformAccount[]
}

// 话术
model Speech {
  id               Int    @id @default(autoincrement())
  content          String
  speechType       Int    @default(0)
  userId           Int    @default(0)
  Team             Team   @relation(fields: [teamId], references: [id])
  teamId           Int
  speechCategoryId Int    @default(0)
}

// 话术分类
model SpeechCategory {
  id         Int      @id @default(autoincrement())
  name       String
  teamId     Int
  createTime DateTime @default(now())
  updateTime DateTime @default(now())
}

// 会话配置
model SessionConfig {
  id        Int      @id @default(autoincrement())
  sessionId String   @unique
  userId    Int
  top       Boolean? @default(false)
  mute      Boolean? @default(false)
  User      User     @relation(fields: [userId], references: [id])
}

// 账号配置
model PlatformAccountConfig {
  id                Int      @id @default(autoincrement())
  top               Boolean? @default(false)
  mute              Boolean? @default(false)
  platformAccountId Int
  userId            Int
  User              User     @relation(fields: [userId], references: [id])

  @@unique([platformAccountId, userId])
  @@index([userId])
}

model Autoresponder {
  id                  Int      @id @default(autoincrement())
  name                String
  contents            Json
  scene               Int
  contentType         Int
  rule                String
  keywords            Json
  platformAccountType Int
  platformAccountIds  Json
  state               Boolean  @default(true)
  teamId              Int
  ownerTeamMemberId   Int
  opusers             Json
  singleDegree        Int      @default(0)
  commentDegree       Int      @default(0)
  stopReply           Boolean? @default(false)
  stopInterval        Boolean? @default(false)
  stopTime            Int?     @default(0)
  platform            Int      @default(0)
  isDelay             Boolean? @default(false)
  delayTime           Int?     @default(0)
  executionCount      Int?     @default(0)
  isNew               Boolean? @default(false)
  parentId            Int?     @default(0)
  trigger             Int?     @default(0)
}

model Vip {
  id     Int  @id @default(autoincrement())
  teamId Int  @unique
  team   Team @relation(fields: [teamId], references: [id])

  // 消息数量
  messageLimit               Int
  // 账号数量限制
  platformAccountNumberLimit Int
  // 成员数量限制
  teamMemberNumberLimit      Int

  // 抖音评论数量限制
  douyinCommentNumberLimit Int @default(0)

  wechatMessageLimit Int @default(300)

  // 权益包数量
  interestCount Int

  // 当前vip所支付价格
  price Float @default(0)

  // 当前vip的有效月份数量
  month Int @default(0)

  day Int @default(0)

  freeDay Int @default(0)

  // 赠送月份
  freeMonth Int @default(0)

  // 过期时间
  createTime DateTime @default(now())

  // 最新一笔订单号
  lastOrderNo String @default("")

  // 有效期可为空
  expirationTime DateTime?

  order Order[]
}

model SystemDosage {
  id Int @id @default(autoincrement())

  standardMessageLimit               Int @default(0)
  standardTeamMemberNumberLimit      Int @default(0)
  standardPlatformAccountNumberLimit Int @default(0)
  standardDouyinCommentCount         Int @default(0)
}

model AdminUser {
  id         Int      @id @default(autoincrement())
  nickname   String   @default("")
  username   String   @unique
  password   String
  role       Int
  createTime DateTime @default(now())
  salt       String
  mfaSecret  String   @default("")
  secretIv   String   @default("")
}

// 渠道商后台账号
model ChannelAdminUser {
  id         Int      @id @default(autoincrement())
  nickname   String   @default("")
  username   String   @unique
  password   String
  role       Int
  createTime DateTime @default(now())
  salt       String
  channelId  Int?
  channel    Channel? @relation(fields: [channelId], references: [id])
}

model Order {
  id            Int       @id @default(autoincrement())
  phone         String
  orderNo       String    @unique
  teamId        Int
  team          Team      @relation(fields: [teamId], references: [id])
  vipId         Int
  vip           Vip       @relation(fields: [vipId], references: [id])
  type          String
  price         Float
  dueAmount     Float
  payAmount     Float?
  payTime       DateTime?
  fromTime      DateTime  @default(now())
  toTime        DateTime?
  expireTime    DateTime
  payType       String?
  usersCouponId Int?
  orderStatus   String
  isPay         Boolean   @default(true)
  priceDiff     Float?    @default(0)
  orderType     String    @default("")

  vipMonth  Int
  freeMonth Int

  interestId Int
  interest   Interest @relation(fields: [interestId], references: [id])

  channelId Int?
  channel   Channel? @relation(fields: [channelId], references: [id])

  isUpgrade Boolean @default(false)

  interestCount Int

  remark      String?
  // 创建人id
  creatorId   Int?
  // 创建人名称
  creatorName String?

  isGiftOrder   Boolean @default(false)
  giftDays      Int     @default(0)
  remainingDays Int     @default(0)

  // 订单销售类型：新购，复购，未购买
  salesType     String  @default("NotBuy")
  days          Int     @default(0)
  // 苹果支付id
  transactionId String?

  orderInfos   OrderInfo[]
  UsersCoupons UsersCoupons[]
  Contract     Contract[]

  @@index([orderNo])
}

model OrderInfo {
  id           Int       @id @default(autoincrement())
  orderId      Int
  order        Order     @relation(fields: [orderId], references: [id])
  orderNo      String
  payType      String
  payStatus    String
  payTime      DateTime?
  tradeNo      String?
  urlInfo      String    @db.Text()
  callbackInfo Json

  @@index([orderId])
  @@index([orderNo])
}

model Contract {
  id            Int      @id @default(autoincrement())
  orderId       Int
  order         Order    @relation(fields: [orderId], references: [id])
  orderNo       String
  startTime     DateTime
  endTime       DateTime
  amount        Float
  teamId        Int
  team          Team     @relation(fields: [teamId], references: [id])
  interestId    Int
  interest      Interest @relation(fields: [interestId], references: [id])
  interestCount Int
  createTime    DateTime
  isFree        Boolean  @default(false)
  status        Int      @default(0)
}

model Refund {
  id                 Int      @id @default(autoincrement())
  refundNo           String   @unique
  teamId             Int
  team               Team     @relation(fields: [teamId], references: [id])
  createTime         DateTime
  refundAmount       Float
  actualRefundAmount Float
  remark             String
  orderInfo          Json
}

model Variable {
  id     Int    @id @default(autoincrement())
  teamId Int
  Team   Team   @relation(fields: [teamId], references: [id])
  name   String
  value  Json
}

model Interest {
  id                   Int        @id @default(autoincrement())
  platformAccountCount Int        @default(0)
  messageCount         Int        @default(0)
  memberCount          Int        @default(0)
  douyinCommentCount   Int        @default(0)
  price                Float
  Order                Order[]
  Contract             Contract[]
}

// 优惠券
model Coupons {
  id                    Int      @id @default(autoincrement())
  // 优惠券名称
  name                  String
  // 最低消费金额，满足这个金额才能使用优惠券
  minimumSpendingAmount Int      @default(0)
  // 满减金额/优惠金额
  discountAmount        Int      @default(0)
  // 创建人id
  creatorId             Int
  // 创建人名称
  creatorName           String
  // 创建时间
  createTime            DateTime @default(now())
  // 发放数量
  postAmount            Int      @default(0)
  // 使用/激活数量
  activeAmount          Int      @default(0)
  // 优惠券状态 0:正常 -1:删除
  status                Int      @default(0)
  // 优惠券发放后有效天数
  expireDaysNum         Int

  channels Channel[]
}

// 优惠券发放记录
model UsersCoupons {
  id         Int      @id @default(autoincrement())
  // 优惠券名称
  name       String
  // 优惠券id
  couponsId  Int
  // 发放人用户id
  creatorId  Int
  // 接受优惠券的用户手机号
  phone      String
  // 接受优惠券的用户id
  userId     Int
  // 创建时间
  createTime DateTime @default(now())
  // 过期时间
  expireTime DateTime

  // 满减金额
  minimumSpendingAmount Int

  // 优惠金额
  discountAmount Int
  // 状态，0:正常，1已使用
  status         Int @default(0)

  // 渠道id
  channelId Int?
  channel   Channel? @relation(fields: [channelId], references: [id])

  // 使用优惠券（付款）时间
  castTime DateTime?

  // 使用优惠券(付款)时的团队id
  teamId Int?
  team   Team? @relation(fields: [teamId], references: [id])

  // 使用优惠券（付款）的订单号
  orderNo String?
  order   Order?  @relation(fields: [orderNo], references: [orderNo])

  @@unique([userId, channelId])
  @@index([couponsId, userId])
  @@index([phone, createTime, expireTime])
}

// 渠道
model Channel {
  id               Int                @id @default(autoincrement())
  couponId         Int?
  coupon           Coupons?           @relation(fields: [couponId], references: [id])
  name             String
  code             String
  createTime       DateTime           @default(now())
  status           Boolean            @default(true)
  Order            Order[]
  User             User[]
  UsersCoupons     UsersCoupons[]
  ChannelAdminUser ChannelAdminUser[]

  @@unique([code])
  @@index([couponId])
}

model OrderRecord {
  id           Int      @id @default(autoincrement())
  orderNo      String   @default("")
  teamId       Int
  Team         Team     @relation(fields: [teamId], references: [id])
  type         String
  price        Float
  // 实际退款
  realityPrice Float?   @default(0)
  createTime   DateTime @default(now())
}

// 收支日报
model CashDailyReport {
  id      Int      @id @default(autoincrement())
  // 日期
  date    DateTime @db.Date
  // 收入
  income  Float    @default(0)
  // 支出
  expense Float    @default(0)
  // 类型
  type    Int      @default(0)

  @@unique([date, type])
}

// 私有信息用户
model PrivateMessageUser {
  id         Int                       @id @default(autoincrement())
  openId     String                    @unique
  phone      String                    @default("")
  wechat     String                    @default("")
  province   Int                       @default(0)
  city       Int                       @default(0)
  remark     String                    @default("")
  createTime DateTime                  @default(now())
  labels     PrivateMessageUserLabel[]
}

// 省市数据
model City {
  id       Int    @id @default(autoincrement())
  parentId Int
  name     String
  type     Int

  @@index([parentId])
}

// 私有信息用户标签
model PrivateMessageUserLabel {
  id                 Int                  @id @default(autoincrement())
  title              String               @default("")
  teamId             Int
  createTime         DateTime             @default(now())
  privateMessageUser PrivateMessageUser[]

  @@index([teamId])
}

model CardMessage {
  id         Int      @id @default(autoincrement())
  cardId     String
  teamId     Int
  fromUserId String   @default("")
  fromAvatar String   @default("")
  fromName   String   @default("")
  toUserId   String   @default("")
  toAvatar   String   @default("")
  toName     String   @default("")
  name       String   @default("")
  phone      String   @default("")
  address    String   @default("")
  cardData   Json
  createTime DateTime @default(now())

  @@index([cardId])
  @@index([teamId])
  @@index([toUserId])
}
