import { Module } from '@nestjs/common'
import { OverviewService } from './overview.service'
import { OverviewController } from './overview.controller'
import {
  OverviewMongoose,
  MessagesMongoose,
  CommentMongoose,
  UserRegisterStatisticsMongoose,
  DailyOverviewMongoose,
  PersonalChatMessagesMongoose,
  WorkCommentMongoose
} from '@qdy/mongo'
import { ScheduleModule } from '@nestjs/schedule'
import { TransferService } from './transfer.service'
import { ContractService } from './contract.service'
import { OrderTransferService } from './orderTransfer.service'
import { VipTransferService } from './vipTransfer.service'
import { AutoresponderService } from './autoresponder.service'
import { AutoresponderManageModule } from '@qdy/common'

@Module({
  imports: [
    OverviewMongoose,
    MessagesMongoose,
    CommentMongoose,
    UserRegisterStatisticsMongoose,
    DailyOverviewMongoose,
    PersonalChatMessagesMongoose,
    WorkCommentMongoose,
    AutoresponderManageModule,
    ScheduleModule.forRoot()
  ],
  providers: [
    OverviewService,
    TransferService,
    ContractService,
    OrderTransferService,
    VipTransferService,
    AutoresponderService
  ],
  controllers: [OverviewController]
})
export class OverviewModule {}
