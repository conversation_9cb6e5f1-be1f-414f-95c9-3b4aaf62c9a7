import { Modu<PERSON> } from '@nestjs/common'
import { TeamController } from './team.controller'
import { TeamService } from './team.service'
import { OverviewMongoose, DailyOverviewMongoose } from '@qdy/mongo'
import { OrderManageModule } from '@qdy/common'
import { TeamStatisticCornService } from './teamStatistic.cron.service'

@Module({
  imports: [OverviewMongoose, DailyOverviewMongoose, OrderManageModule],
  providers: [TeamService, TeamStatisticCornService],
  controllers: [TeamController],
  exports: [TeamService]
})
export class TeamModule {}
