import { EventEmitter } from 'events'
import { DailyMessageStatisticEntity } from '@qdy/mongo'
import dayjs from 'dayjs'

export const overviewEventEmitter = new EventEmitter()

export const eventKey = 'overview-create-event'

export function sendEvent(
  data: Partial<DailyMessageStatisticEntity> & {
    platformAccountId: number
    autoresponderId?: number
    isDouyinComment?: boolean
  }
): void {
  overviewEventEmitter.emit(eventKey, { ...data, createTime: dayjs().format('YYYY-MM-DD') })
}
