import { Module } from '@nestjs/common'
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core'
import { GlobalExceptionFilter } from './common/filters'

import { ResponseTransformInterceptor } from './common/interceptors'
import { TokenGuard } from './common/guards'

import { RedisModule } from '@qdy/redis'
import { MongoModule } from '@qdy/mongo'
import { PrismaModule } from '@qdy/mysql'
import { ConfigModule } from '@qdy/config'
import { TeamModule } from './modules/team/team.module'
import { UserModule } from './modules/user/user.module'
import { VipModule } from './modules/vip/vip.module'
import { OverviewModule } from './modules/overview/overview.module'
import { CouponsModule } from './modules/coupons/coupons.module'
import { PublicModule } from './modules/public/public.module'
import { ChannelModule } from './modules/channel/channel.module'
import { MemberModule } from './modules/member/member.module'
import { AccountModule } from './modules/account/account.module'
import { WechatModule } from './modules/wechat/wechat.module'
import { OrderManageModule } from '@qdy/common'

@Module({
  imports: [
    ConfigModule,
    RedisModule,
    MongoModule,
    PublicModule,
    PrismaModule,
    TeamModule,
    UserModule,
    VipModule,
    OverviewModule,
    CouponsModule,
    ChannelModule,
    MemberModule,
    AccountModule,
    OrderManageModule,
    WechatModule
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseTransformInterceptor
    },
    {
      provide: APP_GUARD,
      useClass: TokenGuard
    }
  ]
})
export class AdminModule {}
