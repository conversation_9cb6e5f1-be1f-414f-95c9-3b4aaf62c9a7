/*
  Warnings:

  - You are about to drop the column `creatTime` on the `coupons` table. All the data in the column will be lost.
  - Added the required column `creatorId` to the `UsersCoupons` table without a default value. This is not possible if the table is not empty.
  - Added the required column `userId` to the `UsersCoupons` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `coupons` DROP COLUMN `creatTime`,
    ADD COLUMN `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);

-- AlterTable
ALTER TABLE `userscoupons` ADD COLUMN `creatorId` INTEGER NOT NULL,
    ADD COLUMN `userId` INTEGER NOT NULL;
