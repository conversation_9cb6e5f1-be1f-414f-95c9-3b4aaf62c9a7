import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { postUserPage } from '../interact/external.wechat'
import { InjectModel } from '@nestjs/mongoose'
import { WechatOpusEntity } from 'packages/mongo/lib'
import { AnyObject, Model } from 'mongoose'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { getAuthorizeAccountRefresh } from './external.kuaishou'
import { Platform } from '@qdy/utils'

export const updateOpusEventKey = 'update-wechat-opus'

export const refreshTokenEventKey = 'refresh-token'

export const eventEmitter = new EventEmitter()

@Injectable()
export class AccountEventService implements OnModuleInit {
  logger = new Logger('WechatOpusEventService')

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @InjectModel(WechatOpusEntity.name) private wechatOpusModel: Model<WechatOpusEntity>
  ) {}

  taskQueue: Queue

  taskWorker: Worker

  refreshTokenQueue: Queue

  refreshTokenWorker: Worker

  onModuleInit() {
    eventEmitter.on(updateOpusEventKey, this.createUpdateOpusTask.bind(this))

    eventEmitter.on(refreshTokenEventKey, this.refreshTokenTask.bind(this))

    this.taskQueue = new Queue('wechat-opus-event', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'wechat-opus-event',
      async (job) => {
        const { wxid, maxTime } = job.data
        await this.updateWechatOpus(wxid, maxTime)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.refreshTokenQueue = new Queue('refresh-token-event', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.refreshTokenWorker = new Worker(
      'refresh-token-event',
      async (job) => {
        const { openId, refreshToken } = job.data
        await this.refreshAccessToken(openId, refreshToken)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  async createUpdateOpusTask({ wxid }: { wxid: string }) {
    this.logger.log('createUpdateOpusTask')
    const lastOpusRecord = await this.wechatOpusModel
      .findOne({
        wxid
      })
      .sort({ createTime: -1 })

    let maxTime = 0

    if (lastOpusRecord) {
      maxTime = lastOpusRecord.createTime
    }

    await this.taskQueue.add(
      'wechat-opus-event',
      { wxid, maxTime },
      {
        removeOnComplete: true,
        removeOnFail: true,
        jobId: `${wxid}-update-opus-task`
      }
    )
  }

  async updateWechatOpus(wxid: string, maxTime: number) {
    try {
      const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
      const wechatAccount = await this.prisma.platformAccount.findUnique({
        where: {
          openId: wxid
        }
      })

      if (!wechatAccount || wechatAccount.expiresIn === 0) {
        this.logger.log('账号不存在或者失效')
        ;(await this.taskQueue.getJob(`${wxid}-update-opus-task`)).remove()
      }

      this.logger.log(maxTime, 'maxTime')

      await this.getOpusList({
        wxid: wechatAccount.openId,
        appid: wechatAccount.appId,
        token: wechatConfig.Token,
        myUserName: wechatAccount.username,
        maxTime,
        isNew: wechatAccount.isNew
      })
    } catch (error) {
      this.logger.error(error)
    }
  }

  async getOpusList(data: {
    wxid: string
    appid: string
    token: string
    myUserName: string
    maxTime: number
    isNew: boolean
  }) {
    let lastBuffer = ''
    let maxId = '0'
    let continueFlag = 0
    let result = []

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const record = []
        let recordLength = 0
        let maxRecordLength = 0
        const videoList = await postUserPage({
          appId: data.appid,
          toUserName: data.myUserName,
          lastBuffer,
          maxId,
          token: data.token,
          isNew: data.isNew
        })

        ;({ continueFlag, lastBuffer } = videoList)

        if (videoList.object) {
          maxId = videoList.object[videoList.object.length - 1].id
          if (data.maxTime === 0) {
            // 如果maxTime为0 不比较时间
            result = (videoList.object as AnyObject[]).filter((item) => item?.privateFlag !== 1)
          } else {
            recordLength = (videoList.object as AnyObject[]).filter(
              (item) => item?.privateFlag !== 1
            ).length

            maxRecordLength = (videoList.object as AnyObject[]).filter(
              (item) => item?.privateFlag !== 1 && item.createtime > data.maxTime
            ).length

            result = (videoList.object as AnyObject[]).filter(
              (item) => item?.privateFlag !== 1 && item.createtime > data.maxTime
            )
          }

          result.forEach((item) => {
            const opus = {
              objectId: item.id,
              wxid: data.wxid,
              username: item.username,
              nickname: item.nickName,
              headUrl: item?.contact?.headUrl,
              objectNonceId: item.objectNonceId,
              createTime: item.createtime,
              sessionBuffer: item.sessionBuffer,
              description: item?.objectDesc?.description,
              forwardCount: item.forwardCount,
              likeCount: item.likeCount,
              commentCount: item.commentCount,
              thumbUrl: `${item?.objectDesc?.media[0]?.ThumbUrl}${item?.objectDesc?.media[0]?.thumbUrlToken}`,
              jsonData: JSON.stringify(item)
            }
            record.push(opus)
          })

          await this.wechatOpusModel.insertMany(record)

          if (recordLength !== maxRecordLength) {
            break
          }
        } else {
          break
        }

        if (!continueFlag) {
          break
        }
      } catch (err) {
        this.logger.error('获取视频列表失败', err)
        break
      }
    }
  }

  async refreshTokenTask({ openId }: { openId: string }) {
    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId
      }
    })

    if (
      platformAccount &&
      platformAccount.platform === Platform.Kuaishou &&
      platformAccount.refreshExpiresIn + platformAccount.createTime.getTime() > Date.now()
    ) {
      await this.refreshTokenQueue.add(
        'refresh-token-event',
        {
          openId: platformAccount.openId,
          refreshToken: platformAccount.refreshToken
        },
        {
          delay: 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${platformAccount.openId}-refresh-token-task`
        }
      )
    }
  }

  async refreshAccessToken(openId: string, refreshToken: string) {
    const { kuaishouClientKey, kuaishouClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')

    try {
      const refreshResult = await getAuthorizeAccountRefresh({
        clientKey: kuaishouClientKey,
        clientSecret: kuaishouClientSecret,
        refreshToken
      })

      await this.prisma.platformAccount.update({
        where: {
          openId
        },
        data: {
          accessToken: refreshResult.accessToken,
          expiresIn: refreshResult.expiresIn,
          refreshToken: refreshResult.refreshToken,
          refreshExpiresIn: refreshResult.refreshExpiresIn,
          refreshTime: new Date()
        }
      })

      await this.refreshTokenQueue.add(
        'refresh-token-event',
        {
          openId,
          refreshToken
        },
        {
          delay: 60 * 1000,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${openId}-refresh-token-task`
        }
      )
    } catch (error) {
      this.logger.error(error)
    }
  }
}
