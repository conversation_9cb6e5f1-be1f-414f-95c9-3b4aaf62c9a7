import { ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

class OverviewItem {
  @ApiResponseProperty({
    type: Number
  })
  teamId: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  uploadImageCount: 1

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  commentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  groupCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  singleCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  manageGroupCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  autoCommentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 20
  })
  autoGroupCount: number

  @ApiResponseProperty({
    type: Number,
    example: 20
  })
  autoSingleCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  wechatCommentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 2
  })
  wechatMessageCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  wechatAutoCommentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  wechatAutoMessageCount: number

  @ApiResponseProperty({
    type: String,
    example: '2024-06-04'
  })
  createTime: string
}

class OverviewTeamMemberResponse {
  @ApiResponseProperty({
    type: Number
  })
  id: number

  @ApiResponseProperty({
    type: String
  })
  name: string

  @ApiResponseProperty({
    type: String
  })
  avatar: string

  @ApiResponseProperty({
    type: Number
  })
  replyMessage: number
}

class OverviewPlatformAccountResponse {
  @ApiResponseProperty({
    type: Number
  })
  id: number

  @ApiResponseProperty({
    type: String
  })
  name: string

  @ApiResponseProperty({
    type: String
  })
  avatar: string

  @ApiResponseProperty({
    type: Number
  })
  receiveMessageCount: number

  @ApiResponseProperty({
    type: Number
  })
  replyMessage: number

  @ApiResponseProperty({
    type: Number
  })
  autoMessage: number

  @ApiResponseProperty({
    type: Number
  })
  totalMessage: number
}

class OverviewResponse {
  @ApiResponseProperty({
    type: [OverviewItem]
  })
  list: OverviewItem[]

  @ApiResponseProperty({
    type: [OverviewTeamMemberResponse]
  })
  teamMemberList: OverviewTeamMemberResponse[]

  @ApiResponseProperty({
    type: [OverviewPlatformAccountResponse]
  })
  platformAccountList: OverviewPlatformAccountResponse[]
}

export class OverviewResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [OverviewResponse]
  })
  data: OverviewResponse[]
}

export class OverviewDosage {
  @ApiResponseProperty({
    type: Number,
    example: '1 评论回复最大数'
  })
  commentTotalCount: number

  @ApiResponseProperty({
    type: Number,
    example: '1 群聊回复最大数'
  })
  groupTotalCount: number

  @ApiResponseProperty({
    type: Number,
    example: '1 单聊回复最大数'
  })
  singleTotalCount: number

  @ApiResponseProperty({
    type: Number,
    example: '1 单聊回复总数'
  })
  singleCount: number

  @ApiResponseProperty({
    type: Number,
    example: '1 群聊回复总数'
  })
  groupCount: number

  @ApiResponseProperty({
    type: Number,
    example: '1 评论回复总数'
  })
  commentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  wechatCommentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 2
  })
  wechatMessageCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  wechatAutoCommentCount: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  wechatAutoMessageCount: number
}

export class OverviewDosageResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OverviewDosage
  })
  data: OverviewDosage
}
