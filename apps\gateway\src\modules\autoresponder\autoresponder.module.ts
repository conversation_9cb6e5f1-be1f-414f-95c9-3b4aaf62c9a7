import { Module } from '@nestjs/common'
import { AutoresponderKeywordService } from './autoresponder.keyword.service'
import { AutoresponderController } from './autoresponder.controller'
// import { AutoresponderGreetingService } from './autoresponder.greeting.service'
import { AutoresponderInitService } from './autoresponder.init'
import { AutoresponderVariableService } from './autoresponder.variable.service'

@Module({
  imports: [],
  controllers: [AutoresponderController],
  providers: [
    AutoresponderInitService,
    AutoresponderKeywordService,
    // AutoresponderGreetingService,
    AutoresponderVariableService
  ],
  exports: [AutoresponderKeywordService]
})
export class AutoresponderModule {}
