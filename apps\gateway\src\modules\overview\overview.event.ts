import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import {
  DailyMessageStatisticEntity,
  DailyOverviewEntity,
  MessageStatisticEntity,
  OverviewEntity,
  UsageEntity
} from '@qdy/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { eventKey, overviewEventEmitter } from './event'
import { PrismaService } from '@qdy/mysql'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import dayjs from 'dayjs'
import { TlsManageService } from '@qdy/common'

@Injectable()
export class OverviewEventService implements OnModuleInit {
  logger = new Logger('OverviewEventService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(OverviewEntity.name) private overviewModel: Model<OverviewEntity>,
    @InjectModel(DailyMessageStatisticEntity.name)
    private dailyMessageStatisticModel: Model<DailyMessageStatisticEntity>,
    @InjectModel(DailyOverviewEntity.name)
    private dailyOverviewModel: Model<DailyOverviewEntity>,
    @InjectModel(MessageStatisticEntity.name)
    private messageStatisticModel: Model<MessageStatisticEntity>,
    @InjectModel(UsageEntity.name) private usageModel: Model<UsageEntity>,
    private readonly prisma: PrismaService,
    private readonly tlsManageService: TlsManageService
  ) {}

  onModuleInit() {
    overviewEventEmitter.on(eventKey, this.overviewByDaily.bind(this))
  }

  async overviewByDaily(
    data: DailyMessageStatisticEntity & {
      autoresponderId?: number
      platformAccountId: number
      isDouyinComment?: boolean
      openId: string
    }
  ) {
    if (data.platformAccountId) {
      await this.prisma.platformAccount.update({
        where: {
          id: data.platformAccountId
        },
        data: data.autoresponderId
          ? {
              autoMessage: {
                increment: 1
              }
            }
          : {
              replyMessage: {
                increment: 1
              }
            }
      })
    }

    if (data.autoresponderId) {
      const autoresponder = await this.prisma.autoresponder.findUnique({
        where: {
          id: data.autoresponderId
        }
      })

      if (autoresponder) {
        await this.prisma.autoresponder.update({
          where: {
            id: data.autoresponderId
          },
          data: {
            singleDegree: data.autoSingleCount
              ? autoresponder.singleDegree + 1
              : autoresponder.singleDegree,
            commentDegree: data.autoCommentCount
              ? autoresponder.commentDegree + 1
              : autoresponder.commentDegree
          }
        })
      }
    }

    await this.createUsageByTeam(data)
    await this.createDailyMessageByTeam(data)

    this.tlsManageService.putLogs({
      logData: `概览数据更新成功`,
      logLevel: 'info',
      requestUri: 'createDailyMessageByTeam',
      jobStatus: 'createDailyMessageByTeam'
    })

    await this.createDailyOverviewByTeam(data)
  }

  async createDailyMessageByTeam(
    data: DailyMessageStatisticEntity & {
      autoresponderId?: number
      platformAccountId: number
      isDouyinComment?: boolean
      openId: string
    }
  ) {
    // const dailyMessageStatisticByTeam = await this.dailyMessageStatisticModel.findOne({
    //   createTime: data.createTime,
    //   platformType: data.platformType,
    //   teamId: data.teamId,
    //   openId: data.openId
    // })

    // // 判断当前团队账号是否有数据,有则更新数据
    // if (dailyMessageStatisticByTeam) {
    //   await this.dailyMessageStatisticModel.findByIdAndUpdate(dailyMessageStatisticByTeam.id, {
    //     singleCount: dailyMessageStatisticByTeam.singleCount + Number(data.singleCount),
    //     autoSingleCount: dailyMessageStatisticByTeam.autoSingleCount + Number(data.autoSingleCount),
    //     commentCount: dailyMessageStatisticByTeam.commentCount + Number(data.commentCount),
    //     autoCommentCount:
    //       dailyMessageStatisticByTeam.autoCommentCount + Number(data.autoCommentCount),
    //     groupCount: dailyMessageStatisticByTeam.groupCount + Number(data.groupCount)
    //   })
    // }

    const result = await this.dailyMessageStatisticModel.findOne({
      createTime: data.createTime,
      openId: data.openId,
      teamId: data.teamId
    })

    if (result) {
      await this.dailyMessageStatisticModel.findByIdAndUpdate(result.id, {
        singleCount: result.singleCount + Number(data.singleCount),
        autoSingleCount: result.autoSingleCount + Number(data.autoSingleCount),
        commentCount: result.commentCount + Number(data.commentCount),
        autoCommentCount: result.autoCommentCount + Number(data.autoCommentCount),
        groupCount: result.groupCount + Number(data.groupCount)
      })
    } else {
      await this.dailyMessageStatisticModel.create({
        platformType: data.platformType,
        createTime: data.createTime,
        openId: data.openId,
        teamId: data.teamId,
        singleCount: Number(data.singleCount),
        autoSingleCount: Number(data.autoSingleCount),
        commentCount: Number(data.commentCount),
        autoCommentCount: Number(data.autoCommentCount),
        groupCount: Number(data.groupCount)
      })
    }
  }

  async createDailyOverviewByTeam(
    data: DailyMessageStatisticEntity & {
      autoresponderId?: number
      platformAccountId: number
      isDouyinComment?: boolean
      openId: string
    }
  ) {
    const dailyOverviewByTeam = await this.dailyOverviewModel.findOne({
      createTime: data.createTime,
      teamId: data.teamId
    })

    // 判断当前团队账号是否有数据,有则更新数据
    if (dailyOverviewByTeam) {
      await this.dailyOverviewModel.findByIdAndUpdate(dailyOverviewByTeam.id, {
        singleCount: dailyOverviewByTeam.singleCount + Number(data.singleCount),
        autoSingleCount: dailyOverviewByTeam.autoSingleCount + Number(data.autoSingleCount),
        commentCount: dailyOverviewByTeam.commentCount + Number(data.commentCount),
        autoCommentCount: dailyOverviewByTeam.autoCommentCount + Number(data.autoCommentCount),
        groupCount: dailyOverviewByTeam.groupCount + Number(data.groupCount)
      })
    } else {
      await this.dailyOverviewModel.create({
        createTime: data.createTime,
        openId: data.openId,
        teamId: data.teamId,
        singleCount: Number(data.singleCount),
        autoSingleCount: Number(data.autoSingleCount),
        commentCount: Number(data.commentCount),
        autoCommentCount: Number(data.autoCommentCount),
        groupCount: Number(data.groupCount)
      })
    }
  }

  async createUsageByTeam(
    data: DailyMessageStatisticEntity & {
      autoresponderId?: number
      platformAccountId: number
      isDouyinComment?: boolean
    }
  ) {
    const now = dayjs().tz('Asia/Shanghai')

    const [teamVIP, systemSetting] = await Promise.all([
      this.prisma.vip.findUnique({
        where: {
          teamId: data.teamId
        }
      }),
      this.prisma.systemDosage.findFirst()
    ])

    let residueCount = 0
    let douyinCommentNumber = 0
    let wxResidueCount = 0

    const douyinComment = (await this.cacheManager.get(`douyinComment:${data.teamId}`)) as {
      residueCount: number
    }

    const messageByTeam = await this.dailyMessageStatisticModel.aggregate([
      { $match: { createTime: data.createTime, teamId: data.teamId } },
      {
        $group: {
          _id: null,
          singleCount: {
            $sum: { $ifNull: ['$singleCount', 0] }
          },
          autoSingleCount: {
            $sum: { $ifNull: ['$autoSingleCount', 0] }
          },
          commentCount: {
            $sum: { $ifNull: ['$commentCount', 0] }
          },
          autoCommentCount: {
            $sum: { $ifNull: ['$autoCommentCount', 0] }
          }
        }
      }
    ])

    this.tlsManageService.putLogs({
      logData: `概览数据更新成功`,
      logLevel: 'info',
      requestUri: `createTime: ${data.teamId}, createTime: ${data.createTime}`,
      jobStatus: 'createUsageByTeam'
    })

    let singleCount = 0
    let commentCount = 0
    let autoSingleCount = 0
    let autoCommentCount = 0

    if (messageByTeam.length > 0) {
      ;({ singleCount } = messageByTeam[0])
      ;({ commentCount } = messageByTeam[0])
      ;({ autoSingleCount } = messageByTeam[0])
      ;({ autoCommentCount } = messageByTeam[0])
    }

    this.tlsManageService.putLogs({
      logData: `概览数据更新成功`,
      logLevel: 'info',
      requestUri: `createUsageByTeam-singleTotal: ${singleCount}-commentTotal: ${commentCount}-autoSingleTotal: ${autoSingleCount}-autoCommentTotal: ${autoCommentCount}`,
      jobStatus: 'createUsageByTeam'
    })

    if (
      teamVIP &&
      teamVIP.expirationTime &&
      teamVIP.expirationTime.getTime() > new Date().getTime()
    ) {
      residueCount =
        teamVIP.messageLimit - (singleCount + commentCount + autoSingleCount + autoCommentCount)
      if (douyinComment) {
        douyinCommentNumber = douyinComment.residueCount
      } else {
        douyinCommentNumber = teamVIP.douyinCommentNumberLimit
      }

      if (data.isDouyinComment === true) {
        douyinCommentNumber -= 1
      }
    } else {
      residueCount =
        systemSetting.standardMessageLimit -
        (singleCount + commentCount + autoSingleCount + autoCommentCount)
      if (data.isDouyinComment === true) {
        douyinCommentNumber = -1
      }
    }

    let today8AM = now.startOf('day').hour(8)

    if (now.hour() >= 8) {
      today8AM = today8AM.add(1, 'day')
    }

    this.tlsManageService.putLogs({
      logData: `概览数据更新成功`,
      logLevel: 'info',
      requestUri: `teamId: ${data.teamId}, residueCount: ${residueCount}`,
      jobStatus: 'createUsageByTeam'
    })

    const ttlMilliseconds = today8AM.diff(now, 'millisecond')

    await this.cacheManager.set(
      `overview:${data.teamId}`,
      {
        residueCount: Number(residueCount)
      },
      ttlMilliseconds
    )

    await this.cacheManager.set(
      `douyinComment:${data.teamId}`,
      {
        residueCount: Number(douyinCommentNumber)
      },
      ttlMilliseconds
    )

    if (data.platformType === 'wechat') {
      const wxResidueKey = `wxResidue:${data.openId}`
      const wxResidue = (await this.cacheManager.get(wxResidueKey)) as { residueCount: number }

      this.logger.log(wxResidueKey, wxResidueKey)
      this.logger.log(wxResidue, `wxResidue:${data.openId}`)

      if (wxResidue) {
        this.logger.log('haveData')
        wxResidueCount = wxResidue.residueCount - 1
      } else {
        this.logger.log('noData')
        wxResidueCount = teamVIP.wechatMessageLimit
      }

      this.logger.log(wxResidueCount, 'wxResidueCount')

      await this.cacheManager.set(
        wxResidueKey,
        {
          residueCount: Number(wxResidueCount)
        },
        ttlMilliseconds
      )
    }
  }
}
