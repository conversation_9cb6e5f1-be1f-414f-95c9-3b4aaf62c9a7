import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import {
  CityResponseDTO,
  LastAppVersionRequestDTO,
  LastAppVersionResponseDTO,
  LastAppVersionWebSiteResponseDTO,
  PublicCreateUploadUrlRequestBodyDTO,
  PublicCreateUploadUrlResponseDTO,
  PublicResponseNotifyDTO
} from './public.dto'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { PublicService } from './public.service'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { wait } from '../../common/utils'

@Controller('public')
@ApiTags('公共的')
export class PublicController {
  constructor(private readonly publicService: PublicService) {}

  @Post('upload-urls')
  @ApiOperation({ summary: '创建上传文件URL' })
  @ApiOkResponse({ type: PublicCreateUploadUrlResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async createUploadUrl(@Body() body: PublicCreateUploadUrlRequestBodyDTO) {
    return this.publicService.createUploadUrl(body)
  }

  @Get('notices')
  @ApiOperation({ summary: '获取系统公告' })
  @ApiOkResponse({ type: PublicResponseNotifyDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getSystemNotice() {
    return this.publicService.getSystemNotice()
  }

  @Get('regions')
  @ApiOperation({ summary: '视频号region信息接口' })
  regions() {
    const regions = [
      {
        regionId: '110000',
        regionName: '北京市'
      },
      {
        regionId: '310000',
        regionName: '上海市'
      },
      {
        regionId: '320000',
        regionName: '江苏省'
      },
      {
        regionId: '440000',
        regionName: '广东省'
      },
      {
        regionId: '500000',
        regionName: '重庆市'
      },
      {
        regionId: '510000',
        regionName: '四川省'
      },
      {
        regionId: '330000',
        regionName: '浙江省'
      },
      {
        regionId: '340000',
        regionName: '安徽省'
      },
      {
        regionId: '130000',
        regionName: '河北省'
      },
      {
        regionId: '430000',
        regionName: '湖南省'
      },
      {
        regionId: '420000',
        regionName: '湖北省'
      },
      {
        regionId: '120000',
        regionName: '天津市'
      },
      {
        regionId: '610000',
        regionName: '陕西省'
      },
      {
        regionId: '410000',
        regionName: '河南省'
      }
    ]

    return regions
  }

  @Get('areas')
  @ApiOperation({ summary: '获取地区列表树' })
  getAreas() {
    return this.publicService.getAreas()
  }

  @Get('provice')
  @ApiOkResponse({ type: CityResponseDTO })
  @ApiOperation({ summary: '获取省份' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async provice() {
    return this.publicService.getProvice()
  }

  @Get('city')
  @ApiOkResponse({ type: CityResponseDTO })
  @ApiOperation({ summary: '获取城市' })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'proviceId',
    required: true,
    type: Number,
    description: '省份id'
  })
  async city(@Query('proviceId') proviceId: number) {
    return this.publicService.getCity(proviceId)
  }

  @Get('last-app-version')
  @ApiOperation({ summary: '获取最新App版本' })
  @ApiOkResponse({ type: LastAppVersionResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  getLastAppVersion(@Query() query: LastAppVersionRequestDTO) {
    return this.publicService.getLastAppVersions(query)
  }

  @Get('last-app-version-website')
  @ApiOperation({ summary: '获取最新App版本WebSite' })
  @ApiOkResponse({ type: LastAppVersionWebSiteResponseDTO })
  getLastAppVersionWebSite() {
    return this.publicService.getLastAppVersionWebSite()
  }

  @Post('peport')
  @ApiOperation({ summary: '举报' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async report() {
    wait(1000)
  }
}
