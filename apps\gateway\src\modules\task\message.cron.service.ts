import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Queue, Worker } from 'bullmq'
import { <PERSON>ron } from '@nestjs/schedule'
import {
  DailyMessageStatisticEntity,
  WorkCommentEntity,
  PersonalChatMessageEntity
} from '@qdy/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { PrismaService } from '@qdy/mysql'
import dayjs from 'dayjs'
import { Platform } from '@qdy/utils'
import { TlsManageService } from '@qdy/common'

@Injectable()
export class MessageCronService implements OnModuleInit {
  private readonly logger = new Logger(MessageCronService.name)

  overViewQueue: Queue

  overViewWorker: Worker

  constructor(
    private readonly prisma: PrismaService,
    @InjectModel(DailyMessageStatisticEntity.name)
    private dailyMessageStatisticModel: Model<DailyMessageStatisticEntity>,
    @InjectModel(WorkCommentEntity.name) private workCommontModel: Model<WorkCommentEntity>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    private readonly tlsManageService: TlsManageService
  ) {}

  async onModuleInit() {
    this.overViewQueue = new Queue('messageStatistic-init', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })

    this.overViewWorker = new Worker(
      'messageStatistic-init',
      async (job) => {
        const { data } = job.data
        await this.onUpdateMessageStatisticCron(data)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    this.logger.log('MessageCronService init')
  }

  /**
   * 私信数量统计
   * 每天凌晨1点
   */
  @Cron('0 0 1 * * *', {
    name: 'MessageCron',
    timeZone: 'Asia/Shanghai'
  })
  async MessageCron() {
    const platformAccounts = await this.prisma.platformAccount.findMany({
      select: {
        openId: true,
        platform: true,
        teamId: true
      }
    })

    const yesterday = dayjs().format('YYYY-MM-DD')
    for (let i = 0; i < platformAccounts.length; i++) {
      const { teamId, openId, platform } = platformAccounts[i]
      let platformType = ''
      switch (platform) {
        case Platform.Douyin:
          platformType = 'douyin'
          break
        case Platform.Wechat:
          platformType = 'wechat'
          break
        case Platform.Weibo:
          platformType = 'weibo'
          break
        case Platform.Kuaishou:
          platformType = 'kuaishou'
          break
      }
      this.overViewQueue.add(
        'messageStatistic-init',
        {
          data: {
            teamId,
            openId,
            platformType,
            yesterday
          }
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `messageStatistic-init-move-${openId}`
        }
      )
    }
  }

  async onUpdateMessageStatisticCron({ teamId, openId, platformType, yesterday }) {
    if (!openId || !yesterday) {
      return
    }

    const startDate = dayjs(yesterday).toDate()
    startDate.setHours(0, 0, 0, 0)
    const zeroTimestamp = startDate.getTime()

    try {
      const yesterdayFormat = yesterday

      const messageResult = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            $and: [
              {
                event: 'im_receive_msg',
                openId
              },
              {
                createTime: {
                  $gte: zeroTimestamp
                }
              }
            ]
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      const count = messageResult.length > 0 ? messageResult[0].totalCount : 0

      if (count > 0) {
        const result = await this.dailyMessageStatisticModel.findOne({
          createTime: yesterdayFormat,
          teamId,
          openId
        })

        if (!result) {
          await this.dailyMessageStatisticModel.create({
            platformType,
            teamId,
            openId,
            createTime: yesterdayFormat,
            receiveMessageCount: count
          })
        } else {
          await this.dailyMessageStatisticModel.findByIdAndUpdate(result.id, {
            receiveMessageCount: count
          })
        }

        const platfromAccount = await this.prisma.platformAccount.findUnique({
          where: {
            openId
          }
        })

        if (platfromAccount) {
          await this.prisma.platformAccount.update({
            where: {
              openId
            },
            data: {
              receiveMessage: platfromAccount.receiveMessage + count
            }
          })
        }

        await this.onUpdatePeopleOverViewCron({ teamId, openId, platformType, yesterday })
        this.tlsManageService.putLogs({
          logData: `私信统计数据更新成功 teamId: ${teamId} 账号openId：${openId} 数量：${count} 时间：${yesterdayFormat}`,
          logLevel: 'info',
          requestUri: 'onUpdateMessageStatisticCron',
          jobStatus: 'message-cron'
        })
      }
    } catch (error) {
      this.logger.error('私信统计数据更新失败', error)
    }
  }

  /**
   * 账号接收信息人数汇总
   * @param param0
   * @returns
   */
  async onUpdatePeopleOverViewCron({ teamId, openId, platformType, yesterday }) {
    if (!teamId || !yesterday) {
      return
    }

    try {
      let commentPeopleCount = 0
      let singlePeopleCount = 0

      const yesterdayFormat = yesterday
      const startDate = dayjs(yesterday).toDate()
      startDate.setHours(0, 0, 0, 0)
      const zeroTimestamp = startDate.getTime()

      // 获取评论人数
      const commentPeople = await this.workCommontModel.aggregate([
        {
          $match: {
            $and: [
              { openId },
              {
                createTime: {
                  $gte: zeroTimestamp
                }
              }
            ]
          }
        },
        {
          $group: {
            _id: '$fromUserId'
          }
        },
        {
          $count: 'totalCount'
        }
      ])
      // 获取抖音私信人数
      const singlePeople = await this.personalChatMessageModel.aggregate([
        {
          $match: {
            $and: [
              {
                event: 'im_receive_msg',
                platformType,
                openId
              },
              {
                createTime: {
                  $gte: zeroTimestamp
                }
              }
            ]
          }
        },
        {
          $group: {
            _id: '$fromUserId',
            count: { $sum: 1 }
          }
        },
        {
          $count: 'totalCount'
        }
      ])

      commentPeopleCount = commentPeople.length > 0 ? commentPeople[0].totalCount : 0
      singlePeopleCount = singlePeople.length > 0 ? singlePeople[0].totalCount : 0

      const dailyMessageStatistic = await this.dailyMessageStatisticModel.findOne({
        createTime: yesterdayFormat,
        openId,
        teamId
      })

      if (!dailyMessageStatistic) {
        await this.dailyMessageStatisticModel.create({
          platformType,
          createTime: yesterdayFormat,
          openId,
          teamId,
          commentPeopleCount,
          singlePeopleCount
        })
      } else {
        await this.dailyMessageStatisticModel.findByIdAndUpdate(dailyMessageStatistic.id, {
          commentPeopleCount,
          singlePeopleCount
        })
      }

      if (commentPeopleCount > 0 || singlePeopleCount > 0) {
        this.tlsManageService.putLogs({
          logData: `账号人数概览数据更新成功 teamId: ${teamId}, openId: ${openId} 时间：${yesterdayFormat} 评论人数：${commentPeopleCount} 私信人数：${singlePeopleCount}`,
          logLevel: 'info',
          requestUri: 'onUpdatePeopleOverViewCron',
          jobStatus: 'message-cron'
        })
      }
    } catch (error) {
      this.logger.error('账号人数概览数据更新失败', error)
    }
  }
}
