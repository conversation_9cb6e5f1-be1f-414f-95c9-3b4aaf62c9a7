-- CreateTable
CREATE TABLE `PrivateMessageUser` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `openId` VARCHAR(191) NOT NULL,
    `platform` INTEGER NOT NULL,
    `phone` VARCHAR(191) NOT NULL DEFAULT '',
    `wechat` VARCHAR(191) NOT NULL DEFAULT '',
    `provice` INTEGER NOT NULL DEFAULT 0,
    `city` INTEGER NOT NULL DEFAULT 0,
    `remark` VARCHAR(191) NOT NULL DEFAULT '',
    `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `PrivateMessageUser_openId_key`(`openId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `City` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `parentId` INTEGER NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `type` INTEGER NOT NULL,

    INDEX `City_parentId_idx`(`parentId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PrivateMessageUserLabel` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(191) NOT NULL DEFAULT '',
    `teamId` INTEGER NOT NULL,
    `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `PrivateMessageUserLabel_teamId_idx`(`teamId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `_PrivateMessageUserToPrivateMessageUserLabel` (
    `A` INTEGER NOT NULL,
    `B` INTEGER NOT NULL,

    UNIQUE INDEX `_PrivateMessageUserToPrivateMessageUserLabel_AB_unique`(`A`, `B`),
    INDEX `_PrivateMessageUserToPrivateMessageUserLabel_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `_PrivateMessageUserToPrivateMessageUserLabel` ADD CONSTRAINT `_PrivateMessageUserToPrivateMessageUserLabel_A_fkey` FOREIGN KEY (`A`) REFERENCES `PrivateMessageUser`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_PrivateMessageUserToPrivateMessageUserLabel` ADD CONSTRAINT `_PrivateMessageUserToPrivateMessageUserLabel_B_fkey` FOREIGN KEY (`B`) REFERENCES `PrivateMessageUserLabel`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
