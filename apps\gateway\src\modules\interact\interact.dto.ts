import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length
} from 'class-validator'

export class InteractGroup {
  @ApiResponseProperty({
    type: Number,
    example: 22
  })
  teamId: number

  @ApiResponseProperty({
    type: Number,
    example: 22
  })
  platformAccountId: number

  @ApiResponseProperty({
    type: String,
    example: 'KXLD8811'
  })
  groupId: string

  @ApiResponseProperty({
    type: Number,
    example: 'KXLD8811'
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '天才之家'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'https://example.com/avatar.jpg'
  })
  avatar: string

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  isGreeting: boolean
}

export class InteractGroupResponesDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [InteractGroup],
    example: [
      {
        id: 1,
        name: '天才之家',
        avatar: 'https://example.com/avatar.jpg'
      }
    ]
  })
  data: InteractGroup[]
}

enum InteractGroupDoorsill {
  All = 'all',
  Active = 'active'
}

enum InteractJoinGroupLevel {
  Not = 0,
  One = 1,
  Three = 3,
  Five = 5,
  Nine = 9,
  Twelve = 12,
  Fifteen = 15,
  Seventeen = 17,
  Twenty = 20
}

enum InteractJoinGroupAttention {
  Not = 0,
  One = 1,
  Seven = 7,
  Thirty = 30,
  Sixty = 60
}

enum InteractGroupType {
  Anchor = 2,
  Myriad = 5
}

export class InteractGroupCreateDTO {
  @ApiProperty({
    type: String,
    example: '天才之家',
    required: true
  })
  @IsString()
  @Length(2, 20)
  name: string

  @ApiProperty({
    type: String,
    example: '@baihq==',
    required: true
  })
  @IsString()
  avatar: string

  @ApiProperty({
    type: String,
    example: '交流群',
    required: true
  })
  @IsString()
  @Length(1, 300)
  description: string

  @ApiProperty({
    enum: Object.values(InteractGroupDoorsill),
    type: String,
    example: 'all',
    required: true,
    description: 'all: 所有人, active: 活跃用户'
  })
  @IsEnum(InteractGroupDoorsill)
  doorsill: InteractGroupDoorsill

  @ApiProperty({
    type: Boolean,
    example: false,
    required: true
  })
  @IsBoolean()
  allowInvitations: boolean

  @ApiProperty({
    enum: Object.values(InteractJoinGroupLevel).filter((v) => typeof v === 'number'),
    type: Number,
    example: 0,
    required: true,
    description: '0: 不限制, 1: 1级, 3: 3级, 5: 5级, 9: 9级, 12: 12级, 15: 15级, 17: 17级, 20: 20级'
  })
  joinGroupLevel: number

  @ApiProperty({
    enumName: 'InteractJoinGroupAttention',
    enum: Object.values(InteractJoinGroupAttention).filter((v) => typeof v === 'number'),
    type: Number,
    example: 0,
    required: true,
    description: '0: 不限制, 1: 1天, 7: 7天, 30: 30天, 60: 60天'
  })
  joinAttention: InteractJoinGroupAttention

  @ApiProperty({
    type: Boolean,
    example: false,
    required: true,
    description: '是否开启作品同步'
  })
  @IsBoolean()
  opusAsync: boolean

  @ApiProperty({
    type: Boolean,
    example: false,
    required: true,
    description: '是否同步直播'
  })
  @IsBoolean()
  liveAsync: boolean

  @ApiProperty({
    type: Boolean,
    example: false,
    required: true,
    description: '是否开启加群验证'
  })
  @IsBoolean()
  joinCheck: boolean

  @ApiProperty({
    type: Boolean,
    example: false,
    required: true,
    description: '是否显示首页'
  })
  @IsBoolean()
  showHomePage: boolean

  @ApiProperty({
    type: Number,
    enum: Object.values(InteractGroupType).filter((v) => typeof v === 'number'),
    example: 0,
    required: true,
    description: '群类型 2: 主播群, 5: 万粉能群'
  })
  groupType: number
}

export class InteractSessionConfig {
  @ApiResponseProperty({
    type: String,
    example: 1
  })
  id: string

  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  top: boolean

  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  mute: boolean
}

export class InteractResponseSessionConfigsDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [InteractSessionConfig],
    example: [
      {
        id: 1,
        top: true,
        mute: true
      }
    ]
  })
  data: InteractSessionConfig[]
}

export class InteractRequestSessionConfigDTO {
  @ApiProperty({
    type: Boolean,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  top?: boolean

  @ApiProperty({
    type: Boolean,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  mute?: boolean

  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '会话ID不能为空' })
  sessionId: string
}

export enum InteractResponseMessageType {
  Text = 'text',

  Image = 'image',

  Video = 'video',

  Emoji = 'emoji',

  Card = 'retain_consult_card',

  Other = 'other'
}

export class InteractMessage {
  @ApiResponseProperty({
    type: String,
    example: InteractResponseMessageType.Text,
    enum: Object.values(InteractResponseMessageType)
  })
  messageType: InteractResponseMessageType

  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  fromUserId: string

  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  toUserId: string

  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  conversationShortId: string

  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  serverMessageId: string

  @ApiResponseProperty({
    type: String,
    example: '张三'
  })
  fromName: string

  @ApiResponseProperty({
    type: String,
    example: '李四'
  })
  toName: string

  @ApiResponseProperty({
    type: String,
    example: 'https://example.com/avatar.jpg'
  })
  fromAvatar: string

  @ApiResponseProperty({
    type: String,
    example: 'https://example.com/avatar.jpg'
  })
  toAvatar: string

  @ApiResponseProperty({
    type: Date,
    example: new Date()
  })
  createTime: Date

  @ApiResponseProperty({
    type: String,
    example: '你好'
  })
  content: string
}

export class InteractResponseHistoryMessagesDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [InteractMessage],
    example: [
      {
        messageType: 'text',
        fromUserId: '13311',
        toUserId: '13311',
        conversationShortId: '13311',
        serverMessageId: '13311',
        fromName: '张三',
        toName: '李四',
        fromAvatar: 'https://example.com/avatar.jpg',
        toAvatar: 'https://example.com/avatar.jpg',
        createTime: new Date(),
        content: '你好'
      }
    ]
  })
  data: InteractMessage[]
}

export class InteractComment {
  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  commentId: string

  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  commentUserId: string

  @ApiResponseProperty({
    type: String,
    example: '这在哪里买呀?'
  })
  content: string

  @ApiResponseProperty({
    type: Number,
    example: 32
  })
  diggCount: number

  @ApiResponseProperty({
    type: Number,
    example: 32
  })
  replyCommentTotal: number

  @ApiResponseProperty({
    type: String,
    example: '@9VxT0uuFWsE7M3Koc4=='
  })
  replyToCommentId: string

  @ApiResponseProperty({
    type: String,
    example: '@9VxT0uuFWsE7M3Koc4=='
  })
  replyToItemId: string

  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  atUserId: string

  @ApiResponseProperty({
    type: String,
    example: '13311'
  })
  avatar: string

  @ApiResponseProperty({
    type: String,
    example: '张三'
  })
  name: string

  @ApiResponseProperty({
    type: Date,
    example: new Date()
  })
  createTime: Date

  @ApiResponseProperty({
    type: String,
    example: 'ALOP123'
  })
  parentId: string
}

export class InteractResponseHistoryCommentDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [InteractComment],
    example: [
      {
        commentId: '13311',
        commentUserId: '13311',
        content: '这在哪里买呀?',
        diggCount: 32,
        replyCommentTotal: 32,
        replyToCommentId: '@9VxT0uuFWsE7M3Koc4==',
        replyToItemId: '@9VxT0uuFWsE7M3Koc4==',
        atUserId: '13311',
        avatar: '@9VxT0uuFWsE7M3Koc4==',
        name: '张三',
        createTime: new Date(),
        parentId: 'ALOP123'
      }
    ]
  })
  data: InteractComment[]
}

export enum InteractRequestSendType {
  Single = 'single',
  Group = 'group'
}

export class InteractRequestSendMessage {
  @ApiProperty({
    type: String,
    example: '13311<文本/图片id/留资卡片id>',
    required: true
  })
  @IsNotEmpty({ message: '消息内容不能为空' })
  @IsString()
  content: string

  @ApiProperty({
    enum: InteractRequestSendType,
    example: InteractRequestSendType.Single,
    required: true,
    description: '消息类型'
  })
  @IsEnum(InteractRequestSendType)
  sendType: InteractRequestSendType

  @ApiProperty({
    type: InteractResponseMessageType,
    example: InteractResponseMessageType.Text,
    enum: [
      InteractResponseMessageType.Text,
      InteractResponseMessageType.Image,
      InteractResponseMessageType.Card
    ],
    required: true
  })
  @IsEnum(InteractResponseMessageType)
  @IsNotEmpty({ message: '消息类型不能为空' })
  messageType:
    | InteractResponseMessageType.Image
    | InteractResponseMessageType.Text
    | InteractResponseMessageType.Card

  @ApiProperty({
    type: Number,
    example: '13311',
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '账号ID不能为空' })
  platformAccountId: number

  @ApiProperty({
    type: String,
    example: '13311 <最新一条消息ID> 单聊必须传',
    required: false
  })
  @IsString()
  messageId: string

  @ApiProperty({
    type: String,
    example: '单聊 13311 <最新一条消息的ConversationId>  群聊 group_id',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: 'conversationId不能为空' })
  conversationId: string

  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '会话ID不能为空' })
  toUserId: string
}

export class InteractRequestCommentMessage {
  @ApiProperty({
    type: String,
    example: '13311<文本>',
    required: true
  })
  @IsString()
  @Length(1, 100, { message: '评论内容长度最大 100 个字符' })
  @IsNotEmpty({ message: '消息内容不能为空' })
  content: string

  @ApiProperty({
    type: Number,
    example: '13311',
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '账号ID不能为空' })
  platformAccountId: number

  @ApiProperty({
    type: String,
    example: '13311',
    required: false
  })
  @IsString()
  @IsOptional()
  commentId?: string

  @ApiProperty({
    type: String,
    example: '13311 <作品id>',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '会话ID不能为空' })
  itemId: string
}

export class InteractRequestTopComment {
  @ApiProperty({
    type: Boolean,
    example: true,
    required: true
  })
  @IsNotEmpty({ message: '是否置顶不能为空' })
  @IsBoolean()
  top: boolean

  @ApiProperty({
    type: Number,
    example: '13311',
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '账号ID不能为空' })
  platformAccountId: number

  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '评论id不能为空' })
  commentId: string

  @ApiProperty({
    type: String,
    example: '13311 <作品id>',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '会话ID不能为空' })
  sessionId: string
}

export enum SpeechType {
  Team = 0,
  User = 1
}

export class InteractRequestSpeech {
  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @Length(1, 1000, { message: '话术长度不合法' })
  content: string

  @ApiProperty({
    enum: Object.values(SpeechType).filter((v) => typeof v === 'number'),
    type: Number,
    example: 0,
    required: true,
    description: '0: 团队,1: 个人'
  })
  @IsNumber()
  speechType: number

  @ApiProperty({
    type: Number,
    example: 0,
    required: false,
    description: '话术分类ID'
  })
  @IsNumber()
  @IsOptional()
  speechCategoryId: number
}

export class InteractRequestGetHistory {
  @ApiProperty({
    type: Number,
    example: 1633036800000,
    required: false
  })
  @IsNumber()
  @IsOptional()
  createTime?: number
}

export class InteractRequestSpeechCategory {
  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @Length(1, 1000, { message: '话术分类长度不合法' })
  name: string
}

class UploadImageContent {
  @ApiResponseProperty({
    type: String,
    example: '@baihq=='
  })
  imageId: string
}

export class InteractResponseUploadImage extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UploadImageContent
  })
  data: UploadImageContent
}

export class InteractRequestVideoDetailDTO {
  @ApiProperty({
    type: [String],
    example: ['13311', '3122'],
    required: false
  })
  @IsArray()
  @IsOptional()
  videoIds: string[]

  @ApiProperty({
    type: [String],
    example: ['13311', '3122'],
    required: true
  })
  @IsArray()
  @IsNotEmpty({ message: '视频ItemIds不能为空' })
  itemIds: string[]
}

export enum InteractRequestAuditStatus {
  Pass = 2,
  Reject = 3
}

export class InteractRequestAuditDTO {
  @ApiProperty({
    enum: Object.values(InteractRequestAuditStatus).filter((v) => typeof v === 'number'),
    example: InteractRequestAuditStatus.Pass,
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '审核状态不能为空' })
  status: number

  @ApiProperty({
    enum: String,
    example: '8hxdhauTCMppanGnM4ltGM',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '审核ID不能为空' })
  applyId: string
}

export class WechatVideoRequestDTO {
  @ApiProperty({
    description: 'platformAccountId',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  platformAccountId: number

  @ApiProperty({
    description: '首次传空,后续传接口返回的lastBuffer',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  lastBuffer: string

  @ApiProperty({
    description: '首次传0,后续传响应结果中最后一条的id',
    type: String,
    required: true
  })
  @IsString()
  @IsOptional()
  maxId?: string
}

export class WechatVideo {
  @ApiResponseProperty({
    type: String,
    example: ''
  })
  objectId: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  wxid: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  nickname: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  headUrl: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  username: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  description: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  thumbUrl: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  objectNonceId: string

  @ApiResponseProperty({
    type: Number,
    example: ''
  })
  createTime: number

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  sessionBuffer: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  jsonData: string
}

export class WechatVideoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [WechatVideo]
  })
  data: WechatVideo[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class WechatCommentRequestDTO {
  @ApiProperty({
    description: 'platformAccountId',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  platformAccountId: number

  @ApiProperty({
    description: '视频号ID',
    type: String,
    required: true
  })
  @IsNotEmpty()
  @IsString()
  objectId: string

  @ApiProperty({
    description: '首次传空,后续传接口返回的lastBuffer',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  lastBuffer: string

  @ApiProperty({
    description: '视频号的sessionBuffer',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  sessionBuffer: string

  @ApiProperty({
    description: '视频号的objectNonceId',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  objectNonceId: string

  @ApiProperty({
    description: 'rootCommentId',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  rootCommentId?: string
}

export class WechatDeleteCommentRequestDTO {
  @ApiProperty({
    description: 'platformAccountId',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  platformAccountId: number

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  objectId: string

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  content: string

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  sessionBuffer: string

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  commentId: string

  // @ApiProperty({
  //   type: String,
  //   required: true
  // })
  // @IsOptional()
  // @IsString()
  // objectNonceId: string
}

export class WechatCommentReplyRequestDTO {
  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  wxid: string

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  content: string
}

export class WechatMessagesRequestDTO {
  @ApiProperty({
    description: 'platformAccountId',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  platformAccountId: number

  @ApiProperty({
    description: 'hello',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  content: string

  @ApiProperty({
    description: 'https://www.heelo.png',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  imgUrl?: string

  @ApiProperty({
    description: 'toUserName',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  toUserName: string

  // @ApiProperty({
  //   description: 'myUserName',
  //   type: String,
  //   required: true
  // })
  // @IsString()
  // @IsNotEmpty()
  // myUserName: string

  @ApiProperty({
    description: 'msgSessionId',
    type: String,
    required: true
  })
  @IsString()
  @IsOptional()
  sessionId?: string
}

export class KuaishouMessagesRequestDTO {
  @ApiProperty({
    description: 'platformAccountId',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  platformAccountId: number

  @ApiProperty({
    description: '文字:hello/图片:https://www.heelo.png',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  content: string

  @ApiProperty({
    type: String,
    description: '文本: text, 图片: image, 卡片: retain_consult_card',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '消息类型不能为空' })
  messageType: string

  @ApiProperty({
    description: '图片宽度',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  width?: number

  @ApiProperty({
    description: '图片高度',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  height?: number

  @ApiProperty({
    description: '图片大小',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  contentLenght?: number

  @ApiProperty({
    description: 'toUserId',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  toUserId: string

  @ApiProperty({
    description: '接收人昵称',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  toName: string

  @ApiProperty({
    description: '接收人头像',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  toAvatar: string
}

export class XiaohongshuMessagesRequestDTO {
  @ApiProperty({
    description: 'platformAccountId',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  platformAccountId: number

  @ApiProperty({
    description: '文字:hello/图片:https://www.heelo.png',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  content: string

  @ApiProperty({
    type: String,
    description:
      '文本: text, 图片: image, 卡片: retain_consult_card, 名片: consult_card, 交易卡: trade_business_card 落地页: common',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '消息类型不能为空' })
  messageType: string

  @ApiProperty({
    description: '图片宽度',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  width?: number

  @ApiProperty({
    description: '图片高度',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  height?: number

  @ApiProperty({
    description: '图片大小',
    example: 1024,
    required: false
  })
  @IsOptional()
  @IsNumber()
  contentLenght?: number

  @ApiProperty({
    description: 'toUserId',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  toUserId: string

  @ApiProperty({
    description: '接收人昵称',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  toName: string

  @ApiProperty({
    description: '接收人头像',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  toAvatar: string

  @ApiProperty({
    description: '标题',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  title: string

  @ApiProperty({
    description: '副标题',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  subTitle: string

  @ApiProperty({
    description: '跳转链接类型',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  linkPlatform: string

  @ApiProperty({
    description: '头像',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  image: string

  @ApiProperty({
    description: '作品封面',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  cover: string

  @ApiProperty({
    description: '笔记标题',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  noteTitle: string

  @ApiProperty({
    description: '评论内容',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  commentContent: string

  @ApiProperty({
    description: '小红书名片类型',
    type: Number,
    required: false
  })
  @IsNumber()
  @IsOptional()
  socialType: number

  @ApiProperty({
    description: '评论id',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  commentId: string

  @ApiProperty({
    description: '落地页url',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  link: string

  @ApiProperty({
    description: '落地页描述',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  desc: string
}

export class WeiboMessagesRequestDTO {
  @ApiProperty({
    description: 'platformAccountId',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  platformAccountId: number

  @ApiProperty({
    description: '文字:hello/图片:https://www.heelo.png',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  content: string

  @ApiProperty({
    type: String,
    description: '文本: text',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '消息类型不能为空' })
  messageType: string

  @ApiProperty({
    description: 'toUserId',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  toUserId: string

  @ApiProperty({
    description: '接收人昵称',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  toName: string

  @ApiProperty({
    description: '接收人头像',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  toAvatar: string
}

export class AuthorContact {
  @ApiResponseProperty({
    type: String,
    example: '评论方username'
  })
  username: string

  @ApiResponseProperty({
    type: String,
    example: '评论方昵称'
  })
  nickname: string

  @ApiResponseProperty({
    type: String,
    example: '评论方头像'
  })
  headUrl: string
}

export class IpRegionInfo {
  @ApiResponseProperty({
    type: Array<string>,
    example: ''
  })
  regionText: Array<string>
}

export class CommentInfo {
  @ApiResponseProperty({
    type: Number,
    example: '评论id'
  })
  commentId: number

  @ApiResponseProperty({
    type: String,
    example: '评论方username'
  })
  username: string

  @ApiResponseProperty({
    type: String,
    example: '昵称'
  })
  nickname: string

  @ApiResponseProperty({
    type: String,
    example: '评论内容'
  })
  content: string

  @ApiResponseProperty({
    type: Number,
    example: '回复评论ID'
  })
  replyCommentId: number

  @ApiResponseProperty({
    type: String,
    example: '头像'
  })
  headUrl: string

  @ApiResponseProperty({
    type: Number,
    example: '评论时间'
  })
  createtime: number

  @ApiResponseProperty({
    type: Number,
    example: '可展开的评论数'
  })
  expandCommentCount: number

  @ApiResponseProperty({
    type: Number,
    example: '点赞数'
  })
  likeCount: number

  @ApiResponseProperty({
    type: String,
    example: '回复评论内容'
  })
  replyContent: string

  @ApiResponseProperty({
    type: Array<string>,
    example: ''
  })
  levelTwoComment: Array<string>

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  reportJson: string

  @ApiResponseProperty({
    type: Number,
    example: '评论类型'
  })
  contentType: number

  @ApiResponseProperty({
    type: Number,
    example: ''
  })
  continueFlag: number

  @ApiResponseProperty({
    type: Number,
    example: ''
  })
  upContinueFlag: number

  @ApiResponseProperty({
    type: AuthorContact,
    example: '评论者信息'
  })
  authorContact: AuthorContact

  @ApiResponseProperty({
    type: IpRegionInfo,
    example: '地区信息'
  })
  ipRegionInfo: IpRegionInfo
}

export class WechatVideoCommentResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [CommentInfo]
  })
  data: CommentInfo[]

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  lastBuffer: string
}

export class InteractRequestCommentMessageWeChatDTO {
  @ApiProperty({
    type: String,
    example: '13311<文本>',
    required: true
  })
  @IsString()
  @Length(1, 100, { message: '评论内容长度最大 100 个字符' })
  @IsNotEmpty({ message: '消息内容不能为空' })
  content: string

  @ApiProperty({
    type: Number,
    example: '13311',
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '账号ID不能为空' })
  platformAccountId: number

  @ApiProperty({
    type: String,
    example: '13311',
    required: false
  })
  @IsString()
  @IsOptional()
  replyUserName?: string

  @ApiProperty({
    type: String,
    example: '13311',
    required: false
  })
  @IsString()
  @IsOptional()
  refCommentId?: string

  @ApiProperty({
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  rootCommentId?: string

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  objectId: string

  @ApiProperty({
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  objectNonceId?: string

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  sessionBuffer: string
}

export class RecallMessageRequestDTO {
  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: 'uniqueId不能为空' })
  uniqueId: string

  @ApiProperty({
    type: Number,
    example: '13311',
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '账号ID不能为空' })
  platformAccountId: number
}

export class MessageUserLabel {
  @ApiResponseProperty({
    type: Number,
    example: 'id'
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'title'
  })
  title: string
}

export class MessageUserResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 'id'
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: 'openId'
  })
  openId: string

  @ApiResponseProperty({
    type: Number,
    example: 'platform'
  })
  platform: number

  @ApiResponseProperty({
    type: String,
    example: '手机号码'
  })
  phone: string

  @ApiResponseProperty({
    type: String,
    example: '微信号'
  })
  wechat: string

  @ApiResponseProperty({
    type: Number,
    example: '省份id'
  })
  province: number

  @ApiResponseProperty({
    type: Number,
    example: '城市id'
  })
  city: number

  @ApiResponseProperty({
    type: String,
    example: '备注'
  })
  remark: string

  @ApiResponseProperty({
    type: [MessageUserLabel],
    example: [
      {
        id: 1,
        title: '张三'
      }
    ]
  })
  labels: MessageUserLabel[]
}

export class MessageUserRequestDTO {
  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '账号openid' })
  openId: string

  @ApiProperty({
    type: String,
    example: '手机号码'
  })
  @IsString()
  phone: string

  @ApiProperty({
    type: String,
    example: '微信号'
  })
  @IsString()
  wechat: string

  @ApiProperty({
    type: Number,
    example: '13311',
    required: false
  })
  @IsNumber()
  @IsOptional()
  province?: number

  @ApiProperty({
    type: Number,
    example: '13311',
    required: false
  })
  @IsNumber()
  @IsOptional()
  city?: number

  @ApiProperty({
    type: String,
    example: '备注'
  })
  @IsString()
  remark: string

  @ApiProperty({
    type: [Number],
    example: '1',
    required: false
  })
  @IsArray()
  labels: number[]
}

export class Speechs {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '话术2'
  })
  content: string

  @ApiResponseProperty({
    type: Number,
    example: 0
  })
  speechType: number

  @ApiResponseProperty({
    type: Number,
    example: 0
  })
  userId: number

  @ApiResponseProperty({
    type: Number,
    example: 123
  })
  teamId: number
}

export class SpeechsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [Speechs]
  })
  data: Speechs[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  total: number
}

export class SpeechCategories {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '话术分类'
  })
  name: string
}

export class SpeechCategoriesResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [SpeechCategories]
  })
  data: SpeechCategories[]
}

export class DownloadMediaRequestDTO {
  @ApiProperty({
    type: Number,
    example: 1,
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '账号id不能为空' })
  platformAccountId: number

  @ApiProperty({
    type: String,
    example: '手机号码',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: 'xml内容不能为空' })
  xml: string

  @ApiProperty({
    type: String,
    example: '信息类型(image图片,video视频)',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  messageType: string

  @ApiProperty({
    type: String,
    example: '信息唯一id',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  uniqueId: string
}

export class UserMessageReadRecordRequestDTO {
  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '消息唯一id不能为空' })
  uniqueId: string

  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '账号openid不能为空' })
  openId: string

  @ApiProperty({
    type: String,
    example: '13311',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '账号sessionId不能为空' })
  sessionId: string

  @ApiProperty({
    type: Number,
    example: 1742353279774,
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '消息时间戳不能为空' })
  messageTime: number
}

export class UserMessageReadRecord {
  @ApiResponseProperty({
    type: String,
    example: '唯一id'
  })
  uniqueId: string

  @ApiResponseProperty({
    type: String,
    example: '账号openId'
  })
  openId: string

  @ApiResponseProperty({
    type: String,
    example: '会话id'
  })
  sessionId: string

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  userId: number

  @ApiResponseProperty({
    type: Number,
    example: 1742353279774
  })
  messageTime: number
}

export class UserMessageReadRecordResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [UserMessageReadRecord]
  })
  data: UserMessageReadRecord[]
}
