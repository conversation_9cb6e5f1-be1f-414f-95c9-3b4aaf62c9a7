import { Body, Controller, Get, Param, Patch, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import {
  ChannelPasswordUpdateRequestDTO,
  ChannelRequestBodyDTO,
  ChannelRequestUpdateDTO,
  ChannelResponseDTO,
  ChannelStatusRequestDTO
} from './channel.dto'
import { ChannelService } from './channel.service'

@Controller('channels')
@ApiTags('渠道管理')
export class ChannelController {
  constructor(private readonly channelService: ChannelService) {}

  @Post()
  @ApiOperation({ summary: '创建渠道' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async createChannel(@Body() data: ChannelRequestBodyDTO) {
    const response = await this.channelService.createChannel(data)
    return response
  }

  @Get()
  @ApiOperation({ summary: '获取渠道列表' })
  @ApiOkResponse({ description: '操作成功', type: ChannelResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码 <默认 1>'
  })
  @ApiQuery({
    name: 'size',
    required: false,
    type: Number,
    description: '每页数量 <默认 10>'
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '渠道名称'
  })
  @ApiQuery({
    name: 'startTime',
    required: false,
    type: Number,
    description: '开始时间'
  })
  @ApiQuery({
    name: 'endTime',
    required: false,
    type: Number,
    description: '到期时间'
  })
  async getChannels(
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number,
    @Query('name', {
      transform: (value) => value || ''
    })
    name: string,
    @Query('startTime', {
      transform: (value) => value || ''
    })
    startTime: number,
    @Query('endTime', {
      transform: (value) => value || 0
    })
    endTime: number
  ) {
    const response = this.channelService.getChannels({
      page,
      size,
      name,
      startTime,
      endTime
    })

    return response
  }

  @Put(`:id`)
  @ApiOperation({ summary: '修改渠道' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async putChannel(@Param('id') id: number, @Body() data: ChannelRequestUpdateDTO) {
    const response = await this.channelService.updateChannel(id, data)
    return response
  }

  @Patch(`:id/status`)
  @ApiOperation({ summary: '修改渠道状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async putChannelStatus(@Param('id') id: number, @Body() data: ChannelStatusRequestDTO) {
    const response = this.channelService.updateChannelStatus(id, data)
    return response
  }

  @Patch(`:id/password`)
  @ApiOperation({ summary: '修改渠道密码' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async putChannelPassword(@Param('id') id: number, @Body() data: ChannelPasswordUpdateRequestDTO) {
    const response = this.channelService.updateChannelUserPassword(id, data)
    return response
  }
}
