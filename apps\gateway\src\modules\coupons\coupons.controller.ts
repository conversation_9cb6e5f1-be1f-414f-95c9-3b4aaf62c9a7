import { Body, Controller, Get, Post, Query } from '@nestjs/common'

import {
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiQuery
} from '@nestjs/swagger'

import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { ChangeCouponsRequestDTO, UserCouponsResponseDTO } from './coupons.dto'
import { UserCouponsService } from './coupons.service'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('coupons')
@ApiTags('用户侧优惠券管理')
export class UserCouponsController {
  constructor(private readonly couponsService: UserCouponsService) {}

  @Get()
  @ApiOperation({ summary: '获取优惠券列表' })
  @ApiOkResponse({ description: '操作成功', type: UserCouponsResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: Number,
    description: '状态码，0:可用，1:不可用，-1：全部 <默认 -1>'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '单页数量 <默认 10>' })
  async getCoupons(
    @Query('status') status: number,
    @Query('page') page: number,
    @Query('size') size: number
  ) {
    const response = await this.couponsService.getUserCoupons(status, page, size)
    return response
  }

  @Post()
  @ApiOperation({ summary: '兑换优惠券' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async exchangeCoupons(@Body() data: ChangeCouponsRequestDTO) {
    const response = await this.couponsService.exchangeCoupons(data)
    return response
  }
}
