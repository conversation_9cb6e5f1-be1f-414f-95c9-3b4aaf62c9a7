-- CreateTable
CREATE TABLE `Autoresponder` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `content` VARCHAR(191) NULL,
    `imageId` VARCHAR(191) NULL,
    `rule` VARCHAR(191) NOT NULL,
    `keywords` VARCHAR(191) NOT NULL,
    `platformAccountIds` VARCHAR(191) NOT NULL,
    `opuserIds` VARCHAR(191) NOT NULL,
    `state` BOOLEAN NOT NULL DEFAULT true,
    `teamId` INTEGER NOT NULL,
    `ownerTeamMemberId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Greeting` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `content` VARCHAR(191) NULL,
    `imageId` VARCHAR(191) NULL,
    `platformAccountIds` VARCHAR(191) NOT NULL,
    `state` BOOLEAN NOT NULL DEFAULT true,
    `groupIds` VARCHAR(191) NOT NULL,
    `teamId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
