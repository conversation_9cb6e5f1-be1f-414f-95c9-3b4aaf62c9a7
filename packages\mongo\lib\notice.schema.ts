import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: false,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class NoticeEntity {
  @Prop({
    type: String,
    required: true
  })
  title: string

  @Prop({
    type: String,
    required: true
  })
  content: string

  @Prop({
    type: Number,
    default: () => Date.now()
  })
  createTime: number

  @Prop({
    type: Boolean
  })
  isToast: boolean
}

export const NoticeSchema: ModelDefinition = {
  name: NoticeEntity.name,
  schema: SchemaFactory.createForClass(NoticeEntity)
}

export const NoticeMongoose = MongooseModule.forFeature([NoticeSchema])
