-- CreateTable
CREATE TABLE `CardMessage` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `cardId` VARCHAR(191) NOT NULL,
    `teamId` INTEGER NOT NULL,
    `fromUserId` VARCHAR(191) NOT NULL DEFAULT '',
    `fromAvatar` VARCHAR(191) NOT NULL DEFAULT '',
    `fromName` VARCHAR(191) NOT NULL DEFAULT '',
    `toUserId` VARCHAR(191) NOT NULL DEFAULT '',
    `toAvatar` VARCHAR(191) NOT NULL DEFAULT '',
    `toName` VARCHAR(191) NOT NULL DEFAULT '',
    `name` VARCHAR(191) NOT NULL DEFAULT '',
    `phone` VARCHAR(191) NOT NULL DEFAULT '',
    `address` VARCHAR(191) NOT NULL DEFAULT '',
    `cardData` JSON NOT NULL,
    `createTime` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `CardMessage_cardId_idx`(`cardId`),
    INDEX `CardMessage_teamId_idx`(`teamId`),
    INDEX `CardMessage_toUserId_idx`(`toUserId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
