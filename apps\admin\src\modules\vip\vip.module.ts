import { Module } from '@nestjs/common'
import { VipController } from './vip.controller'
import { VipService } from './vip.service'
import { OrderManageModule } from '@qdy/common'
import { OrderEventService } from './order.event'

@Module({
  imports: [OrderManageModule],
  providers: [VipService, OrderEventService],
  controllers: [VipController],
  exports: [VipService]
})
export class VipModule {}
