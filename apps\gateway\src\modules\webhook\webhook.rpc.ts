import { Injectable, OnModuleInit } from '@nestjs/common'
import { Client, ClientGrpc } from '@nestjs/microservices'
import { GrpcOption } from './constant'
import { Socket } from '@qdy/proto'

@Injectable()
export class WebHookServiceGrpc implements OnModuleInit {
  @Client(GrpcOption)
  private readonly client: ClientGrpc

  public socketService: Socket

  onModuleInit() {
    this.socketService = this.client.getService<Socket>('Socket')
  }
}
