import { Controller, Get, Query } from '@nestjs/common'

import { ApiOperation, ApiTags, ApiOkResponse, ApiHeader, ApiQuery } from '@nestjs/swagger'
import { VipOrdersResponseDTO } from './order.dto'
import { OrderService } from './order.service'

@Controller('orders')
@ApiTags('订单管理')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get()
  @ApiOperation({ summary: '获取订单列表' })
  @ApiOkResponse({ type: VipOrdersResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'orderNo',
    required: false,
    type: String,
    description: '订单号'
  })
  @ApiQuery({
    name: 'payStartTime',
    required: false,
    type: Number,
    description: '支付开始时间'
  })
  @ApiQuery({
    name: 'payEndTime',
    required: false,
    type: Number,
    description: '支付结束时间'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码 <默认 1>'
  })
  @ApiQuery({
    name: 'size',
    required: false,
    type: Number,
    description: '每页数量 <默认 10>'
  })
  async getAdminUser(
    @Query('orderNo', {
      transform: (value) => value || ''
    })
    orderNo: string,
    @Query('payStartTime', {
      transform: (value) => value || 0
    })
    payStartTime: number,
    @Query('payEndTime', {
      transform: (value) => value || 0
    })
    payEndTime: number,
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number
  ) {
    return this.orderService.getOrders({
      page,
      size,
      orderNo,
      payStartTime,
      payEndTime
    })
  }
}
