import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import {
  CommentEntity,
  MessagesEntity,
  PersonalChatMessageEntity,
  WechatCommentEntity,
  WechatMessagesEntity,
  WorkCommentEntity
} from '@qdy/mongo'
import { WebhookEvents } from './constant'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'

@Injectable()
export class TasksService implements OnModuleInit {
  private readonly logger = new Logger(TasksService.name)

  constructor(
    @InjectModel(WechatMessagesEntity.name) private wechatMessageModel: Model<WechatMessagesEntity>,
    @InjectModel(WechatCommentEntity.name)
    private wechatCommentMessageModel: Model<WechatCommentEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(MessagesEntity.name) private messageModel: Model<MessagesEntity>,
    @InjectModel(CommentEntity.name) private commentModel: Model<CommentEntity>,
    @InjectModel(WorkCommentEntity.name)
    private workCommentModel: Model<WorkCommentEntity>
  ) {}

  async onModuleInit() {
    const lock = ((await this.cacheManager.get('task-change-transfer')) || 0) as number

    if (!lock) {
      this.cacheManager.set('task-change-transfer', 1, 1000 * 60 * 60 * 24 * 10)
      // this.syncMessageData()
      // this.syncCommentData()
      // this.syncWechatMessageData()
      // this.syncWechatCommentData()
    }

    this.logger.log('TasksService init')
  }

  async syncMessageData() {
    const messageChangeStream = this.messageModel.watch()

    messageChangeStream.on('change', async (change) => {
      if (change.operationType === 'insert') {
        let openId = change.fullDocument.fromUserId
        switch (change.fullDocument.event) {
          case WebhookEvents.IMSendMessage:
            openId = change.fullDocument.fromUserId
            break
          case WebhookEvents.CommentReply:
            openId = change.fullDocument.toUserId
            break
          case WebhookEvents.IMReceiveMessage:
            openId = change.fullDocument.toUserId
            break
          case WebhookEvents.IMGroupSendMessage:
            openId = change.fullDocument.fromUserId
            break
          case WebhookEvents.IMGroupReceiveMessage:
            openId = change.fullDocument.toUserId
            break
          case WebhookEvents.GroupFansEvent:
            openId = change.fullDocument.toUserId
            break
          case WebhookEvents.EnterGroupAuditChange:
            openId = change.fullDocument.fromUserId
            break
          default:
            return
            break
        }
        await this.personalChatMessageModel.create({
          uniqueId: change.fullDocument.uniqueId,
          platformType: 'douyin',
          event: change.fullDocument.event,
          openId,
          fromUserId: change.fullDocument.fromUserId,
          toUserId: change.fullDocument.toUserId,
          fromAvatar: change.fullDocument.fromAvatar,
          fromName: change.fullDocument.fromName,
          toAvatar: change.fullDocument.toAvatar,
          toName: change.fullDocument.toName,
          sessionId: change.fullDocument.sessionId,
          content: change.fullDocument.content,
          createTime: change.fullDocument.createTime,
          isAuto: 0,
          messageId: change.fullDocument.content.serverMessageId ?? ''
        })
      }
    })
  }

  async syncCommentData() {
    const commentChangeStream = this.commentModel.watch()

    commentChangeStream.on('change', async (change) => {
      if (change.operationType === 'insert') {
        await this.workCommentModel.create({
          uniqueId: change.fullDocument.uniqueId,
          platformType: 'douyin',
          event: change.fullDocument.event,
          fromUserId: change.fullDocument.fromUserId,
          toUserId: change.fullDocument.toUserId,
          fromName: change.fullDocument.content.name,
          fromAvatar: change.fullDocument.content.avatar,
          toAvatar: '',
          toName: '',
          sessionId: change.fullDocument.sessionId,
          openId: change.fullDocument.fromUserId,
          createTime: change.fullDocument.createTime,
          content: change.fullDocument.content,
          isAuto: 0,
          messageId: change.fullDocument.uniqueId
        })
      }
    })
  }

  async syncWechatMessageData() {
    const wechatMessageStream = this.wechatMessageModel.watch()

    wechatMessageStream.on('change', async (change) => {
      if (change.operationType === 'insert') {
        await this.personalChatMessageModel.create({
          uniqueId: change.fullDocument.newMsgId,
          platformType: 'wechat',
          event: change.fullDocument.isSender ? 'im_send_msg' : 'im_receive_msg',
          openId: change.fullDocument.wxid,
          fromUserId: change.fullDocument.fromUserId,
          toUserId: change.fullDocument.toUserId,
          fromAvatar: change.fullDocument.fromAvatar,
          fromName: change.fullDocument.fromName,
          toAvatar: change.fullDocument.toAvatar,
          toName: change.fullDocument.toName,
          sessionId: change.fullDocument.sessionId,
          content: {
            appId: change.fullDocument.appid,
            messageType:
              change.fullDocument.msgType === 1
                ? 'text'
                : change.fullDocument.msgType === 3
                  ? 'image'
                  : 'video',
            text: change.fullDocument.content,
            sessionId: change.fullDocument.newMsgId,
            isSender: change.fullDocument.isSender,
            responseUrl:
              change.fullDocument.msgType === 3
                ? change.fullDocument.imageUrl
                : change.fullDocument.msgType === 43
                  ? change.fullDocument.videoUrl
                  : ''
          },
          createTime: change.fullDocument.createTime,
          isAuto: 0,
          messageId: change.fullDocument.newMsgId
        })
      }
    })
  }

  async syncWechatCommentData() {
    const wechatCommentStream = this.wechatCommentMessageModel.watch()

    wechatCommentStream.on('change', async (change) => {
      if (change.operationType === 'insert') {
        const jsonData = JSON.parse(change.fullDocument.jsonData)
        await this.personalChatMessageModel.create({
          platformType: 'wechat',
          uniqueId: change.fullDocument.uniqueId,
          fromUserId: change.fullDocument.fromUserName,
          fromName: change.fullDocument.nickname,
          fromAvatar: change.fullDocument.headUrl,
          toUserId: change.fullDocument.toUserName,
          toName: '',
          toAvatar: '',
          sessionId: change.fullDocument.refObjectId,
          openId: change.fullDocument.wxid,
          createTime: change.fullDocument.createTime,
          isAuto: 0,
          event: 'item_comment_reply',
          content: {
            commentId: change.fullDocument.refCommentId,
            name: change.fullDocument.nickname,
            avatar: change.fullDocument.headUrl,
            mentionType: change.fullDocument.mentionType,
            content: change.fullDocument.mentionContent,
            replyToItemId: change.fullDocument.refObjectId,
            refObjectNonceId: change.fullDocument.refObjectNonceId,
            thumbUrl: change.fullDocument.thumbUrl,
            description: change.fullDocument.description,
            sessionBuffer: change.fullDocument.sessionBuffer,
            refContent: jsonData.refContent,
            replyNickname: jsonData?.replyContact?.contact?.nickname,
            replyHeadUrl: jsonData?.replyContact?.contact?.headUrl,
            refCommentId: jsonData.refCommentId,
            appId: change.fullDocument.appid
          }
        })
      }
    })
  }
}
