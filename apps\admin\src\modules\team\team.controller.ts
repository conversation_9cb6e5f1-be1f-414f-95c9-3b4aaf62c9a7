import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common'
import { TeamService } from './team.service'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  TeamResponseDTO,
  AdminTeamQueryDTO,
  TeamResponseByPhoneDTO,
  TeamMessageDTO,
  TeamOrderResponseDTO,
  TeamDauResponesDTO,
  TeamOrderRecordResponseDTO,
  TeamRefundResponseDTO,
  RefundOrderRequestBodyDTO
} from './team.dto'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('teams')
@ApiTags('团队管理')
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  @Get('/:phone/team')
  @ApiOperation({ summary: '根据手机号获取团队' })
  @ApiOkResponse({ description: '操作成功', type: TeamResponseByPhoneDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeamByPhone(@Param('phone') phone: string) {
    return this.teamService.getTeamByPhone(phone)
  }

  @Get()
  @ApiOperation({ summary: '获取团队列表' })
  @ApiOkResponse({ description: '操作成功', type: TeamResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({
    required: false,
    type: AdminTeamQueryDTO
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeams(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('name') name: string,
    @Query('phone') phone: string,
    @Query('invitationCode') invitationCode: string,
    @Query('createStartTime') createStartTime: number,
    @Query('createEndTime') createEndTime: number,
    @Query('isVip') isVip: boolean,
    @Query('vipExpirationStartTime') vipExpirationStartTime: number,
    @Query('vipExpirationEndTime') vipExpirationEndTime: number,
    @Query('salesType') salesType: string,
    @Query('sort') sort: string
  ) {
    const querys = {
      page,
      size,
      name,
      createStartTime,
      createEndTime,
      isVip,
      vipExpirationStartTime,
      vipExpirationEndTime,
      phone,
      invitationCode,
      salesType,
      sort
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    if (!querys.sort) {
      delete querys.sort
    }

    return this.teamService.getTeams(querys)
  }

  @Get('/:teamId/messages')
  @ApiOperation({ summary: '获取团队消息' })
  @ApiOkResponse({ description: '操作成功', type: TeamMessageDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeamMessages(@Param('teamId') teamId: number) {
    return this.teamService.getTeamMessages(teamId)
  }

  @Get('/:invitationCode/order')
  @ApiOperation({ summary: '获取团队订单' })
  @ApiOkResponse({ description: '操作成功', type: TeamOrderResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getTeamOrder(@Param('invitationCode') invitationCode: string) {
    return this.teamService.getTeamOrder(invitationCode)
  }

  @Get('dau')
  @ApiOperation({ summary: '获取团队日活' })
  @ApiOkResponse({ description: '操作成功', type: TeamDauResponesDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getDau() {
    return this.teamService.getDau()
  }

  @Get('/:teamId/orderRecord')
  @ApiOperation({ summary: '获取团队资金变化记录' })
  @ApiOkResponse({ description: '操作成功', type: TeamOrderRecordResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getOrderRecord(@Param('teamId') teamId: number) {
    return this.teamService.getTeamOrderRecord(teamId)
  }

  @Get('/:teamId/refund')
  @ApiOperation({ summary: '获取团队退款记录' })
  @ApiOkResponse({ description: '操作成功', type: TeamRefundResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getRefund(@Param('teamId') teamId: number) {
    return this.teamService.getTeamRefund(teamId)
  }

  @Post(':teamId/refund/orders')
  @ApiOperation({ summary: '退款对应订单' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getOrderListByRefund(@Param('teamId') teamId: number) {
    return this.teamService.getOrderListByRefund(teamId)
  }

  @Post(':teamId/refund')
  @ApiOperation({ summary: '退款' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async refund(@Param('teamId') teamId: number, @Body() body: RefundOrderRequestBodyDTO) {
    return this.teamService.refund(teamId, body)
  }
}
