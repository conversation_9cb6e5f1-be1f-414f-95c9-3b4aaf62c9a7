import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req } from '@nestjs/common'
import {
  AppPlatformType,
  AppVersionListRequestDTO,
  AppVersionResponseDTO,
  CreateAppVersionRequestBodyDTO,
  PublicCreateUploadUrlRequestBodyDTO,
  PublicCreateUploadUrlResponseDTO,
  PublicRequestNotifyDTO,
  PublicResponseNotifyDTO
} from './public.dto'
import { ApiHeader, ApiOkResponse, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import { FastifyRequest } from 'fastify'
import { PublicService } from './public.service'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('public')
@ApiTags('公共的')
export class PublicController {
  constructor(private readonly publicService: PublicService) {}

  @Get('notices')
  @ApiOperation({ summary: '获取系统公告' })
  @ApiOkResponse({ type: PublicResponseNotifyDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'page',
    required: false
  })
  @ApiQuery({
    name: 'size',
    required: false
  })
  getSystemNotice(
    @Query('size', {
      transform(value) {
        return Number(value) || 10
      }
    })
    size: number,
    @Query('page', {
      transform(value) {
        return Number(value) || 1
      }
    })
    page: number
  ) {
    return this.publicService.getSystemNotice(page, size)
  }

  @Put('notices/:noticeId')
  @ApiOperation({ summary: '更新系统公告' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  updateSystemNotice(@Param('noticeId') noticeId: string, @Body() body: PublicRequestNotifyDTO) {
    return this.publicService.updateSystemNotice(noticeId, body)
  }

  @Delete('notices/:noticeId')
  @ApiOperation({ summary: '删除系统公告' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  deleteSystemNotice(@Param('noticeId') noticeId: string) {
    return this.publicService.deleteSystemNotice(noticeId)
  }

  @Post('notices')
  @ApiOperation({ summary: '创建系统公告' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createSystemNotice(@Body() body: PublicRequestNotifyDTO) {
    return this.publicService.createSystemNotice(body)
  }

  @Post('app-versions')
  @ApiOperation({ summary: '创建App版本' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  createAppVersion(@Body() body: CreateAppVersionRequestBodyDTO, @Req() req: FastifyRequest) {
    return this.publicService.createAppVersion(body, req.headers.authorization)
  }

  @Get('app-versions')
  @ApiOperation({ summary: '获取App版本列表' })
  @ApiOkResponse({ type: AppVersionResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    type: AppVersionListRequestDTO
  })
  getAppVersions(
    @Query('type') type: AppPlatformType | undefined,
    @Query('page') page: number | undefined,
    @Query('size') size: number | undefined,
    @Query('releaseTimeMin') releaseTimeMin: number | undefined,
    @Query('releaseTimeMax') releaseTimeMax: number | undefined
  ) {
    return this.publicService.getAppVersion({
      page: page || 1,
      size: size || 10,
      releaseTimeMin,
      releaseTimeMax,
      type
    })
  }

  @Get('last-app-version')
  @ApiOperation({ summary: '获取最新App版本' })
  @ApiOkResponse({ type: CreateAppVersionRequestBodyDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'type',
    enum: AppPlatformType
  })
  getLastAppVersion(@Query('type') type: AppPlatformType) {
    return this.publicService.getLastAppVersions(type)
  }

  @Delete('app-versions/:id')
  @ApiOperation({ summary: '删除App版本' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  deleteAppVersion(@Param('id') id: string) {
    return this.publicService.deleteAppVersion(id)
  }

  @Post('upload-urls')
  @ApiOperation({ summary: '创建上传文件URL' })
  @ApiOkResponse({ type: PublicCreateUploadUrlResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async createUploadUrl(@Body() body: PublicCreateUploadUrlRequestBodyDTO) {
    return this.publicService.createUploadUrl(body)
  }
}
