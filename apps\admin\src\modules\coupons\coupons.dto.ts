import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsNumber, Length, Min } from 'class-validator'
import { UserCouponsStatus } from '../vip/vip.dto'

export class CouponsCreateDTO {
  @ApiProperty({
    description: '优惠券名称',
    example: '618满2000-500',
    required: true
  })
  @IsNotEmpty({ message: '优惠券名称不能为空' })
  name: string

  @ApiProperty({
    description: '满减最低消费金额,门槛金额',
    example: 2000,
    required: true
  })
  @IsNotEmpty({ message: '满减门槛金额不能为空' })
  @IsNumber()
  @Min(1, { message: '门槛金额不能小于1' })
  minimumSpendingAmount: number

  @ApiProperty({
    description: '优惠金额',
    example: 500,
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  discountAmount: number

  @ApiProperty({
    description: '优惠券发放后有效天数/用户收到优惠券后，X天后过期',
    example: 365,
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  expireDaysNum: number
}

export class UpdateCouponsRequestDTO extends CouponsCreateDTO {
  @ApiProperty({
    description: '优惠id',
    example: 123,
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  id: number
}
/**
 * 优惠券
 */
export class CouponsDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '满2000减200'
  })
  // 优惠券名称
  name: string

  @ApiResponseProperty({
    type: Number,
    example: 5000
  })
  // 最低金额，满减额
  minimumSpendingAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 200
  })
  // 优惠金额
  discountAmount: number

  @ApiResponseProperty({
    type: String,
    example: '管理员'
  })
  // 创建人名称
  creatorName: number

  @ApiResponseProperty({
    type: Date,
    example: '2024-09-09T6:33:01Z'
  })
  // 创建时间
  createTime: number

  @ApiResponseProperty({
    type: Number,
    example: 200
  })
  // 发放数量
  postAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 199
  })
  // 激活，使用数量
  activeAmount: number

  // @ApiResponseProperty({
  //   type: Number,
  //   example: 0
  // })
  // status: CouponsStatus
  @ApiResponseProperty({
    type: Number,
    example: 199
  })
  // 领取后有效天数
  expireDaysNum: number
}

export enum CouponsStatus {
  /**
   * 已删除
   */
  Deleted = -1,
  /**
   * 正常
   */
  Normal = 0
}

export class SendCouponsRequestDTO {
  @ApiProperty({
    description: '优惠券id',
    example: 123,
    required: true
  })
  @IsNotEmpty()
  @IsNumber()
  // 优惠券id
  couponsId: number

  @ApiProperty({
    description: '接收优惠券用户手机号',
    example: '13882063847',
    required: true
  })
  @Length(11, 11, { message: '请输入11位手机号' })
  // 接收优惠券的用户手机号
  phone: string

  // @ApiProperty({
  //   description: '过期时间',
  //   example: '2025-09-09 00:00:00',
  //   required: true
  // })
  // @IsNotEmpty()
  // @IsDateString()
  // // 过期时间
  // expireTime: Date
}

export class CouponsResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [CouponsDTO]
  })
  data: CouponsDTO[]
}

export class CouponResponse {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '满2000减200'
  })
  // 优惠券名称
  name: string
}

export class CouponResponseByIdDTO {
  @ApiResponseProperty({
    type: CouponResponse
  })
  data: CouponResponse
}

export class UserCouponsRecordDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '13882063847'
  })
  phone: string

  @ApiResponseProperty({
    type: Number,
    example: 12
  })
  channelId: number

  @ApiResponseProperty({
    type: String,
    example: '渠道name'
  })
  channelName: string

  @ApiResponseProperty({
    type: Number,
    example: 132
  })
  teamId: number

  @ApiResponseProperty({
    type: String,
    example: '团队name'
  })
  teamName: string

  @ApiProperty({
    type: Number,
    example: 0,
    description: '0:可用，1:不可用-已使用，2:不可用-已过期'
  })
  // 状态
  status: UserCouponsStatus

  @ApiResponseProperty({
    type: Date,
    example: '2024-09-27T05:55:32.516Z'
  })
  createTime: Date

  @ApiResponseProperty({
    type: Date,
    example: '2024-09-27T05:55:32.516Z'
  })
  castTime: Date

  @ApiResponseProperty({
    type: Date,
    example: '2024-09-27T05:55:32.516Z'
  })
  expireTime: Date

  @ApiResponseProperty({
    type: String,
    example: 'II3VNFA0LOWT1M0QY7'
  })
  orderNo: string
}
export class CouponsRecordResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [UserCouponsRecordDTO]
  })
  data: UserCouponsRecordDTO[]
}
