import { BadRequestException, Controller, Get, Param, Post, Query } from '@nestjs/common'

import {
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiQuery,
  ApiNotFoundResponse
} from '@nestjs/swagger'

import {
  BaseBadRequestDTO,
  BaseNotFoundRequestDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseRequestDTO'
import { AccountAccountsResponseDTO, AccountsRequestDto } from './account.dto'
import { validate } from 'class-validator'
import { AccountService } from './account.service'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Platform } from '@qdy/utils'

@Controller('account')
@ApiTags('账号管理')
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @Get()
  @ApiOperation({ summary: '获取账号列表' })
  @ApiOkResponse({ description: '操作成功', type: AccountAccountsResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '按帐户名称搜索'
  })
  @ApiQuery({
    name: 'invitationCode',
    required: false,
    type: String,
    description: '按团队ID搜索'
  })
  @ApiQuery({
    name: 'platform',
    required: false,
    type: Number,
    enum: Platform,
    description: '按平台搜索(0是抖音,1是视频号)'
  })
  @ApiQuery({
    name: 'startTime',
    required: false,
    type: Number,
    description: '开始时间'
  })
  @ApiQuery({
    name: 'endTime',
    required: false,
    type: Number,
    description: '到期时间'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: '状态(normal正常,abnormal异常)'
  })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async getAccounts(
    @Query('name') name: string,
    @Query('invitationCode') invitationCode: string,
    @Query('size') size: number,
    @Query('page') page: number,
    @Query('platform') platform: number,
    @Query('startTime', {
      transform: (value) => value || 0
    })
    startTime: number,
    @Query('endTime', {
      transform: (value) => value || 0
    })
    endTime: number,
    @Query('status') status: string
  ) {
    const querys = {
      platform,
      name,
      invitationCode,
      status,
      startTime,
      endTime,
      page,
      size
    }

    if (!querys.page) {
      delete querys.page
    }

    if (!querys.size) {
      delete querys.size
    }

    if (!querys.platform && querys.platform !== 0) {
      delete querys.platform
    } else {
      const accountsRequest = new AccountsRequestDto()
      accountsRequest.platform = platform
      const validationErrors = await validate(accountsRequest)
      if (validationErrors.length > 0) {
        throw new BadRequestException('平台参数错误')
      }
    }

    return this.accountService.getAccounts(querys)
  }

  @Post(':platformAccountId/unAuthorize')
  @ApiOperation({ summary: '解除账号' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  async unAuthorize(@Param('platformAccountId') platformAccountId: number) {
    return this.accountService.unAuthorize(platformAccountId)
  }
}
