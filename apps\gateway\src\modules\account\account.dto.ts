import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Platform } from '@qdy/utils'

export class AccountResponseCreateDTO extends BaseResponseDTO {}

export class AccountResponseJoinDTO extends BaseResponseDTO {}

export class AccountsRequestDto {
  @ApiProperty({
    description: '平台',
    enum: Platform,
    required: false
  })
  @IsEnum(Platform)
  @IsOptional()
  platform: number
}

export class AccountAffiliate {
  @ApiResponseProperty({
    example: 'EX123KIO112',
    type: String
  })
  affiliateId: string

  @ApiResponseProperty({
    example: '张三',
    type: String
  })
  name: string

  @ApiResponseProperty({
    example: '***********',
    type: String
  })
  phone: string
}

class Affiliate {
  @ApiResponseProperty({
    example: 123,
    type: Number
  })
  id: number

  @ApiResponseProperty({
    example: '张三',
    type: String
  })
  name: string

  @ApiResponseProperty({
    example: 'https://www.baidu.com/avatar,png',
    type: String
  })
  avatar: string
}

export enum AccountAccountsStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * vip过期冻结/禁用
   */
  Disable = 1
}

class AccountAccounts {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  platformAccountId: string

  @ApiResponseProperty({
    type: [Affiliate],
    example: [
      {
        id: 1,
        name: '张三',
        avatar: 'https://www.baidu.com/avatar,png'
      }
    ]
  })
  affiliates: Affiliate[]

  @ApiResponseProperty({
    type: String,
    example: 'https://www.baidu.com'
  })
  avatar: string

  @ApiResponseProperty({
    type: String,
    example: 'SDFLKKL123'
  })
  openId: string

  @ApiResponseProperty({
    type: Number,
    example: '********** <如果为0,则代表过期了>'
  })
  expiresIn: number

  @ApiResponseProperty({
    type: Number,
    example: '********** <如果为0,则代表过期了>'
  })
  refreshExpiresIn: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  teamId: number

  @ApiResponseProperty({
    type: String,
    example:
      'EAccountM <EAccountM: 普通企业号,STAFF: 员工号, AUTH_COMPANY: 认证企业号, COMPANY_BAND: 品牌企业号>'
  })
  accountRole: string

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  isGreeting?: boolean

  @ApiResponseProperty({
    enum: Platform,
    example: 0
  })
  platform: Platform

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  scopes: string

  /**
   * 媒体账号可用状态，正常,vip过期冻结
   */
  @ApiResponseProperty({
    type: Number,
    enum: Object.values(AccountAccountsStatus).filter((v) => typeof v === 'number'),
    example: 0
  })
  status: AccountAccountsStatus

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  appId: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  regionId: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  remark: string
}

export class AccountMemberAccountsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AccountAccounts]
  })
  data: AccountAccounts[]
}

export class AccountAccountsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AccountAccounts]
  })
  data: AccountAccounts[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  totalPage: number
}

export class AccountInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AccountAccounts,
    example: 1
  })
  data: AccountAccounts
}

export class AccountAccountAuthorizeResponseDTO extends BaseResponseDTO {}

export class AccountUpdateAccountAuthorizationDTO extends BaseResponseDTO {}

export class AccountAccountAuthorizeRequestBodyDTO {
  @ApiProperty({
    description: '授权码',
    example: '320XJX',
    required: true
  })
  @IsNotEmpty({ message: '授权码不能为空' })
  @IsString()
  code: string

  @ApiProperty({
    type: Boolean,
    example: true
  })
  @IsBoolean()
  isRegister: boolean
}

export class AccountUpdateAccountConfigsDTO {
  @ApiProperty({
    type: Boolean,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  top?: boolean

  @ApiProperty({
    type: Boolean,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  mute?: boolean
}

export class AccountResponseConfigs {
  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  top: boolean

  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  mute: boolean

  @ApiResponseProperty({
    type: String,
    example: '333ddd'
  })
  platformAccountId: string
}

export class AccountResponseConfigsDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AccountResponseConfigs
  })
  data: AccountResponseConfigs
}

export class WechatQrCodeRequestDTO {
  @ApiProperty({
    description: 'appId',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  appId?: string

  @ApiProperty({
    description: '地区id',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  regionId: string
}

export class WechatQrCode {
  @ApiResponseProperty({
    type: String,
    example: ''
  })
  qrData: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  appId: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  qrImgBase64: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  uuid: string
}

export class WechatQrCodeResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: WechatQrCode
  })
  data: WechatQrCode
}

export class WechatCheckLoginRequestDTO {
  @ApiProperty({
    description: 'appId',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  appId: string

  @ApiProperty({
    description: '地区id',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  regionId: string

  @ApiProperty({
    description: 'uuid',
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  uuid: string

  @ApiProperty({
    description: 'captchCode',
    type: String
  })
  @IsString()
  captchCode: string
}

export class WechatLoginInfo {
  @ApiResponseProperty({
    type: Number,
    example: ''
  })
  uin: number

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  wxid: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  nickName: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  mobile: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  alias: string
}

export class WechatAuthInfo {
  @ApiResponseProperty({
    type: String,
    example: ''
  })
  uuid: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  headImgUrl: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  nickName: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  expiredTime: number

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  status: number

  @ApiResponseProperty({
    type: WechatLoginInfo,
    example: ''
  })
  loginInfo: WechatLoginInfo

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  hasChangeTeam: boolean
}

export class WechatAuthInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: WechatAuthInfo
  })
  data: WechatAuthInfo
}

export class AccountAdditionPermissionDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Boolean,
    example: 'true or false'
  })
  data: boolean
}

export class AccountUpdateRemarkRequestDTO {
  @ApiProperty({
    description: '备注',
    type: String
  })
  @IsString()
  remark: string
}

export class AccountMessageCardRequestDTO {
  @ApiProperty({
    description: '留资卡片ID,修改卡片时需要填写',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  cardId?: string

  @ApiProperty({
    description: '标题',
    type: String,
    required: true
  })
  @IsString()
  title: string

  @ApiProperty({
    description: '图片的 ID',
    type: String,
    required: true
  })
  @IsString()
  mediaId: string

  @ApiProperty({
    description: '需要添加的输入框,(1:姓名,2:手机号,3:城市)',
    type: [Number],
    required: true
  })
  @IsArray()
  @IsNotEmpty()
  components: number[]
}

export class CardMessage {
  @ApiResponseProperty({
    type: String,
    example: '留资卡片id'
  })
  cardId: string

  @ApiResponseProperty({
    type: Number,
    example: '团队id'
  })
  teamId: number

  @ApiResponseProperty({
    type: String,
    example: '发送人openid'
  })
  fromUserId: string

  @ApiResponseProperty({
    type: String,
    example: '发送人头像'
  })
  fromAvatar: string

  @ApiResponseProperty({
    type: String,
    example: '发送人昵称'
  })
  fromName: string

  @ApiResponseProperty({
    type: String,
    example: '接收人openid'
  })
  toUserId: string

  @ApiResponseProperty({
    type: String,
    example: '接收人头像'
  })
  toAvatar: string

  @ApiResponseProperty({
    type: String,
    example: '接收人昵称'
  })
  toName: string

  @ApiResponseProperty({
    type: String,
    example: '姓名'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '手机号'
  })
  phone: string

  @ApiResponseProperty({
    type: String,
    example: '地址'
  })
  address: string

  @ApiResponseProperty({
    type: Number,
    example: '创建时间'
  })
  createTime: number
}

export class CardMessageListRequestDTO {
  @ApiProperty({
    description: '留资卡片ID,修改卡片时需要填写',
    type: String,
    required: true
  })
  @IsString()
  @IsOptional()
  cardId: string

  @ApiProperty({
    description: '页码',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsOptional()
  page?: number

  @ApiProperty({
    description: '每页数量',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsOptional()
  size?: number
}

export class CardMessageResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [CardMessage]
  })
  data: CardMessage[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number
}

export class KuaishouCardMessage {
  @ApiResponseProperty({
    type: String,
    example: 'mHQrwyMCHEPb0Ys1TbcPIg==:esJLXPFgMObOCzPHEndAqg=='
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '留资卡片'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '留资卡片'
  })
  card_title: string

  @ApiResponseProperty({
    type: Number,
    example: '类型'
  })
  type: number

  @ApiResponseProperty({
    type: Number,
    example: '短信验证方式'
  })
  sms_verification_type: number

  @ApiResponseProperty({
    type: String,
    example: '按钮文案'
  })
  button_words: string

  @ApiResponseProperty({
    type: [String],
    example: '福利说明'
  })
  rights_explain: [string]

  @ApiResponseProperty({
    type: String,
    example: '引导留资语'
  })
  guide_words: string
}

export class KuaishouCardMessageListResponseDTO {
  @ApiResponseProperty({
    type: [KuaishouCardMessage]
  })
  data: KuaishouCardMessage[]
}

export class XiaohongshuPageDetail {
  @ApiResponseProperty({
    type: String,
    example: '660a6b96a000014925de80b2'
  })
  page_id: string

  @ApiResponseProperty({
    type: String,
    example: '标题'
  })
  title: string

  @ApiResponseProperty({
    type: String,
    example: '封面'
  })
  cover: string

  @ApiResponseProperty({
    type: String,
    example: '描述'
  })
  page_desc: string

  @ApiResponseProperty({
    type: String,
    example: '链接'
  })
  page_url: string

  @ApiResponseProperty({
    type: Number,
    example: '创建时间'
  })
  create_time: number
}

export class XiaohongshuPageListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [XiaohongshuPageDetail]
  })
  data: XiaohongshuPageDetail[]

  @ApiResponseProperty({
    type: Number,
    example: '总数'
  })
  total: number
}

export class XiaohongshuCardMessage {
  @ApiResponseProperty({
    type: String,
    example: 'mHQrwyMCHEPb0Ys1TbcPIg==:esJLXPFgMObOCzPHEndAqg=='
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '留资卡片'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '留资卡片'
  })
  title: string

  @ApiResponseProperty({
    type: String,
    example: '封面图'
  })
  image: string

  @ApiResponseProperty({
    type: {},
    example: '扩展信息'
  })
  ext: {
    social_card_ext: {
      card_type: number
    }
    trade_card_ext: {
      sub_title: string
      link_platform: string
    }
  }
}

export class XiaohongshuMessageListResponseDTO {
  @ApiResponseProperty({
    type: [XiaohongshuCardMessage]
  })
  data: XiaohongshuCardMessage[]

  total: number
}

export class XiaohongshuCommentListRequestDTO {
  @ApiProperty({
    description: '账号id',
    type: Number,
    required: true
  })
  @IsNumber()
  platformAccountId: number

  @ApiProperty({
    description: '开始日期(2025-03-31)',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  beginTime: string

  @ApiProperty({
    description: '结束日期(2025-03-31)',
    type: String,
    required: false
  })
  @IsString()
  @IsOptional()
  endTime: string

  @ApiProperty({
    description: '页码',
    type: Number,
    required: true
  })
  @IsNumber()
  page: number

  @ApiProperty({
    description: '每页数量（不超过20）',
    type: Number,
    required: true
  })
  @IsNumber()
  size: number
}

export class XiaohongshuComment {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  platformAccountId: number

  @ApiResponseProperty({
    type: String,
    example: '唯一id'
  })
  uniqueId: string

  @ApiResponseProperty({
    type: String,
    example: '评论id'
  })
  commentId: string

  @ApiResponseProperty({
    type: String,
    example: '评论内容'
  })
  content: string

  @ApiResponseProperty({
    type: Number,
    example: '评论时间'
  })
  commentTime: number

  @ApiResponseProperty({
    type: String,
    example: '作品id'
  })
  itemId: string

  @ApiResponseProperty({
    type: String,
    example: '作品封面'
  })
  cover: string

  @ApiResponseProperty({
    type: String,
    example: '作品标题'
  })
  noteTitle: string

  @ApiResponseProperty({
    type: String,
    example: '作者id'
  })
  authorId: string

  @ApiResponseProperty({
    type: String,
    example: '评论人id'
  })
  fromUserId: string

  @ApiResponseProperty({
    type: String,
    example: '评论人昵称'
  })
  fromUserName: string

  @ApiResponseProperty({
    type: Number,
    example: '回复状态'
  })
  state: number

  @ApiResponseProperty({
    type: String,
    example: '回复客服'
  })
  replyAccount: string
}

export class XiaohongshuCommentListResponseDTO {
  @ApiResponseProperty({
    type: [XiaohongshuComment]
  })
  data: XiaohongshuComment[]

  @ApiResponseProperty({
    type: Number,
    example: '内容总数'
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: '页码'
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: '分页长度'
  })
  size: number
}
