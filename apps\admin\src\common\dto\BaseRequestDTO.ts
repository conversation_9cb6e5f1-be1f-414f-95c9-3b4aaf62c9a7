import { ApiResponseProperty } from '@nestjs/swagger'

export class BaseNotFoundRequestDTO {
  @ApiResponseProperty({
    type: Number,
    example: 404
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '资源不存在'
  })
  message: string
}

export class BaseBadRequestDTO {
  @ApiResponseProperty({
    type: Number,
    example: 400
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '操作失败'
  })
  message: string
}

export class BaseUnauthorizedResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 401
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '参数错误'
  })
  message: string
}

export class BaseForbiddenResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 403
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '该账号已被禁用'
  })
  message: string
}

export class BaseConflictResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 409
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '资源已存在'
  })
  message: string
}
