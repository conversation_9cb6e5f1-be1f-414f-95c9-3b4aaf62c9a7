import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsNotEmpty } from 'class-validator'

export enum UserCouponsStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * 已使用
   */
  used = 1,

  /**
   * 已过期
   */
  expired
}

export enum CouponsStatus {
  /**
   * 已删除
   */
  Deleted = -1,
  /**
   * 正常
   */
  Normal = 0
}

/**
 * 优惠券
 */
export class UserCouponsDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: String,
    example: '满2000减200'
  })
  // 优惠券名称
  name: string

  @ApiResponseProperty({
    type: Number,
    example: 5000
  })
  // 最低金额，满减额
  minimumSpendingAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 200
  })
  // 优惠金额
  discountAmount: number

  @ApiResponseProperty({
    type: Date,
    example: '2025-09-09T00:00:00Z'
  })
  // 过期时间
  expireTime: Date

  @ApiProperty({
    type: Number,
    example: 0,
    description: '0:可用，1:不可用-已使用，2:不可用-已过期'
  })
  // 状态
  status: UserCouponsStatus
}
export class UserCouponsResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  total: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: [UserCouponsDTO]
  })
  data: UserCouponsDTO[]
}

export class ChangeCouponsRequestDTO {
  @ApiProperty({
    type: String,
    example: '123456',
    description: '渠道码'
  })
  @IsNotEmpty({ message: '渠道码不能为空' })
  // 状态
  channelCode: string
}
