import { BadRequestException, Logger } from '@nestjs/common'
import axios from 'axios'
import { sendEvent } from '../overview/event'
import JsonBigint from 'json-bigint'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'

const commentApi = '/api/finder/comment'
const userPage = '/api/finder/userPage'
const commentList = '/api/finder/commentList'
const downloadImage = '/api/message/downloadImage'
const downloadVideo = '/api/message/downloadVideo'
const contactList = '/api/finder/contactList'
const reconnection = '/api/login/reconnection'
const mentionList = '/api/finder/mentionList'
const sessionIdApi = '/api/finder/getMsgSessionId'

const logger = new Logger('interact external wechat')

export function deleteComment(data: any) {
  const url = `${process.env.WECHAT_BASE_URL}${commentApi}`

  const videosApiToken = process.env.WECHAT_TOKEN

  axios.post(
    url,
    {
      appId: data.appId,
      myRoleType: 3,
      myUserName: data.myUserName,
      opType: 1,
      sessionBuffer: data.sessionBuffer,
      objectId: data.objectId,
      content: '',
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )
}

export async function createComment({
  appId,
  myUserName,
  objectNonceId,
  sessionBuffer,
  content,
  refCommentId,
  rootCommentId,
  autoresponderId,
  objectId,
  replyUserName,
  teamId,
  openId,
  platformAccountId,
  redisClient
}: {
  appId: string
  myUserName: string
  objectNonceId?: string
  token: string
  sessionBuffer: string
  content: string
  refCommentId?: string
  rootCommentId?: string
  replyUserName?: string
  autoresponderId?: number
  objectId: string
  teamId: number
  openId: string
  platformAccountId: number
  redisClient: Cache<RedisStore>
}) {
  const teamInfo = (await redisClient.get(`overview:${teamId}`)) as {
    residueCount: number
  }

  if (teamInfo) {
    if (teamInfo.residueCount <= 0) {
      throw new BadRequestException('剩余回复次数不足')
    }
  }

  const url = `${process.env.WECHAT_BASE_URL}${commentApi}`

  const videosApiToken = process.env.WECHAT_TOKEN

  try {
    const res = await axios.post(
      url,
      {
        appId,
        replyUserName,
        myRoleType: 3,
        myUserName,
        objectNonceId,
        opType: 0,
        sessionBuffer,
        objectId,
        content,
        refCommentId,
        rootCommentId,
        useProxy: true
      },
      {
        headers: {
          'VideosApi-token': videosApiToken
        }
      }
    )

    if (res.data.ret !== 200) {
      logger.log(
        JSON.stringify({
          url,
          appId,
          replyUserName,
          myRoleType: 3,
          myUserName,
          objectNonceId,
          opType: 0,
          sessionBuffer,
          objectId,
          content,
          refCommentId,
          rootCommentId,
          useProxy: true
        })
      )
      // await postReconnection({ appId, token: videosApiToken, isNew })
      throw new BadRequestException(`[视频号]:${res.data.ret}-${res.data.msg}`)
    }

    sendEvent({
      platformType: 'wechat',
      teamId,
      platformAccountId,
      autoresponderId,
      autoSingleCount: 0,
      singleCount: 0,
      groupCount: 0,
      openId,
      ...(autoresponderId
        ? { autoCommentCount: 1, commentCount: 0 }
        : { commentCount: 1, autoCommentCount: 0 })
    })

    return res.data.data
  } catch (e) {
    logger.debug(e)
    throw new BadRequestException('评论失败')
  }
}

export async function postUserPage(data: {
  appId: string
  toUserName: string
  lastBuffer: string
  maxId: string
  token: string
}) {
  try {
    const url = `${process.env.WECHAT_BASE_URL}${userPage}`

    const videosApiToken = process.env.WECHAT_TOKEN

    const res = await axios.post(
      url,
      {
        appId: data.appId,
        toUserName: data.toUserName,
        lastBuffer: data.lastBuffer,
        maxId: data.maxId,
        useProxy: false
      },
      {
        headers: {
          'VideosApi-token': videosApiToken
        },
        transformResponse: [
          (data) => {
            return JsonBigint.parse(data)
          }
        ]
      }
    )

    if (res.data.ret !== 200) {
      // await postReconnection({ appId: data.appId, token: videosApiToken, isNew: data.isNew })
      throw new BadRequestException(`[视频号主页信息]:${res.data.msg}`)
    }

    return res.data.data
  } catch (error) {
    logger.error(error)
    throw new BadRequestException(error)
  }
}

export async function postVideoComments(data: {
  appId: string
  objectId: string
  lastBuffer: string
  sessionBuffer: string
  objectNonceId: string
  token: string
  rootCommentId: string
}) {
  try {
    const url = `${process.env.WECHAT_BASE_URL}${commentList}`
    const videosApiToken = process.env.WECHAT_TOKEN

    const res = await axios.post(
      url,
      {
        appId: data.appId,
        objectId: data.objectId,
        lastBuffer: data.lastBuffer,
        sessionBuffer: data.sessionBuffer,
        objectNonceId: data.objectNonceId,
        rootCommentId: data.rootCommentId,
        useProxy: true
      },
      {
        headers: {
          'VideosApi-token': videosApiToken
        },
        transformResponse: [
          (data) => {
            return JsonBigint.parse(data)
          }
        ]
      }
    )

    if (res.data.ret !== 200) {
      // await postReconnection({ appId: data.appId, token: videosApiToken, isNew: data.isNew })
      throw new BadRequestException(`[视频号]:${res.data.msg}`)
    }

    return res.data.data
  } catch (error) {
    logger.error(error)
    throw new BadRequestException('获取视频评论列表失败')
  }
}

export async function getWechatMessageSessionId({
  appId,
  toUserName,
  myUserName
}: {
  appId: string
  toUserName: string
  myUserName: string
  token: string
}): Promise<{
  sessionId: string
  toUsername: string
}> {
  const url = `${process.env.WECHAT_BASE_URL}${sessionIdApi}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const requestPayload = {
    appId,
    toUserName,
    myUserName,
    myAccountType: 1,
    useProxy: true
  }

  try {
    const res = await axios.post(url, requestPayload, {
      headers: {
        'VideosApi-token': videosApiToken
      }
    })

    if (res.data.ret !== 200) {
      throw new Error(`[视频号]:${res.data.msg}`)
    }

    return res.data.data
  } catch (error) {
    logger.warn(`Initial request failed: ${error.message}. Retrying...`)

    try {
      const retryRes = await axios.post(url, requestPayload, {
        headers: {
          'VideosApi-token': videosApiToken
        }
      })

      if (retryRes.data.ret !== 200) {
        throw new Error(`[视频号]:${retryRes.data.msg}`)
      }

      return retryRes.data.data
    } catch (retryError) {
      logger.error(`Retry failed: ${retryError.message}`)
      throw new BadRequestException(
        `[视频号]:${retryError.message}:${url}:${JSON.stringify(requestPayload)}`
      )
    }
  }
}

export async function wechatSendMessages({
  appId,
  toUserName,
  myUserName,
  sessionId,
  content,
  imgUrl,
  teamId,
  autoresponderId,
  redisClient,
  platformAccountId,
  openId
}: {
  appId: string
  toUserName: string
  myUserName: string
  token: string
  sessionId: string
  content: string
  imgUrl: string
  teamId: number
  platformAccountId: number
  autoresponderId?: number
  redisClient: Cache<RedisStore>
  openId: string
}) {
  const teamInfo = (await redisClient.get(`overview:${teamId}`)) as {
    residueCount: number
  }

  if (teamInfo && teamInfo.residueCount <= 0) {
    throw new BadRequestException('剩余回复次数不足')
  }

  const wxResidueCount = (await redisClient.get(`wxResidue:${openId}`)) as {
    residueCount: number
  }

  logger.error('wxResidueCount', wxResidueCount)
  logger.error('autoresponderId', autoresponderId)

  if (autoresponderId && wxResidueCount && wxResidueCount.residueCount <= 0) {
    // 自动回复策略做限制判断
    throw new BadRequestException('剩余回复次数不足')
  }

  const value: {
    appId: string
    imgUrl?: string
    content?: string
    toUserName: string
    myUserName: string
    msgSessionId: string
    useProxy: boolean
  } = {
    appId,
    content,
    toUserName,
    myUserName,
    msgSessionId: sessionId,
    useProxy: true
  }

  const videosApiToken = process.env.WECHAT_TOKEN

  const url = `${process.env.WECHAT_BASE_URL}`

  let uri = '/api/finder/postPrivateLetter'

  if (imgUrl && !content) {
    uri = '/api/finder/postPrivateLetterImg'

    delete value.content
    value.imgUrl = imgUrl
  }

  const res = await axios.post(`${url}${uri}`, value, {
    headers: {
      'VideosApi-token': videosApiToken
    },
    transformResponse: [
      (data) => {
        return JsonBigint.parse(data)
      }
    ]
  })

  if (res.data.ret !== 200) {
    logger.error(res.data)
    // await postReconnection({ appId, token: videosApiToken, isNew })
    throw new BadRequestException(`[视频号]:${res.data.msg}`)
  }

  let singleCount = 0
  let autoSingleCount = 0

  if (autoresponderId) {
    autoSingleCount = 1
  } else {
    singleCount = 1
  }

  sendEvent({
    platformType: 'wechat',
    teamId,
    platformAccountId,
    autoresponderId,
    autoCommentCount: 0,
    commentCount: 0,
    autoSingleCount,
    singleCount,
    groupCount: 0,
    openId
  })

  return {
    messageId: res.data.data.newMsgId
  }
}

export function deleteWechatComment({
  appId,
  content,
  objectId,
  sessionBuffer,
  commentId,
  myUserName
}: {
  token: string
  appId: string
  content: string
  objectId: string
  sessionBuffer: string
  commentId: string
  myUserName: string
}) {
  const url = `${process.env.WECHAT_BASE_URL}${commentApi}`
  const videosApiToken = process.env.WECHAT_TOKEN
  return axios.post(
    url,
    {
      appId,
      content,
      objectId,
      sessionBuffer,
      opType: 1,
      myUserName,
      commentId,
      myRoleType: 3,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )
}

/**
 * 获取私信个人信息
 * @param data
 * @returns
 */
export async function postContactList(data: {
  appId: string
  myUserName: string
  myRoleType: number
  queryInfo: string
  token: string
}) {
  const url = `${process.env.WECHAT_BASE_URL}${contactList}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      myUserName: data.myUserName,
      myRoleType: data.myRoleType,
      queryInfo: data.queryInfo,
      useProxy: false
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      data: {
        username: string
        nickname: string
        headUrl: string
        signature: string
        extInfo: {
          country: string
          province: string
          city: string
          sex: number
        }
        msgInfo: {
          msgUsername: string
          sessionId: string
        }
        wxUsernameV5: string
      }[]
      msg: string
      ret: number
    }
  }

  if (res.data.ret !== 200) {
    logger.debug(res.data)
    // await postReconnection({ appId: data.appId, token: videosApiToken, isNew: data.isNew })
    throw new BadRequestException(`[获取私人信息]:${res.data.msg}`)
  }

  return res.data.data[0]
}

/**
 * 下载图片信息
 * @param data
 * @returns
 */
export async function postDownloadImage(data: {
  appId: string
  xml: string
  type: number
  token: string
}) {
  const url = `${process.env.WECHAT_BASE_URL}${downloadImage}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      xml: data.xml,
      type: data.type,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      data: {
        fileUrl: string
      }
      msg: string
      ret: number
    }
  }

  // if (res.data.ret !== 200) {
  //   logger.debug(res.data)
  //   throw new BadRequestException(`[下载图片]:${res.data.msg}`)
  // }

  return res.data
}

/**
 * 下载视频信息
 * @param data
 * @returns
 */
export async function postDownloadVideo(data: { appId: string; xml: string; token: string }) {
  const url = `${process.env.WECHAT_BASE_URL}${downloadVideo}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      xml: data.xml,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      data: {
        fileUrl: string
      }
      msg: string
      ret: number
    }
  }

  // if (res.data.ret !== 200) {
  //   logger.debug(res.data)
  //   throw new BadRequestException(`[下载视频]:${res.data.msg}`)
  // }

  return res.data
}

export async function postReconnection(data: { appId: string; token: string }) {
  const url = `${process.env.WECHAT_BASE_URL}${reconnection}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      useProxy: true
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      }
    }
  )) as {
    data: {
      data: boolean
      msg: string
      ret: number
    }
  }

  if (res.data.ret !== 200) {
    // 重新登录如果报错，则把当前账号改成掉线状态
    logger.debug(res.data)
    const regex = /\[CDATA\[([^\]]+)\]\]/
    const match = res.data.msg.match(regex)
    let str = '断线重连异常'
    if (match && match.length > 1) {
      str = match[1]
    }
    throw new BadRequestException(`[掉线重登]:${str}`)
  }

  return res.data
}

/**
 * 消息列表
 * @param data
 * @returns
 */
export async function postMentionList(data: {
  appId: string
  token: string
  myUserName: string
  myRoleType: number
  reqScene: number
  lastBuff: string
}) {
  const url = `${process.env.WECHAT_BASE_URL}${mentionList}`

  const videosApiToken = process.env.WECHAT_TOKEN

  const res = (await axios.post(
    url,
    {
      appId: data.appId,
      myUserName: data.myUserName,
      myRoleType: data.myRoleType,
      reqScene: data.reqScene,
      lastBuff: data.lastBuff,
      useProxy: false
    },
    {
      headers: {
        'VideosApi-token': videosApiToken
      },
      transformResponse: [
        (data) => {
          return JsonBigint.parse(data)
        }
      ]
    }
  )) as {
    data: {
      data: {
        list: {
          mentions: {
            headUrl: string
            username: string
            nickname: string
            mentionType: string
            mentionContent: string
            createtime: number
            thumbUrl: string
            refObjectId: string
            refCommentId: string
            refObjectNonceId: string
            description: string
            refContent: string
            replyNickname: string
            contact: {
              contact: {
                nickname: string
                username: string
                headUrl: string
              }
            }
            replyContact: {
              contact: {
                nickname: string
                headUrl: string
              }
            }
          }[]
        }
        lastBuff: string
      }
      msg: string
      ret: number
    }
  }

  if (res.data.ret !== 200) {
    logger.debug(res.data)
    // await postReconnection({ appId: data.appId, token: data.token, isNew: data.isNew })
    throw new BadRequestException(
      `[获取消息列表]:${res.data.msg}:${JSON.stringify({
        appId: data.appId,
        myUserName: data.myUserName,
        myRoleType: data.myRoleType,
        reqScene: data.reqScene,
        lastBuff: data.lastBuff,
        useProxy: true
      })}:response:${JSON.stringify(res.data)}`
    )
  }

  return res.data
}
