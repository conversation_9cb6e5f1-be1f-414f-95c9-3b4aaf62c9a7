import { Injectable, OnModuleInit, OnModuleD<PERSON>roy, <PERSON><PERSON> } from '@nestjs/common'
import { PrismaClient } from '../client'

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  logger = new Logger('PrismaService')

  async onModuleInit() {
    this.logger.log('Prisma client init')

    this.logger.log(process.env.DATABASE_URL_MYSQL)
    await this.$connect()

    this.logger.log('123')
    

    const count = await this.systemDosage.count()
    if (!count) {
      await this.systemDosage.create({
        data: {
          standardMessageLimit: 200,
          standardTeamMemberNumberLimit: 1,
          standardPlatformAccountNumberLimit: 2
        }
      })
    }
  }

  async onModuleDestroy() {
    await this.$disconnect()
  }
}
