import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger
} from '@nestjs/common'
import { PrismaService, type PlatformAccount } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { type Cache } from 'cache-manager'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { type RedisStore } from 'cache-manager-ioredis-yet'
import { genSocketRedisKey, Platform } from '@qdy/utils'
import { AccountAccountAuthorizeRequestBodyDTO, AccountAccountsStatus } from './account.dto'
import { TeamMemberRole } from '../team/team.dto'
import { AutoresponderKeywordKey } from '../autoresponder/constant'
import { AutoresponderOpuser } from '../autoresponder/autoresponder.dto'
import { type FastifyReply } from 'fastify'
import { AccountSocketService } from './account.task'
import { getAuthorizeAccount, getAuthorizeAccountInfoApi } from './external.kuaishou'
import { PlatformAccountManageService } from '@qdy/common'

@Injectable()
export class AccountKuaishouService {
  logger = new Logger('AccountKuaishouService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly socketService: AccountSocketService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  /**
   * 授权账号
   * @param authorization
   * @param code
   */
  async authorizeAccount(
    { code, isRegister }: AccountAccountAuthorizeRequestBodyDTO,
    req: FastifyReply
  ) {
    const { user } = this.request

    const { kuaishouClientKey, kuaishouClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const [team, platformAccountCount, systemDosage] = await Promise.all([
      this.prisma.team.findUnique({
        where: {
          id: teamMember.teamId
        },
        include: {
          vip: true
        }
      }),
      this.prisma.platformAccount.count({ where: { teamId: teamMember.teamId } }),
      this.prisma.systemDosage.findFirst()
    ])

    if (isRegister) {
      if (team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() > Date.now()) {
        if (team.vip.platformAccountNumberLimit <= platformAccountCount) {
          throw new ForbiddenException('账号数量已达上限')
        }
      } else if (systemDosage.standardPlatformAccountNumberLimit <= platformAccountCount) {
        throw new ForbiddenException('账号数量已达上限')
      }
    }

    const res = await getAuthorizeAccount({
      clientKey: kuaishouClientKey,
      clientSecret: kuaishouClientSecret,
      code
    })

    const oldPlatformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        openId: res.openId,
        platform: Platform.Kuaishou
      },
      include: {
        affiliates: true
      }
    })

    if (!isRegister) {
      if (oldPlatformAccount && oldPlatformAccount.teamId !== user.currentTeamId) {
        // 重新授权，登录的账号在当前团队无法找到，并且账号已超了提示错误
        if (team.vip.platformAccountNumberLimit <= platformAccountCount) {
          throw new ForbiddenException('账号数量已达上限')
        }
      }
      if (!oldPlatformAccount && team.vip.platformAccountNumberLimit <= platformAccountCount) {
        throw new ForbiddenException('账号数量已达上限')
      }
    }

    if (oldPlatformAccount) {
      // false 没有过期 true
      const isoverdue =
        oldPlatformAccount.expiresIn * 1000 + oldPlatformAccount.createTime.getTime() < Date.now()

      if (!isoverdue && oldPlatformAccount.teamId !== teamMember.teamId) {
        await this.prisma.platformAccount.update({
          where: {
            id: oldPlatformAccount.id
          },
          data: {
            accessToken: res.accessToken,
            refreshToken: res.refreshToken,
            expiresIn: res.expiresIn,
            refreshExpiresIn: res.refreshExpiresIn,
            refreshTime: new Date(),
            createTime: new Date(),
            tokenTime: new Date(),
            status: AccountAccountsStatus.Normal,
            unauthorize: ''
          }
        })
        throw new ForbiddenException('账号已授权过，如需重新授权，请在手机上解除授权后再次授权')
      }
    }

    const info = await getAuthorizeAccountInfoApi({
      accessToken: res.accessToken,
      clientKey: kuaishouClientKey
    })

    const value = {
      name: info.name,
      avatar: info.avatar,
      openId: res.openId,
      accessToken: res.accessToken,
      refreshToken: res.refreshToken,
      expiresIn: res.expiresIn,
      refreshExpiresIn: res.refreshExpiresIn,
      teamId: teamMember.teamId,
      status: AccountAccountsStatus.Normal,
      wechatInfo: info.userinfo,
      unauthorize: '',
      platform: Platform.Kuaishou,
      affiliates: {
        connect: {
          id: teamMember.id
        }
      }
    }

    if (oldPlatformAccount && oldPlatformAccount.teamId !== teamMember.teamId) {
      await this.prisma.platformAccount.update({
        where: {
          id: oldPlatformAccount.id
        },
        data: {
          affiliates: {
            disconnect: oldPlatformAccount.affiliates.map((item) => ({ id: item.id }))
          }
        }
      })

      const userIds = oldPlatformAccount.affiliates.map((item) => item.userId)
      const teamMemberByOldPlatformAccount = await this.prisma.teamMember.findMany({
        where: {
          teamId: oldPlatformAccount.teamId,
          role: {
            not: TeamMemberRole.Member
          }
        }
      })

      const manageUserIds = teamMemberByOldPlatformAccount.map((item) => item.userId)

      const combinedAndUniqueIds = [...new Set([...userIds, ...manageUserIds])]

      const users = await this.prisma.user.findMany({
        where: {
          id: {
            in: combinedAndUniqueIds
          }
        }
      })
      if (users.length > 0) {
        for (let i = 0; i < users.length; i++) {
          const userInfo = users[i]
          this.logger.log(userInfo)
          const auth = await this.cacheManager.get<string>(userInfo.phone)
          if (auth) {
            await this.cacheManager.store.client.hdel(
              genSocketRedisKey(auth),
              oldPlatformAccount.openId
            )
          }
        }
      }

      // 删除旧账号的socketids
      await this.cacheManager.store.client.del(genSocketRedisKey(oldPlatformAccount.openId))

      // 该账号之前团队对应的自动回复移除该账号
      const autoresponderList = await this.prisma.autoresponder.findMany({
        where: {
          platformAccountIds: {
            array_contains: oldPlatformAccount.id
          }
        }
      })

      autoresponderList.forEach(async (item) => {
        if (item.platformAccountIds) {
          const accountIds = (item.platformAccountIds as number[]).filter(
            (id) => id !== oldPlatformAccount.id
          )
          await this.prisma.autoresponder.update({
            where: { id: item.id },
            data: { platformAccountIds: accountIds }
          })
        }
      })
    }

    const platformAccount = await this.prisma.platformAccount.upsert({
      create: value,
      where: {
        openId: res.openId
      },
      update: {
        ...value,
        createTime: new Date(),
        refreshTime: new Date(),
        tokenTime: new Date()
      }
    })

    const teamMembers = await this.prisma.teamMember.findMany({
      where: {
        teamId: teamMember.teamId,
        role: {
          not: TeamMemberRole.Member
        },
        userId: {
          not: user.id
        }
      },
      include: {
        user: true
      }
    })

    const socketIds = []

    for (let i = 0; i < teamMembers.length; i++) {
      const item = teamMembers[i]
      const [socketId, appSocketId] = await Promise.all([
        this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
        this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
      ])

      this.logger.debug(`teamMembers-item-${item.name}`, {
        socketId,
        reidsKey: genSocketRedisKey(item.userId),
        openId: platformAccount.id
      })

      await Promise.allSettled([
        (async () => {
          if (appSocketId) {
            socketIds.push(appSocketId)
            try {
              const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

              if (authApp) {
                await this.cacheManager.store.client.hset(
                  genSocketRedisKey(authApp),
                  platformAccount.openId,
                  platformAccount.id
                )
              }
            } catch (error) {
              this.logger.error('更新缓存错误', error)
            }

            await this.cacheManager.set(genSocketRedisKey(item.userId + 'app'), appSocketId, 0)
          }
        })(),
        (async () => {
          if (socketId) {
            socketIds.push(socketId)
            try {
              const auth = await this.cacheManager.get<string>(item.user.phone)

              if (auth) {
                await this.cacheManager.store.client.hset(
                  genSocketRedisKey(auth),
                  platformAccount.openId,
                  platformAccount.id
                )
              }
            } catch (error) {
              this.logger.error('更新缓存错误', error)
            }

            await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
          }
        })()
      ])
    }

    const [socketId, appSocketId] = await Promise.all([
      this.cacheManager.get<string>(genSocketRedisKey(user.id)),
      this.cacheManager.get<string>(genSocketRedisKey(user.id + 'app'))
    ])

    await this.platformAccountManageService.updatePlatformAccountRedisInfo(platformAccount)

    await Promise.allSettled([
      (async () => {
        if (appSocketId) {
          socketIds.push(appSocketId)
          try {
            const authApp = await this.cacheManager.get<string>(`${user.phone}app`)

            if (authApp) {
              await this.cacheManager.store.client.hset(
                genSocketRedisKey(authApp),
                platformAccount.openId,
                platformAccount.id
              )
            }
          } catch (error) {
            this.logger.error('更新缓存错误', error)
          }

          await this.cacheManager.store.client.hset(
            genSocketRedisKey(res.openId),
            appSocketId,
            platformAccount.id
          )
        }
      })(),
      (async () => {
        if (socketId) {
          socketIds.push(socketId)
          try {
            const auth = await this.cacheManager.get<string>(user.phone)

            if (auth) {
              await this.cacheManager.store.client.hset(
                genSocketRedisKey(auth),
                platformAccount.openId,
                platformAccount.id
              )
            }
          } catch (error) {
            this.logger.error('更新缓存错误', error)
          }

          await this.cacheManager.store.client.hset(
            genSocketRedisKey(res.openId),
            socketId,
            platformAccount.id
          )
        }
      })()
    ])

    req.status(201).send({
      statusCode: 0,
      data: {
        name: info.name,
        avatar: info.avatar
      }
    })

    return this.initPlatformAccount(platformAccount, socketIds)
  }

  async deleteAutoresponder(platformAccount: PlatformAccount) {
    // 查找所有符合条件的记录
    const autoresponders = await this.prisma.autoresponder.findMany({
      where: {
        opusers: {
          path: '$[*].platformAccountId',
          array_contains: [platformAccount.id]
        }
      }
    })

    autoresponders.forEach((item) => {
      ;(item.opusers as unknown as AutoresponderOpuser[]).forEach(async (opuser) => {
        const key = `${platformAccount.openId}:${opuser.id}`
        try {
          await this.cacheManager.store.client.hdel(AutoresponderKeywordKey, key)
        } catch {
          // ignore
        }
      })
    })

    try {
      await this.cacheManager.store.client.hdel(AutoresponderKeywordKey, platformAccount.openId)
    } catch {
      // ignore
    }

    // 如果找到了记录，进行更新
    if (autoresponders.length > 0) {
      // 获取需要更新的 ID 列表
      const updates = autoresponders.map((responder) => ({
        id: responder.id,
        updatedIds: (responder.platformAccountIds as number[]).filter(
          (id) => id !== platformAccount.id
        ) // 移除指定的 ID
      }))

      // 批量更新所有符合条件的记录
      const updatePromises = updates.map((update) =>
        this.prisma.autoresponder.update({
          where: { id: update.id },
          data: {
            platformAccountIds: update.updatedIds // 使用更新后的 ID 列表
          }
        })
      )

      // 等待所有更新完成
      await Promise.all(updatePromises)
    }
  }

  async initPlatformAccount(platformAccount: PlatformAccount, socketIds: string[]) {
    if (socketIds.length > 0) {
      this.socketService.socketService
        .send({
          list: JSON.stringify(
            socketIds.map((socketId) => ({
              socketId,
              data: {
                type: 'changePlatformAccount',
                data: [
                  {
                    action: 'add',
                    platform: platformAccount.platform,
                    name: platformAccount.name,
                    avatar: platformAccount.avatar,
                    openId: platformAccount.openId,
                    accountRole: platformAccount.accountRole,
                    id: platformAccount.id,
                    teamId: platformAccount.teamId,
                    expiresTime: platformAccount.expiresIn
                      ? platformAccount.expiresIn * 1000 + platformAccount.createTime.getTime()
                      : 0
                  }
                ]
              }
            }))
          )
        })
        .subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`发送失败 error${err.message}`)
          },
          complete: () => {}
        })
    }
  }
}
