import { ForbiddenException, HttpException, Inject, Injectable, Logger } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { PrismaService } from '@qdy/mysql'
import { FastifyRequest } from 'fastify'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import {
  postAuthorizeByWechat,
  postCheckLoginByWechat,
  postWechatProfile,
  wechatLogout
} from './external.wechat'
import { AccountAccountsStatus, WechatCheckLoginRequestDTO } from './account.dto'
import { TeamMemberRole } from '../team/team.dto'
import { genSocketRedisKey, Platform } from '@qdy/utils'
import { AccountSocketService } from './account.task'
import { AutoresponderKeywordKey } from '../autoresponder/constant'
import { updateOpusEventKey, eventEmitter } from './account.event'
import { PlatformAccountManageService } from '@qdy/common'
import { postReconnection } from '../interact/external.wechat'
import { AutoresponderOpuser } from 'apps/common/modules/autoresponderManage/autoresponderManage.dto'

@Injectable()
export class AccountWechatService {
  logger = new Logger('InteractWechatService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly socketService: AccountSocketService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly platformAccountManageService: PlatformAccountManageService
  ) {}

  checkOnline() {}

  async getLoginQrCode(regionId: string, appId: string = '') {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
    const { user } = this.request

    try {
      const team = await this.prisma.team.findUnique({
        where: {
          id: user.currentTeamId
        },
        include: {
          vip: true
        }
      })

      let newAppId = appId

      if (!appId) {
        newAppId = ''
        if (team.vip && team.vip.expirationTime && team.vip.expirationTime.getTime() < Date.now()) {
          // 新增账号限制，除了"青豆云掌柜"这个团队以外
          throw new ForbiddenException('非vip不能添加视频号')
        }
      } else {
        const platformInfo = await this.prisma.platformAccount.findFirst({
          where: {
            appId
          }
        })

        newAppId = platformInfo.isNew ? platformInfo.appId : ''

        if (!platformInfo) {
          throw new ForbiddenException('设备id不存在')
        }

        const platformAccountCount = await this.prisma.platformAccount.count({
          where: {
            appId: { not: appId },
            teamId: user.currentTeamId,
            status: AccountAccountsStatus.Normal
          }
        })

        if (
          team.vip.expirationTime &&
          team.vip.expirationTime.getTime() > Date.now() &&
          team.vip.platformAccountNumberLimit <= platformAccountCount
        ) {
          throw new ForbiddenException('账号数量已达上限')
        }
      }

      return await postAuthorizeByWechat({
        appId: newAppId,
        regionId,
        token: wechatConfig.NewToken,
        type: 'car'
      })
    } catch (err) {
      this.logger.error('获取二维码失败', err)
      throw new ForbiddenException(err)
    }
  }

  async checkLoginByWechat({ appId, uuid, captchCode, regionId }: WechatCheckLoginRequestDTO) {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const result = await postCheckLoginByWechat({
      appId,
      uuid,
      captchCode,
      token: wechatConfig.NewToken
    })

    this.logger.log(result)

    if (result.ret !== 200) {
      throw new HttpException(result?.data?.msg ?? result?.msg, -2)
    }

    try {
      const userInfo = result.data
      let hasChangeTeam = false

      if (userInfo.loginInfo && userInfo.loginInfo.wxid) {
        // 登录成功后写入数据库
        const oldPlatformAccount = await this.prisma.platformAccount.findUnique({
          where: {
            openId: userInfo.loginInfo.wxid,
            platform: Platform.Wechat
          },
          include: {
            affiliates: true
          }
        })

        // if (oldPlatformAccount) {
        //   // false 没有过期 true
        //   const isoverdue = oldPlatformAccount.expiresIn * 1000 < Date.now()

        //   if (!isoverdue && oldPlatformAccount.teamId !== teamMember.teamId) {
        //     // 没有过期的情况下不运行在其他团队下重新授权
        //     throw new ForbiddenException('账号已授权过，如需重新授权，请在手机上解除授权后再次授权')
        //   }
        // }

        const profileInfo = await postWechatProfile({
          appId,
          token: wechatConfig.NewToken
        })

        if (!profileInfo.mainFinderUsername) {
          throw new ForbiddenException('该账号没有开通视频号')
        }

        const profileUserInfo = profileInfo.aliasInfo.find((item) => item.roleType === 3)

        const value = {
          openId: userInfo.loginInfo.wxid,
          teamId: teamMember.teamId,
          name: profileUserInfo?.nickname ? profileUserInfo?.nickname : '',
          avatar: profileUserInfo?.headImgUrl ? profileUserInfo?.headImgUrl : '',
          accessToken: '',
          refreshToken: '',
          refreshExpiresIn: 0,
          expiresIn: **********,
          platform: Platform.Wechat,
          appId,
          regionId,
          username: profileInfo.mainFinderUsername,
          wechatInfo: JSON.stringify({ ...profileInfo, userInfo }),
          status: AccountAccountsStatus.Normal,
          isNew: true,
          affiliates: {
            connect: {
              id: teamMember.id
            }
          }
        }

        if (oldPlatformAccount && oldPlatformAccount.teamId !== teamMember.teamId) {
          hasChangeTeam = true
          await this.prisma.platformAccount.update({
            where: {
              id: oldPlatformAccount.id
            },
            data: {
              affiliates: {
                disconnect: oldPlatformAccount.affiliates.map((item) => ({ id: item.id }))
              }
            }
          })

          // 删除之前团队里面的自动回复数据
          await this.cacheManager.store.client.hdel(
            AutoresponderKeywordKey,
            oldPlatformAccount.openId
          )

          const userIds = oldPlatformAccount.affiliates.map((item) => item.userId)
          const teamMemberByOldPlatformAccount = await this.prisma.teamMember.findMany({
            where: {
              teamId: oldPlatformAccount.teamId,
              role: {
                not: TeamMemberRole.Member
              }
            }
          })

          const manageUserIds = teamMemberByOldPlatformAccount.map((item) => item.userId)

          const combinedAndUniqueIds = [...new Set([...userIds, ...manageUserIds])]

          const users = await this.prisma.user.findMany({
            where: {
              id: {
                in: combinedAndUniqueIds
              }
            }
          })
          if (users.length > 0) {
            for (let i = 0; i < users.length; i++) {
              const userInfo = users[i]
              this.logger.log(userInfo)
              const auth = await this.cacheManager.get<string>(userInfo.phone)
              if (auth) {
                await this.cacheManager.store.client.hdel(
                  genSocketRedisKey(auth),
                  oldPlatformAccount.openId
                )
              }
            }
          }

          // 删除旧账号的socketids
          await this.cacheManager.store.client.del(genSocketRedisKey(oldPlatformAccount.openId))

          // 该账号之前团队对应的自动回复移除该账号
          const autoresponderList = await this.prisma.autoresponder.findMany({
            where: {
              platformAccountIds: {
                array_contains: oldPlatformAccount.id
              }
            }
          })

          autoresponderList.forEach(async (item) => {
            if (item.platformAccountIds) {
              const accountIds = (item.platformAccountIds as number[]).filter(
                (id) => id !== oldPlatformAccount.id
              )
              await this.prisma.autoresponder.update({
                where: { id: item.id },
                data: { platformAccountIds: accountIds }
              })

              // 删除账号策略缓存
              await this.cacheManager.store.client.hdel(
                AutoresponderKeywordKey,
                oldPlatformAccount.openId
              )

              for (let i = 0; i < (item.opusers as unknown as AutoresponderOpuser[]).length; i++) {
                const opuser = item.opusers[i] as AutoresponderOpuser
                const opusersKey = `${oldPlatformAccount.openId}:${opuser.id}`
                await this.cacheManager.store.client.hdel(AutoresponderKeywordKey, opusersKey)
              }
            }
          })
        }

        const platformAccount = await this.prisma.platformAccount.upsert({
          create: value,
          where: {
            openId: userInfo.loginInfo.wxid,
            platform: Platform.Wechat
          },
          update: {
            ...value,
            isNew: true,
            createTime: new Date(),
            refreshTime: new Date(),
            tokenTime: new Date()
          }
        })

        await this.platformAccountManageService.updatePlatformAccountRedisInfo(platformAccount)

        const teamMembers = await this.prisma.teamMember.findMany({
          where: {
            teamId: teamMember.teamId,
            role: {
              not: TeamMemberRole.Member
            },
            userId: {
              not: user.id
            }
          },
          include: {
            user: true
          }
        })

        const socketIds = []

        for (let i = 0; i < teamMembers.length; i++) {
          const item = teamMembers[i]
          const [socketId, appSocketId] = await Promise.all([
            this.cacheManager.get<string>(genSocketRedisKey(item.userId)),
            this.cacheManager.get<string>(genSocketRedisKey(item.userId + 'app'))
          ])
          this.logger.debug(`teamMembers-item-${item.name}`, {
            socketId,
            reidsKey: genSocketRedisKey(item.userId),
            openId: platformAccount.id
          })
          await Promise.allSettled([
            (async () => {
              if (appSocketId) {
                socketIds.push(appSocketId)
                try {
                  const authApp = await this.cacheManager.get<string>(`${item.user.phone}app`)

                  if (authApp) {
                    await this.cacheManager.store.client.hset(
                      genSocketRedisKey(authApp),
                      platformAccount.openId,
                      platformAccount.id
                    )
                  }
                } catch (error) {
                  this.logger.error('更新缓存错误', error)
                }

                await this.cacheManager.set(genSocketRedisKey(item.userId + 'app'), appSocketId, 0)
              }
            })(),
            (async () => {
              if (socketId) {
                socketIds.push(socketId)
                try {
                  const auth = await this.cacheManager.get<string>(item.user.phone)

                  if (auth) {
                    await this.cacheManager.store.client.hset(
                      genSocketRedisKey(auth),
                      platformAccount.openId,
                      platformAccount.id
                    )
                  }
                } catch (error) {
                  this.logger.error('更新缓存错误', error)
                }

                await this.cacheManager.set(genSocketRedisKey(item.userId), socketId, 0)
              }
            })()
          ])
        }

        const [socketId, appSocketId] = await Promise.all([
          this.cacheManager.get<string>(genSocketRedisKey(user.id)),
          this.cacheManager.get<string>(genSocketRedisKey(user.id + 'app'))
        ])
        await Promise.allSettled([
          (async () => {
            if (appSocketId) {
              socketIds.push(appSocketId)
              try {
                const authApp = await this.cacheManager.get<string>(`${user.phone}app`)

                if (authApp) {
                  await this.cacheManager.store.client.hset(
                    genSocketRedisKey(authApp),
                    platformAccount.openId,
                    platformAccount.id
                  )
                }
              } catch (error) {
                this.logger.error('更新缓存错误', error)
              }

              await this.cacheManager.store.client.hset(
                genSocketRedisKey(platformAccount.openId),
                appSocketId,
                platformAccount.id
              )
            }
          })(),
          (async () => {
            if (socketId) {
              socketIds.push(socketId)
              try {
                const auth = await this.cacheManager.get<string>(user.phone)

                if (auth) {
                  await this.cacheManager.store.client.hset(
                    genSocketRedisKey(auth),
                    platformAccount.openId,
                    platformAccount.id
                  )
                }
              } catch (error) {
                this.logger.error('更新缓存错误', error)
              }

              await this.cacheManager.store.client.hset(
                genSocketRedisKey(platformAccount.openId),
                socketId,
                platformAccount.id
              )
            }
          })()
        ])

        this.socketService.socketService
          .send({
            list: JSON.stringify(
              socketIds.map((socketId) => ({
                socketId,
                data: {
                  type: 'changePlatformAccount',
                  data: [
                    {
                      action: 'add',
                      platform: platformAccount.platform,
                      name: platformAccount.name,
                      avatar: platformAccount.avatar,
                      openId: platformAccount.openId,
                      accountRole: platformAccount.accountRole,
                      id: platformAccount.id,
                      teamId: platformAccount.teamId,
                      expiresTime: **********
                    }
                  ]
                }
              }))
            )
          })
          .subscribe({
            next: () => {},
            error: (err) => {
              this.logger.error('发送失败', err)
            },
            complete: () => {}
          })

        eventEmitter.emit(updateOpusEventKey, { wxid: userInfo.loginInfo.wxid })

        return { ...userInfo, hasChangeTeam }
      }

      return userInfo
    } catch (err) {
      this.logger.error('获取二维码失败', err)
      throw new ForbiddenException(err)
    }
  }

  async reconnectionByWechat({ id }: { id: number }) {
    const { user } = this.request

    const account = await this.prisma.platformAccount.findUnique({
      where: {
        id,
        teamId: user.currentTeamId
      }
    })

    if (!account) {
      throw new ForbiddenException('账号不存在')
    }

    if (account.expiresIn <= 1) {
      throw new ForbiddenException('账号已过期')
    }

    try {
      await postReconnection({
        appId: account.appId,
        token: process.env.WECHAT_TOKEN
      })
    } catch (error) {
      this.logger.error('reconnectionByWechat error', error)
      await this.prisma.platformAccount.update({
        where: {
          id
        },
        data: {
          expiresIn: 0,
          refreshExpiresIn: 0
        }
      })

      await wechatLogout({
        appId: account.appId
      })

      throw new ForbiddenException(error.message)
    }
  }
}
