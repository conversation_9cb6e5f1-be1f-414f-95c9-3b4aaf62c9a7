import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class OverviewEntity {
  @Prop({
    type: Number,
    required: true,
    index: true
  })
  teamId: number

  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  // 自动私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoSingleCount: number

  // 自动群发数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoGroupCount: number

  // 自动评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  autoCommentCount: number

  // 私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  singleCount: number

  // 群发数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  groupCount: number

  // 评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  commentCount: number

  // 视频号私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatMessageCount: number

  // 视频号自动私信数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatAutoMessageCount: number

  // 视频号评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatCommentCount: number

  // 视频号自动评论数量
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatAutoCommentCount: number

  //评论人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  commentPeopleCount: number

  //私信人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  singlePeopleCount: number

  //视频号评论人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatCommentPeopleCount: number

  //微信私信人数
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  wechatSinglePeopleCount: number
}

export const OverviewSchema: ModelDefinition = {
  name: OverviewEntity.name,
  schema: SchemaFactory.createForClass(OverviewEntity)
}

export const OverviewMongoose = MongooseModule.forFeature([OverviewSchema])
