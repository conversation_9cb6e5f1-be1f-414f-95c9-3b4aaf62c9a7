import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsOptional } from 'class-validator'
import { Type } from 'class-transformer'

export const AutoresponderKeywordKey = 'autoresponder:keyword'

export enum AutoresponderKeywordRule {
  Match = 'match',
  Instantly = 'instantly',
  Comment = 'comment',
  Follow = 'follow',
  Like = 'like'
}

export enum SalesType {
  NotBuy = 'NotBuy',
  FirstBuy = 'FirstBuy',
  ReBuy = 'ReBuy'
}

export enum AutoresponderTriggerType {
  welcome,
  Chat,
  Comment,
  Follow,
  Like
}

export class Overview {
  @ApiResponseProperty({
    type: Number
  })
  totalCount: number

  @ApiResponseProperty({
    type: Number
  })
  singleTotalCount: number

  @ApiResponseProperty({
    type: Number
  })
  groupTotalCount: number

  @ApiResponseProperty({
    type: Number
  })
  commentTotalCount: number

  @ApiResponseProperty({
    type: Number
  })
  teamId: number

  @ApiResponseProperty({
    type: String
  })
  teamName: string

  @ApiResponseProperty({
    type: String
  })
  invitationCode: string

  @ApiResponseProperty({
    type: String
  })
  teamAvatar: string
}

export class OverviewType {
  @ApiResponseProperty({
    type: [Overview]
  })
  auto: Overview[]

  @ApiResponseProperty({
    type: [Overview]
  })
  normal: Overview[]
}

export class OverviewDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OverviewType
  })
  data: OverviewType
}

export class FinancialDataDTO {
  @ApiResponseProperty({
    type: Date
  })
  date: Date

  @ApiResponseProperty({
    type: Number
  })
  income: number

  @ApiResponseProperty({
    type: Number
  })
  expense: number
}
export class FinancialResponseDTO {
  @ApiResponseProperty({
    type: [FinancialDataDTO]
  })
  list: [FinancialDataDTO]
}
// 收入支出

// 用户注册
export class UserRegister {
  @ApiResponseProperty({
    type: String
  })
  date: string

  @ApiResponseProperty({
    type: Number
  })
  count: number
}

export class UserRegisterRespones {
  @ApiResponseProperty({
    type: [UserRegister]
  })
  list: UserRegister[]
}

export class UserRegisterResponesDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserRegisterRespones
  })
  data: UserRegisterRespones
}

export class TeamStatisticListRequest {
  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10

  @ApiProperty({
    type: Number,
    description: '到期时间开始时间',
    example: 0,
    required: false
  })
  // @IsNotEmpty({ message: '请选择开始时间' })
  @IsOptional()
  startTime: number

  @ApiProperty({
    type: Number,
    description: '到期时间结束时间',
    example: 0,
    required: false
  })
  // @IsNotEmpty({ message: '请选择结束时间' })
  @IsOptional()
  endTime: number
}

export class TeamStatistic {
  @ApiProperty({
    type: Number,
    description: '日期'
  })
  statisticDate: number

  @ApiProperty({
    type: Number,
    description: '注册团队数'
  })
  registerTeamCount: number

  @ApiProperty({
    type: Number,
    description: '付费团队数'
  })
  paidTeamCount: number

  @ApiProperty({
    type: Number,
    description: '转化率'
  })
  conversionRate: number

  @ApiProperty({
    type: Number,
    description: '过期团队数'
  })
  expiredTeamCount: number

  @ApiProperty({
    type: Number,
    description: '续费订单数'
  })
  renewTeamCount: number

  @ApiProperty({
    type: Number,
    description: '续费率'
  })
  renewRate: number

  @ApiProperty({
    type: Number,
    description: '实付订单总金额'
  })
  payAmountTotal: number

  @ApiProperty({
    type: Number,
    description: '客单价'
  })
  customerUnitPrice: number
}

export class TeamStatisticDTO {
  @ApiResponseProperty({
    type: [TeamStatistic]
  })
  data: TeamStatistic[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  total: number

  @ApiProperty({
    type: Number,
    description: '注册团队数',
    example: 100
  })
  registerTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '过期团队数',
    example: 100
  })
  expiredTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '付费团队数',
    example: 100
  })
  paidTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '续费团队数',
    example: 100
  })
  renewTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '转化率',
    example: 100
  })
  conversionRateTotal: number

  @ApiProperty({
    type: Number,
    description: '续费率',
    example: 100
  })
  renewRateTotal: number

  @ApiProperty({
    type: Number,
    description: '实付订单总金额',
    example: 100
  })
  payAmountTotal: number

  @ApiProperty({
    type: Number,
    description: '客单价',
    example: 100
  })
  customerUnitPrice: number
}
