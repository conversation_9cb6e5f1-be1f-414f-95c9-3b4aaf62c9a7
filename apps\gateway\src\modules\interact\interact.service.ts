import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { PrismaService } from '@qdy/mysql'
import {
  InteractGroupCreateDTO,
  InteractRequestAuditDTO,
  InteractRequestCommentMessage,
  InteractRequestSendMessage,
  InteractRequestSpeech,
  InteractRequestSpeechCategory,
  InteractRequestTopComment,
  MessageUserRequestDTO,
  RecallMessageRequestDTO,
  SpeechType,
  UserMessageReadRecordRequestDTO
} from './interact.dto'
import {
  getFanGroups,
  getVideoList,
  postAuditFans,
  postClientToken,
  postCreateFanGroups,
  postRecallMsg,
  postReplyComment,
  postSendMessage,
  postTopComment,
  postUploadImageFile,
  postVideoDetail
} from './external'
import { InjectModel } from '@nestjs/mongoose'
import {
  PersonalChatMessageEntity,
  UserMessageReadRecordEntity,
  WorkCommentEntity
} from '@qdy/mongo'
import type { Model, AnyObject } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { RootConfigMap } from '@qdy/config'
import { ConfigService } from '@nestjs/config'
import { WebhookEvents } from '../webhook/constant'
import { TeamMemberRole } from '../team/team.dto'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { AccountAccountsStatus } from '../account/account.dto'
import { genSocketRedisKey } from 'packages/utils'
import { AccountSocketService } from '../account/account.task'

@Injectable()
export class InteractService {
  logger = new Logger('InteractService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(WorkCommentEntity.name)
    private workCommentModel: Model<WorkCommentEntity>,
    @InjectModel(UserMessageReadRecordEntity.name)
    private userMessageReadRecordModel: Model<UserMessageReadRecordEntity>,
    private readonly socketService: AccountSocketService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async getSessionConfig() {
    const { user } = this.request

    const res = await this.prisma.sessionConfig.findMany({
      where: {
        userId: user.id
      }
    })

    return res.map((item) => {
      const sessionId = item.sessionId.split('__session__')[0]
      return {
        sessionId,
        top: item.top,
        mute: item.mute
      }
    })
  }

  async getGroupsByAccountId(platformAccountId: number, autoresponder: boolean) {
    if (!platformAccountId) {
      throw new ForbiddenException('缺少参数: platformAccountId')
    }

    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        teamId: teamMember.teamId,
        id: platformAccountId
      }
    })

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在')
    }

    // if (!platformAccount.accountRole) {
    //   return []
    // }

    try {
      const res = await getFanGroups({
        accessToken: platformAccount.accessToken,
        openId: platformAccount.openId,
        platformAccountId,
        teamId: teamMember.teamId
      })

      const groups = []

      if (autoresponder) {
        // const groupIds = res.map((item) => item.groupId)

        // const greetings = await this.prisma.greeting.findMany({
        //   where: {
        //     groups: {
        //       path: '$[*].id',
        //       array_contains: groupIds
        //     }
        //   }
        // })

        // const greetingInGroups = new Set(
        //   greetings.map((item) => (item.groups as { id: string }[]).map(({ id }) => id)).flat()
        // )

        // res.forEach((item) => {
        //   groups.push({
        //     ...item,
        //     isGreeting: greetingInGroups.has(item.groupId)
        //   })
        // })
        groups.push(...res)
      } else {
        groups.push(...res)
      }

      return groups
    } catch (e) {
      this.logger.error(e)
      return []
    }
  }

  async updateSessionConfig({
    sessionId,
    top,
    mute
  }: {
    top?: boolean
    mute?: boolean
    sessionId: string
  }) {
    if ((mute === undefined && top === undefined) || !sessionId) {
      throw new ForbiddenException('参数错误')
    }
    const { user } = this.request

    await this.prisma.sessionConfig.upsert({
      where: { sessionId: `${sessionId}__session__${user.id}`, userId: user.id },
      create: {
        sessionId: `${sessionId}__session__${user.id}`,
        userId: user.id,
        top,
        mute
      },
      update: {
        top,
        mute
      }
    })
  }

  async updateSpeech(speechId: number, content: string, speechCategoryId: number) {
    if (!speechId) {
      throw new ForbiddenException('缺少参数: speechId')
    }
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    if (speechCategoryId) {
      const speechCategory = await this.prisma.speechCategory.findUnique({
        where: {
          id: speechCategoryId // 0 为默认分类 无需校验
        }
      })
      if (!speechCategory) {
        throw new ForbiddenException('分类不存在')
      }
    }

    await this.prisma.speech.update({
      where: { id: speechId },
      data: { content, speechCategoryId: speechCategoryId || 0 }
    })
  }

  async deleteSpeech(speechId: number) {
    if (!speechId) {
      throw new ForbiddenException('缺少参数: speechId')
    }
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const speech = await this.prisma.speech.findUnique({
      where: {
        id: speechId
      }
    })

    if (!speech) {
      throw new NotFoundException('策略不存在')
    }

    await this.prisma.speech.delete({
      where: { id: speechId, teamId: teamMember.teamId }
    })
  }

  async getSpeechCategories() {
    const { user } = this.request

    const result = await this.prisma.speechCategory
      .findMany({
        where: {
          teamId: user.currentTeamId
        },
        select: {
          id: true,
          name: true
        },
        orderBy: {
          id: 'desc'
        }
      })
      .then((categories) => categories.map((category) => ({ ...category, count: 0 })))

    const countByCategory = await this.prisma.speech.groupBy({
      where: {
        teamId: user.currentTeamId,
        speechType: SpeechType.Team
      },
      by: ['speechCategoryId'],
      _count: {
        _all: true
      }
    })

    // 循环result,根据分类id获取对应数量
    result.forEach((category) => {
      const count =
        countByCategory.find((item) => item.speechCategoryId === category.id)?._count._all || 0
      category.count = count
    })

    const uncategorizedCount =
      countByCategory.find((item) => item.speechCategoryId === 0)?._count._all || 0

    result.unshift({
      id: 0,
      name: '未分类',
      count: uncategorizedCount
    })

    return result
  }

  async createSpeechCategory({ name }: InteractRequestSpeechCategory) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    await this.prisma.speechCategory.create({
      data: {
        name,
        teamId: teamMember.teamId
      }
    })
  }

  async updateSpeechCategory(speechCategoryId: number, name: string) {
    if (!speechCategoryId) {
      throw new ForbiddenException('缺少参数: speechCategoryId')
    }
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const speechCategory = await this.prisma.speechCategory.findUnique({
      where: {
        id: speechCategoryId // 0 为默认分类 无需校验
      }
    })
    if (!speechCategory) {
      throw new ForbiddenException('分类不存在')
    }

    await this.prisma.speechCategory.update({
      where: { id: speechCategoryId, teamId: teamMember.teamId },
      data: { name }
    })
  }

  async deleteSpeechCategory(speechCategoryId: number) {
    if (!speechCategoryId) {
      throw new ForbiddenException('缺少参数: speechCategoryId')
    }
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    await this.prisma.speech.updateMany({
      where: { speechCategoryId },
      data: { speechCategoryId: 0 }
    })

    await this.prisma.speechCategory.delete({
      where: { id: speechCategoryId, teamId: teamMember.teamId }
    })
  }

  async getSpeechs({
    speechType,
    content,
    speechCategoryId,
    page = 1,
    size = 10
  }: {
    speechType: number
    content: string
    speechCategoryId: number
    page: number
    size: number
  }) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    let contentWhere = {}
    if (content) {
      contentWhere = {
        content: {
          contains: content
        }
      }
    }

    let speechTypeWhere = {}
    if (!speechType) {
      speechTypeWhere = {
        teamId: teamMember.teamId
      }
    } else {
      speechTypeWhere = {
        userId: user.id
      }
    }

    let speechCategoryWhere = {}
    if (speechCategoryId >= 0) {
      speechCategoryWhere = {
        speechCategoryId
      }
    }

    const total = await this.prisma.speech.count({
      where: {
        speechType,
        ...contentWhere,
        ...speechTypeWhere,
        ...speechCategoryWhere
      }
    })

    const speechList = await this.prisma.speech.findMany({
      where: {
        speechType,
        ...contentWhere,
        ...speechTypeWhere,
        ...speechCategoryWhere
      },
      orderBy: {
        id: 'desc'
      },
      skip: (page - 1) * size,
      take: size
    })

    return {
      total,
      page,
      size,
      data: speechList
    }
  }

  async createSpeechs({ content, speechType, speechCategoryId }: InteractRequestSpeech) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    if (speechCategoryId) {
      const speechCategory = await this.prisma.speechCategory.findUnique({
        where: {
          id: speechCategoryId
        }
      })

      if (!speechCategory) {
        throw new ForbiddenException('分类不存在')
      }
    }

    await this.prisma.speech.create({
      data: {
        content,
        teamId: teamMember.teamId,
        userId: speechType === 1 ? user.id : 0,
        speechType,
        speechCategoryId: speechCategoryId || 0
      }
    })
  }

  async createGroupByAccountId(platformAccountId: number, data: InteractGroupCreateDTO) {
    if (!platformAccountId) {
      throw new ForbiddenException('缺少参数: platformAccountId')
    }

    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId
      }
    })

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在')
    }

    const openApiData = {
      avatar_uri: data.avatar,
      description: data.description,
      group_name: data.name,
      active_fans: data.doorsill,
      allow_invite: data.allowInvitations,
      fans_limit: data.joinGroupLevel,
      group_type: data.groupType,
      item_auto_sync: data.opusAsync,
      live_auto_sync: data.liveAsync,
      open_audit_switch: data.joinCheck,
      relation_type: data.joinAttention,
      show_at_profile: data.showHomePage
    }

    const groupId = await postCreateFanGroups(openApiData)

    return groupId
  }

  async sendMessage(data: InteractRequestSendMessage) {
    const { platformAccount, teamMember } = await this.checkUserInPlatformAccount(
      data.platformAccountId
    )

    const res = await postSendMessage({
      platformAccountId: platformAccount.id,
      teamId: platformAccount.teamId,
      accessToken: platformAccount.accessToken,
      openId: platformAccount.openId,
      content: data.content,
      messageType: data.messageType,
      messageId: data.messageId,
      conversationId: data.conversationId,
      toUserId: data.toUserId,
      sendType: data.sendType,
      redisClient: this.cacheManager
    })

    await this.prisma.teamMember.update({
      where: {
        id: teamMember.id
      },
      data: {
        replyMessage: {
          increment: 1
        }
      }
    })

    return {
      messageId: res.msg_id
    }
  }

  async sendReplyMessage(data: InteractRequestCommentMessage) {
    const { platformAccount, teamMember } = await this.checkUserInPlatformAccount(
      data.platformAccountId
    )

    const res = await postReplyComment({
      teamId: platformAccount.teamId,
      platformAccountId: platformAccount.id,
      accessToken: platformAccount.accessToken,
      openId: platformAccount.openId,
      commentId: data.commentId,
      content: data.content,
      itemId: data.itemId,
      redisClient: this.cacheManager,
      commentUserId: ''
    })

    await this.prisma.teamMember.update({
      where: {
        id: teamMember.id
      },
      data: {
        replyMessage: {
          increment: 1
        }
      }
    })

    return res
  }

  async updateCommentTop(data: InteractRequestTopComment) {
    const { platformAccount } = await this.checkUserInPlatformAccount(data.platformAccountId)

    await postTopComment({
      accessToken: platformAccount.accessToken,
      openId: platformAccount.openId,
      commentId: data.commentId,
      top: data.top,
      itemId: data.sessionId
    })

    await this.workCommentModel.updateOne(
      { 'content.commentId': data.commentId },
      { top: data.top }
    )
  }

  private async checkUserInPlatformAccount(platformAccountId: number) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true,
        team: {
          include: {
            vip: true
          }
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    if (teamMember.role === TeamMemberRole.Member) {
      const platformAccount = teamMember.platformAccounts.find(
        (item) => item.id === platformAccountId
      )

      if (!platformAccount) {
        throw new ForbiddenException('账号不存在')
      }

      if (platformAccount.status === AccountAccountsStatus.Disable) {
        throw new ForbiddenException('账号已冻结')
      }

      return { platformAccount, teamMember }
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId,
        teamId: teamMember.teamId
      }
    })

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在')
    }

    if (platformAccount.status === AccountAccountsStatus.Disable) {
      throw new ForbiddenException('账号已冻结')
    }

    return { platformAccount, teamMember }
  }

  async getSingleHistoryMessages({
    createTime,
    platformAccountId,
    sessionId
  }: {
    createTime: number
    platformAccountId: number
    sessionId: string
  }) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('用户未加入此团队')
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    const platformAccount = platformAccounts.find((item) => item.id === platformAccountId)

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在或你不属于运营人员')
    }

    const query = {
      createTime: { $lt: createTime }
    }

    if (!createTime) {
      delete query.createTime
    }

    return this.personalChatMessageModel
      .find({
        openId: platformAccount.openId,
        sessionId,
        ...query
      })
      .sort({ createdAt: -1 })
      .limit(50)
      .allowDiskUse(true) // 允许使用磁盘
      .lean()
  }

  async getHistoryMessages(createTime?: number, platformAccountId?: number) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    if (platformAccountId) {
      platformAccounts = platformAccounts.filter((item) => item.id === platformAccountId)
    }

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const comparisonDate =
      createTime && createTime > sevenDaysAgo.getTime() ? createTime : sevenDaysAgo.getTime()

    const platformAccountMap: Record<string, AnyObject[]> = {}

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]
      const { id } = platformAccount
      platformAccountMap[id] = []

      if (
        platformAccount.tokenTime.getTime() + platformAccount.expiresIn * 1000 * 5 * 2 >
        Date.now()
      ) {
        const sessionIds = await this.getMessagesActiveSessionIds(
          platformAccount.openId,
          comparisonDate
        )

        const batchPromises = sessionIds.map((id) =>
          this.getMessagesForSessionIds(id, platformAccount.openId, comparisonDate)
        )

        // 并行处理每一批
        const batchResults = await Promise.all(batchPromises)

        for (let j = 0; j < batchResults.length; j++) {
          const item = batchResults[j]

          item.forEach((doc) => {
            platformAccountMap[id].unshift({
              platformAccountId: id,
              id: doc._id.toString(),
              ...doc
            })
          })

          platformAccountMap[platformAccount.id].sort((a, b) => a.createTime - b.createTime)
        }
      }
    }

    return platformAccountMap
  }

  async getHistoryMessagesV2(
    createTime?: number,
    platformAccountId?: number,
    page?: number,
    size?: number
  ) {
    const { user } = this.request

    const skip = (page - 1) * size

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    if (platformAccountId) {
      platformAccounts = platformAccounts.filter((item) => item.id === platformAccountId)
    }

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    const comparisonDate =
      createTime && createTime > sevenDaysAgo.getTime() ? createTime : sevenDaysAgo.getTime()

    const platformAccountIds: string[] = []
    const platformAccountMapByOpenId: Record<string, number> = {}

    for (let i = 0; i < platformAccounts.length; i++) {
      const platformAccount = platformAccounts[i]

      platformAccountMapByOpenId[platformAccount.openId] = platformAccount.id

      if (
        platformAccount.tokenTime.getTime() + platformAccount.expiresIn * 1000 * 5 * 2 >
        Date.now()
      ) {
        platformAccountIds.push(platformAccount.openId)
      }
    }

    if (platformAccountIds.length > 0) {
      const result = await this.personalChatMessageModel
        .find({
          createTime: {
            $gt: comparisonDate
          },
          openId: {
            $in: platformAccountIds
          }
        })
        .sort({ createTime: 1 })
        .skip(skip)
        .limit(size)
        .lean()

      const total = await this.personalChatMessageModel.countDocuments({
        createTime: {
          $gt: comparisonDate
        },
        openId: {
          $in: platformAccountIds
        }
      })

      return {
        data: result.map((doc) => ({
          ...doc,
          platformAccountId: platformAccountMapByOpenId[doc.openId]
        })),
        total,
        page,
        size
      }
    }

    return []
  }

  async getMessagesActiveSessionIds(openId: string, comparisonDate: number) {
    return this.personalChatMessageModel.distinct('sessionId', {
      createdAt: {
        $gt: comparisonDate
      },
      openId
    })
  }

  async getMessagesForSessionIds(sessionId: string, openId: string, comparisonDate: number) {
    return this.personalChatMessageModel
      .find({
        openId,
        sessionId,
        createTime: {
          $gt: comparisonDate
        }
      })
      .sort({ openId: 1, sessionId: 1, createTime: -1 })
      .limit(25)
      .allowDiskUse(true) // 允许使用磁盘
      .lean()
  }

  async getCommentActiveSessionIds(openId: string, comparisonDate: number) {
    return this.workCommentModel.distinct('sessionId', {
      createTime: {
        $gt: comparisonDate
      },
      openId
    })
  }

  async getCommentForSessionIds(sessionId: string, openId: string, comparisonDate: number) {
    return this.workCommentModel
      .find({
        sessionId,
        openId,
        createTime: {
          $gt: comparisonDate
        }
      })
      .sort({ openId: 1, sessionId: 1, createTime: -1 })
      .limit(25)
      .allowDiskUse(true) // 允许使用磁盘
      .lean()
  }

  async getSingleHistoryComments({
    createTime,
    platformAccountId,
    sessionId
  }: {
    createTime: number
    platformAccountId: number
    sessionId: string
  }) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('用户未加入此团队')
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    const platformAccount = platformAccounts.find((item) => item.id === platformAccountId)

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在或你不属于运营人员')
    }

    const query = {
      createTime: { $lt: createTime }
    }

    if (!createTime) {
      delete query.createTime
    }

    return this.workCommentModel
      .find({
        sessionId,
        ...query
      })
      .sort({ createTime: -1 })
      .limit(50)
      .exec()
  }

  async getHistoryComments(createTime?: number, platformAccountId?: number) {
    try {
      const { user } = this.request

      const teamMember = await this.prisma.teamMember.findUnique({
        where: {
          userId_teamId: {
            teamId: user.currentTeamId,
            userId: user.id
          }
        },
        include: {
          platformAccounts: true
        }
      })

      if (!teamMember) {
        throw new HttpException('用户未加入团队', -1)
      }

      let { platformAccounts } = teamMember

      if (teamMember.role !== TeamMemberRole.Member) {
        platformAccounts = await this.prisma.platformAccount.findMany({
          where: {
            teamId: teamMember.teamId
          }
        })
      }

      if (platformAccountId) {
        platformAccounts = platformAccounts.filter((item) => item.id === platformAccountId)
      }

      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      const comparisonDate =
        createTime && createTime > sevenDaysAgo.getTime() ? createTime : sevenDaysAgo.getTime()

      const platformAccountMap: Record<string, AnyObject[]> = {}

      for (let i = 0; i < platformAccounts.length; i++) {
        const platformAccount = platformAccounts[i]

        if (
          platformAccount.tokenTime.getTime() + platformAccount.expiresIn * 1000 * 5 * 2 >
          Date.now()
        ) {
          const { id } = platformAccount
          platformAccountMap[id] = []
          const sessionIds = await this.getCommentActiveSessionIds(
            platformAccount.openId,
            comparisonDate
          )

          const batchPromises = sessionIds.map((id) =>
            this.getCommentForSessionIds(id, platformAccount.openId, comparisonDate)
          )

          // 并行处理每一批
          const batchResults = await Promise.all(batchPromises)

          for (let j = 0; j < batchResults.length; j++) {
            const item = batchResults[j]

            item.forEach((doc) => {
              platformAccountMap[id].unshift({
                platformAccountId: id,
                id: doc._id.toString(),
                ...doc
              })
            })

            platformAccountMap[platformAccount.id].sort((a, b) => a.createTime - b.createTime)
          }
        }
      }

      return platformAccountMap
    } catch (error) {
      this.logger.error(error)
      throw new ForbiddenException(`[获取历史评论错误]`)
    }
  }

  async getVideoList(
    platformAccountId: number,
    { cursor, size }: { cursor: number; size: number }
  ) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const where = {
      id: platformAccountId,
      teamId: teamMember.teamId,
      affiliates: {
        some: {
          userId: user.id
        }
      }
    }

    if (teamMember.role !== TeamMemberRole.Member) {
      delete where.affiliates
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where
    })

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在')
    }

    const res = await getVideoList({
      accessToken: platformAccount.accessToken,
      openId: platformAccount.openId,
      cursor,
      count: size
    })

    return {
      ...res,
      list: ((res.list as AnyObject[]) ?? []).filter(
        (item) => item.video_status !== 2 && item.video_status !== 4 && item.video_status !== 7
        // &&
        // item.video_status !== 6
      )
    }
  }

  async uploadImage(image: Buffer, filename: string) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      },
      include: {
        team: true
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const { clientKey, clientSecret, secondClientKey, secondClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')

    let accountByClientKey = clientKey
    let accountByClientSecret = clientSecret
    let keyFlag = 'clientToken'

    if (teamMember.team.douyinClientKey) {
      if (teamMember.team.douyinClientKey === secondClientKey) {
        accountByClientKey = secondClientKey
        accountByClientSecret = secondClientSecret
        keyFlag = 'secondClientToken'
      }
    }

    let clientToken = await this.cacheManager.get(keyFlag)

    if (!clientToken) {
      const clientTokenInfo = await postClientToken({
        clientSecret: accountByClientSecret,
        clientKey: accountByClientKey
      })

      await this.cacheManager.set(
        keyFlag,
        clientTokenInfo.access_token,
        clientTokenInfo.expires_in * 1000
      )

      clientToken = clientTokenInfo.access_token
    }

    const res = await postUploadImageFile(image, filename, clientToken as string)

    return res
  }

  async getVideoDetail({
    platformAccountId,
    itemIds,
    videoIds
  }: {
    platformAccountId: number
    itemIds: string[]
    videoIds: string[]
  }) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const where = {
      id: platformAccountId,
      teamId: teamMember.teamId,
      affiliates: {
        some: {
          userId: user.id
        }
      }
    }

    if (teamMember.role !== TeamMemberRole.Member) {
      delete where.affiliates
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where
    })

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在')
    }

    return postVideoDetail({
      openId: platformAccount.openId,
      accessToken: platformAccount.accessToken,
      itemIds,
      videoIds
    })
  }

  async updateJoinGroupState(platformAccountId: number, body: InteractRequestAuditDTO) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new HttpException('用户未加入团队', -1)
    }

    const where = {
      id: platformAccountId,
      teamId: teamMember.teamId,
      affiliates: {
        some: {
          userId: user.id
        }
      }
    }

    if (teamMember.role !== TeamMemberRole.Member) {
      delete where.affiliates
    }

    const platformAccount = await this.prisma.platformAccount.findUnique({
      where
    })

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在')
    }

    return postAuditFans({
      accessToken: platformAccount.accessToken,
      applyId: body.applyId,
      openId: platformAccount.openId,
      status: body.status
    })
  }

  async postRecallMessage({ uniqueId, platformAccountId }: RecallMessageRequestDTO) {
    const account = await this.prisma.platformAccount.findUnique({
      where: {
        id: platformAccountId
      }
    })

    if (!account) {
      throw new NotFoundException('账号不存在')
    }

    if (account.status === AccountAccountsStatus.Disable) {
      throw new ForbiddenException('此账号已被冻结')
    }

    const message = await this.personalChatMessageModel.findOne({
      openId: account.openId,
      uniqueId
    })

    let conversationType = 1

    if (!message) {
      throw new NotFoundException('消息不存在')
    }

    if (message.isRecall) {
      throw new NotFoundException('此消息已删除')
    }

    if (account.openId !== message.fromUserId) {
      throw new NotFoundException('只能撤回自己发送的消息')
    }

    const allowEvents = [WebhookEvents.IMGroupSendMessage, WebhookEvents.IMSendMessage].map(
      (event) => event.toString()
    )

    if (!allowEvents.includes(message.event)) {
      throw new NotFoundException('此消息类型不支持删除')
    }

    if (message.event.includes('group')) {
      conversationType = 2
    }

    const conversationShortId = message.content.get('conversationShortId')
    const serverMessageId = message.content.get('serverMessageId')

    if (!conversationShortId || !serverMessageId) {
      throw new NotFoundException('此消息类型不支持删除')
    }

    const data = await postRecallMsg({
      accessToken: account.accessToken,
      openId: account.openId,
      conversationId: conversationShortId,
      conversationType,
      msgId: serverMessageId
    })

    if (data.err_no === 0) {
      await this.personalChatMessageModel.findByIdAndUpdate(message.id, { isRecall: 1 })
    }
  }

  async getMessageUser(openId: string) {
    const messageUser = await this.prisma.privateMessageUser.findUnique({
      where: {
        openId
      },
      include: {
        labels: {
          select: {
            id: true,
            title: true
          }
        }
      }
    })

    return messageUser
  }

  async postMessageUser(data: MessageUserRequestDTO) {
    const { user } = this.request

    const labels = await this.prisma.privateMessageUserLabel.findMany({
      where: {
        teamId: user.currentTeamId,
        id: {
          in: data.labels
        }
      }
    })

    const value = {
      openId: data.openId,
      phone: data.phone,
      wechat: data.wechat,
      remark: data.remark,
      province: data.province ? data.province : 0,
      city: data.city ? data.city : 0,
      labels: {
        connect: labels.map((label) => ({ id: label.id }))
      }
    }

    return this.prisma.privateMessageUser.upsert({
      create: value,
      where: {
        openId: data.openId
      },
      update: {
        ...value,
        createTime: new Date()
      }
    })
  }

  async postUserMessageReadRecord(
    { uniqueId, openId, sessionId, messageTime }: UserMessageReadRecordRequestDTO,
    deviceType?: string
  ) {
    if (deviceType && deviceType !== 'app') {
      throw new BadRequestException('无效的设备类型')
    }

    this.logger.log(openId)

    const { user } = this.request

    const lastUserMessage = await this.userMessageReadRecordModel.findOne({
      userId: user.id,
      openId,
      sessionId
    })

    const source = deviceType === 'app' ? 'app' : 'web'

    if (lastUserMessage) {
      await this.userMessageReadRecordModel.findByIdAndUpdate(lastUserMessage.id, {
        uniqueId,
        openId,
        sessionId,
        messageTime,
        source
      })
    } else {
      await this.userMessageReadRecordModel.create({
        uniqueId,
        openId,
        sessionId,
        userId: user.id,
        messageTime,
        source
      })
    }

    // 发送socket消息
    const [socketId, appSocketId] = await Promise.all([
      this.cacheManager.get<string>(genSocketRedisKey(user.id)),
      this.cacheManager.get<string>(genSocketRedisKey(user.id + 'app'))
    ])

    const socketIds: string[] = []

    if (socketId) {
      socketIds.push(socketId)
    }

    if (appSocketId) {
      socketIds.push(appSocketId)
    }

    if (socketIds.length) {
      try {
        this.socketService.socketService
          .send({
            list: JSON.stringify(
              socketIds.map((socketId) => ({
                socketId,
                data: {
                  type: 'userMessageReadRecord',
                  data: [
                    {
                      openId,
                      uniqueId,
                      sessionId,
                      messageTime,
                      source,
                      userId: user.id
                    }
                  ]
                }
              }))
            )
          })
          .subscribe({
            next: () => {},
            error: (err) => {
              throw new BadRequestException(`发送失败 error${err.message}`)
            },
            complete: () => {}
          })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }
  }

  async getUserMessageReadRecord() {
    const { user } = this.request

    const result = await this.userMessageReadRecordModel.find({
      userId: user.id
    })

    return result
  }
}
