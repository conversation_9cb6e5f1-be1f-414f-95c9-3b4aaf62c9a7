import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { PrismaService } from 'packages/mysql/lib'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import {
  orderPriceRequestDTO,
  OrderRequestCreateOrderDTO,
  OrderStatus,
  PayType,
  RenewOrderRequest,
  UpgradeOrderRequest
} from './order.dto'
import { orderEventEmitter, eventKey } from './order.event'
import { OrderManageService } from '@qdy/common'

@Injectable()
export class OrderService {
  logger = new Logger('order')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly orderManageService: OrderManageService
  ) {}

  async getOrders({ page = 1, size = 10 }: { page: number; size: number }) {
    const { user } = this.request

    if (!user.currentTeamId) {
      throw new NotFoundException('团队不存在')
    }

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const total = await this.prisma.order.count({
      where: {
        teamId: teamMember.teamId
      }
    })

    const orders = await this.prisma.order.findMany({
      where: {
        teamId: teamMember.teamId
      },
      include: {
        team: true
      },
      orderBy: {
        fromTime: 'desc'
      },
      skip: (page - 1) * size,
      take: size
    })

    return {
      total,
      page,
      size,
      data: orders.map((item) => ({
        id: item.id,
        orderNo: item.orderNo,
        team: item.team,
        isUpgrade: item.isUpgrade,
        orderStatus: item.orderStatus,
        orderType: item.orderType,
        type: item.type,
        fromTime: item.fromTime.getTime(),
        payTime: item.payTime ? item.payTime.getTime() : 0,
        price: item.price,
        dueAmount: item.dueAmount,
        payAmount: item.payAmount,
        payType: item.payType,
        expireTime: item.expireTime.getTime(),
        remainingTimeInSeconds:
          item.expireTime.getTime() > Date.now() ? item.expireTime.getTime() - Date.now() : 0
      }))
    }
  }

  async calculateOrderPrice(body: orderPriceRequestDTO) {
    const { user } = this.request

    return this.orderManageService.calculateOrderPrice({
      userId: user.id,
      teamId: user.currentTeamId,
      interestCount: body.interestCount,
      interestId: body.interestId,
      couponId: body.couponId,
      month: body.month,
      days: 0,
      orderType: body.orderType
    })
  }

  async createOrder({
    interestId,
    interestCount,
    month,
    couponId,
    isCorporateTransfer
  }: OrderRequestCreateOrderDTO) {
    const { user } = this.request

    const unexpiredOrderCount = await this.prisma.order.count({
      where: {
        teamId: user.currentTeamId,
        orderStatus: OrderStatus.PENDING,
        expireTime: {
          gt: new Date()
        }
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new HttpException('还有未支付的订单', -101)
    }

    const orderNo = await this.orderManageService.createOrder({
      teamId: user.currentTeamId,
      userId: user.id,
      interestCount,
      interestId,
      month,
      days: 0,
      couponId,
      isPay: true,
      isCorporateTransfer
    })

    if (isCorporateTransfer === undefined || isCorporateTransfer === false) {
      // 对公转账没有15分钟自动取消订单
      orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })
    }

    return { orderNo }
  }

  async createUpgradeOrder({
    interestId,
    interestCount,
    couponId,
    isCorporateTransfer
  }: UpgradeOrderRequest) {
    const { user } = this.request

    const unexpiredOrderCount = await this.prisma.order.count({
      where: {
        teamId: user.currentTeamId,
        orderStatus: OrderStatus.PENDING,
        expireTime: {
          gt: new Date()
        }
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new HttpException('还有未支付的订单', -101)
    }

    const orderNo = await this.orderManageService.upgradeOrder({
      teamId: user.currentTeamId,
      userId: user.id,
      interestCount,
      interestId,
      isPay: true,
      couponId,
      isCorporateTransfer
    })

    if (isCorporateTransfer === undefined || isCorporateTransfer === false) {
      orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })
    }

    return {
      orderNo
    }
  }

  async createRenewOrder({ interestId, month, couponId, isCorporateTransfer }: RenewOrderRequest) {
    const { user } = this.request

    const unexpiredOrderCount = await this.prisma.order.count({
      where: {
        teamId: user.currentTeamId,
        orderStatus: OrderStatus.PENDING,
        expireTime: {
          gt: new Date()
        }
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new HttpException('还有未支付的订单', -101)
    }

    const orderNo = await this.orderManageService.renewOrder({
      teamId: user.currentTeamId,
      userId: user.id,
      interestId,
      days: 0,
      isPay: true,
      isCorporateTransfer,
      month,
      couponId
    })

    if (isCorporateTransfer === undefined || isCorporateTransfer === false) {
      orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })
    }

    return {
      orderNo
    }
  }

  async getOrderStatus(orderNo: string) {
    const { user } = this.request

    if (!user.currentTeamId) {
      throw new NotFoundException('团队不存在')
    }

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const order = await this.prisma.order.findUnique({
      where: {
        orderNo
      }
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.teamId !== user.currentTeamId) {
      throw new NotFoundException('没有权限查看此订单')
    }

    return {
      orderStatus: order.orderStatus
    }
  }

  /**
   * 取消支付
   * @param orderNumber
   */
  async putOrderStatus(orderNo: string, orderStatus: string) {
    const { user } = this.request

    const order = await this.prisma.order.findUnique({
      where: {
        orderNo
      }
    })

    if (orderStatus !== OrderStatus.CANCELED) {
      throw new NotFoundException('状态不正确')
    }

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.teamId !== user.currentTeamId) {
      throw new ForbiddenException('当前订单不属于当前团队，无法取消')
    }

    if (order.orderStatus !== OrderStatus.PENDING) {
      throw new ForbiddenException('订单不是待支付状态，无法取消')
    }

    try {
      await this.prisma.order.update({
        where: {
          orderNo
        },
        data: {
          orderStatus: OrderStatus.CANCELED
        }
      })
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
    } catch (error) {
      throw new ForbiddenException(error)
    }
  }

  async getOrderInfo(orderNo: string) {
    const { user } = this.request

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const order = await this.prisma.order.findUnique({
      where: {
        orderNo
      },
      include: {
        orderInfos: true,
        interest: true,
        vip: true,
        team: true
      }
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.teamId !== user.currentTeamId) {
      throw new NotFoundException('没权获取当前订单信息')
    }
    let usersCouponPrice = 0
    if (order.usersCouponId) {
      const coupon = await this.prisma.usersCoupons.findUnique({
        where: {
          id: order.usersCouponId
        }
      })
      if (coupon) {
        usersCouponPrice = coupon.discountAmount
      }
    }

    return {
      vipInfo: {
        ...order.vip,
        interestCount: order.interestCount,
        platformAccountCount: order.interest.platformAccountCount * order.interestCount,
        teamMemberCount: order.interest.memberCount * order.interestCount,
        messageCount: order.interest.messageCount * order.interestCount,
        month: order.vipMonth,
        freeMonth: order.freeMonth,
        expirationTime:
          order.vip.expirationTime && order.vip.expirationTime.getTime() > Date.now()
            ? order.vip.expirationTime.getTime()
            : 0
      },
      orderInfo: {
        orderNo: order.orderNo,
        teamName: order.team.name,
        fromTime: order.fromTime.getTime(),
        orderStatus:
          order.orderStatus === OrderStatus.PENDING && order.expireTime.getTime() < Date.now()
            ? OrderStatus.CANCELED
            : order.orderStatus,
        type: order.type,
        invitationCode: order.team.invitationCode,
        diffPrice: parseFloat(order.priceDiff.toFixed(2)) || 0,
        couponPrice: usersCouponPrice,
        price: parseFloat(order.price.toFixed(2)),
        discountAmount: parseFloat((order.price - order.dueAmount).toFixed(2)),
        dueAmount: order.dueAmount || 0,
        payAmount: order.payAmount || 0,
        payTime: order.payTime ? order.payTime.getTime() : 0,
        payType: order.payType
      }
    }
  }

  async getPayInfo(orderNo: string) {
    const { user } = this.request
    const { serviceCodeUrl } = this.configService.get<RootConfigMap['app']>('app')

    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId: user.currentTeamId,
          userId: user.id
        }
      }
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const order = await this.prisma.order.findUnique({
      where: {
        orderNo
      },
      include: {
        orderInfos: true
      }
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.teamId !== user.currentTeamId) {
      throw new NotFoundException('没权获取当前订单信息')
    }

    if (order.orderStatus !== OrderStatus.PENDING) {
      throw new NotFoundException('当前订单已支付或取消，无法查询')
    }

    if (order.expireTime.getTime() <= Date.now()) {
      throw new NotFoundException('当前订单已取消，无法查询')
    }

    return {
      payInfo: {
        alipay: {
          urlInfo: order.orderInfos.find((item) => item.payType === PayType.ALIPAY)?.urlInfo || ''
        },
        wechat: {
          urlInfo: order.orderInfos.find((item) => item.payType === PayType.WECHAT)?.urlInfo || ''
        },
        corporateTransfer: {
          urlInfo: serviceCodeUrl
        }
      },
      isUpgrade: order.isUpgrade,
      price: order.price,
      dueAmount: order.dueAmount,
      orderNo: order.orderNo,
      remainingTimeInSeconds: order.expireTime.getTime() - Date.now()
    }
  }

  async getPeddingOrders() {
    const { user } = this.request

    const count = await this.prisma.order.count({
      where: {
        teamId: user.currentTeamId,
        orderStatus: OrderStatus.PENDING,
        expireTime: {
          gt: new Date()
        }
      }
    })

    return {
      hasPendingOrder: count > 0,
      count
    }
  }
}
