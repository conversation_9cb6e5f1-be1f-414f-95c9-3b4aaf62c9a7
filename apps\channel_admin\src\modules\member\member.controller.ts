import { Controller, Get, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import { MemberResponseDTO } from './member.dto'
import { MemberService } from './member.service'

@Controller('members')
@ApiTags('成员管理')
export class MemberController {
  constructor(private readonly memberService: MemberService) {}

  @Get()
  @ApiOperation({ summary: '获取成员列表' })
  @ApiOkResponse({ description: '操作成功', type: MemberResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({
    name: 'authorization',
    required: true
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: '页码 <默认 1>'
  })
  @ApiQuery({
    name: 'size',
    required: false,
    type: Number,
    description: '每页数量 <默认 10>'
  })
  @ApiQuery({
    name: 'phone',
    required: false,
    type: String,
    description: '手机号码'
  })
  @ApiQuery({
    name: 'startTime',
    required: false,
    type: Number,
    description: '注册开始时间'
  })
  @ApiQuery({
    name: 'endTime',
    required: false,
    type: Number,
    description: '注册结束时间'
  })
  async getChannels(
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number,
    @Query('phone', {
      transform: (value) => value || ''
    })
    phone: string,
    @Query('startTime', {
      transform: (value) => value || 0
    })
    startTime: number,
    @Query('endTime', {
      transform: (value) => value || 0
    })
    endTime: number
  ) {
    return this.memberService.getMembers({
      page,
      size,
      phone,
      startTime,
      endTime
    })
  }
}
