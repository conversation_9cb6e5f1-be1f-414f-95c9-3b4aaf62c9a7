import { BaseConfigRegister, type BaseConfig } from './base'
import { ConfigModule as _ConfigModule } from '@nestjs/config'

export const RootConfig = [BaseConfigRegister]

export interface RootConfigMap {
  app: BaseConfig
}

export const ConfigModule = _ConfigModule.forRoot({
  envFilePath: `.env.${process.env.NODE_ENV}`,
  load: RootConfig,
  ignoreEnvFile: false,
  isGlobal: true,
  cache: true
})
