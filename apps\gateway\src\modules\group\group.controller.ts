import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { GroupsService } from './group.service'
import { BaseBadRequestDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseRequestDTO'
import {
  GroupsDetailResponseDTO,
  GroupsResponseDTO,
  PatchGroupsRequest,
  PostGroupsRequest
} from './group.dto'

@Controller('groups')
@ApiTags('账号分组管理')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({
  name: 'authorization',
  required: true
})
export class GroupsController {
  constructor(private readonly groupService: GroupsService) {}

  @Get()
  @ApiOperation({ summary: '分组列表' })
  @ApiOkResponse({ description: '操作成功', type: GroupsResponseDTO })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: '分组名称'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '单页数量 <默认 10>' })
  async getCoupons(
    @Query('name') name: string,
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number,
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number
  ) {
    const response = await this.groupService.getGroups(name, page, size)
    return response
  }

  @Post()
  @ApiOperation({ summary: '创建分组' })
  @ApiOkResponse({ description: '操作成功', type: GroupsResponseDTO })
  async postCoupons(@Body() body: PostGroupsRequest) {
    const response = await this.groupService.postGroups(body)
    return response
  }

  @Patch(':groupId')
  @ApiOperation({ summary: '更新分组' })
  @ApiOkResponse({ type: GroupsDetailResponseDTO })
  async patchGroups(@Param('groupId') groupId: number, @Body() body: PatchGroupsRequest) {
    return this.groupService.patchGroups(groupId, body)
  }

  @Delete(':groupId')
  @ApiOperation({ summary: '删除分组' })
  @ApiOkResponse()
  async deleteGroups(@Param('groupId') groupId: number) {
    await this.groupService.deleteGroups(groupId)
  }
}
