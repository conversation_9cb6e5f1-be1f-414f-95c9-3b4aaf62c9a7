import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { PrismaService } from '@qdy/mysql'
import { FastifyRequest } from 'fastify'
import { KuaishouMessagesRequestDTO } from './interact.dto'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from 'packages/config'
import { TeamMemberRole } from '../team/team.dto'
import { AccountAccountsStatus } from '../account/account.dto'
import { ContentType, postSendMessage } from '../webhook/external.kuaishou'
import { Platform } from '@qdy/utils'

@Injectable()
export class InteractKuaishouService {
  logger = new Logger('InteractKuaishouService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async sendKuaishouMessage({
    toUserId,
    platformAccountId,
    content,
    messageType,
    width,
    height,
    contentLenght,
    toName,
    toAvatar
  }: KuaishouMessagesRequestDTO) {
    const { user } = this.request

    const { platformAccount, teamMember } = await this.checkPlatformAccount({
      teamId: user.currentTeamId,
      userId: user.id,
      platformAccountId
    })

    let contentType = ContentType.Text

    switch (messageType) {
      case 'text':
        contentType = ContentType.Text
        break
      case 'image':
        contentType = ContentType.Image
        break
      case 'retain_consult_card':
        contentType = ContentType.Card
        break
    }

    const { kuaishouClientKey, kuaishouClientSecret } =
      this.configService.get<RootConfigMap['app']>('app')

    const res = await postSendMessage({
      platformAccountId: platformAccount.id,
      teamId: platformAccount.teamId,
      accessToken: platformAccount.accessToken,
      openId: platformAccount.openId,
      appId: kuaishouClientKey,
      secret: kuaishouClientSecret,
      content,
      messageType: contentType,
      toUserId,
      scene: 'im_replay_msg',
      redisClient: this.cacheManager,
      width,
      height,
      contentLenght,
      fromName: platformAccount.name,
      fromAvatar: platformAccount.avatar,
      toName,
      toAvatar
    })

    await this.prisma.teamMember.update({
      where: {
        id: teamMember.id
      },
      data: {
        replyMessage: {
          increment: 1
        }
      }
    })

    return {
      messageId: res.messageId
    }
  }

  private async checkPlatformAccount({
    teamId,
    userId,
    platformAccountId
  }: {
    teamId: number
    userId: number
    platformAccountId: number
  }) {
    const teamMember = await this.prisma.teamMember.findUnique({
      where: {
        userId_teamId: {
          teamId,
          userId
        }
      },
      include: {
        platformAccounts: true
      }
    })

    if (!teamMember) {
      throw new ForbiddenException('用户未加入此团队')
    }

    let { platformAccounts } = teamMember

    if (teamMember.role !== TeamMemberRole.Member) {
      platformAccounts = await this.prisma.platformAccount.findMany({
        where: {
          teamId: teamMember.teamId
        }
      })
    }

    const platformAccount = platformAccounts.find((item) => item.id === platformAccountId)

    if (!platformAccount) {
      throw new ForbiddenException('账号不存在或你不属于运营人员')
    }

    if (platformAccount.platform !== Platform.Kuaishou) {
      throw new ForbiddenException('此账号不是快手账号')
    }

    if (platformAccount.status === AccountAccountsStatus.Disable) {
      throw new ForbiddenException('账号已被冻结')
    }

    return { platformAccount, teamMember }
  }
}
