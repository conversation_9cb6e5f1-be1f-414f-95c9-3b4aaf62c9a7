import { NestFactory } from '@nestjs/core'
import { AdminModule } from './admin.module'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'
import compression from '@fastify/compress'
import helmet from '@fastify/helmet'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { HttpStatus, Logger, ValidationPipe } from '@nestjs/common'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import multipart from '@fastify/multipart'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(timezone)

export async function bootstrap() {
  const fastify = new FastifyAdapter()
  fastify.register(compression, { encodings: ['gzip', 'deflate'] })
  fastify.register(helmet)
  fastify.register(multipart, {
    limits: {
      fileSize: 1024 * 1024 * 10
    }
  })

  const app = await NestFactory.create<NestFastifyApplication>(AdminModule, fastify)
  const configService = app.get(ConfigService) as ConfigService<RootConfigMap, true>
  const appConfig = configService.get<RootConfigMap['app']>('app')

  app.enableCors({
    origin: appConfig.cors.allowOrigin,
    methods: appConfig.cors.allowMethod,
    allowedHeaders: appConfig.cors.allowHeader,
    preflightContinue: false,
    credentials: true,
    optionsSuccessStatus: HttpStatus.NO_CONTENT
  })

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true
    })
  )

  if (process.env.NODE_ENV !== 'test') {
    const config = new DocumentBuilder().setTitle('GPT API 文档').setVersion('1.0').build()
    const ClientDocument = SwaggerModule.createDocument(app, config)

    SwaggerModule.setup('admin-api', app, ClientDocument)
  }

  await app.listen(3008, appConfig.http.host)

  Logger.log(`Server running on ${await app.getUrl()}`, 'NestApplication')
}

bootstrap()
