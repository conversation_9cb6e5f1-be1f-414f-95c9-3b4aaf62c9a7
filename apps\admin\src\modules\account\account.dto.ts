import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { Autoresponder } from '@qdy/mysql'
import { AnyObject } from 'mongoose'
import { Platform } from '@qdy/utils'

export enum AccountAccountsStatus {
  /**
   * 正常
   */
  Normal = 0,
  /**
   * vip过期冻结/禁用
   */
  Disable = 1
}

class AccountAccounts {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  platformAccountId: string

  @ApiResponseProperty({
    type: String,
    example: 'https://www.baidu.com'
  })
  avatar: string

  @ApiResponseProperty({
    type: String,
    example: 'SDFLKKL123'
  })
  openId: string

  @ApiResponseProperty({
    type: Number,
    example: '********** <如果为0,则代表过期了>'
  })
  expiresIn: number

  @ApiResponseProperty({
    type: Number,
    example: '********** <创建时间>'
  })
  createTime: number

  @ApiResponseProperty({
    type: Number,
    example: '********** <过期时间,小于当前时间戳则代表过期了>'
  })
  expiresTime: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  id: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  teamId: number

  @ApiResponseProperty({
    type: String,
    example: '张三的团队'
  })
  teamName: string

  @ApiResponseProperty({
    type: String,
    example:
      'EAccountM <EAccountM: 普通企业号,STAFF: 员工号, AUTH_COMPANY: 认证企业号, COMPANY_BAND: 品牌企业号>'
  })
  accountRole: string

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  isGreeting?: boolean

  @ApiResponseProperty({
    enum: Platform,
    example: 0
  })
  platform: Platform

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  scopes: string

  /**
   * 媒体账号可用状态，正常,vip过期冻结
   */
  @ApiResponseProperty({
    type: Number,
    enum: Object.values(AccountAccountsStatus).filter((v) => typeof v === 'number'),
    example: 0
  })
  status: AccountAccountsStatus

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  appId: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  regionId: string

  @ApiResponseProperty({
    type: String,
    example: ''
  })
  remark: string
}

export class AccountAccountsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AccountAccounts]
  })
  data: AccountAccounts[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  totalPage: number
}

export class AccountsRequestDto {
  @ApiProperty({
    description: '平台',
    enum: Platform,
    required: false
  })
  @IsEnum(Platform)
  @IsOptional()
  platform: number
}

export class AutoresponderKeywordRedisValue {
  platform: Autoresponder['platform']

  scene: Autoresponder['scene']

  trigger: Autoresponder['trigger']

  keyword: Autoresponder['keywords']

  stopReply: Autoresponder['stopReply']

  stopInterval: Autoresponder['stopInterval']

  isDelay: Autoresponder['isDelay']

  delayTime: Autoresponder['delayTime']

  stopTime: Autoresponder['stopTime']

  rule: Autoresponder['rule']

  contents: AnyObject[]

  contentType: Autoresponder['contentType']

  executionCount: Autoresponder['executionCount']

  isNew: Autoresponder['isNew']

  state: Autoresponder['state']

  autoresponderId: number
}

export class AutoresponderOpuser {
  @ApiProperty({
    description: 'id',
    example: 4,
    required: true
  })
  @IsNotEmpty({ message: 'platformAccountId不能为空' })
  @IsNumber({}, { message: 'platformAccountId必须是数字' })
  platformAccountId: number

  @ApiProperty({
    description: 'id',
    example: 'xx',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  id: string

  @ApiProperty({
    description: 'title',
    example: ' 你好',
    required: false
  })
  @IsString()
  @IsOptional()
  title: string

  @ApiProperty({
    description: '头像',
    example: ' xxx.png',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  avatar: string

  @ApiProperty({
    description: '创建时间',
    example: '2021-01-01T00:00:00.000Z',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  createTime: number

  @ApiProperty({
    description: '评论数',
    example: 123,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  commentCount: number
}
