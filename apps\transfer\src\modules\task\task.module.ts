import { Module } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'
import { TasksService } from './task.service'
import {
  CommentMongoose,
  MessagesMongoose,
  WechatMessagesMongoose,
  WechatCommentMongoose,
  PersonalChatMessagesMongoose,
  WorkCommentMongoose,
  MessageStatisticsMongoose,
  DailyMessageStatisticMongoose,
  DailyOverviewMongoose,
  OverviewMongoose
} from '@qdy/mongo'
import { DataTransferService } from './dataTransfer.service'
import { autoresponderRedisTransferService } from './autoresponderRedisTransfer.service'

@Module({
  imports: [
    CommentMongoose,
    MessagesMongoose,
    WechatMessagesMongoose,
    WechatCommentMongoose,
    PersonalChatMessagesMongoose,
    WorkCommentMongoose,
    MessageStatisticsMongoose,
    DailyMessageStatisticMongoose,
    DailyOverviewMongoose,
    OverviewMongoose,
    ScheduleModule.forRoot()
  ],
  controllers: [],
  providers: [TasksService, DataTransferService, autoresponderRedisTransferService]
})
export class TaskModule {}
