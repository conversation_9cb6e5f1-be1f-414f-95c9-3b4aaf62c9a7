/* eslint-disable no-continue */
import { BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import { WebhookEvents, WebhookWechatEvents } from './constant'
import { WebHookWechatBody, WebhookWechatFinderSyncMsg, WechatCommentBody } from './types'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { PrismaService } from '@qdy/mysql'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@qdy/config'
import { InjectModel } from '@nestjs/mongoose'
import {
  WechatMessagesEntity,
  WechatCommentEntity,
  WechatOpusEntity,
  PersonalChatMessageEntity,
  WorkCommentEntity,
  MessagesByAutoresponderEntity
} from 'packages/mongo/lib'
import { AnyObject, Model } from 'mongoose'
import { genSocketRedisKey } from '@qdy/utils'
import { WebHookServiceGrpc } from './webhook.rpc'
import {
  AutoresponderContentChildType,
  AutoresponderContentTextType,
  AutoresponderContentType,
  AutoresponderKeywordContent,
  AutoresponderKeywordRedisValue,
  AutoresponderKeywordRule,
  AutoresponderTriggerType
} from '../autoresponder/autoresponder.dto'
import {
  AutoresponderKeywordKey,
  AutoresponderVariableKey,
  PlatformAccountKeywordKey
} from '../autoresponder/constant'
import { generateRandom, wait } from '../../common/utils'
import { Queue, Worker } from 'bullmq'
import {
  createComment,
  getWechatMessageSessionId,
  postUserPage,
  wechatSendMessages,
  postContactList,
  postDownloadImage,
  postDownloadVideo,
  postMentionList
} from '../interact/external.wechat'
import { AccountAccountsStatus } from '../account/account.dto'
import dayjs from 'dayjs'
import { PlatformAccountManageService, TosManageService } from '@qdy/common'
import { sendJpushMessageEventKey, sendJpushMessageEventEmitter } from './webhook.jpush'

@Injectable()
export class WebHookServiceWechatNew {
  taskQueue: Queue

  taskWorker: Worker

  taskCommentQueue: Queue

  taskCommentWorker: Worker

  wechatToken: string

  private isNew = true

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly webhookGrpcService: WebHookServiceGrpc,
    @InjectModel(WechatCommentEntity.name) private wechatCommentModel: Model<WechatCommentEntity>,
    @InjectModel(WechatMessagesEntity.name) private wechatMessageModel: Model<WechatMessagesEntity>,
    @InjectModel(WechatOpusEntity.name) private wechatOpusModel: Model<WechatOpusEntity>,
    @InjectModel(PersonalChatMessageEntity.name)
    private personalChatMessageModel: Model<PersonalChatMessageEntity>,
    @InjectModel(MessagesByAutoresponderEntity.name)
    private messagesByAutoresponderModel: Model<MessagesByAutoresponderEntity>,
    @InjectModel(WorkCommentEntity.name) private workCommentModel: Model<WorkCommentEntity>,
    private readonly platformAccountManageService: PlatformAccountManageService,
    private readonly tosManageService: TosManageService
  ) {}

  logger = new Logger('WebHookServiceWechatNew')

  onModuleInit() {
    this.logger.log('WebHookServiceWechatNew init')
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    this.wechatToken = wechatConfig.Token

    this.taskQueue = new Queue('autoresponder-new-wechat', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'autoresponder-new-wechat',
      async (job) => {
        const { contents, ...others } = job.data
        setTimeout(() => {
          this.autoresponderTask(contents, others).catch((err) => {
            this.logger.error('task', err)
          })
        }, 600)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
    this.taskWorker.on('completed', (job) => {
      this.logger.log(`taskWorker completed------------${job.id}`)
    })

    // 评论回复队列
    this.taskCommentQueue = new Queue('autoresponder-new-wechat-comment', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskCommentWorker = new Worker(
      'autoresponder-new-wechat-comment',
      async (job) => {
        setTimeout(() => {
          this.commentAutoresponderSend(job.data).catch((err) => {
            this.logger.error('task', err)
          })
        }, 100)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
    this.taskCommentWorker.on('completed', (job) => {
      this.logger.log(`taskCommentWorker completed------------${job.id}`)
    })
  }

  startsWithWord(str: string, word: string) {
    return str.startsWith(word)
  }

  async webHookWechat(body: WebHookWechatBody) {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    if (!body.Wxid) {
      return
    }

    // 视频号消息
    const accountInfo = await this.prisma.platformAccount.findUnique({
      where: {
        openId: body.Wxid
      }
    })

    if (!accountInfo) {
      this.logger.error(`wxid:${body.Wxid}:归属账号找不到对应信息`)
      return
    }

    const allowMsgType = [1, 3, 43]

    if (
      body.TypeName === WebhookWechatEvents.FinderMsg &&
      allowMsgType.includes(body.Data.msg.MsgType)
    ) {
      let fromAvatar = ''
      let fromName = ''
      let toAvatar = ''
      let toName = ''
      let fromUserName = ''
      let toUserName = ''

      if (accountInfo) {
        if (body.Data.isSender === 1) {
          fromAvatar = accountInfo.avatar
          fromName = accountInfo.name
          fromUserName = accountInfo.username
        }

        if (body.Data.isSender === 0) {
          toAvatar = accountInfo.avatar
          toName = accountInfo.name
          toUserName = accountInfo.username
        }
      }

      if (!fromAvatar && !fromName) {
        // 获取消息发送人相关信息
        if (this.startsWithWord(body.Data.msg.FromUserName.string, 'v2')) {
          const fromUser = await postUserPage({
            appId: body.Appid,
            toUserName: body.Data.msg.FromUserName.string,
            lastBuffer: '',
            maxId: '',
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          fromAvatar = fromUser.contact.headUrl
          fromName = fromUser.contact.nickname
          fromUserName = body.Data.msg.FromUserName.string
        } else if (this.startsWithWord(body.Data.msg.FromUserName.string, 'fv1')) {
          const fromUser = await postContactList({
            appId: body.Appid,
            myUserName: accountInfo.username,
            myRoleType: 3,
            queryInfo: body.Data.msg.FromUserName.string,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          fromAvatar = fromUser.headUrl
          fromName = fromUser.nickname
          fromUserName = body.Data.msg.FromUserName.string
        } else if (this.startsWithWord(body.Data.msg.FromUserName.string, 'wxid_')) {
          const fromUser = await this.prisma.platformAccount.findUnique({
            where: {
              openId: body.Data.msg.FromUserName.string
            }
          })

          if (fromUser) {
            fromAvatar = fromUser.avatar
            fromName = fromUser.name
            fromUserName = fromUser.username
          }
        }
      }

      if (!toAvatar && !toName) {
        // 获取消息接收人相关信息
        if (this.startsWithWord(body.Data.msg.ToUserName.string, 'v2')) {
          const toUser = await postUserPage({
            appId: body.Appid,
            toUserName: body.Data.msg.ToUserName.string,
            lastBuffer: '',
            maxId: '',
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          toAvatar = toUser.contact.headUrl
          toName = toUser.contact.nickname
          toUserName = body.Data.msg.ToUserName.string
        } else if (this.startsWithWord(body.Data.msg.ToUserName.string, 'fv1')) {
          const toUser = await postContactList({
            appId: body.Appid,
            myUserName: accountInfo.username,
            myRoleType: 3,
            queryInfo: body.Data.msg.ToUserName.string,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          toAvatar = toUser.headUrl
          toName = toUser.nickname
          toUserName = body.Data.msg.ToUserName.string
        } else if (this.startsWithWord(body.Data.msg.ToUserName.string, 'wxid_')) {
          const toUser = await this.prisma.platformAccount.findUnique({
            where: {
              openId: body.Data.msg.ToUserName.string
            }
          })

          if (toUser) {
            toAvatar = toUser.avatar
            toName = toUser.name
            toUserName = toUser.username
          }
        }
      }

      let imageUrl = ''
      if (body.Data.msg.MsgType === 3) {
        const imageCacheKey = `wechat-image:${body.Data.msg.NewMsgId}`

        imageUrl = await this.cacheManager.get(imageCacheKey)
        if (!imageUrl) {
          // 图片信息，下载图片， 并缓存
          let picInfo
          picInfo = await postDownloadImage({
            appId: body.Appid,
            xml: body.Data.msg.Content.string,
            type: 2,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          if (picInfo.ret !== 200) {
            picInfo = await postDownloadImage({
              appId: body.Appid,
              xml: body.Data.msg.Content.string,
              type: 1,
              token: wechatConfig.Token,
              isNew: this.isNew
            })
          }

          imageUrl = picInfo.ret === 200 ? picInfo.data.fileUrl : ''

          if (picInfo.ret === 200) {
            await this.cacheManager.set(
              imageCacheKey,
              picInfo.data.fileUrl,
              wechatConfig.pictureExpiredTime
            )
          }
        }
      }

      let videoUrl = ''
      if (body.Data.msg.MsgType === 43) {
        const videoCacheKey = `wechat-video:${body.Data.msg.NewMsgId}`

        videoUrl = await this.cacheManager.get(videoCacheKey)
        if (!videoUrl) {
          // 视频信息，下载视频， 并缓存
          const videoInfo = await postDownloadVideo({
            appId: body.Appid,
            xml: body.Data.msg.Content.string,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          videoUrl = videoInfo.ret === 200 ? videoInfo.data.fileUrl : ''
          if (videoInfo.ret === 200) {
            await this.cacheManager.set(
              videoCacheKey,
              videoInfo.data.fileUrl,
              wechatConfig.pictureExpiredTime
            )
          }
        }
      }

      const data = {
        platformType: 'wechat',
        event: body.Data.msg.MsgType,
        typeName: body.TypeName,
        appid: body.Appid,
        wxid: body.Wxid,
        msgid: body.Data.msg.MsgId,
        msgType: body.Data.msg.MsgType,
        content: body.Data.msg.Content.string,
        status: body.Data.msg.Status,
        imgStatus: body.Data.msg.ImgStatus,
        createTime: body.Data.msg.CreateTime * 1000,
        newMsgId: body.Data.msg.NewMsgId,
        msgSessionId: body.Data.msgSessionId,
        sessionId: body.Data.msgSessionId,
        imageUrl,
        videoUrl,
        fromAvatar,
        fromName,
        toAvatar,
        toName,
        fromUserName,
        toUserName,
        isSender: body.Data.isSender,
        isAuto: 0
      }

      try {
        const wechatMessage = await this.wechatMessageModel.create(data)

        if (data.msgType === 1) {
          this.autoresponderKeywordChat({
            text: body.Data.msg.Content.string,
            fromUserId: data.fromUserName,
            toUserId: data.toUserName,
            wxid: data.wxid,
            sessionId: data.msgSessionId
          })
        }

        const dataList: { socketId: string; data: Record<string, unknown> }[] = []

        if (body.Data.msg.NewMsgId) {
          const isAutoData = await this.cacheManager.get(body.Data.msg.NewMsgId)
          if (isAutoData) {
            data.isAuto = 1
          }
        }

        const userIdMaps = await this.cacheManager.store.client.hgetall(
          genSocketRedisKey(data.wxid)
        )
        if (userIdMaps) {
          Object.keys(userIdMaps).forEach(async (socketId) => {
            dataList.push({
              socketId,
              data: {
                ...data,
                id: wechatMessage.id,
                platformAccountId: parseInt(userIdMaps[socketId], 10)
              }
            })
          })
        }
        if (dataList.length) {
          try {
            return this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) })
          } catch (error) {
            this.logger.error('socketService error', error)
          }
        }
      } catch (error) {
        this.logger.error('私信写入失败', error)
      }
    }
  }

  async webHookNewWechat(body: WebHookWechatBody) {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')

    if (!body.Wxid) {
      return
    }

    // 视频号消息
    const accountInfo = await this.prisma.platformAccount.findUnique({
      where: {
        openId: body.Wxid
      },
      include: {
        Team: true
      }
    })

    if (!accountInfo) {
      this.logger.error(`wxid:${body.Wxid}:归属账号找不到对应信息`)
      return
    }

    const allowMsgType = [1, 3, 43]
    let fromUserId = ''
    let toUserId = ''
    let fromAvatar = ''
    let fromName = ''
    let toAvatar = ''
    let toName = ''
    if (
      body.TypeName === WebhookWechatEvents.FinderMsg &&
      allowMsgType.includes(body.Data.msg.MsgType)
    ) {
      if (accountInfo) {
        if (body.Data.isSender === 1) {
          fromAvatar = accountInfo.avatar
          fromName = accountInfo.name
        }

        if (body.Data.isSender === 0) {
          toAvatar = accountInfo.avatar
          toName = accountInfo.name
        }
      }

      if (!fromAvatar && !fromName) {
        // 获取消息发送人相关信息
        if (this.startsWithWord(body.Data.msg.FromUserName.string, 'v2')) {
          const fromUser = await postUserPage({
            appId: body.Appid,
            toUserName: body.Data.msg.FromUserName.string,
            lastBuffer: '',
            maxId: '',
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          fromAvatar = fromUser.contact.headUrl
          fromName = fromUser.contact.nickname
        } else if (this.startsWithWord(body.Data.msg.FromUserName.string, 'fv1')) {
          const fromUser = await postContactList({
            appId: body.Appid,
            myUserName: accountInfo.username,
            myRoleType: 3,
            queryInfo: body.Data.msg.FromUserName.string,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          fromAvatar = fromUser.headUrl
          fromName = fromUser.nickname
        } else if (this.startsWithWord(body.Data.msg.FromUserName.string, 'wxid_')) {
          const fromUser = await this.prisma.platformAccount.findUnique({
            where: {
              openId: body.Data.msg.FromUserName.string
            }
          })

          if (fromUser) {
            fromAvatar = fromUser.avatar
            fromName = fromUser.name
          }
        }
      }

      if (!toAvatar && !toName) {
        // 获取消息接收人相关信息
        if (this.startsWithWord(body.Data.msg.ToUserName.string, 'v2')) {
          const toUser = await postUserPage({
            appId: body.Appid,
            toUserName: body.Data.msg.ToUserName.string,
            lastBuffer: '',
            maxId: '',
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          toAvatar = toUser.contact.headUrl
          toName = toUser.contact.nickname
        } else if (this.startsWithWord(body.Data.msg.ToUserName.string, 'fv1')) {
          const toUser = await postContactList({
            appId: body.Appid,
            myUserName: accountInfo.username,
            myRoleType: 3,
            queryInfo: body.Data.msg.ToUserName.string,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          toAvatar = toUser.headUrl
          toName = toUser.nickname
        } else if (this.startsWithWord(body.Data.msg.ToUserName.string, 'wxid_')) {
          const toUser = await this.prisma.platformAccount.findUnique({
            where: {
              openId: body.Data.msg.ToUserName.string
            }
          })

          if (toUser) {
            toAvatar = toUser.avatar
            toName = toUser.name
          }
        }
      }
      fromUserId = body.Data.msg.FromUserName.string
      toUserId = body.Data.msg.ToUserName.string

      const data = {
        platformType: 'wechat',
        uniqueId: `${body.Wxid}-${body.Data.msg.NewMsgId}`,
        event: body.Data.isSender ? WebhookEvents.IMSendMessage : WebhookEvents.IMReceiveMessage,
        openId: body.Wxid,
        fromUserId,
        toUserId,
        fromAvatar,
        fromName,
        toAvatar,
        toName,
        sessionId: body.Data.msgSessionId,
        content: {},
        createTime: (body.Data.msg.CreateTime as number) * 1000,
        isAuto: 0,
        messageId: body.Data.msg.NewMsgId
      }

      let mediaUrl
      let responseUrl

      if (body.Data.msg.MsgType === 3) {
        if (!mediaUrl) {
          // 图片信息，下载图片， 并缓存
          let picInfo
          picInfo = await postDownloadImage({
            appId: body.Appid,
            xml: body.Data.msg.Content.string,
            type: 2,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          if (picInfo.ret !== 200) {
            picInfo = await postDownloadImage({
              appId: body.Appid,
              xml: body.Data.msg.Content.string,
              type: 1,
              token: wechatConfig.Token,
              isNew: this.isNew
            })
          }

          mediaUrl = picInfo.ret === 200 ? picInfo.data.fileUrl : ''
        }
      }

      if (body.Data.msg.MsgType === 43) {
        if (!mediaUrl) {
          // 视频信息，下载视频， 并缓存
          const videoInfo = await postDownloadVideo({
            appId: body.Appid,
            xml: body.Data.msg.Content.string,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          mediaUrl = videoInfo.ret === 200 ? videoInfo.data.fileUrl : ''
        }
      }

      if (mediaUrl) {
        // 下载资源并上传到oss
        responseUrl = await this.uploadFromUrl({
          teamCode: accountInfo.Team.invitationCode,
          url: mediaUrl
        })
      }

      data.content = {
        appId: body.Appid,
        messageType: 'text',
        text: body.Data.msg.Content.string,
        sessionId: body.Data.msgSessionId,
        isSender: body.Data.isSender
      }

      if (body.Data.msg.MsgType === 3) {
        data.content = {
          appId: body.Appid,
          messageType: 'image',
          text: body.Data.msg.Content.string,
          sessionId: body.Data.msgSessionId,
          isSender: body.Data.isSender,
          responseUrl
        }
      }

      if (body.Data.msg.MsgType === 43) {
        data.content = {
          appId: body.Appid,
          messageType: 'video',
          text: body.Data.msg.Content.string,
          sessionId: body.Data.msgSessionId,
          isSender: body.Data.isSender,
          responseUrl
        }
      }

      try {
        if (body.Data.msg.NewMsgId) {
          const isAutoData = await this.cacheManager.get(body.Data.msg.NewMsgId)
          if (isAutoData) {
            data.isAuto = 1
          }
        }

        if (body.Data.isSender === 0) {
          sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
            openId: data.openId,
            messageId: data.messageId,
            event: data.event,
            content: '收到一条视频号私信消息'
          })
        }

        const wechatMessage = await this.personalChatMessageModel.create(data)

        if (body.Data.msg.MsgType === 1) {
          this.autoresponderKeywordChat({
            text: body.Data.msg.Content.string,
            fromUserId: data.fromUserId,
            toUserId: data.toUserId,
            wxid: data.openId,
            sessionId: body.Data.msgSessionId
          })
        }

        const dataList: { socketId: string; data: Record<string, unknown> }[] = []

        const userIdMaps = await this.cacheManager.store.client.hgetall(
          genSocketRedisKey(data.openId)
        )
        if (userIdMaps) {
          Object.keys(userIdMaps).forEach(async (socketId) => {
            dataList.push({
              socketId,
              data: {
                ...data,
                id: wechatMessage.id,
                platformAccountId: parseInt(userIdMaps[socketId], 10)
              }
            })
          })
        }
        if (dataList.length) {
          try {
            this.webhookGrpcService.socketService
              .send({ list: JSON.stringify(dataList) })
              .subscribe({
                next: () => {},
                error: (error) => {
                  throw new BadRequestException(`socket发送失败 error${error.message}`)
                },
                complete: () => {}
              })
          } catch (error) {
            this.logger.error('socketService error', error)
          }
        }
      } catch (error) {
        this.logger.error('私信写入失败', error)
      }
    }
  }

  /**
   * 掉线回调
   * @param body
   * @returns
   */
  async webHookOffline(body: WebHookWechatBody) {
    if (!body.Appid || !body.Wxid) {
      return
    }

    // 视频号消息
    const accountInfo = await this.prisma.platformAccount.findUnique({
      where: {
        openId: body.Wxid,
        appId: body.Appid
      }
    })

    if (!accountInfo) {
      this.logger.error(`wxid:${body.Wxid}-appid:${body.Appid}:归属账号找不到对应信息`)
      return
    }

    const expiresIn = 0

    const platformAccount = await this.prisma.platformAccount.update({
      where: {
        id: accountInfo.id
      },
      data: {
        expiresIn
      }
    })

    await this.platformAccountManageService.deletePlatformAccountRedisInfo(platformAccount)

    const userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(body.Wxid))

    const changePlatformAccounts = []
    Object.keys(userIdMaps).forEach((socketId) => {
      changePlatformAccounts.push({
        socketId,
        data: {
          type: 'changePlatformAccount',
          data: [
            {
              action: 'timeout',
              platform: platformAccount.platform,
              name: platformAccount.name,
              avatar: platformAccount.avatar,
              openId: platformAccount.openId,
              accountRole: platformAccount.accountRole,
              id: platformAccount.id,
              teamId: platformAccount.teamId,
              expiresTime: 0
            }
          ]
        }
      })
    })

    try {
      return this.webhookGrpcService.socketService
        .send({ list: JSON.stringify(changePlatformAccounts) })
        .subscribe({
          next: () => {},
          error: (error) => {
            throw new BadRequestException(`socket发送失败 error${error.message}`)
          },
          complete: () => {}
        })
    } catch (error) {
      this.logger.error('socketService error', error)
    }
  }

  async webHookFinderSyncMsg(body: WebhookWechatFinderSyncMsg) {
    if (!body.Wxid) {
      return
    }

    const showInfo = body.Data.showInfos.find((item) => item.showType === 27)
    if (!showInfo) {
      return
    }

    this.logger.log('showInfo.showExtInfo', showInfo.showExtInfo)

    switch (showInfo.showExtInfo) {
      case 'CAI=':
        await this.webHookFinderSyncMsgByNewCommment(body)
        await this.webHookFinderSyncChatByComment(body)
        break
      case 'CAE=':
        await this.webHookFinderSyncChatByLike(body)
        break
      case 'CAM=':
        await this.webHookFinderSyncChatByFollow(body)
        break
      default:
        break
    }
  }

  async webHookFinderSyncMsgByComment(body: WebhookWechatFinderSyncMsg) {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
    const dayTime = dayjs().startOf('day').unix() * 1000

    // 获取当前视频号最后一个作品数据
    const lastOpusRecord = await this.wechatOpusModel
      .findOne({
        wxid: body.Wxid
      })
      .sort({ createTime: -1 })

    let maxOpusTime = 0
    if (lastOpusRecord) {
      maxOpusTime = lastOpusRecord.createTime
    }

    // 获取视频号作品数据
    const opusList = await this.getOpusList({
      appid: body.Appid,
      token: wechatConfig.Token,
      myUserName: body.Data.selfFinderUsername,
      maxTime: maxOpusTime
    })

    const record = []
    opusList.forEach((item) => {
      const data = {
        objectId: item.id,
        wxid: body.Wxid,
        username: item.username,
        nickname: item.nickName,
        headUrl: item?.contact?.headUrl,
        objectNonceId: item.objectNonceId,
        createTime: item.createtime,
        sessionBuffer: item.sessionBuffer,
        description: item?.objectDesc?.description,
        forwardCount: item.forwardCount,
        likeCount: item.likeCount,
        commentCount: item.commentCount,
        thumbUrl: `${item?.objectDesc?.media[0]?.ThumbUrl}${item?.objectDesc?.media[0]?.thumbUrlToken}`,
        jsonData: JSON.stringify(item)
      }
      record.push(data)
    })

    if (record) {
      await this.wechatOpusModel.create(record)
    }

    // 查询该视频号最后评论记录
    const latestRecord = await this.wechatCommentModel
      .findOne({
        wxid: body.Wxid
      })
      .sort({ createTime: -1 })

    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0)
    const thirtyDaysInMilliseconds = 30 * 24 * 60 * 60 * 1000
    let maxTime = startDate.getTime() - thirtyDaysInMilliseconds

    if (latestRecord) {
      // 查询到大于最后日期的数据
      maxTime = latestRecord.createTime
    }

    // 获取评论数据
    const commentRecord = await this.getMentionList({
      appid: body.Appid,
      token: wechatConfig.Token,
      myUserName: body.Data.selfFinderUsername,
      maxTime,
      reqScene: 4
    })

    commentRecord.reverse()

    const userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(body.Wxid))
    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    const data = {
      platformType: 'wechat',
      uniqueId: '',
      typeName: body.TypeName,
      fromUserName: '',
      toUserName: body.Data.selfFinderUsername,
      wxid: body.Wxid,
      appid: body.Appid,
      mentionType: '',
      refObjectId: '',
      refObjectNonceId: '',
      thumbUrl: '',
      description: '',
      content: {},
      createTime: 0,
      sessionBuffer: '',
      jsonData: ''
    }

    if (commentRecord) {
      const objectIds = commentRecord.map((item) => item.refObjectId)

      const uniqueObjectIds = [...new Set(objectIds)]

      const opusListByIds = await this.wechatOpusModel.find({
        objectId: { $in: uniqueObjectIds },
        wxid: data.wxid
      })

      // eslint-disable-next-line no-restricted-syntax
      for (const comment of commentRecord) {
        const opus = opusListByIds.find((item) => item.objectId === comment.refObjectId.toString())

        if (!opus) {
          continue
        }

        data.uniqueId = comment.refCommentId.toString()
        data.createTime = comment.createtime * 1000
        data.fromUserName = comment?.contact?.contact?.username
        data.mentionType = comment.mentionType
        data.refObjectId = comment.refObjectId.toString()
        data.refObjectNonceId = opus.objectNonceId.toString()
        data.thumbUrl = comment.thumbUrl
        data.description = comment.description
        data.sessionBuffer = opus.sessionBuffer
        data.jsonData = JSON.stringify(comment)

        data.content = {
          nickname: comment.nickname,
          headUrl: comment.headUrl,
          mentionContent: comment.mentionContent,
          refContent: comment.refContent,
          replyNickname: comment.replyNickname,
          replyHeadUrl: comment?.replyContact?.contact?.headUrl,
          refCommentId: comment.refCommentId.toString()
        }

        try {
          const commentData = await this.wechatCommentModel.create(data)

          if (userIdMaps) {
            Object.keys(userIdMaps).forEach((socketId) => {
              dataList.push({
                socketId,
                data: {
                  ...data,
                  id: commentData._id,
                  platformAccountId: parseInt(userIdMaps[socketId], 10)
                }
              })
            })
          }

          // 回复评论
          if (comment.createtime * 1000 > dayTime) {
            // 只有当天的评论会进行自动回复
            this.autoresponderKeywordComment({
              appid: body.Appid,
              wxid: body.Wxid,
              objectId: comment.refObjectId,
              objectNonceId: opus.objectNonceId,
              fromUserName: comment?.contact?.contact?.username,
              toUserName: body.Data.selfFinderUsername,
              content: comment.mentionContent,
              refCommentId: comment.refCommentId,
              sessionBuffer: opus.sessionBuffer
            })
          }
        } catch (error) {
          this.logger.error('wechatCommentModel error', error)
          if (error.code !== 11000) {
            this.logger.error('wechatCommentModel error', error)
          }
          throw new BadRequestException(`wechatCommentModel error${error.message}`)
        }
      }
    }

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`socket发送失败${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }

    return 'success'
  }

  async webHookFinderSyncMsgByNewCommment(body: WebhookWechatFinderSyncMsg) {
    const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
    const dayTime = dayjs().startOf('day').unix() * 1000

    // 获取当前视频号最后一个作品数据
    const lastOpusRecord = await this.wechatOpusModel
      .findOne({
        wxid: body.Wxid
      })
      .sort({ createTime: -1 })

    let maxOpusTime = 0
    if (lastOpusRecord) {
      maxOpusTime = lastOpusRecord.createTime
    }

    // 获取视频号作品数据
    const opusList = await this.getOpusList({
      appid: body.Appid,
      token: wechatConfig.Token,
      myUserName: body.Data.selfFinderUsername,
      maxTime: maxOpusTime
    })

    const record = []
    opusList.forEach((item) => {
      const data = {
        objectId: item.id,
        wxid: body.Wxid,
        username: item.username,
        nickname: item.nickName,
        headUrl: item?.contact?.headUrl,
        objectNonceId: item.objectNonceId,
        createTime: item.createtime,
        sessionBuffer: item.sessionBuffer,
        description: item?.objectDesc?.description,
        forwardCount: item.forwardCount,
        likeCount: item.likeCount,
        commentCount: item.commentCount,
        thumbUrl: `${item?.objectDesc?.media[0]?.ThumbUrl}${item?.objectDesc?.media[0]?.thumbUrlToken}`,
        jsonData: JSON.stringify(item)
      }
      record.push(data)
    })

    if (record) {
      await this.wechatOpusModel.create(record)
    }

    // 查询该视频号最后评论记录
    const latestRecord = await this.workCommentModel
      .findOne({
        openId: body.Wxid
      })
      .sort({ createTime: -1 })

    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0)
    const thirtyDaysInMilliseconds = 30 * 24 * 60 * 60 * 1000
    let maxTime = startDate.getTime() - thirtyDaysInMilliseconds

    if (latestRecord) {
      // 查询到大于最后日期的数据
      maxTime = latestRecord.createTime
    }
    // 获取评论数据
    const commentRecord = await this.getMentionList({
      appid: body.Appid,
      token: wechatConfig.Token,
      myUserName: body.Data.selfFinderUsername,
      maxTime,
      reqScene: 4
    })

    commentRecord.reverse()

    if (commentRecord.length > 0) {
      sendJpushMessageEventEmitter.emit(sendJpushMessageEventKey, {
        openId: body.Wxid,
        messageId: body.Data.tipsUuid,
        event: 'item_comment_reply',
        content: '收到一条视频号评论消息'
      })
    }

    const userIdMaps = await this.cacheManager.store.client.hgetall(genSocketRedisKey(body.Wxid))
    const dataList: { socketId: string; data: Record<string, unknown> }[] = []

    const data = {
      platformType: 'wechat',
      uniqueId: '',
      event: WebhookEvents.CommentReply,
      openId: body.Wxid,
      fromUserId: '',
      toUserId: body.Data.selfFinderUsername,
      fromAvatar: '',
      fromName: '',
      toAvatar: '',
      toName: '',
      sessionId: '',
      content: {},
      createTime: 0,
      messageId: '',
      isAuto: 0
    }

    if (commentRecord) {
      const objectIds = commentRecord.map((item) => item.refObjectId)

      const uniqueObjectIds = [...new Set(objectIds)]

      const opusListByIds = await this.wechatOpusModel.find({
        objectId: { $in: uniqueObjectIds },
        wxid: data.openId
      })

      // eslint-disable-next-line no-restricted-syntax
      for (const comment of commentRecord) {
        const opus = opusListByIds.find((item) => item.objectId === comment.refObjectId.toString())

        if (!opus) {
          continue
        }

        if (!comment.username) {
          continue
        }

        const isAutoData = await this.cacheManager.get(comment.refCommentId.toString())
        if (isAutoData) {
          data.isAuto = 1
        }

        data.uniqueId = comment.refCommentId.toString()
        data.createTime = comment.createtime * 1000
        data.fromUserId = comment?.contact?.contact?.username
        data.fromName = comment.nickname
        data.fromAvatar = comment.headUrl
        data.sessionId = comment.refObjectId.toString()
        data.messageId = comment.refCommentId.toString()
        data.content = {
          commentId: comment.refCommentId.toString(),
          name: comment.nickname,
          avatar: comment.headUrl,
          content: comment.mentionContent,
          mentionType: comment.mentionType,
          diggCount: opus.likeCount,
          replyToItemId: comment.refObjectId.toString(),
          refObjectNonceId: opus.objectNonceId.toString(),
          thumbUrl: comment.thumbUrl,
          description: comment.description,
          sessionBuffer: opus.sessionBuffer,
          refContent: comment.refContent,
          replyNickname: comment.replyNickname,
          replyHeadUrl: comment?.replyContact?.contact?.headUrl,
          refCommentId: comment.refCommentId.toString(),
          appId: body.Appid
        }

        try {
          const commentData = await this.workCommentModel.create(data)

          if (userIdMaps) {
            Object.keys(userIdMaps).forEach((socketId) => {
              dataList.push({
                socketId,
                data: {
                  ...data,
                  id: commentData._id,
                  platformAccountId: parseInt(userIdMaps[socketId], 10)
                }
              })
            })
          }

          // 回复评论
          if (comment.createtime * 1000 > dayTime) {
            // 只有当天的评论会进行自动回复
            this.autoresponderKeywordComment({
              appid: body.Appid,
              wxid: body.Wxid,
              objectId: comment.refObjectId,
              objectNonceId: opus.objectNonceId,
              fromUserName: comment?.contact?.contact?.username,
              toUserName: body.Data.selfFinderUsername,
              content: comment.mentionContent,
              refCommentId: comment.refCommentId,
              sessionBuffer: opus.sessionBuffer
            })
          }
        } catch (error) {
          this.logger.error('微信视频号回复评论写入数据库失败 error', error)
          if (error.code !== 11000) {
            this.logger.error('微信视频号回复评论写入数据库失败 error', error)
          }
          this.logger.log(JSON.stringify(data))
          throw new BadRequestException(
            `wechatCommentModel error${error.message} ${JSON.stringify(data)}`
          )
        }
      }
    }

    if (dataList.length) {
      try {
        this.webhookGrpcService.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {},
          error: (err) => {
            throw new BadRequestException(`socket发送失败${err.message}`)
          },
          complete: () => {}
        })
      } catch (error) {
        throw new BadRequestException(`socketService error${error.message}`)
      }
    }

    return 'success'
  }

  async webHookFinderSyncChatByLike(body: WebhookWechatFinderSyncMsg) {
    try {
      const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
      const maxTime = dayjs().startOf('day').unix()

      // 获取点赞数据
      const likeRecords = await this.getMentionList({
        appid: body.Appid,
        token: wechatConfig.Token,
        myUserName: body.Data.selfFinderUsername,
        maxTime,
        reqScene: 3
      })

      if (likeRecords.length > 0) {
        // 如果有点赞数据
        // 1.主动去获取私信Sessionid
        const likeRecord = likeRecords[0]
        const likeFromUserName = likeRecord?.contact?.contact?.username

        if (!likeFromUserName) {
          return
        }

        const [res, res2] = await Promise.all([
          this.cacheManager.store.client.hget(AutoresponderKeywordKey, body.Wxid),
          this.cacheManager.store.client.hget(
            AutoresponderKeywordKey,
            `${body.Wxid}:${likeRecord.refObjectId}`
          )
        ])

        const toUserId = body.Data.selfFinderUsername

        const fromUser = await getWechatMessageSessionId({
          appId: body.Appid,
          myUserName: body.Data.selfFinderUsername,
          toUserName: likeFromUserName,
          token: wechatConfig.Token,
          isNew: this.isNew
        })

        const { sessionId, toUsername: fromUserId } = fromUser

        await wait(1000)
        const arr = [
          ...JSON.parse(res || '[]'),
          ...JSON.parse(res2 || '[]')
        ] as AutoresponderKeywordRedisValue[]
        const items = []

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          body.Wxid
        )

        const {
          token: appId,
          accountExpired,
          username,
          platformAccountId,
          teamId,
          status
        } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const { contents, contentType, trigger, state, stopReply, scene, autoresponderId } = arr[
            i
          ] as AutoresponderKeywordRedisValue

          if (toUserId !== username || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (scene) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired * 1000 < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          const send = trigger === AutoresponderTriggerType.Like

          if (send && !job) {
            items.push(arr[i])
          }
        }

        const matchItem = items[generateRandom(items.length - 1)]

        this.logger.debug('视频号点赞回复私信策略', JSON.stringify(matchItem))

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId,
              toUserId,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'wechat'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponder-new-wechat',
            {
              sessionId,
              toUserId,
              teamId,
              appId,
              fromUserId,
              autoresponderId,
              contents,
              platformAccountId,
              openId: body.Wxid
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`视频号点赞回复私信策略回复失败 error${error.message}`)
    }
  }

  async webHookFinderSyncChatByFollow(body: WebhookWechatFinderSyncMsg) {
    try {
      const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, body.Wxid)

      if (res) {
        const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
        const maxTime = dayjs().startOf('day').unix()

        // 获取点赞数据
        const FollowRecords = await this.getMentionList({
          appid: body.Appid,
          token: wechatConfig.Token,
          myUserName: body.Data.selfFinderUsername,
          maxTime,
          reqScene: 5
        })

        if (FollowRecords.length > 0) {
          // 如果有关注数据
          // 1.主动去获取私信Sessionid
          const followRecord = FollowRecords[0]
          const followFromUserName = followRecord?.contact?.contact?.username

          if (!followFromUserName) {
            return
          }

          const toUserId = body.Data.selfFinderUsername

          const fromUser = await getWechatMessageSessionId({
            appId: body.Appid,
            myUserName: body.Data.selfFinderUsername,
            toUserName: followFromUserName,
            token: wechatConfig.Token,
            isNew: this.isNew
          })

          const { sessionId, toUsername: fromUserId } = fromUser

          await wait(1000)
          const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]
          const items = []

          const accountRes = await this.cacheManager.store.client.hget(
            PlatformAccountKeywordKey,
            body.Wxid
          )

          const {
            token: appId,
            accountExpired,
            username,
            platformAccountId,
            teamId,
            status
          } = JSON.parse(accountRes)

          for (let i = 0; i < arr.length; i++) {
            const { contents, contentType, trigger, state, stopReply, scene, autoresponderId } =
              arr[i] as AutoresponderKeywordRedisValue

            if (toUserId !== username || status === AccountAccountsStatus.Disable) {
              continue
            }

            if (scene) {
              continue
            }

            if (!Array.isArray(contents)) {
              continue
            }

            if (!contents.length) {
              continue
            }

            if (contentType !== AutoresponderContentType.Autoresponder) {
              continue
            }

            if (accountExpired * 1000 < Date.now() || !state) {
              continue
            }

            const job = await this.taskQueue.getJob(
              `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            )

            if (stopReply && job) {
              await job.remove()
              continue
            }

            const send = trigger === AutoresponderTriggerType.Follow

            if (send && !job) {
              items.push(arr[i])
            }
          }

          const matchItem = items[generateRandom(items.length - 1)]

          this.logger.debug('视频号关注回复私信策略', JSON.stringify(matchItem))

          if (matchItem) {
            const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

            if (
              stopInterval &&
              (await this.cacheManager.get(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`
              ))
            ) {
              return
            }

            if (stopInterval && stopTime > 0) {
              await this.cacheManager.set(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
                1,
                stopTime * 1000
              )
            }

            if (executionCount) {
              // 查询是否有对这个账号有发送过消息
              const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
                fromUserId,
                toUserId,
                autoresponderId
              })

              if (messageByAutoresponder) {
                return
              }

              await this.messagesByAutoresponderModel.create({
                fromUserId,
                toUserId,
                autoresponderId,
                platformType: 'wechat'
              })
            }

            const [start] = contents

            await this.taskQueue.add(
              'autoresponder-new-wechat',
              {
                sessionId,
                toUserId,
                teamId,
                appId,
                fromUserId,
                autoresponderId,
                contents,
                platformAccountId,
                openId: body.Wxid
              },
              {
                delay: start.delay * 1000 || 100,
                removeOnComplete: true,
                removeOnFail: true,
                jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
              }
            )
          }
        }
      }
    } catch (error) {
      throw new BadRequestException(`视频号关注回复私信回复失败${error.message}`)
    }
  }

  async webHookFinderSyncChatByComment(body: WebhookWechatFinderSyncMsg) {
    try {
      const { wechatConfig } = this.configService.get<RootConfigMap['app']>('app')
      const maxTime = dayjs().startOf('day').unix()

      // 获取点赞数据
      const commentRecords = await this.getMentionList({
        appid: body.Appid,
        token: wechatConfig.Token,
        myUserName: body.Data.selfFinderUsername,
        maxTime,
        reqScene: 4
      })

      if (commentRecords.length > 0) {
        // 如果有评论数据
        // 1.主动去获取私信Sessionid
        const commentRecord = commentRecords[0]
        const commentFromUserName = commentRecord?.contact?.contact?.username

        if (!commentFromUserName) {
          return
        }

        const [res, res2] = await Promise.all([
          this.cacheManager.store.client.hget(AutoresponderKeywordKey, body.Wxid),
          this.cacheManager.store.client.hget(
            AutoresponderKeywordKey,
            `${body.Wxid}:${commentRecord.refObjectId}`
          )
        ])

        const toUserId = body.Data.selfFinderUsername

        const fromUser = await getWechatMessageSessionId({
          appId: body.Appid,
          myUserName: body.Data.selfFinderUsername,
          toUserName: commentFromUserName,
          token: wechatConfig.Token,
          isNew: this.isNew
        })

        const { sessionId, toUsername: fromUserId } = fromUser

        await wait(1000)
        const arr = [
          ...JSON.parse(res || '[]'),
          ...JSON.parse(res2 || '[]')
        ] as AutoresponderKeywordRedisValue[]
        const items = []

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          body.Wxid
        )

        const {
          token: appId,
          accountExpired,
          username,
          platformAccountId,
          teamId,
          status
        } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const { contents, contentType, trigger, state, stopReply, scene, autoresponderId } = arr[
            i
          ] as AutoresponderKeywordRedisValue

          if (toUserId !== username || status === AccountAccountsStatus.Disable) {
            continue
          }

          if (scene) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (contentType !== AutoresponderContentType.Autoresponder) {
            continue
          }

          if (accountExpired * 1000 < Date.now() || !state) {
            continue
          }

          const job = await this.taskQueue.getJob(
            `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
          )

          if (stopReply && job) {
            await job.remove()
            continue
          }

          const send = trigger === AutoresponderTriggerType.Comment

          if (send && !job) {
            items.push(arr[i])
          }
        }

        const matchItem = items[generateRandom(items.length - 1)]

        this.logger.debug('视频号评论回复私信回复策略', JSON.stringify(matchItem))

        if (matchItem) {
          const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`))
          ) {
            return
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId,
              toUserId,
              autoresponderId
            })

            if (messageByAutoresponder) {
              return
            }

            await this.messagesByAutoresponderModel.create({
              fromUserId,
              toUserId,
              autoresponderId,
              platformType: 'wechat'
            })
          }

          const [start] = contents

          await this.taskQueue.add(
            'autoresponder-new-wechat',
            {
              sessionId,
              toUserId,
              teamId,
              appId,
              fromUserId,
              autoresponderId,
              contents,
              platformAccountId,
              openId: body.Wxid
            },
            {
              delay: start.delay * 1000 || 100,
              removeOnComplete: true,
              removeOnFail: true,
              jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            }
          )
        }
      }
    } catch (error) {
      throw new BadRequestException(`视频号评论回复私信回复失败${error.message}`)
    }
  }

  async autoresponderKeywordChat({
    fromUserId,
    toUserId,
    text,
    sessionId,
    wxid
  }: {
    fromUserId: string
    toUserId: string
    text: string
    sessionId: string
    wxid: string
  }) {
    if (fromUserId === toUserId) {
      return
    }

    if (text) {
      try {
        const res = await this.cacheManager.store.client.hget(AutoresponderKeywordKey, wxid)

        if (res) {
          await wait(1000)
          const arr = JSON.parse(res) as AutoresponderKeywordRedisValue[]
          const items = []

          const accountRes = await this.cacheManager.store.client.hget(
            PlatformAccountKeywordKey,
            wxid
          )

          const {
            token: appId,
            accountExpired,
            username,
            platformAccountId,
            teamId,
            status
          } = JSON.parse(accountRes)

          for (let i = 0; i < arr.length; i++) {
            const {
              contents,
              contentType,
              keyword,
              rule,
              state,
              stopReply,
              scene,
              autoresponderId,
              trigger
            } = arr[i] as AutoresponderKeywordRedisValue

            if (toUserId !== username || status === AccountAccountsStatus.Disable) {
              continue
            }

            if (scene) {
              continue
            }

            if (!Array.isArray(contents)) {
              continue
            }

            if (!contents.length) {
              continue
            }

            if (contentType !== AutoresponderContentType.Autoresponder) {
              continue
            }

            if (accountExpired * 1000 < Date.now() || !state) {
              continue
            }

            const job = await this.taskQueue.getJob(
              `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
            )

            if (stopReply && job) {
              await job.remove()
              continue
            }

            if (
              rule !== AutoresponderKeywordRule.Match &&
              rule !== AutoresponderKeywordRule.Instantly
            ) {
              // 收到私信回复只有两种规则（关键字和即刻回复）
              continue
            }

            let send = rule !== AutoresponderKeywordRule.Match

            if (!send) {
              send = !!(keyword as string[]).find((item) => {
                return RegExp(item, 'g').test(text)
              })
            }

            const chatMessage = trigger === AutoresponderTriggerType.Chat

            if (send && !job && chatMessage) {
              items.push(arr[i])
            }
          }

          const matchItem =
            items.find((item) => item.rule === AutoresponderKeywordRule.Match) ||
            items[generateRandom(items.length - 1)]

          this.logger.debug('autoresponderKeywordChat matchItem', JSON.stringify(matchItem))

          if (matchItem) {
            const { contents, autoresponderId, stopTime, stopInterval, executionCount } = matchItem

            if (
              stopInterval &&
              (await this.cacheManager.get(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`
              ))
            ) {
              return
            }

            if (stopInterval && stopTime > 0) {
              await this.cacheManager.set(
                `stop-chat:${fromUserId}-${toUserId}-${autoresponderId}`,
                1,
                stopTime * 1000
              )
            }

            if (executionCount) {
              // 查询是否有对这个账号有发送过消息
              const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
                fromUserId,
                toUserId,
                autoresponderId
              })

              if (messageByAutoresponder) {
                return
              }

              await this.messagesByAutoresponderModel.create({
                fromUserId,
                toUserId,
                autoresponderId,
                platformType: 'wechat'
              })
            }

            const [start] = contents

            await this.taskQueue.add(
              'autoresponder-new-wechat',
              {
                sessionId,
                toUserId,
                teamId,
                appId,
                fromUserId,
                autoresponderId,
                contents,
                platformAccountId,
                openId: wxid
              },
              {
                delay: start.delay * 1000 || 100,
                removeOnComplete: true,
                removeOnFail: true,
                jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
              }
            )
          }
        }
      } catch (error) {
        throw new BadRequestException(`AutoresponderKeywordKey error${error.message}`)
      }
    }
  }

  async autoresponderTask(
    contents: AnyObject[],
    {
      fromUserId,
      sessionId,
      toUserId,
      teamId,
      autoresponderId,
      appId,
      platformAccountId,
      openId
    }: {
      sessionId: string
      toUserId: string
      fromUserId: string
      teamId: number
      autoresponderId: number
      appId: string
      platformAccountId: number
      openId: string
    }
  ) {
    const [content, ...otherContents] = contents as AutoresponderKeywordContent[]

    if (fromUserId === toUserId) {
      return
    }

    if (!content) {
      return
    }

    await this.autoresponderSend(content, {
      sessionId,
      appId,
      toUserId,
      fromUserId,
      teamId,
      autoresponderId,
      platformAccountId,
      openId
    })

    if (otherContents.length) {
      const [start] = otherContents

      await this.taskQueue.add(
        'autoresponder-new-wechat',
        {
          sessionId,
          toUserId,
          teamId,
          appId,
          fromUserId,
          autoresponderId,
          contents: otherContents,
          platformAccountId,
          openId
        },
        {
          delay: start.delay * 1000 || 100,
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${AutoresponderKeywordKey}-${fromUserId}-${toUserId}-${autoresponderId}`
        }
      )
    }
  }

  async autoresponderSend(
    content: AutoresponderKeywordContent,
    {
      fromUserId,
      sessionId,
      appId,
      toUserId,
      teamId,
      autoresponderId,
      platformAccountId,
      openId
    }: {
      appId: string
      fromUserId: string
      sessionId: string
      toUserId: string
      teamId: number
      autoresponderId: number
      platformAccountId: number
      openId: string
    }
  ) {
    let contentText = ''

    if (content.messageType === AutoresponderContentChildType.Text) {
      for (let j = 0; j < content.texts.length; j++) {
        const text = content.texts[j]
        if (text.type === AutoresponderContentTextType.Text) {
          contentText += text.text
        } else if (text.type === AutoresponderContentTextType.Variable) {
          const value = await this.cacheManager.store.client.hget(
            AutoresponderVariableKey,
            `${text.variableId}`
          )
          try {
            const values = JSON.parse(value).value
            contentText += values[generateRandom(values.length - 1)]
          } catch (err) {
            throw new BadRequestException(`autoresponderSend error${err.message}`)
          }
        }
      }
    }

    try {
      const data = await wechatSendMessages({
        platformAccountId,
        appId,
        toUserName: fromUserId,
        myUserName: toUserId,
        sessionId,
        content: contentText,
        imgUrl: content.imageUrl,
        token: this.wechatToken,
        teamId,
        autoresponderId,
        redisClient: this.cacheManager,
        openId,
        isNew: this.isNew
      })

      await this.cacheManager.set(data.messageId, 1, 1000 * 60 * 10)
    } catch (err) {
      throw new BadRequestException(`自动回复失败 error${err.message}`)
    }
  }

  async autoresponderKeywordComment(body: WechatCommentBody) {
    try {
      if (body.fromUserName === body.toUserName) {
        return
      }

      const [res, res2] = await Promise.all([
        this.cacheManager.store.client.hget(AutoresponderKeywordKey, body.wxid),
        this.cacheManager.store.client.hget(
          AutoresponderKeywordKey,
          `${body.wxid}:${body.objectId}`
        )
      ])

      if (res || res2) {
        const arr = [
          ...JSON.parse(res || '[]'),
          ...JSON.parse(res2 || '[]')
        ] as AutoresponderKeywordRedisValue[]

        const accountRes = await this.cacheManager.store.client.hget(
          PlatformAccountKeywordKey,
          body.wxid
        )

        const { accountExpired, platformAccountId, teamId } = JSON.parse(accountRes)

        for (let i = 0; i < arr.length; i++) {
          const item = arr[i]

          const {
            contents,
            keyword,
            rule,
            state,
            scene,
            autoresponderId,
            stopTime,
            stopInterval,
            isDelay,
            trigger,
            delayTime,
            executionCount
          } = item

          if (
            stopInterval &&
            (await this.cacheManager.get(`stop-comment:${body.fromUserName}-${autoresponderId}`))
          ) {
            continue
          }

          if (!scene) {
            continue
          }

          if (trigger !== AutoresponderTriggerType.Comment) {
            continue
          }

          if (!state || accountExpired * 1000 < Date.now()) {
            continue
          }

          if (!Array.isArray(contents)) {
            continue
          }

          if (!contents.length) {
            continue
          }

          if (executionCount) {
            // 查询是否有对这个账号有发送过消息
            const messageByAutoresponder = await this.messagesByAutoresponderModel.findOne({
              fromUserId: body.fromUserName,
              toUserId: body.toUserName,
              autoresponderId
            })

            if (messageByAutoresponder) {
              continue
            }
          }

          const content = contents[
            generateRandom(contents.length - 1)
          ] as AutoresponderKeywordContent

          if (!content || !Array.isArray(content.texts)) {
            continue
          }

          const contentText = content.texts.reduce((acc, cur) => {
            acc += cur.text
            return acc
          }, '')

          if (rule === AutoresponderKeywordRule.Match) {
            const value = (keyword as string[]).find((item) => {
              return RegExp(item, 'g').test(body.content as string)
            })

            this.logger.debug('autoresponderKeywordComment matchItem', JSON.stringify(item))

            if (value) {
              this.taskCommentQueue.add(
                'autoresponder-new-wechat-comment',
                {
                  appId: body.appid,
                  myUserName: body.toUserName,
                  objectNonceId: body.objectNonceId,
                  token: this.wechatToken,
                  sessionBuffer: body.sessionBuffer,
                  content: contentText,
                  refCommentId: body.refCommentId,
                  rootCommentId: body.refCommentId,
                  replyUserName: body.fromUserName,
                  objectId: body.objectId,
                  openId: body.wxid,
                  autoresponderId,
                  teamId,
                  platformAccountId
                },
                {
                  delay: isDelay && delayTime > 0 ? generateRandom(delayTime) * 1000 : 100,
                  removeOnComplete: true,
                  removeOnFail: true,
                  jobId: `${AutoresponderKeywordKey}-${body.wxid}-${body.fromUserName}-${body.refCommentId}`
                }
              )
            }
          } else {
            this.logger.debug('autoresponderKeywordCommentCommon', JSON.stringify(item))
            this.taskCommentQueue.add(
              'autoresponder-new-wechat-comment',
              {
                appId: body.appid,
                myUserName: body.toUserName,
                objectNonceId: body.objectNonceId,
                token: this.wechatToken,
                sessionBuffer: body.sessionBuffer,
                content: contentText,
                refCommentId: body.refCommentId,
                rootCommentId: body.refCommentId,
                replyUserName: body.fromUserName,
                objectId: body.objectId,
                openId: body.wxid,
                autoresponderId,
                teamId,
                platformAccountId
              },
              {
                delay: isDelay && delayTime > 0 ? generateRandom(delayTime) * 1000 : 100,
                removeOnComplete: true,
                removeOnFail: true,
                jobId: `${AutoresponderKeywordKey}-${body.wxid}-${body.fromUserName}-${body.refCommentId}`
              }
            )
          }

          if (stopInterval && stopTime > 0) {
            await this.cacheManager.set(
              `stop-comment:${body.fromUserName}-${autoresponderId}`,
              1,
              stopTime * 1000
            )
          }

          if (executionCount) {
            await this.messagesByAutoresponderModel.create({
              fromUserId: body.fromUserName,
              toUserId: body.toUserName,
              autoresponderId,
              platformType: 'wechat'
            })
          }
        }
      }
    } catch (error) {
      throw new BadRequestException(`autoresponderKeywordComment error${error.message}`)
    }
  }

  async commentAutoresponderSend({
    appId,
    myUserName,
    objectNonceId,
    token,
    sessionBuffer,
    content,
    refCommentId,
    rootCommentId,
    autoresponderId,
    objectId,
    openId,
    replyUserName,
    teamId,
    platformAccountId
  }: {
    appId: string
    myUserName: string
    objectNonceId?: string
    token: string
    sessionBuffer: string
    content: string
    refCommentId?: string
    rootCommentId?: string
    replyUserName?: string
    autoresponderId?: number
    objectId: string
    openId: string
    teamId: number
    platformAccountId: number
  }) {
    try {
      const data = await createComment({
        appId,
        myUserName,
        objectNonceId,
        token,
        sessionBuffer,
        content,
        refCommentId,
        rootCommentId,
        replyUserName,
        objectId,
        autoresponderId,
        teamId,
        platformAccountId,
        openId,
        redisClient: this.cacheManager,
        isNew: this.isNew
      })

      if (data?.commentId) {
        await this.cacheManager.set(data.data, 1, 1000 * 60 * 10)
      }
    } catch (err) {
      throw new BadRequestException(`自动回复失败 ${err.message}`)
    }
  }

  async getMentionList(data: {
    appid: string
    token: string
    myUserName: string
    maxTime: number
    reqScene: number
  }) {
    let attempts = 0
    let lastBuff = ''
    const mentionData = []
    while (attempts < 10) {
      try {
        const mentionList = await postMentionList({
          appId: data.appid,
          token: data.token,
          myUserName: data.myUserName,
          myRoleType: 3,
          reqScene: data.reqScene,
          lastBuff,
          isNew: this.isNew
        })

        let breakFlag = false

        if (
          mentionList &&
          lastBuff === mentionList.data.lastBuff &&
          mentionList?.data?.list?.mentions?.length === 0
        ) {
          break
        }

        if (mentionList && mentionList?.data?.list?.mentions?.length > 0) {
          mentionList.data.list.mentions.forEach((item) => {
            if (item.createtime * 1000 > data.maxTime) {
              mentionData.push(item)
            } else {
              breakFlag = true
            }
          })

          if (breakFlag) {
            break
          }
        }
        ;({ lastBuff } = mentionList.data)
        attempts++
      } catch (error) {
        throw new BadRequestException(`getMentionList error${error.message}`)
      }
    }

    return mentionData
  }

  async getOpusList(data: { appid: string; token: string; myUserName: string; maxTime: number }) {
    let attempts = 0
    let lastBuffer = ''
    let maxId = '0'
    const result = []
    let continueFlag = 0

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const videoList = await postUserPage({
          appId: data.appid,
          toUserName: data.myUserName,
          lastBuffer,
          maxId,
          token: data.token,
          isNew: this.isNew
        })

        ;({ continueFlag, lastBuffer } = videoList)

        if (videoList.object) {
          maxId = videoList.object[videoList.object.length - 1].id
          if (data.maxTime === 0) {
            // 如果maxTime为0 不比较时间
            const list = videoList.object.filter((item) => item?.privateFlag !== 1)
            list.forEach((element) => {
              result.push(element)
            })
          } else {
            const recordLength = videoList.object.filter((item) => item?.privateFlag !== 1).length

            const maxRecordLength = videoList.object.filter(
              (item) => item?.privateFlag !== 1 && item.createtime > data.maxTime
            ).length

            const list = videoList.object.filter(
              (item) => item?.privateFlag !== 1 && item.createtime > data.maxTime
            )

            list.forEach((element) => {
              result.push(element)
            })

            if (recordLength !== maxRecordLength) {
              break
            }
          }
        } else {
          break
        }

        if (attempts >= 10) {
          break
        }

        if (!continueFlag) {
          break
        }

        attempts++
      } catch (err) {
        throw new BadRequestException(`获取视频列表失败${err.message}`)
      }
    }

    return result
  }

  async uploadFromUrl(data: { url: string; teamCode: string }) {
    try {
      // 使用 axios 请求远程视频文件并获取流
      return await this.tosManageService.putObjectByStream({
        url: data.url,
        referer: '',
        teamCode: data.teamCode
      })
    } catch (error) {
      this.logger.log(error.message)
    }
  }
}
